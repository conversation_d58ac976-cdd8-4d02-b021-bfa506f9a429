{"name": "collection-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.13", "vue-router": "^4.2.5", "pinia": "^2.1.7", "ant-design-vue": "^4.0.8", "@ant-design/icons-vue": "^7.0.1", "axios": "^1.6.5", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "echarts": "^5.4.3", "vue-echarts": "^6.6.8"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.2", "vite": "^5.0.10", "unplugin-vue-components": "^0.26.0", "unplugin-auto-import": "^0.17.3", "sass": "^1.69.7", "@types/lodash-es": "^4.17.12"}}