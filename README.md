# 催收管理系统前端

基于 Vue 3 + Vite + Ant Design Vue 开发的催收管理系统前端应用。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 新一代前端构建工具
- **Ant Design Vue** - 企业级 UI 组件库
- **Vue Router** - 官方路由管理器
- **Pinia** - 新一代状态管理工具
- **Axios** - HTTP 客户端
- **ECharts** - 数据可视化图表库

## 项目结构

```
催收管理系统/
├── src/
│   ├── api/               # API 接口层
│   ├── assets/            # 静态资源
│   ├── components/        # 公共组件
│   ├── layouts/           # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── cases/         # 案件管理
│   │   ├── customers/     # 客户管理
│   │   ├── collection/    # 催收记录
│   │   ├── payment/       # 还款管理
│   │   ├── reports/       # 报表统计
│   │   └── system/        # 系统设置
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── public/                # 公共资源
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 功能模块

### 已完成
- ✅ 项目初始化和配置
- ✅ 登录认证
- ✅ 主体布局和导航
- ✅ 首页仪表板
- ✅ 案件列表
- ✅ 案件分配

### 开发中
- 🚧 案件跟进
- 🚧 客户管理
- 🚧 催收记录
- 🚧 还款管理
- 🚧 报表统计
- 🚧 系统设置

## 开发环境

### 前置要求
- Node.js >= 16
- npm >= 7

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## API 接口

前端通过代理转发请求到后端 API：

```javascript
// vite.config.js
proxy: {
  '/api': {
    target: 'http://localhost:8080',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api/, '')
  }
}
```

## 登录信息

测试账号：
- 用户名：admin
- 密码：123456

## 注意事项

1. 确保后端服务运行在 http://localhost:8080
2. 开发时使用 Chrome 浏览器的最新版本
3. 推荐使用 VSCode 编辑器配合 Volar 插件

## 部署

### 构建

```bash
npm run build
```

### 部署到 Nginx

将 `dist` 目录下的文件部署到 Nginx 服务器：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend-server:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 贡献

欢迎提交 Issue 和 Pull Request。

## 许可证

[MIT License](LICENSE)