# 催收管理系统 - 快速启动指南

## 启动步骤

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **访问系统**
   - 打开浏览器访问：http://localhost:3000
   - 如果 3000 端口被占用，会自动使用其他端口（如 3001）

## 登录信息

- **用户名**：admin
- **密码**：123456

## 功能说明

### 已完成功能
- ✅ 登录认证（模拟登录，无需后端）
- ✅ 主界面布局
- ✅ 首页仪表板（统计图表）
- ✅ 案件列表（搜索、筛选、表格）
- ✅ 案件分配（分配规则、工作量统计）

### 开发中功能
- 🚧 其他模块页面（显示占位内容）

## 常见问题

### 1. 首页仪表板显示问题
已修复，图标现在使用正确的插槽方式渲染。

### 2. 登录无响应
系统使用模拟登录，请使用以下账号：
- 用户名：admin
- 密码：123456

### 3. 页面空白
请确保：
- Node.js 版本 >= 16
- 正确安装了所有依赖
- 使用现代浏览器（Chrome/Firefox/Edge 最新版）

## 技术支持

如遇到问题，请检查：
1. 控制台是否有错误信息
2. 网络请求是否正常
3. 依赖是否正确安装