import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '首页仪表板' }
      },
      // 案件管理
      {
        path: 'cases',
        name: 'Cases',
        redirect: '/cases/list',
        meta: { title: '案件管理' },
        children: [
          {
            path: 'list',
            name: 'CaseList',
            component: () => import('@/views/cases/CaseList.vue'),
            meta: { title: '案件列表' }
          },
          {
            path: 'allocation',
            name: 'CaseAllocation',
            component: () => import('@/views/cases/CaseAllocation.vue'),
            meta: { title: '案件分配' }
          },
          {
            path: 'tracking',
            name: 'CaseTracking',
            component: () => import('@/views/cases/CaseTracking.vue'),
            meta: { title: '案件跟进' }
          },
          {
            path: 'transfer',
            name: 'CaseTransfer',
            component: () => import('@/views/cases/CaseTransfer.vue'),
            meta: { title: '案件转移' }
          },
          {
            path: 'archive',
            name: 'CaseArchive',
            component: () => import('@/views/cases/CaseArchive.vue'),
            meta: { title: '案件归档' }
          }
        ]
      },
      // 客户管理
      {
        path: 'customers',
        name: 'Customers',
        redirect: '/customers/list',
        meta: { title: '客户管理' },
        children: [
          {
            path: 'list',
            name: 'CustomerList',
            component: () => import('@/views/customers/CustomerList.vue'),
            meta: { title: '客户档案' }
          },
          {
            path: 'contacts',
            name: 'ContactManagement',
            component: () => import('@/views/customers/ContactManagement.vue'),
            meta: { title: '联系人管理' }
          },
          {
            path: 'segmentation',
            name: 'CustomerSegmentation',
            component: () => import('@/views/customers/CustomerSegmentation.vue'),
            meta: { title: '客户分群' }
          },
          {
            path: 'credit',
            name: 'CreditAssessment',
            component: () => import('@/views/customers/CreditAssessment.vue'),
            meta: { title: '信用评估' }
          },
          {
            path: 'blacklist',
            name: 'Blacklist',
            component: () => import('@/views/customers/Blacklist.vue'),
            meta: { title: '黑名单管理' }
          }
        ]
      },
      // 催收记录
      {
        path: 'collection',
        name: 'Collection',
        redirect: '/collection/call',
        meta: { title: '催收记录' },
        children: [
          {
            path: 'call',
            name: 'CallRecords',
            component: () => import('@/views/collection/CallRecords.vue'),
            meta: { title: '通话记录' }
          },
          {
            path: 'sms',
            name: 'SmsRecords',
            component: () => import('@/views/collection/SmsRecords.vue'),
            meta: { title: '短信记录' }
          },
          {
            path: 'visit',
            name: 'VisitRecords',
            component: () => import('@/views/collection/VisitRecords.vue'),
            meta: { title: '外访记录' }
          },
          {
            path: 'legal',
            name: 'LegalRecords',
            component: () => import('@/views/collection/LegalRecords.vue'),
            meta: { title: '法务记录' }
          },
          {
            path: 'email',
            name: 'EmailRecords',
            component: () => import('@/views/collection/EmailRecords.vue'),
            meta: { title: '邮件记录' }
          }
        ]
      },
      // 还款管理
      {
        path: 'payment',
        name: 'Payment',
        redirect: '/payment/plan',
        meta: { title: '还款管理' },
        children: [
          {
            path: 'plan',
            name: 'PaymentPlan',
            component: () => import('@/views/payment/PaymentPlan.vue'),
            meta: { title: '还款计划' }
          },
          {
            path: 'records',
            name: 'PaymentRecords',
            component: () => import('@/views/payment/PaymentRecords.vue'),
            meta: { title: '还款记录' }
          },
          {
            path: 'installment',
            name: 'InstallmentManagement',
            component: () => import('@/views/payment/InstallmentManagement.vue'),
            meta: { title: '分期管理' }
          },
          {
            path: 'reduction',
            name: 'ReductionManagement',
            component: () => import('@/views/payment/ReductionManagement.vue'),
            meta: { title: '减免管理' }
          },
          {
            path: 'writeoff',
            name: 'WriteoffManagement',
            component: () => import('@/views/payment/WriteoffManagement.vue'),
            meta: { title: '核销管理' }
          }
        ]
      },
      // 风险控制
      {
        path: 'risk-control',
        name: 'RiskControl',
        redirect: '/risk-control/assessment',
        meta: { title: '风险控制' },
        children: [
          {
            path: 'assessment',
            name: 'RiskAssessment',
            component: () => import('@/views/risk-control/RiskAssessment.vue'),
            meta: { title: '风险评估' }
          },
          {
            path: 'warning',
            name: 'WarningManagement',
            component: () => import('@/views/risk-control/WarningManagement.vue'),
            meta: { title: '预警管理' }
          },
          {
            path: 'monitoring',
            name: 'RiskMonitoring',
            component: () => import('@/views/risk-control/RiskMonitoring.vue'),
            meta: { title: '风险监控' }
          },
          {
            path: 'anti-fraud',
            name: 'AntiFraud',
            component: () => import('@/views/risk-control/AntiFraud.vue'),
            meta: { title: '反欺诈' }
          },
          {
            path: 'compliance-check',
            name: 'ComplianceCheck',
            component: () => import('@/views/risk-control/ComplianceCheck.vue'),
            meta: { title: '合规检查' }
          }
        ]
      },
      // 合规管理
      {
        path: 'compliance',
        name: 'Compliance',
        redirect: '/compliance/policy',
        meta: { title: '合规管理' },
        children: [
          {
            path: 'policy',
            name: 'CompliancePolicy',
            component: () => import('@/views/compliance/CompliancePolicy.vue'),
            meta: { title: '合规政策' }
          },
          {
            path: 'check',
            name: 'ComplianceInspection',
            component: () => import('@/views/compliance/ComplianceInspection.vue'),
            meta: { title: '合规检查' }
          },
          {
            path: 'complaint',
            name: 'ComplaintHandling',
            component: () => import('@/views/compliance/ComplaintHandling.vue'),
            meta: { title: '投诉处理' }
          },
          {
            path: 'regulation',
            name: 'RegulationUpdate',
            component: () => import('@/views/compliance/RegulationUpdate.vue'),
            meta: { title: '法规更新' }
          },
          {
            path: 'training',
            name: 'ComplianceTraining',
            component: () => import('@/views/compliance/ComplianceTraining.vue'),
            meta: { title: '合规培训' }
          }
        ]
      },
      // 绩效管理
      {
        path: 'performance',
        name: 'Performance',
        redirect: '/performance/assessment',
        meta: { title: '绩效管理' },
        children: [
          {
            path: 'assessment',
            name: 'PerformanceAssessment',
            component: () => import('@/views/performance/PerformanceAssessment.vue'),
            meta: { title: '绩效考核' }
          },
          {
            path: 'target',
            name: 'TargetManagement',
            component: () => import('@/views/performance/TargetManagement.vue'),
            meta: { title: '目标管理' }
          },
          {
            path: 'incentive',
            name: 'IncentiveScheme',
            component: () => import('@/views/performance/IncentiveScheme.vue'),
            meta: { title: '激励方案' }
          },
          {
            path: 'ranking',
            name: 'PerformanceRanking',
            component: () => import('@/views/performance/PerformanceRanking.vue'),
            meta: { title: '排行榜' }
          },
          {
            path: 'analysis',
            name: 'PerformanceAnalysis',
            component: () => import('@/views/performance/PerformanceAnalysis.vue'),
            meta: { title: '绩效分析' }
          }
        ]
      },
      // 培训管理
      {
        path: 'training',
        name: 'Training',
        redirect: '/training/plan',
        meta: { title: '培训管理' },
        children: [
          {
            path: 'plan',
            name: 'TrainingPlan',
            component: () => import('@/views/training/TrainingPlan.vue'),
            meta: { title: '培训计划' }
          },
          {
            path: 'course',
            name: 'CourseManagement',
            component: () => import('@/views/training/CourseManagement.vue'),
            meta: { title: '课程管理' }
          },
          {
            path: 'exam',
            name: 'ExamManagement',
            component: () => import('@/views/training/ExamManagement.vue'),
            meta: { title: '考试管理' }
          },
          {
            path: 'skill',
            name: 'SkillAssessment',
            component: () => import('@/views/training/SkillAssessment.vue'),
            meta: { title: '技能评估' }
          },
          {
            path: 'archive',
            name: 'TrainingArchive',
            component: () => import('@/views/training/TrainingArchive.vue'),
            meta: { title: '培训档案' }
          }
        ]
      },
      // 客户服务
      {
        path: 'customer-service',
        name: 'CustomerService',
        redirect: '/customer-service/online',
        meta: { title: '客户服务' },
        children: [
          {
            path: 'online',
            name: 'OnlineService',
            component: () => import('@/views/customer-service/OnlineService.vue'),
            meta: { title: '在线客服' }
          },
          {
            path: 'complaint',
            name: 'ComplaintManagement',
            component: () => import('@/views/customer-service/ComplaintManagement.vue'),
            meta: { title: '投诉管理' }
          },
          {
            path: 'survey',
            name: 'SatisfactionSurvey',
            component: () => import('@/views/customer-service/SatisfactionSurvey.vue'),
            meta: { title: '满意度调查' }
          },
          {
            path: 'record',
            name: 'ServiceRecord',
            component: () => import('@/views/customer-service/ServiceRecord.vue'),
            meta: { title: '服务记录' }
          },
          {
            path: 'knowledge',
            name: 'KnowledgeBase',
            component: () => import('@/views/customer-service/KnowledgeBase.vue'),
            meta: { title: '知识库' }
          }
        ]
      },
      // 报表统计
      {
        path: 'reports',
        name: 'Reports',
        redirect: '/reports/collection',
        meta: { title: '报表统计' },
        children: [
          {
            path: 'collection',
            name: 'CollectionReport',
            component: () => import('@/views/reports/CollectionReport.vue'),
            meta: { title: '催收效果报表' }
          },
          {
            path: 'case-analysis',
            name: 'CaseAnalysis',
            component: () => import('@/views/reports/CaseAnalysis.vue'),
            meta: { title: '案件分析报表' }
          },
          {
            path: 'performance',
            name: 'PerformanceReport',
            component: () => import('@/views/reports/PerformanceReport.vue'),
            meta: { title: '人员绩效报表' }
          },
          {
            path: 'financial',
            name: 'FinancialReport',
            component: () => import('@/views/reports/FinancialReport.vue'),
            meta: { title: '财务报表' }
          },
          {
            path: 'payment',
            name: 'PaymentReport',
            component: () => import('@/views/reports/PaymentReport.vue'),
            meta: { title: '回款报表' }
          },
          {
            path: 'risk',
            name: 'RiskReport',
            component: () => import('@/views/reports/RiskReport.vue'),
            meta: { title: '风险监控' }
          },
          {
            path: 'risk-analysis',
            name: 'RiskAnalysisReport',
            component: () => import('@/views/reports/RiskAnalysisReport.vue'),
            meta: { title: '风险分析报表' }
          },
          {
            path: 'compliance-report',
            name: 'ComplianceReport',
            component: () => import('@/views/reports/ComplianceReport.vue'),
            meta: { title: '合规报表' }
          },
          {
            path: 'operation',
            name: 'OperationReport',
            component: () => import('@/views/reports/OperationReport.vue'),
            meta: { title: '运营报表' }
          },
          {
            path: 'visualization',
            name: 'DataVisualization',
            component: () => import('@/views/reports/DataVisualization.vue'),
            meta: { title: '数据可视化' }
          }
        ]
      },
      // 流程管理
      {
        path: 'workflow',
        name: 'Workflow',
        redirect: '/workflow/design',
        meta: { title: '流程管理' },
        children: [
          {
            path: 'design',
            name: 'WorkflowDesign',
            component: () => import('@/views/workflow/WorkflowDesign.vue'),
            meta: { title: '工作流设计' }
          },
          {
            path: 'approval',
            name: 'ApprovalProcess',
            component: () => import('@/views/workflow/ApprovalProcess.vue'),
            meta: { title: '审批流程' }
          },
          {
            path: 'monitoring',
            name: 'ProcessMonitoring',
            component: () => import('@/views/workflow/ProcessMonitoring.vue'),
            meta: { title: '流程监控' }
          },
          {
            path: 'optimization',
            name: 'ProcessOptimization',
            component: () => import('@/views/workflow/ProcessOptimization.vue'),
            meta: { title: '流程优化' }
          },
          {
            path: 'template',
            name: 'ProcessTemplate',
            component: () => import('@/views/workflow/ProcessTemplate.vue'),
            meta: { title: '流程模板' }
          }
        ]
      },
      // 系统设置
      {
        path: 'system',
        name: 'System',
        redirect: '/system/users',
        meta: { title: '系统设置' },
        children: [
          {
            path: 'users',
            name: 'UserManagement',
            component: () => import('@/views/system/UserManagement.vue'),
            meta: { title: '用户管理' }
          },
          {
            path: 'roles',
            name: 'RoleManagement',
            component: () => import('@/views/system/RoleManagement.vue'),
            meta: { title: '角色权限' }
          },
          {
            path: 'config',
            name: 'SystemConfig',
            component: () => import('@/views/system/SystemConfig.vue'),
            meta: { title: '参数配置' }
          },
          {
            path: 'templates',
            name: 'TemplateManagement',
            component: () => import('@/views/system/TemplateManagement.vue'),
            meta: { title: '模板管理' }
          },
          {
            path: 'data',
            name: 'DataManagement',
            component: () => import('@/views/system/DataManagement.vue'),
            meta: { title: '数据管理' }
          },
          {
            path: 'interface',
            name: 'InterfaceConfig',
            component: () => import('@/views/system/InterfaceConfig.vue'),
            meta: { title: '接口配置' }
          },
          {
            path: 'monitoring',
            name: 'SystemMonitoring',
            component: () => import('@/views/system/SystemMonitoring.vue'),
            meta: { title: '系统监控' }
          }
        ]
      },
      // 质检管理
      {
        path: 'quality',
        name: 'Quality',
        redirect: '/quality/plan',
        meta: { title: '质检管理' },
        children: [
          {
            path: 'plan',
            name: 'QualityPlan',
            component: () => import('@/views/quality/QualityPlan.vue'),
            meta: { title: '质检计划' }
          },
          {
            path: 'execution',
            name: 'QualityExecution',
            component: () => import('@/views/quality/QualityExecution.vue'),
            meta: { title: '质检执行' }
          },
          {
            path: 'report',
            name: 'QualityReport',
            component: () => import('@/views/quality/QualityReport.vue'),
            meta: { title: '质检报告' }
          },
          {
            path: 'improvement',
            name: 'QualityImprovement',
            component: () => import('@/views/quality/QualityImprovement.vue'),
            meta: { title: '问题整改' }
          },
          {
            path: 'analysis',
            name: 'QualityAnalysis',
            component: () => import('@/views/quality/QualityAnalysis.vue'),
            meta: { title: '质检分析' }
          }
        ]
      },
      // 工作台
      {
        path: 'workspace',
        name: 'Workspace',
        redirect: '/workspace/personal',
        meta: { title: '工作台' },
        children: [
          {
            path: 'personal',
            name: 'PersonalWorkspace',
            component: () => import('@/views/workspace/PersonalWorkspace.vue'),
            meta: { title: '个人工作台' }
          },
          {
            path: 'team',
            name: 'TeamWorkspace',
            component: () => import('@/views/workspace/TeamWorkspace.vue'),
            meta: { title: '团队工作台' }
          },
          {
            path: 'dashboard',
            name: 'ManagementDashboard',
            component: () => import('@/views/workspace/ManagementDashboard.vue'),
            meta: { title: '管理驾驶舱' }
          },
          {
            path: 'message',
            name: 'MessageCenter',
            component: () => import('@/views/workspace/MessageCenter.vue'),
            meta: { title: '消息中心' }
          },
          {
            path: 'quick',
            name: 'QuickOperations',
            component: () => import('@/views/workspace/QuickOperations.vue'),
            meta: { title: '快捷操作' }
          }
        ]
      },
      // 智能分析
      {
        path: 'intelligence',
        name: 'Intelligence',
        redirect: '/intelligence/portrait',
        meta: { title: '智能分析' },
        children: [
          {
            path: 'portrait',
            name: 'CustomerPortrait',
            component: () => import('@/views/intelligence/CustomerPortrait.vue'),
            meta: { title: '客户画像' }
          },
          {
            path: 'behavior',
            name: 'BehaviorAnalysis',
            component: () => import('@/views/intelligence/BehaviorAnalysis.vue'),
            meta: { title: '行为分析' }
          },
          {
            path: 'prediction',
            name: 'PredictionModel',
            component: () => import('@/views/intelligence/PredictionModel.vue'),
            meta: { title: '预测模型' }
          },
          {
            path: 'strategy',
            name: 'StrategyRecommendation',
            component: () => import('@/views/intelligence/StrategyRecommendation.vue'),
            meta: { title: '策略推荐' }
          },
          {
            path: 'evaluation',
            name: 'EffectEvaluation',
            component: () => import('@/views/intelligence/EffectEvaluation.vue'),
            meta: { title: '效果评估' }
          }
        ]
      },
      // 移动应用
      {
        path: 'mobile',
        name: 'Mobile',
        redirect: '/mobile/workspace',
        meta: { title: '移动应用' },
        children: [
          {
            path: 'workspace',
            name: 'MobileWorkspace',
            component: () => import('@/views/mobile/MobileWorkspace.vue'),
            meta: { title: '移动工作台' }
          },
          {
            path: 'field',
            name: 'FieldManagement',
            component: () => import('@/views/mobile/FieldManagement.vue'),
            meta: { title: '外勤管理' }
          },
          {
            path: 'communication',
            name: 'RealtimeCommunication',
            component: () => import('@/views/mobile/RealtimeCommunication.vue'),
            meta: { title: '实时通讯' }
          },
          {
            path: 'offline',
            name: 'OfflineSync',
            component: () => import('@/views/mobile/OfflineSync.vue'),
            meta: { title: '离线同步' }
          },
          {
            path: 'location',
            name: 'LocationService',
            component: () => import('@/views/mobile/LocationService.vue'),
            meta: { title: '位置服务' }
          }
        ]
      },
      // 第三方集成
      {
        path: 'integration',
        name: 'Integration',
        redirect: '/integration/system',
        meta: { title: '第三方集成' },
        children: [
          {
            path: 'system',
            name: 'SystemInterface',
            component: () => import('@/views/integration/SystemInterface.vue'),
            meta: { title: '系统接口' }
          },
          {
            path: 'sync',
            name: 'DataSync',
            component: () => import('@/views/integration/DataSync.vue'),
            meta: { title: '数据同步' }
          },
          {
            path: 'message',
            name: 'MessagePush',
            component: () => import('@/views/integration/MessagePush.vue'),
            meta: { title: '消息推送' }
          },
          {
            path: 'payment',
            name: 'PaymentIntegration',
            component: () => import('@/views/integration/PaymentIntegration.vue'),
            meta: { title: '支付集成' }
          },
          {
            path: 'credit',
            name: 'CreditInterface',
            component: () => import('@/views/integration/CreditInterface.vue'),
            meta: { title: '征信接口' }
          }
        ]
      },
      // 决策支持
      {
        path: 'decision',
        name: 'Decision',
        redirect: '/decision/model',
        meta: { title: '决策支持' },
        children: [
          {
            path: 'model',
            name: 'DecisionModel',
            component: () => import('@/views/decision/DecisionModel.vue'),
            meta: { title: '决策模型' }
          },
          {
            path: 'engine',
            name: 'StrategyEngine',
            component: () => import('@/views/decision/StrategyEngine.vue'),
            meta: { title: '策略引擎' }
          },
          {
            path: 'rules',
            name: 'RuleConfiguration',
            component: () => import('@/views/decision/RuleConfiguration.vue'),
            meta: { title: '规则配置' }
          },
          {
            path: 'monitoring',
            name: 'EffectMonitoring',
            component: () => import('@/views/decision/EffectMonitoring.vue'),
            meta: { title: '效果监控' }
          },
          {
            path: 'optimization',
            name: 'StrategyOptimization',
            component: () => import('@/views/decision/StrategyOptimization.vue'),
            meta: { title: '策略优化' }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth !== false && !userStore.isAuthenticated) {
    next({ name: 'Login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router