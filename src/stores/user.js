import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '@/api/auth'
import router from '@/router'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
    permissions: []
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    
    username: (state) => state.userInfo?.username || '',
    
    hasPermission: (state) => (permission) => {
      return state.permissions.includes(permission)
    }
  },

  actions: {
    async login(credentials) {
      try {
        // 模拟登录逻辑
        if (credentials.username === 'admin' && credentials.password === '123456') {
          // 模拟成功登录
          this.token = 'mock-token-' + Date.now()
          localStorage.setItem('token', this.token)
          
          // 设置模拟用户信息
          this.userInfo = {
            id: 1,
            username: 'admin',
            name: '管理员',
            role: 'admin'
          }
          this.permissions = ['*']
          
          router.push('/')
          return { success: true }
        } else {
          return { 
            success: false, 
            message: '用户名或密码错误' 
          }
        }
      } catch (error) {
        return { 
          success: false, 
          message: error.message || '登录失败' 
        }
      }
    },

    async fetchUserInfo() {
      try {
        // 如果已有用户信息，直接返回
        if (this.userInfo) {
          return
        }
        
        // 模拟获取用户信息
        this.userInfo = {
          id: 1,
          username: 'admin',
          name: '管理员',
          role: 'admin'
        }
        this.permissions = ['*']
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    async logout() {
      try {
        // 模拟登出
        console.log('用户登出')
      } catch (error) {
        console.error('登出失败:', error)
      } finally {
        this.token = ''
        this.userInfo = null
        this.permissions = []
        localStorage.removeItem('token')
        router.push('/login')
      }
    }
  }
})