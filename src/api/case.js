import request from './request'

// 获取案件列表
export const getCaseList = (params) => {
  return request.get('/cases', { params })
}

// 获取案件详情
export const getCaseDetail = (id) => {
  return request.get(`/cases/${id}`)
}

// 创建案件
export const createCase = (data) => {
  return request.post('/cases', data)
}

// 更新案件
export const updateCase = (id, data) => {
  return request.put(`/cases/${id}`, data)
}

// 删除案件
export const deleteCase = (id) => {
  return request.delete(`/cases/${id}`)
}

// 批量分配案件
export const batchAssignCases = (data) => {
  return request.post('/cases/batch-assign', data)
}

// 批量转移案件
export const batchTransferCases = (data) => {
  return request.post('/cases/batch-transfer', data)
}

// 案件跟进
export const followUpCase = (id, data) => {
  return request.post(`/cases/${id}/follow-up`, data)
}

// 获取案件跟进记录
export const getCaseFollowUps = (id, params) => {
  return request.get(`/cases/${id}/follow-ups`, { params })
}

// 案件归档
export const archiveCase = (id, data) => {
  return request.post(`/cases/${id}/archive`, data)
}

// 导出案件
export const exportCases = (params) => {
  return request.get('/cases/export', { 
    params,
    responseType: 'blob'
  })
}

// 导入案件
export const importCases = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/cases/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取案件统计数据
export const getCaseStatistics = (params) => {
  return request.get('/cases/statistics', { params })
}

// 获取分配规则
export const getAllocationRules = () => {
  return request.get('/cases/allocation-rules')
}

// 更新分配规则
export const updateAllocationRules = (data) => {
  return request.put('/cases/allocation-rules', data)
}

// 执行自动分配
export const executeAutoAllocation = () => {
  return request.post('/cases/auto-allocate')
}