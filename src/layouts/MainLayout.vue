<template>
  <a-layout class="main-layout" has-sider>
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      width="256"
      class="layout-sider"
    >
      <div class="logo">
        <h2 v-if="!collapsed">催收管理系统</h2>
        <h2 v-else>CMS</h2>
      </div>
      
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        theme="dark"
        mode="inline"
        @click="handleMenuClick"
      >
        <template v-for="menu in menuItems" :key="menu.key">
          <a-sub-menu v-if="menu.children" :key="menu.key">
            <template #title>
              <component :is="menu.icon" />
              <span>{{ menu.title }}</span>
            </template>
            <a-menu-item v-for="child in menu.children" :key="child.key">
              {{ child.title }}
            </a-menu-item>
          </a-sub-menu>
          
          <a-menu-item v-else :key="menu.key">
            <component :is="menu.icon" />
            <span>{{ menu.title }}</span>
          </a-menu-item>
        </template>
      </a-menu>
    </a-layout-sider>
    
    <a-layout>
      <a-layout-header class="layout-header">
        <div class="header-content">
          <menu-unfold-outlined
            v-if="collapsed"
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          <menu-fold-outlined
            v-else
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          
          <div class="header-right">
            <a-badge :count="5" class="notification-badge">
              <bell-outlined class="header-icon" />
            </a-badge>
            
            <a-dropdown>
              <div class="user-info">
                <a-avatar :size="32">
                  <template #icon><UserOutlined /></template>
                </a-avatar>
                <span class="username">{{ userStore.username || '管理员' }}</span>
              </div>
              <template #overlay>
                <a-menu @click="handleUserMenuClick">
                  <a-menu-item key="profile">
                    <UserOutlined />
                    个人信息
                  </a-menu-item>
                  <a-menu-item key="settings">
                    <SettingOutlined />
                    个人设置
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>
      
      <a-layout-content class="layout-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { 
  MenuFoldOutlined, 
  MenuUnfoldOutlined,
  DashboardOutlined,
  FileTextOutlined,
  UserOutlined,
  PhoneOutlined,
  DollarOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  LogoutOutlined,
  TeamOutlined,
  AlertOutlined,
  AuditOutlined,
  SecurityScanOutlined,
  SafetyCertificateOutlined,
  TrophyOutlined,
  BookOutlined,
  CustomerServiceOutlined,
  ShareAltOutlined,
  DeploymentUnitOutlined,
  ExperimentOutlined,
  MobileOutlined,
  ApiOutlined,
  FundOutlined,
  MailOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const collapsed = ref(false)
const selectedKeys = ref([])
const openKeys = ref([])

const menuItems = [
  {
    key: 'dashboard',
    title: '首页仪表板',
    icon: DashboardOutlined,
    path: '/'
  },
  {
    key: 'cases',
    title: '案件管理',
    icon: FileTextOutlined,
    children: [
      { key: 'case-list', title: '案件列表', path: '/cases/list' },
      { key: 'case-allocation', title: '案件分配', path: '/cases/allocation' },
      { key: 'case-tracking', title: '案件跟进', path: '/cases/tracking' },
      { key: 'case-transfer', title: '案件转移', path: '/cases/transfer' },
      { key: 'case-archive', title: '案件归档', path: '/cases/archive' }
    ]
  },
  {
    key: 'customers',
    title: '客户管理',
    icon: TeamOutlined,
    children: [
      { key: 'customer-list', title: '客户档案', path: '/customers/list' },
      { key: 'contact-management', title: '联系人管理', path: '/customers/contacts' },
      { key: 'customer-segmentation', title: '客户分群', path: '/customers/segmentation' },
      { key: 'credit-assessment', title: '信用评估', path: '/customers/credit' },
      { key: 'blacklist', title: '黑名单管理', path: '/customers/blacklist' }
    ]
  },
  {
    key: 'collection',
    title: '催收记录',
    icon: PhoneOutlined,
    children: [
      { key: 'call-records', title: '通话记录', path: '/collection/call' },
      { key: 'sms-records', title: '短信记录', path: '/collection/sms' },
      { key: 'visit-records', title: '外访记录', path: '/collection/visit' },
      { key: 'legal-records', title: '法务记录', path: '/collection/legal' },
      { key: 'email-records', title: '邮件记录', path: '/collection/email' }
    ]
  },
  {
    key: 'payment',
    title: '还款管理',
    icon: DollarOutlined,
    children: [
      { key: 'payment-plan', title: '还款计划', path: '/payment/plan' },
      { key: 'payment-records', title: '还款记录', path: '/payment/records' },
      { key: 'installment-management', title: '分期管理', path: '/payment/installment' },
      { key: 'reduction-management', title: '减免管理', path: '/payment/reduction' },
      { key: 'writeoff-management', title: '核销管理', path: '/payment/writeoff' }
    ]
  },
  {
    key: 'reports',
    title: '报表统计',
    icon: BarChartOutlined,
    children: [
      { key: 'collection-report', title: '催收效果报表', path: '/reports/collection' },
      { key: 'case-analysis', title: '案件分析报表', path: '/reports/case-analysis' },
      { key: 'performance-report', title: '人员绩效报表', path: '/reports/performance' },
      { key: 'financial-report', title: '财务报表', path: '/reports/financial' },
      { key: 'payment-report', title: '回款报表', path: '/reports/payment' },
      { key: 'risk-report', title: '风险监控', path: '/reports/risk' },
      { key: 'risk-analysis-report', title: '风险分析报表', path: '/reports/risk-analysis' },
      { key: 'compliance-report', title: '合规报表', path: '/reports/compliance-report' },
      { key: 'operation-report', title: '运营报表', path: '/reports/operation' },
      { key: 'data-visualization', title: '数据可视化', path: '/reports/visualization' }
    ]
  },
  {
    key: 'risk-control',
    title: '风险控制',
    icon: SecurityScanOutlined,
    children: [
      { key: 'risk-assessment', title: '风险评估', path: '/risk-control/assessment' },
      { key: 'warning-management', title: '预警管理', path: '/risk-control/warning' },
      { key: 'risk-monitoring', title: '风险监控', path: '/risk-control/monitoring' },
      { key: 'anti-fraud', title: '反欺诈', path: '/risk-control/anti-fraud' },
      { key: 'compliance-check', title: '合规检查', path: '/risk-control/compliance-check' }
    ]
  },
  {
    key: 'compliance',
    title: '合规管理',
    icon: SafetyCertificateOutlined,
    children: [
      { key: 'compliance-policy', title: '合规政策', path: '/compliance/policy' },
      { key: 'compliance-inspection', title: '合规检查', path: '/compliance/check' },
      { key: 'complaint-handling', title: '投诉处理', path: '/compliance/complaint' },
      { key: 'regulation-update', title: '法规更新', path: '/compliance/regulation' },
      { key: 'compliance-training', title: '合规培训', path: '/compliance/training' }
    ]
  },
  {
    key: 'performance',
    title: '绩效管理',
    icon: TrophyOutlined,
    children: [
      { key: 'performance-assessment', title: '绩效考核', path: '/performance/assessment' },
      { key: 'target-management', title: '目标管理', path: '/performance/target' },
      { key: 'incentive-scheme', title: '激励方案', path: '/performance/incentive' },
      { key: 'performance-ranking', title: '排行榜', path: '/performance/ranking' },
      { key: 'performance-analysis', title: '绩效分析', path: '/performance/analysis' }
    ]
  },
  {
    key: 'training',
    title: '培训管理',
    icon: BookOutlined,
    children: [
      { key: 'training-plan', title: '培训计划', path: '/training/plan' },
      { key: 'course-management', title: '课程管理', path: '/training/course' },
      { key: 'exam-management', title: '考试管理', path: '/training/exam' },
      { key: 'skill-assessment', title: '技能评估', path: '/training/skill' },
      { key: 'training-archive', title: '培训档案', path: '/training/archive' }
    ]
  },
  {
    key: 'customer-service',
    title: '客户服务',
    icon: CustomerServiceOutlined,
    children: [
      { key: 'online-service', title: '在线客服', path: '/customer-service/online' },
      { key: 'complaint-management', title: '投诉管理', path: '/customer-service/complaint' },
      { key: 'satisfaction-survey', title: '满意度调查', path: '/customer-service/survey' },
      { key: 'service-record', title: '服务记录', path: '/customer-service/record' },
      { key: 'knowledge-base', title: '知识库', path: '/customer-service/knowledge' }
    ]
  },
  {
    key: 'workflow',
    title: '流程管理',
    icon: ShareAltOutlined,
    children: [
      { key: 'workflow-design', title: '工作流设计', path: '/workflow/design' },
      { key: 'approval-process', title: '审批流程', path: '/workflow/approval' },
      { key: 'process-monitoring', title: '流程监控', path: '/workflow/monitoring' },
      { key: 'process-optimization', title: '流程优化', path: '/workflow/optimization' },
      { key: 'process-template', title: '流程模板', path: '/workflow/template' }
    ]
  },
  {
    key: 'quality',
    title: '质检管理',
    icon: AuditOutlined,
    children: [
      { key: 'quality-plan', title: '质检计划', path: '/quality/plan' },
      { key: 'quality-execution', title: '质检执行', path: '/quality/execution' },
      { key: 'quality-report', title: '质检报告', path: '/quality/report' },
      { key: 'quality-improvement', title: '问题整改', path: '/quality/improvement' },
      { key: 'quality-analysis', title: '质检分析', path: '/quality/analysis' }
    ]
  },
  {
    key: 'workspace',
    title: '工作台',
    icon: DeploymentUnitOutlined,
    children: [
      { key: 'personal-workspace', title: '个人工作台', path: '/workspace/personal' },
      { key: 'team-workspace', title: '团队工作台', path: '/workspace/team' },
      { key: 'management-dashboard', title: '管理驾驶舱', path: '/workspace/dashboard' },
      { key: 'message-center', title: '消息中心', path: '/workspace/message' },
      { key: 'quick-operations', title: '快捷操作', path: '/workspace/quick' }
    ]
  },
  {
    key: 'intelligence',
    title: '智能分析',
    icon: ExperimentOutlined,
    children: [
      { key: 'customer-portrait', title: '客户画像', path: '/intelligence/portrait' },
      { key: 'behavior-analysis', title: '行为分析', path: '/intelligence/behavior' },
      { key: 'prediction-model', title: '预测模型', path: '/intelligence/prediction' },
      { key: 'strategy-recommendation', title: '策略推荐', path: '/intelligence/strategy' },
      { key: 'effect-evaluation', title: '效果评估', path: '/intelligence/evaluation' }
    ]
  },
  {
    key: 'mobile',
    title: '移动应用',
    icon: MobileOutlined,
    children: [
      { key: 'mobile-workspace', title: '移动工作台', path: '/mobile/workspace' },
      { key: 'field-management', title: '外勤管理', path: '/mobile/field' },
      { key: 'realtime-communication', title: '实时通讯', path: '/mobile/communication' },
      { key: 'offline-sync', title: '离线同步', path: '/mobile/offline' },
      { key: 'location-service', title: '位置服务', path: '/mobile/location' }
    ]
  },
  {
    key: 'integration',
    title: '第三方集成',
    icon: ApiOutlined,
    children: [
      { key: 'system-interface', title: '系统接口', path: '/integration/system' },
      { key: 'data-sync', title: '数据同步', path: '/integration/sync' },
      { key: 'message-push', title: '消息推送', path: '/integration/message' },
      { key: 'payment-integration', title: '支付集成', path: '/integration/payment' },
      { key: 'credit-interface', title: '征信接口', path: '/integration/credit' }
    ]
  },
  {
    key: 'decision',
    title: '决策支持',
    icon: FundOutlined,
    children: [
      { key: 'decision-model', title: '决策模型', path: '/decision/model' },
      { key: 'strategy-engine', title: '策略引擎', path: '/decision/engine' },
      { key: 'rule-configuration', title: '规则配置', path: '/decision/rules' },
      { key: 'effect-monitoring', title: '效果监控', path: '/decision/monitoring' },
      { key: 'strategy-optimization', title: '策略优化', path: '/decision/optimization' }
    ]
  },
  {
    key: 'system',
    title: '系统设置',
    icon: SettingOutlined,
    children: [
      { key: 'user-management', title: '用户管理', path: '/system/users' },
      { key: 'role-management', title: '角色权限', path: '/system/roles' },
      { key: 'system-config', title: '参数配置', path: '/system/config' },
      { key: 'template-management', title: '模板管理', path: '/system/templates' },
      { key: 'data-management', title: '数据管理', path: '/system/data' },
      { key: 'interface-config', title: '接口配置', path: '/system/interface' },
      { key: 'system-monitoring', title: '系统监控', path: '/system/monitoring' }
    ]
  }
]

// 根据路由path找到对应的菜单key
const findMenuByPath = (path, menus = menuItems) => {
  for (const menu of menus) {
    if (menu.path === path) {
      return { key: menu.key, parentKey: null }
    }
    if (menu.children) {
      for (const child of menu.children) {
        if (child.path === path) {
          return { key: child.key, parentKey: menu.key }
        }
      }
    }
  }
  return null
}

// 监听路由变化，更新选中菜单
watch(() => route.path, (path) => {
  const menu = findMenuByPath(path)
  if (menu) {
    selectedKeys.value = [menu.key]
    if (menu.parentKey && !collapsed.value) {
      openKeys.value = [menu.parentKey]
    }
  }
}, { immediate: true })

// 处理菜单点击
const handleMenuClick = ({ key }) => {
  const findPath = (menus) => {
    for (const menu of menus) {
      if (menu.key === key && menu.path) {
        return menu.path
      }
      if (menu.children) {
        const path = findPath(menu.children)
        if (path) return path
      }
    }
    return null
  }
  
  const path = findPath(menuItems)
  if (path) {
    router.push(path)
  }
}

// 处理用户菜单点击
const handleUserMenuClick = ({ key }) => {
  if (key === 'logout') {
    userStore.logout()
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  min-height: 100vh;
  
  .layout-sider {
    overflow: auto;
    
    .logo {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      
      h2 {
        color: #fff;
        margin: 0;
        font-size: 20px;
      }
    }
  }
  
  .layout-header {
    background: #fff;
    padding: 0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      padding: 0 24px;
      
      .trigger {
        font-size: 18px;
        cursor: pointer;
        transition: color 0.3s;
        
        &:hover {
          color: #1890ff;
        }
      }
      
      .header-right {
        display: flex;
        align-items: center;
        gap: 24px;
        
        .notification-badge {
          cursor: pointer;
          
          .header-icon {
            font-size: 18px;
          }
        }
        
        .user-info {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          
          .username {
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .layout-content {
    padding: 24px;
    background: #f0f2f5;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>