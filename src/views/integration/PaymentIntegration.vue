<template>
  <div class="payment-integration">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="payment-info">
          <h2>支付集成</h2>
          <p class="payment-desc">管理第三方支付渠道，确保还款流程畅通无阻</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshPaymentData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showTestModal = true">
            <template #icon><ToolOutlined /></template>
            接口测试
          </a-button>
          <a-button type="primary" @click="showAddChannelModal = true">
            <template #icon><PlusOutlined /></template>
            添加渠道
          </a-button>
        </div>
      </div>
    </div>

    <!-- 支付统计 -->
    <div class="payment-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="支付渠道" 
              :value="paymentStats.channels" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><CreditCardOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日交易" 
              :value="paymentStats.todayAmount" 
              :value-style="{ color: '#52c41a' }"
              suffix="万元"
            >
              <template #prefix><DollarCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="成功率" 
              :value="paymentStats.successRate" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="待处理" 
              :value="paymentStats.pending" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 支付渠道 -->
        <a-card title="支付渠道管理" class="payment-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedChannelType" style="width: 120px" @change="filterChannels">
                <a-select-option value="all">全部类型</a-select-option>
                <a-select-option value="alipay">支付宝</a-select-option>
                <a-select-option value="wechat">微信支付</a-select-option>
                <a-select-option value="bank">银行卡</a-select-option>
                <a-select-option value="union">银联</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterChannels">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="active">正常</a-select-option>
                <a-select-option value="disabled">已禁用</a-select-option>
                <a-select-option value="error">异常</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <a-table 
            :columns="channelColumns" 
            :data-source="filteredChannels" 
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="channel-name">
                  <component :is="getChannelIcon(record.type)" style="margin-right: 8px;" />
                  <span>{{ record.name }}</span>
                </div>
              </template>
              <template v-if="column.key === 'type'">
                <a-tag :color="getChannelColor(record.type)">
                  {{ getChannelTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'feeRate'">
                <span>{{ record.feeRate }}%</span>
              </template>
              <template v-if="column.key === 'todayAmount'">
                <span class="amount-text">¥{{ formatNumber(record.todayAmount) }}</span>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="testChannel(record)">
                    测试
                  </a-button>
                  <a-button type="link" size="small" @click="editChannel(record)">
                    编辑
                  </a-button>
                  <a-button type="link" size="small" @click="viewChannelLogs(record)">
                    日志
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewChannelConfig(record)">配置</a-menu-item>
                        <a-menu-item @click="reconciliation(record)">对账</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="toggleChannel(record)">
                          {{ record.status === 'active' ? '禁用' : '启用' }}
                        </a-menu-item>
                        <a-menu-item @click="deleteChannel(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 交易监控 -->
        <a-card title="交易监控" class="payment-card">
          <div class="monitor-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="transactionTrendChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="channelDistributionChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时交易 -->
        <a-card title="实时交易" class="payment-card">
          <div class="realtime-transactions">
            <div v-for="transaction in realtimeTransactions" :key="transaction.id" class="transaction-item">
              <div class="transaction-header">
                <span class="transaction-amount">¥{{ formatNumber(transaction.amount) }}</span>
                <a-badge 
                  :status="getTransactionStatusBadge(transaction.status)" 
                  :text="getTransactionStatusText(transaction.status)" 
                />
              </div>
              <div class="transaction-details">
                <div class="detail-row">
                  <span class="detail-label">渠道</span>
                  <span class="detail-value">{{ transaction.channel }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">时间</span>
                  <span class="detail-value">{{ transaction.time }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">订单号</span>
                  <span class="detail-value">{{ transaction.orderNo }}</span>
                </div>
              </div>
            </div>
            <a-empty v-if="realtimeTransactions.length === 0" description="暂无实时交易" size="small" />
          </div>
        </a-card>

        <!-- 异常告警 -->
        <a-card title="异常告警" class="payment-card">
          <template #extra>
            <a-badge :count="paymentAlerts.filter(a => !a.read).length" />
          </template>
          <div class="payment-alerts">
            <div v-for="alert in paymentAlerts" :key="alert.id" class="alert-item" :class="{ 'unread': !alert.read }">
              <div class="alert-header">
                <div class="alert-type" :class="getAlertClass(alert.type)">
                  {{ getAlertTypeText(alert.type) }}
                </div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
              <div class="alert-content">{{ alert.content }}</div>
              <div class="alert-actions">
                <a-button v-if="!alert.read" type="link" size="small" @click="markAlertRead(alert)">
                  标记已读
                </a-button>
                <a-button type="link" size="small" @click="handleAlert(alert)">
                  处理
                </a-button>
              </div>
            </div>
            <a-empty v-if="paymentAlerts.length === 0" description="暂无告警" size="small" />
          </div>
        </a-card>

        <!-- 对账管理 -->
        <a-card title="对账管理" class="payment-card">
          <div class="reconciliation-panel">
            <div class="reconciliation-stats">
              <div class="stat-item">
                <span class="stat-label">今日对账</span>
                <span class="stat-value success">{{ reconciliationStats.todaySuccess }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">差异订单</span>
                <span class="stat-value error">{{ reconciliationStats.differences }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">待处理</span>
                <span class="stat-value warning">{{ reconciliationStats.pending }}</span>
              </div>
            </div>
            <div class="reconciliation-actions">
              <a-button type="primary" block @click="startReconciliation">
                开始对账
              </a-button>
              <a-button block @click="showReconciliationHistory = true" style="margin-top: 8px;">
                对账历史
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 添加渠道模态框 -->
    <a-modal
      v-model:open="showAddChannelModal"
      title="添加支付渠道"
      width="600px"
      @ok="addPaymentChannel"
    >
      <a-form layout="vertical">
        <a-form-item label="渠道名称" required>
          <a-input v-model:value="channelForm.name" placeholder="请输入渠道名称" />
        </a-form-item>
        <a-form-item label="渠道类型" required>
          <a-select v-model:value="channelForm.type" placeholder="选择渠道类型">
            <a-select-option value="alipay">支付宝</a-select-option>
            <a-select-option value="wechat">微信支付</a-select-option>
            <a-select-option value="bank">银行卡</a-select-option>
            <a-select-option value="union">银联</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="商户号" required>
          <a-input v-model:value="channelForm.merchantId" placeholder="请输入商户号" />
        </a-form-item>
        <a-form-item label="费率(%)" required>
          <a-input-number v-model:value="channelForm.feeRate" :min="0" :max="10" :precision="2" style="width: 100%" />
        </a-form-item>
        <a-form-item label="密钥配置">
          <a-textarea v-model:value="channelForm.secretKey" placeholder="支付密钥或证书内容" :rows="4" />
        </a-form-item>
        <a-form-item label="回调地址">
          <a-input v-model:value="channelForm.notifyUrl" placeholder="支付结果回调地址" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 接口测试模态框 -->
    <a-modal
      v-model:open="showTestModal"
      title="支付接口测试"
      width="500px"
      @ok="runPaymentTest"
    >
      <a-form layout="vertical">
        <a-form-item label="测试渠道">
          <a-select v-model:value="testForm.channelId" placeholder="选择测试渠道">
            <a-select-option v-for="channel in paymentChannels" :key="channel.id" :value="channel.id">
              {{ channel.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="测试金额(元)">
          <a-input-number v-model:value="testForm.amount" :min="0.01" :precision="2" style="width: 100%" />
        </a-form-item>
        <a-form-item label="测试类型">
          <a-radio-group v-model:value="testForm.testType">
            <a-radio value="payment">支付测试</a-radio>
            <a-radio value="query">查询测试</a-radio>
            <a-radio value="refund">退款测试</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <div v-if="testResult" class="test-result">
        <h4>测试结果：</h4>
        <a-alert 
          :type="testResult.success ? 'success' : 'error'" 
          :message="testResult.message" 
          :description="testResult.details"
          show-icon 
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  ToolOutlined,
  PlusOutlined,
  CreditCardOutlined,
  DollarCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DownOutlined,
  AlipayOutlined,
  WechatOutlined,
  BankOutlined,
  GlobalOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showAddChannelModal = ref(false)
const showTestModal = ref(false)
const showReconciliationHistory = ref(false)
const selectedChannelType = ref('all')
const selectedStatus = ref('all')

// 图表引用
const transactionTrendChart = ref(null)
const channelDistributionChart = ref(null)

// 支付统计
const paymentStats = reactive({
  channels: 8,
  todayAmount: 156.8,
  successRate: 98.5,
  pending: 12
})

// 支付渠道列表
const paymentChannels = ref([
  {
    id: 1,
    name: '支付宝直连',
    type: 'alipay',
    status: 'active',
    merchantId: '2088**********12',
    feeRate: 0.6,
    todayAmount: 68500,
    todayCount: 245
  },
  {
    id: 2,
    name: '微信支付',
    type: 'wechat',
    status: 'active',
    merchantId: '**********',
    feeRate: 0.6,
    todayAmount: 52300,
    todayCount: 189
  },
  {
    id: 3,
    name: '工商银行',
    type: 'bank',
    status: 'active',
    merchantId: 'ICBC001',
    feeRate: 0.3,
    todayAmount: 28900,
    todayCount: 67
  },
  {
    id: 4,
    name: '银联支付',
    type: 'union',
    status: 'active',
    merchantId: 'UP123456',
    feeRate: 0.5,
    todayAmount: 18200,
    todayCount: 34
  },
  {
    id: 5,
    name: '建设银行',
    type: 'bank',
    status: 'disabled',
    merchantId: 'CCB002',
    feeRate: 0.3,
    todayAmount: 0,
    todayCount: 0
  }
])

// 实时交易
const realtimeTransactions = ref([
  {
    id: 1,
    amount: 2580,
    channel: '支付宝',
    status: 'success',
    time: '刚刚',
    orderNo: 'ORD202307280001'
  },
  {
    id: 2,
    amount: 1200,
    channel: '微信支付',
    status: 'processing',
    time: '1分钟前',
    orderNo: 'ORD202307280002'
  },
  {
    id: 3,
    amount: 890,
    channel: '工商银行',
    status: 'success',
    time: '2分钟前',
    orderNo: 'ORD202307280003'
  },
  {
    id: 4,
    amount: 3200,
    channel: '支付宝',
    status: 'failed',
    time: '5分钟前',
    orderNo: 'ORD202307280004'
  }
])

// 异常告警
const paymentAlerts = ref([
  {
    id: 1,
    type: 'error',
    time: '5分钟前',
    content: '支付宝渠道出现连续失败，请检查配置',
    read: false
  },
  {
    id: 2,
    type: 'warning',
    time: '15分钟前',
    content: '微信支付费率异常，建议核查账单',
    read: false
  },
  {
    id: 3,
    type: 'info',
    time: '1小时前',
    content: '银联支付渠道维护完成，服务已恢复',
    read: true
  }
])

// 对账统计
const reconciliationStats = reactive({
  todaySuccess: 526,
  differences: 3,
  pending: 8
})

// 表格列定义
const channelColumns = [
  {
    title: '渠道名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '费率',
    dataIndex: 'feeRate',
    key: 'feeRate',
    width: 80
  },
  {
    title: '今日金额',
    dataIndex: 'todayAmount',
    key: 'todayAmount',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 160
  }
]

// 表单数据
const channelForm = reactive({
  name: '',
  type: '',
  merchantId: '',
  feeRate: 0.6,
  secretKey: '',
  notifyUrl: ''
})

const testForm = reactive({
  channelId: null,
  amount: 0.01,
  testType: 'payment'
})

const testResult = ref(null)

// 计算属性
const filteredChannels = computed(() => {
  let filtered = paymentChannels.value
  if (selectedChannelType.value !== 'all') {
    filtered = filtered.filter(item => item.type === selectedChannelType.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getChannelIcon = (type) => {
  const icons = {
    alipay: 'AlipayOutlined',
    wechat: 'WechatOutlined',
    bank: 'BankOutlined',
    union: 'GlobalOutlined'
  }
  return icons[type] || 'CreditCardOutlined'
}

const getChannelColor = (type) => {
  const colors = {
    alipay: 'blue',
    wechat: 'green',
    bank: 'orange',
    union: 'purple'
  }
  return colors[type] || 'default'
}

const getChannelTypeText = (type) => {
  const texts = {
    alipay: '支付宝',
    wechat: '微信支付',
    bank: '银行卡',
    union: '银联'
  }
  return texts[type] || type
}

const getStatusBadge = (status) => {
  const badges = {
    active: 'success',
    disabled: 'default',
    error: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    disabled: '已禁用',
    error: '异常'
  }
  return texts[status] || status
}

const getTransactionStatusBadge = (status) => {
  const badges = {
    success: 'success',
    processing: 'processing',
    failed: 'error'
  }
  return badges[status] || 'default'
}

const getTransactionStatusText = (status) => {
  const texts = {
    success: '成功',
    processing: '处理中',
    failed: '失败'
  }
  return texts[status] || status
}

const getAlertClass = (type) => {
  return `alert-${type}`
}

const getAlertTypeText = (type) => {
  const texts = {
    error: '错误',
    warning: '警告',
    info: '信息'
  }
  return texts[type] || type
}

const formatNumber = (num) => {
  return num.toLocaleString()
}

const refreshPaymentData = () => {
  console.log('刷新支付数据')
  message.success('支付数据已刷新')
}

const filterChannels = () => {
  console.log('筛选渠道:', selectedChannelType.value, selectedStatus.value)
}

const testChannel = (record) => {
  testForm.channelId = record.id
  showTestModal.value = true
}

const editChannel = (record) => {
  console.log('编辑渠道:', record)
  Object.assign(channelForm, record)
  showAddChannelModal.value = true
}

const viewChannelLogs = (record) => {
  console.log('查看渠道日志:', record)
}

const viewChannelConfig = (record) => {
  console.log('查看渠道配置:', record)
}

const reconciliation = (record) => {
  console.log('对账:', record)
  message.info(`正在进行${record.name}对账`)
}

const toggleChannel = (record) => {
  const newStatus = record.status === 'active' ? 'disabled' : 'active'
  record.status = newStatus
  message.success(`渠道已${newStatus === 'active' ? '启用' : '禁用'}`)
}

const deleteChannel = (record) => {
  const index = paymentChannels.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    paymentChannels.value.splice(index, 1)
    message.success('渠道删除成功')
  }
}

const addPaymentChannel = () => {
  console.log('添加支付渠道:', channelForm)
  const newChannel = {
    id: Date.now(),
    ...channelForm,
    status: 'active',
    todayAmount: 0,
    todayCount: 0
  }
  paymentChannels.value.push(newChannel)
  message.success('支付渠道添加成功')
  showAddChannelModal.value = false
  
  // 重置表单
  Object.keys(channelForm).forEach(key => {
    if (key === 'feeRate') {
      channelForm[key] = 0.6
    } else {
      channelForm[key] = ''
    }
  })
}

const runPaymentTest = () => {
  console.log('运行支付测试:', testForm)
  
  // 模拟测试结果
  setTimeout(() => {
    testResult.value = {
      success: Math.random() > 0.3,
      message: Math.random() > 0.3 ? '测试成功' : '测试失败',
      details: Math.random() > 0.3 ? '接口响应正常，参数验证通过' : '连接超时，请检查网络配置'
    }
  }, 1000)
}

const markAlertRead = (alert) => {
  alert.read = true
}

const handleAlert = (alert) => {
  console.log('处理告警:', alert)
  message.info('正在处理告警...')
}

const startReconciliation = () => {
  console.log('开始对账')
  message.info('对账任务已启动，请稍后查看结果')
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 交易趋势图
    if (transactionTrendChart.value) {
      const chart1 = echarts.init(transactionTrendChart.value)
      chart1.setOption({
        title: { text: '交易趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        },
        yAxis: { type: 'value', name: '万元' },
        series: [{
          name: '交易金额',
          type: 'line',
          data: [5.2, 3.8, 12.5, 28.6, 35.2, 24.8, 15.3],
          smooth: true,
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }

    // 渠道分布图
    if (channelDistributionChart.value) {
      const chart2 = echarts.init(channelDistributionChart.value)
      chart2.setOption({
        title: { text: '渠道分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 68.5, name: '支付宝' },
            { value: 52.3, name: '微信支付' },
            { value: 28.9, name: '银行卡' },
            { value: 18.2, name: '银联' }
          ],
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.payment-integration {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.payment-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.payment-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.payment-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.channel-name {
  display: flex;
  align-items: center;
}

.amount-text {
  color: #52c41a;
  font-weight: 500;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.realtime-transactions {
  max-height: 300px;
  overflow-y: auto;
}

.transaction-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.transaction-amount {
  font-weight: 500;
  color: #52c41a;
  font-size: 16px;
}

.transaction-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.detail-label {
  color: #999;
}

.detail-value {
  color: #666;
}

.payment-alerts {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-left: 3px solid transparent;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item.unread {
  background: #f6f8ff;
  border-left-color: #1890ff;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.alert-type {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.alert-error {
  background: #fff2f0;
  color: #ff4d4f;
}

.alert-warning {
  background: #fff7e6;
  color: #faad14;
}

.alert-info {
  background: #f6f8ff;
  color: #1890ff;
}

.alert-time {
  font-size: 11px;
  color: #999;
}

.alert-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.reconciliation-panel {
  padding: 8px 0;
}

.reconciliation-stats {
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 13px;
  color: #666;
}

.stat-value {
  font-weight: 500;
}

.stat-value.success {
  color: #52c41a;
}

.stat-value.error {
  color: #ff4d4f;
}

.stat-value.warning {
  color: #faad14;
}

.test-result {
  margin-top: 16px;
}

.test-result h4 {
  margin-bottom: 8px;
}
</style>