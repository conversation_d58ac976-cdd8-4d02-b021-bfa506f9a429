<template>
  <div class="system-interface">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="interface-info">
          <h2>系统接口</h2>
          <p class="interface-desc">第三方系统接口管理与监控，确保数据互通无阻</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshInterfaces">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showConfigModal = true">
            <template #icon><SettingOutlined /></template>
            全局配置
          </a-button>
          <a-button type="primary" @click="showAddModal = true">
            <template #icon><PlusOutlined /></template>
            添加接口
          </a-button>
        </div>
      </div>
    </div>

    <!-- 接口统计 -->
    <div class="interface-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="总接口数" 
              :value="interfaceStats.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ApiOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="正常运行" 
              :value="interfaceStats.active" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="异常接口" 
              :value="interfaceStats.error" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日调用" 
              :value="interfaceStats.todayCalls" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><ThunderboltOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 接口列表 -->
        <a-card title="接口列表" class="interface-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedType" style="width: 120px" @change="filterInterfaces">
                <a-select-option value="all">全部类型</a-select-option>
                <a-select-option value="rest">REST API</a-select-option>
                <a-select-option value="soap">SOAP</a-select-option>
                <a-select-option value="websocket">WebSocket</a-select-option>
                <a-select-option value="grpc">gRPC</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterInterfaces">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="active">正常</a-select-option>
                <a-select-option value="error">异常</a-select-option>
                <a-select-option value="disabled">已禁用</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <a-table 
            :columns="interfaceColumns" 
            :data-source="filteredInterfaces" 
            :pagination="{ pageSize: 10 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="interface-name">
                  <component :is="getInterfaceIcon(record.type)" style="margin-right: 8px;" />
                  <span>{{ record.name }}</span>
                </div>
              </template>
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ record.type.toUpperCase() }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'responseTime'">
                <span :class="getResponseTimeClass(record.responseTime)">
                  {{ record.responseTime }}ms
                </span>
              </template>
              <template v-if="column.key === 'successRate'">
                <a-progress 
                  :percent="record.successRate" 
                  size="small" 
                  :stroke-color="getSuccessRateColor(record.successRate)"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="testInterface(record)">
                    测试
                  </a-button>
                  <a-button type="link" size="small" @click="editInterface(record)">
                    编辑
                  </a-button>
                  <a-button type="link" size="small" @click="viewLogs(record)">
                    日志
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewDetails(record)">详情</a-menu-item>
                        <a-menu-item @click="exportConfig(record)">导出配置</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="toggleInterface(record)" :disabled="record.status === 'error'">
                          {{ record.status === 'active' ? '禁用' : '启用' }}
                        </a-menu-item>
                        <a-menu-item @click="deleteInterface(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 接口监控 -->
        <a-card title="接口监控" class="interface-card">
          <div class="monitor-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="callTrendChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="responseTimeChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时状态 -->
        <a-card title="实时状态" class="interface-card">
          <div class="realtime-status">
            <div v-for="status in realtimeStatus" :key="status.id" class="status-item">
              <div class="status-header">
                <span class="status-name">{{ status.name }}</span>
                <a-badge 
                  :status="getStatusBadge(status.status)" 
                  :text="status.responseTime + 'ms'" 
                />
              </div>
              <div class="status-metrics">
                <div class="metric">
                  <span class="metric-label">成功率</span>
                  <span class="metric-value">{{ status.successRate }}%</span>
                </div>
                <div class="metric">
                  <span class="metric-label">QPS</span>
                  <span class="metric-value">{{ status.qps }}</span>
                </div>
              </div>
            </div>
            <a-empty v-if="realtimeStatus.length === 0" description="暂无实时数据" size="small" />
          </div>
        </a-card>

        <!-- 异常告警 -->
        <a-card title="异常告警" class="interface-card">
          <template #extra>
            <a-badge :count="errorAlerts.filter(a => !a.read).length" />
          </template>
          <div class="error-alerts">
            <div v-for="alert in errorAlerts" :key="alert.id" class="alert-item" :class="{ 'unread': !alert.read }">
              <div class="alert-header">
                <div class="alert-type" :class="getAlertClass(alert.type)">
                  {{ getAlertTypeText(alert.type) }}
                </div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
              <div class="alert-content">{{ alert.content }}</div>
              <div class="alert-actions">
                <a-button v-if="!alert.read" type="link" size="small" @click="markAlertRead(alert)">
                  标记已读
                </a-button>
                <a-button type="link" size="small" @click="viewAlertDetail(alert)">
                  查看详情
                </a-button>
              </div>
            </div>
            <a-empty v-if="errorAlerts.length === 0" description="暂无告警" size="small" />
          </div>
        </a-card>

        <!-- 快速配置 -->
        <a-card title="快速配置" class="interface-card">
          <div class="quick-config">
            <div class="config-group">
              <h4>全局设置</h4>
              <div class="config-item">
                <span>超时时间</span>
                <a-input-number v-model:value="globalConfig.timeout" :min="1000" :max="60000" />
                <span>ms</span>
              </div>
              <div class="config-item">
                <span>重试次数</span>
                <a-input-number v-model:value="globalConfig.retryCount" :min="0" :max="5" />
              </div>
              <div class="config-item">
                <span>启用缓存</span>
                <a-switch v-model:checked="globalConfig.enableCache" />
              </div>
            </div>
            <div class="config-actions">
              <a-button type="primary" block @click="saveGlobalConfig">
                保存配置
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 添加接口模态框 -->
    <a-modal
      v-model:open="showAddModal"
      title="添加接口"
      width="600px"
      @ok="addInterface"
    >
      <a-form layout="vertical">
        <a-form-item label="接口名称" required>
          <a-input v-model:value="interfaceForm.name" placeholder="请输入接口名称" />
        </a-form-item>
        <a-form-item label="接口类型" required>
          <a-select v-model:value="interfaceForm.type" placeholder="选择接口类型">
            <a-select-option value="rest">REST API</a-select-option>
            <a-select-option value="soap">SOAP</a-select-option>
            <a-select-option value="websocket">WebSocket</a-select-option>
            <a-select-option value="grpc">gRPC</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="接口地址" required>
          <a-input v-model:value="interfaceForm.url" placeholder="请输入接口地址" />
        </a-form-item>
        <a-form-item label="认证方式">
          <a-select v-model:value="interfaceForm.authType" placeholder="选择认证方式">
            <a-select-option value="none">无认证</a-select-option>
            <a-select-option value="basic">Basic Auth</a-select-option>
            <a-select-option value="bearer">Bearer Token</a-select-option>
            <a-select-option value="oauth">OAuth 2.0</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:value="interfaceForm.description" placeholder="接口描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 全局配置模态框 -->
    <a-modal
      v-model:open="showConfigModal"
      title="全局配置"
      @ok="saveGlobalConfig"
    >
      <a-form layout="vertical">
        <a-form-item label="默认超时时间(ms)">
          <a-input-number v-model:value="globalConfig.timeout" :min="1000" :max="60000" style="width: 100%" />
        </a-form-item>
        <a-form-item label="默认重试次数">
          <a-input-number v-model:value="globalConfig.retryCount" :min="0" :max="5" style="width: 100%" />
        </a-form-item>
        <a-form-item label="监控间隔(秒)">
          <a-input-number v-model:value="globalConfig.monitorInterval" :min="10" :max="300" style="width: 100%" />
        </a-form-item>
        <a-form-item label="日志保留天数">
          <a-input-number v-model:value="globalConfig.logRetention" :min="1" :max="365" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ThunderboltOutlined,
  DownOutlined,
  CloudServerOutlined,
  GlobalOutlined,
  LinkOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showAddModal = ref(false)
const showConfigModal = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')

// 图表引用
const callTrendChart = ref(null)
const responseTimeChart = ref(null)

// 接口统计
const interfaceStats = reactive({
  total: 15,
  active: 12,
  error: 2,
  todayCalls: 28543
})

// 接口列表
const interfaceList = ref([
  {
    id: 1,
    name: '客户信息查询API',
    type: 'rest',
    url: 'https://api.customer.com/v1/info',
    status: 'active',
    responseTime: 125,
    successRate: 99.8,
    authType: 'bearer',
    description: '查询客户基本信息'
  },
  {
    id: 2,
    name: '征信数据接口',
    type: 'soap',
    url: 'http://credit.service.com/ws',
    status: 'active',
    responseTime: 89,
    successRate: 97.5,
    authType: 'oauth',
    description: '获取客户征信数据'
  },
  {
    id: 3,
    name: '银行卡验证API',
    type: 'rest',
    url: 'https://bank.verify.com/api/check',
    status: 'error',
    responseTime: 3500,
    successRate: 65.2,
    authType: 'basic',
    description: '验证银行卡有效性'
  },
  {
    id: 4,
    name: '短信发送服务',
    type: 'rest',
    url: 'https://sms.gateway.com/send',
    status: 'active',
    responseTime: 245,
    successRate: 98.9,
    authType: 'bearer',
    description: '发送催收短信'
  },
  {
    id: 5,
    name: '实时通知推送',
    type: 'websocket',
    url: 'wss://notify.service.com/ws',
    status: 'active',
    responseTime: 15,
    successRate: 99.5,
    authType: 'none',
    description: '实时消息推送'
  }
])

// 实时状态
const realtimeStatus = ref([
  {
    id: 1,
    name: '客户信息API',
    status: 'active',
    responseTime: 125,
    successRate: 99.8,
    qps: 45
  },
  {
    id: 2,
    name: '征信数据接口',
    status: 'active',
    responseTime: 89,
    successRate: 97.5,
    qps: 23
  },
  {
    id: 3,
    name: '银行卡验证',
    status: 'error',
    responseTime: 3500,
    successRate: 65.2,
    qps: 2
  }
])

// 异常告警
const errorAlerts = ref([
  {
    id: 1,
    type: 'error',
    time: '2分钟前',
    content: '银行卡验证API响应时间超过3秒，请检查网络连接',
    read: false
  },
  {
    id: 2,
    type: 'warning',
    time: '10分钟前',
    content: '征信数据接口成功率低于98%，建议检查服务状态',
    read: false
  },
  {
    id: 3,
    type: 'info',
    time: '30分钟前',
    content: '系统接口配置已更新，新配置已生效',
    read: true
  }
])

// 表格列定义
const interfaceColumns = [
  {
    title: '接口名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '响应时间',
    dataIndex: 'responseTime',
    key: 'responseTime',
    width: 100
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180
  }
]

// 表单数据
const interfaceForm = reactive({
  name: '',
  type: '',
  url: '',
  authType: 'none',
  description: ''
})

// 全局配置
const globalConfig = reactive({
  timeout: 10000,
  retryCount: 3,
  enableCache: true,
  monitorInterval: 60,
  logRetention: 30
})

// 计算属性
const filteredInterfaces = computed(() => {
  let filtered = interfaceList.value
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(item => item.type === selectedType.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getInterfaceIcon = (type) => {
  const icons = {
    rest: 'ApiOutlined',
    soap: 'CloudServerOutlined',
    websocket: 'GlobalOutlined',
    grpc: 'LinkOutlined'
  }
  return icons[type] || 'DatabaseOutlined'
}

const getTypeColor = (type) => {
  const colors = {
    rest: 'blue',
    soap: 'green',
    websocket: 'orange',
    grpc: 'purple'
  }
  return colors[type] || 'default'
}

const getStatusBadge = (status) => {
  const badges = {
    active: 'success',
    error: 'error',
    disabled: 'default'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    error: '异常',
    disabled: '已禁用'
  }
  return texts[status] || status
}

const getResponseTimeClass = (time) => {
  if (time < 200) return 'response-fast'
  if (time < 1000) return 'response-normal'
  return 'response-slow'
}

const getSuccessRateColor = (rate) => {
  if (rate >= 99) return '#52c41a'
  if (rate >= 95) return '#faad14'
  return '#ff4d4f'
}

const getAlertClass = (type) => {
  return `alert-${type}`
}

const getAlertTypeText = (type) => {
  const texts = {
    error: '错误',
    warning: '警告',
    info: '信息'
  }
  return texts[type] || type
}

const refreshInterfaces = () => {
  console.log('刷新接口列表')
  message.success('接口状态已刷新')
}

const filterInterfaces = () => {
  console.log('筛选接口:', selectedType.value, selectedStatus.value)
}

const testInterface = (record) => {
  console.log('测试接口:', record)
  message.info(`正在测试接口: ${record.name}`)
}

const editInterface = (record) => {
  console.log('编辑接口:', record)
  Object.assign(interfaceForm, record)
  showAddModal.value = true
}

const viewLogs = (record) => {
  console.log('查看日志:', record)
}

const viewDetails = (record) => {
  console.log('查看详情:', record)
}

const exportConfig = (record) => {
  console.log('导出配置:', record)
  message.success('配置导出成功')
}

const toggleInterface = (record) => {
  const newStatus = record.status === 'active' ? 'disabled' : 'active'
  record.status = newStatus
  message.success(`接口已${newStatus === 'active' ? '启用' : '禁用'}`)
}

const deleteInterface = (record) => {
  const index = interfaceList.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    interfaceList.value.splice(index, 1)
    message.success('接口删除成功')
  }
}

const addInterface = () => {
  console.log('添加接口:', interfaceForm)
  const newInterface = {
    id: Date.now(),
    ...interfaceForm,
    status: 'active',
    responseTime: 0,
    successRate: 100
  }
  interfaceList.value.push(newInterface)
  message.success('接口添加成功')
  showAddModal.value = false
  
  // 重置表单
  Object.keys(interfaceForm).forEach(key => {
    interfaceForm[key] = ''
  })
  interfaceForm.authType = 'none'
}

const saveGlobalConfig = () => {
  console.log('保存全局配置:', globalConfig)
  message.success('全局配置保存成功')
  showConfigModal.value = false
}

const markAlertRead = (alert) => {
  alert.read = true
}

const viewAlertDetail = (alert) => {
  console.log('查看告警详情:', alert)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 调用趋势图
    if (callTrendChart.value) {
      const chart1 = echarts.init(callTrendChart.value)
      chart1.setOption({
        title: { text: '接口调用趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '调用次数',
          type: 'line',
          data: [120, 180, 450, 680, 820, 650, 320],
          smooth: true,
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }

    // 响应时间图
    if (responseTimeChart.value) {
      const chart2 = echarts.init(responseTimeChart.value)
      chart2.setOption({
        title: { text: '平均响应时间', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['客户信息', '征信数据', '银行验证', '短信发送', '消息推送']
        },
        yAxis: { type: 'value', name: 'ms' },
        series: [{
          type: 'bar',
          data: [125, 89, 3500, 245, 15],
          itemStyle: {
            color: function(params) {
              return params.value > 1000 ? '#ff4d4f' : '#52c41a'
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.system-interface {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.interface-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.interface-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.interface-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.interface-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.interface-name {
  display: flex;
  align-items: center;
}

.response-fast {
  color: #52c41a;
  font-weight: 500;
}

.response-normal {
  color: #faad14;
  font-weight: 500;
}

.response-slow {
  color: #ff4d4f;
  font-weight: 500;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.realtime-status {
  max-height: 300px;
  overflow-y: auto;
}

.status-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-name {
  font-weight: 500;
  color: #262626;
}

.status-metrics {
  display: flex;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.error-alerts {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-left: 3px solid transparent;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item.unread {
  background: #f6f8ff;
  border-left-color: #1890ff;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.alert-type {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.alert-error {
  background: #fff2f0;
  color: #ff4d4f;
}

.alert-warning {
  background: #fff7e6;
  color: #faad14;
}

.alert-info {
  background: #f6f8ff;
  color: #1890ff;
}

.alert-time {
  font-size: 11px;
  color: #999;
}

.alert-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.quick-config {
  padding: 8px 0;
}

.config-group h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.config-item span {
  font-size: 13px;
  color: #666;
  min-width: 80px;
}

.config-actions {
  margin-top: 16px;
}
</style>