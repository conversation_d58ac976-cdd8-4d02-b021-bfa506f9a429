<template>
  <div class="message-push">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="push-info">
          <h2>消息推送</h2>
          <p class="push-desc">多渠道消息推送管理，提升客户触达效率</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshPushData">
            <template #icon><ReloadOutlined /></template>
            刷新数据
          </a-button>
          <a-button @click="showChannelModal = true">
            <template #icon><SettingOutlined /></template>
            渠道配置
          </a-button>
          <a-button type="primary" @click="showSendModal = true">
            <template #icon><SendOutlined /></template>
            发送消息
          </a-button>
        </div>
      </div>
    </div>

    <!-- 推送统计 -->
    <div class="push-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日推送" 
              :value="pushStats.todayPush" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><SendOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="成功送达" 
              :value="pushStats.successCount" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="送达率" 
              :value="pushStats.deliveryRate" 
              suffix="%" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><BarChartOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="失败数量" 
              :value="pushStats.failedCount" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 推送记录 -->
        <a-card title="推送记录" class="push-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedChannel" style="width: 120px" @change="filterRecords">
                <a-select-option value="all">全部渠道</a-select-option>
                <a-select-option value="sms">短信</a-select-option>
                <a-select-option value="email">邮件</a-select-option>
                <a-select-option value="push">APP推送</a-select-option>
                <a-select-option value="wechat">微信</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterRecords">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="pending">待发送</a-select-option>
                <a-select-option value="sending">发送中</a-select-option>
                <a-select-option value="success">成功</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
              </a-select>
              <a-range-picker v-model:value="dateRange" @change="filterRecords" />
            </a-space>
          </template>
          
          <a-table 
            :columns="recordColumns" 
            :data-source="filteredRecords" 
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'title'">
                <div class="message-title">
                  <component :is="getChannelIcon(record.channel)" style="margin-right: 8px;" />
                  <span>{{ record.title }}</span>
                </div>
              </template>
              <template v-if="column.key === 'channel'">
                <a-tag :color="getChannelColor(record.channel)">
                  {{ getChannelText(record.channel) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'deliveryRate'">
                <a-progress 
                  :percent="record.deliveryRate" 
                  size="small" 
                  :stroke-color="getDeliveryRateColor(record.deliveryRate)"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewDetail(record)">
                    详情
                  </a-button>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="resendMessage(record)"
                    :disabled="record.status === 'sending'"
                  >
                    重发
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="copyMessage(record)">复制</a-menu-item>
                        <a-menu-item @click="exportRecord(record)">导出</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteRecord(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 推送分析 -->
        <a-card title="推送分析" class="push-card">
          <div class="analysis-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="channelChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="trendChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 推送渠道 -->
        <a-card title="推送渠道" class="push-card">
          <template #extra>
            <a-button size="small" @click="showChannelModal = true">
              <template #icon><PlusOutlined /></template>
              配置
            </a-button>
          </template>
          <div class="channel-list">
            <div v-for="channel in pushChannels" :key="channel.id" class="channel-item">
              <div class="channel-header">
                <div class="channel-info">
                  <component :is="getChannelIcon(channel.type)" style="margin-right: 8px;" />
                  <span class="channel-name">{{ channel.name }}</span>
                </div>
                <a-badge 
                  :status="channel.enabled ? 'success' : 'error'" 
                  :text="channel.enabled ? '已启用' : '已禁用'" 
                />
              </div>
              <div class="channel-stats">
                <div class="stat-item">
                  <span class="stat-label">今日发送</span>
                  <span class="stat-value">{{ channel.todaySent }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">成功率</span>
                  <span class="stat-value">{{ channel.successRate }}%</span>
                </div>
              </div>
              <div class="channel-actions">
                <a-button type="link" size="small" @click="testChannel(channel)">
                  测试
                </a-button>
                <a-button type="link" size="small" @click="editChannel(channel)">
                  编辑
                </a-button>
                <a-switch 
                  v-model:checked="channel.enabled" 
                  size="small" 
                  @change="toggleChannel(channel)"
                />
              </div>
            </div>
          </div>
        </a-card>

        <!-- 消息模板 -->
        <a-card title="消息模板" class="push-card">
          <template #extra>
            <a-button size="small" @click="showTemplateModal = true">
              <template #icon><PlusOutlined /></template>
              新建
            </a-button>
          </template>
          <div class="template-list">
            <div v-for="template in messageTemplates" :key="template.id" class="template-item">
              <div class="template-header">
                <span class="template-name">{{ template.name }}</span>
                <a-tag :color="getChannelColor(template.channel)">
                  {{ getChannelText(template.channel) }}
                </a-tag>
              </div>
              <div class="template-content">{{ template.content }}</div>
              <div class="template-actions">
                <a-button type="link" size="small" @click="useTemplate(template)">
                  使用
                </a-button>
                <a-button type="link" size="small" @click="editTemplate(template)">
                  编辑
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 实时状态 -->
        <a-card title="实时状态" class="push-card">
          <div class="realtime-status">
            <div v-for="status in realtimeStatus" :key="status.id" class="status-item">
              <div class="status-header">
                <span class="status-name">{{ status.name }}</span>
                <a-badge 
                  :status="getStatusBadge(status.status)" 
                  :text="status.count + '条'" 
                />
              </div>
              <div class="status-progress">
                <a-progress 
                  :percent="status.progress" 
                  size="small" 
                  :status="status.status === 'failed' ? 'exception' : 'normal'"
                />
              </div>
              <div class="status-time">{{ status.startTime }}</div>
            </div>
            <a-empty v-if="realtimeStatus.length === 0" description="暂无推送任务" size="small" />
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 发送消息模态框 -->
    <a-modal
      v-model:open="showSendModal"
      title="发送消息"
      width="600px"
      @ok="sendMessage"
    >
      <a-form layout="vertical">
        <a-form-item label="消息标题" required>
          <a-input v-model:value="messageForm.title" placeholder="请输入消息标题" />
        </a-form-item>
        <a-form-item label="推送渠道" required>
          <a-select v-model:value="messageForm.channel" placeholder="选择推送渠道">
            <a-select-option value="sms">短信</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="push">APP推送</a-select-option>
            <a-select-option value="wechat">微信</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="目标用户" required>
          <a-select v-model:value="messageForm.targetType" placeholder="选择目标用户">
            <a-select-option value="all">全部用户</a-select-option>
            <a-select-option value="group">用户分组</a-select-option>
            <a-select-option value="custom">自定义用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="用户分组" v-if="messageForm.targetType === 'group'">
          <a-select v-model:value="messageForm.userGroup" mode="multiple" placeholder="选择用户分组">
            <a-select-option value="overdue">逾期用户</a-select-option>
            <a-select-option value="potential">潜在用户</a-select-option>
            <a-select-option value="highvalue">高价值用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="消息内容" required>
          <a-textarea 
            v-model:value="messageForm.content" 
            placeholder="请输入消息内容，支持变量: {name}, {amount}, {date}" 
            :rows="4" 
          />
        </a-form-item>
        <a-form-item label="发送时间">
          <a-radio-group v-model:value="messageForm.sendType">
            <a-radio value="now">立即发送</a-radio>
            <a-radio value="scheduled">定时发送</a-radio>
          </a-radio-group>
          <a-date-picker 
            v-if="messageForm.sendType === 'scheduled'"
            v-model:value="messageForm.sendTime" 
            show-time 
            placeholder="选择发送时间"
            style="margin-top: 8px; width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 渠道配置模态框 -->
    <a-modal
      v-model:open="showChannelModal"
      title="渠道配置"
      width="500px"
      @ok="saveChannel"
    >
      <a-form layout="vertical">
        <a-form-item label="渠道名称" required>
          <a-input v-model:value="channelForm.name" placeholder="请输入渠道名称" />
        </a-form-item>
        <a-form-item label="渠道类型" required>
          <a-select v-model:value="channelForm.type" placeholder="选择渠道类型">
            <a-select-option value="sms">短信</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="push">APP推送</a-select-option>
            <a-select-option value="wechat">微信</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="API地址" required>
          <a-input v-model:value="channelForm.apiUrl" placeholder="请输入API地址" />
        </a-form-item>
        <a-form-item label="API密钥">
          <a-input-password v-model:value="channelForm.apiKey" placeholder="请输入API密钥" />
        </a-form-item>
        <a-form-item label="签名">
          <a-input v-model:value="channelForm.signature" placeholder="请输入签名" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 模板编辑模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="消息模板"
      width="500px"
      @ok="saveTemplate"
    >
      <a-form layout="vertical">
        <a-form-item label="模板名称" required>
          <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
        </a-form-item>
        <a-form-item label="适用渠道" required>
          <a-select v-model:value="templateForm.channel" placeholder="选择适用渠道">
            <a-select-option value="sms">短信</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="push">APP推送</a-select-option>
            <a-select-option value="wechat">微信</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="模板内容" required>
          <a-textarea 
            v-model:value="templateForm.content" 
            placeholder="请输入模板内容，支持变量: {name}, {amount}, {date}" 
            :rows="4" 
          />
        </a-form-item>
        <a-form-item label="描述">
          <a-input v-model:value="templateForm.description" placeholder="模板描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined,
  SendOutlined,
  CheckCircleOutlined,
  BarChartOutlined,
  ExclamationCircleOutlined,
  DownOutlined,
  PlusOutlined,
  MessageOutlined,
  MailOutlined,
  BellOutlined,
  WechatOutlined,
  MobileOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showSendModal = ref(false)
const showChannelModal = ref(false)
const showTemplateModal = ref(false)
const selectedChannel = ref('all')
const selectedStatus = ref('all')
const dateRange = ref([])

// 图表引用
const channelChart = ref(null)
const trendChart = ref(null)

// 推送统计
const pushStats = reactive({
  todayPush: 2845,
  successCount: 2689,
  deliveryRate: 94.5,
  failedCount: 156
})

// 推送记录
const pushRecords = ref([
  {
    id: 1,
    title: '逾期提醒通知',
    channel: 'sms',
    targetCount: 1200,
    sentCount: 1180,
    deliveryRate: 98.3,
    status: 'success',
    sendTime: '2024-01-15 14:30:00',
    content: '尊敬的客户，您的账单已逾期，请及时还款。'
  },
  {
    id: 2,
    title: '还款成功确认',
    channel: 'email',
    targetCount: 856,
    sentCount: 820,
    deliveryRate: 95.8,
    status: 'success',
    sendTime: '2024-01-15 10:15:00',
    content: '您的还款已成功处理，感谢您的配合。'
  },
  {
    id: 3,
    title: '账单到期提醒',
    channel: 'push',
    targetCount: 2400,
    sentCount: 1200,
    deliveryRate: 50.0,
    status: 'sending',
    sendTime: '2024-01-15 09:00:00',
    content: '您的账单即将到期，请提前准备还款。'
  },
  {
    id: 4,
    title: '优惠活动通知',
    channel: 'wechat',
    targetCount: 500,
    sentCount: 0,
    deliveryRate: 0,
    status: 'failed',
    sendTime: '2024-01-15 08:00:00',
    content: '限时优惠活动开始，立即参与享受折扣。'
  }
])

// 推送渠道
const pushChannels = ref([
  {
    id: 1,
    name: '阿里云短信',
    type: 'sms',
    enabled: true,
    todaySent: 1582,
    successRate: 98.5,
    apiUrl: 'https://dysmsapi.aliyuncs.com',
    signature: '催收平台'
  },
  {
    id: 2,
    name: '腾讯企业邮箱',
    type: 'email',
    enabled: true,
    todaySent: 856,
    successRate: 95.2,
    apiUrl: 'smtp.exmail.qq.com',
    signature: '催收管理系统'
  },
  {
    id: 3,
    name: 'Firebase推送',
    type: 'push',
    enabled: true,
    todaySent: 2400,
    successRate: 87.3,
    apiUrl: 'https://fcm.googleapis.com',
    signature: 'CollectionApp'
  },
  {
    id: 4,
    name: '微信服务号',
    type: 'wechat',
    enabled: false,
    todaySent: 0,
    successRate: 0,
    apiUrl: 'https://api.weixin.qq.com',
    signature: '催收助手'
  }
])

// 消息模板
const messageTemplates = ref([
  {
    id: 1,
    name: '逾期提醒模板',
    channel: 'sms',
    content: '尊敬的{name}，您的账单已逾期{days}天，金额{amount}元，请及时还款。',
    description: '用于逾期客户提醒'
  },
  {
    id: 2,
    name: '还款确认模板',
    channel: 'email',
    content: '亲爱的{name}，您于{date}的还款{amount}元已成功处理，感谢您的配合。',
    description: '还款成功确认邮件'
  },
  {
    id: 3,
    name: '账单提醒模板',
    channel: 'push',
    content: '{name}，您的账单{amount}元将于{date}到期，请提前准备还款。',
    description: 'APP推送账单提醒'
  }
])

// 实时状态
const realtimeStatus = ref([
  {
    id: 1,
    name: '短信推送任务',
    status: 'sending',
    progress: 75,
    count: 1200,
    startTime: '14:30:00'
  },
  {
    id: 2,
    name: 'APP推送任务',
    status: 'sending',
    progress: 45,
    count: 2400,
    startTime: '14:25:00'
  }
])

// 表格列定义
const recordColumns = [
  {
    title: '消息标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '渠道',
    dataIndex: 'channel',
    key: 'channel',
    width: 80
  },
  {
    title: '目标数量',
    dataIndex: 'targetCount',
    key: 'targetCount',
    width: 100
  },
  {
    title: '送达率',
    dataIndex: 'deliveryRate',
    key: 'deliveryRate',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '发送时间',
    dataIndex: 'sendTime',
    key: 'sendTime',
    width: 140
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 表单数据
const messageForm = reactive({
  title: '',
  channel: '',
  targetType: 'all',
  userGroup: [],
  content: '',
  sendType: 'now',
  sendTime: null
})

const channelForm = reactive({
  name: '',
  type: '',
  apiUrl: '',
  apiKey: '',
  signature: ''
})

const templateForm = reactive({
  name: '',
  channel: '',
  content: '',
  description: ''
})

// 计算属性
const filteredRecords = computed(() => {
  let filtered = pushRecords.value
  if (selectedChannel.value !== 'all') {
    filtered = filtered.filter(record => record.channel === selectedChannel.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(record => record.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getChannelIcon = (channel) => {
  const icons = {
    sms: 'MessageOutlined',
    email: 'MailOutlined',
    push: 'BellOutlined',
    wechat: 'WechatOutlined'
  }
  return icons[channel] || 'MessageOutlined'
}

const getChannelColor = (channel) => {
  const colors = {
    sms: 'blue',
    email: 'green',
    push: 'orange',
    wechat: 'purple'
  }
  return colors[channel] || 'default'
}

const getChannelText = (channel) => {
  const texts = {
    sms: '短信',
    email: '邮件',
    push: 'APP推送',
    wechat: '微信'
  }
  return texts[channel] || channel
}

const getStatusBadge = (status) => {
  const badges = {
    pending: 'default',
    sending: 'processing',
    success: 'success',
    failed: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待发送',
    sending: '发送中',
    success: '成功',
    failed: '失败'
  }
  return texts[status] || status
}

const getDeliveryRateColor = (rate) => {
  if (rate >= 95) return '#52c41a'
  if (rate >= 80) return '#faad14'
  return '#ff4d4f'
}

const refreshPushData = () => {
  console.log('刷新推送数据')
  message.success('数据已刷新')
}

const filterRecords = () => {
  console.log('筛选记录:', selectedChannel.value, selectedStatus.value, dateRange.value)
}

const viewDetail = (record) => {
  console.log('查看详情:', record)
}

const resendMessage = (record) => {
  console.log('重发消息:', record)
  record.status = 'sending'
  message.success('消息重发中...')
}

const copyMessage = (record) => {
  console.log('复制消息:', record)
  Object.assign(messageForm, {
    title: record.title + '_副本',
    channel: record.channel,
    content: record.content
  })
  showSendModal.value = true
}

const exportRecord = (record) => {
  console.log('导出记录:', record)
  message.success('记录导出成功')
}

const deleteRecord = (record) => {
  const index = pushRecords.value.findIndex(r => r.id === record.id)
  if (index > -1) {
    pushRecords.value.splice(index, 1)
    message.success('记录删除成功')
  }
}

const testChannel = (channel) => {
  console.log('测试渠道:', channel)
  message.info(`正在测试${channel.name}...`)
  setTimeout(() => {
    message.success('渠道测试成功')
  }, 1500)
}

const editChannel = (channel) => {
  console.log('编辑渠道:', channel)
  Object.assign(channelForm, channel)
  showChannelModal.value = true
}

const toggleChannel = (channel) => {
  message.success(`${channel.name}已${channel.enabled ? '启用' : '禁用'}`)
}

const useTemplate = (template) => {
  console.log('使用模板:', template)
  Object.assign(messageForm, {
    title: template.name,
    channel: template.channel,
    content: template.content
  })
  showSendModal.value = true
}

const editTemplate = (template) => {
  console.log('编辑模板:', template)
  Object.assign(templateForm, template)
  showTemplateModal.value = true
}

const sendMessage = () => {
  console.log('发送消息:', messageForm)
  const newRecord = {
    id: Date.now(),
    ...messageForm,
    targetCount: messageForm.targetType === 'all' ? 5000 : 1200,
    sentCount: 0,
    deliveryRate: 0,
    status: 'pending',
    sendTime: new Date().toLocaleString()
  }
  pushRecords.value.unshift(newRecord)
  message.success('消息发送任务已创建')
  showSendModal.value = false
  
  // 重置表单
  Object.keys(messageForm).forEach(key => {
    if (Array.isArray(messageForm[key])) {
      messageForm[key] = []
    } else if (key === 'sendType') {
      messageForm[key] = 'now'
    } else {
      messageForm[key] = ''
    }
  })
}

const saveChannel = () => {
  console.log('保存渠道:', channelForm)
  if (channelForm.id) {
    // 编辑现有渠道
    const index = pushChannels.value.findIndex(c => c.id === channelForm.id)
    if (index > -1) {
      Object.assign(pushChannels.value[index], channelForm)
    }
  } else {
    // 添加新渠道
    const newChannel = {
      id: Date.now(),
      ...channelForm,
      enabled: true,
      todaySent: 0,
      successRate: 0
    }
    pushChannels.value.push(newChannel)
  }
  message.success('渠道配置保存成功')
  showChannelModal.value = false
  
  // 重置表单
  Object.keys(channelForm).forEach(key => {
    channelForm[key] = ''
  })
}

const saveTemplate = () => {
  console.log('保存模板:', templateForm)
  if (templateForm.id) {
    // 编辑现有模板
    const index = messageTemplates.value.findIndex(t => t.id === templateForm.id)
    if (index > -1) {
      Object.assign(messageTemplates.value[index], templateForm)
    }
  } else {
    // 添加新模板
    const newTemplate = {
      id: Date.now(),
      ...templateForm
    }
    messageTemplates.value.push(newTemplate)
  }
  message.success('模板保存成功')
  showTemplateModal.value = false
  
  // 重置表单
  Object.keys(templateForm).forEach(key => {
    templateForm[key] = ''
  })
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 渠道分布图
    if (channelChart.value) {
      const chart1 = echarts.init(channelChart.value)
      chart1.setOption({
        title: { text: '渠道推送分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 1582, name: '短信' },
            { value: 856, name: '邮件' },
            { value: 2400, name: 'APP推送' },
            { value: 7, name: '微信' }
          ]
        }]
      })
    }

    // 推送趋势图
    if (trendChart.value) {
      const chart2 = echarts.init(trendChart.value)
      chart2.setOption({
        title: { text: '推送趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['00:00', '06:00', '12:00', '18:00', '24:00']
        },
        yAxis: { type: 'value', name: '推送量' },
        series: [{
          name: '推送量',
          type: 'line',
          data: [120, 280, 1200, 2400, 800],
          smooth: true,
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.message-push {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.push-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.push-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.push-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.push-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-title {
  display: flex;
  align-items: center;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.channel-list {
  max-height: 300px;
  overflow-y: auto;
}

.channel-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.channel-info {
  display: flex;
  align-items: center;
}

.channel-name {
  font-weight: 500;
  color: #262626;
}

.channel-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.channel-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-list {
  max-height: 300px;
  overflow-y: auto;
}

.template-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-name {
  font-weight: 500;
  color: #262626;
}

.template-content {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.realtime-status {
  max-height: 250px;
  overflow-y: auto;
}

.status-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-name {
  font-weight: 500;
  color: #262626;
}

.status-progress {
  margin-bottom: 8px;
}

.status-time {
  font-size: 12px;
  color: #999;
}
</style>