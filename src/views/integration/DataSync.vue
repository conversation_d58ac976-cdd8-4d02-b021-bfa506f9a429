<template>
  <div class="data-sync">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="sync-info">
          <h2>数据同步</h2>
          <p class="sync-desc">多系统数据同步管理，确保数据一致性和完整性</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshSyncStatus">
            <template #icon><ReloadOutlined /></template>
            刷新状态
          </a-button>
          <a-button @click="showConfigModal = true">
            <template #icon><SettingOutlined /></template>
            同步配置
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建同步任务
          </a-button>
        </div>
      </div>
    </div>

    <!-- 同步统计 -->
    <div class="sync-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="同步任务" 
              :value="syncStats.totalTasks" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><SyncOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="运行中" 
              :value="syncStats.runningTasks" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日同步" 
              :value="syncStats.todaySync" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><CloudSyncOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="失败任务" 
              :value="syncStats.failedTasks" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 同步任务列表 -->
        <a-card title="同步任务" class="sync-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedType" style="width: 120px" @change="filterTasks">
                <a-select-option value="all">全部类型</a-select-option>
                <a-select-option value="realtime">实时同步</a-select-option>
                <a-select-option value="scheduled">定时同步</a-select-option>
                <a-select-option value="manual">手动同步</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterTasks">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="running">运行中</a-select-option>
                <a-select-option value="success">成功</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
                <a-select-option value="paused">已暂停</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <a-table 
            :columns="taskColumns" 
            :data-source="filteredTasks" 
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="task-name">
                  <component :is="getTaskIcon(record.type)" style="margin-right: 8px;" />
                  <span>{{ record.name }}</span>
                </div>
              </template>
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'progress'">
                <a-progress 
                  :percent="record.progress" 
                  size="small" 
                  :status="record.status === 'failed' ? 'exception' : 'normal'"
                />
              </template>
              <template v-if="column.key === 'lastSync'">
                <span :class="getLastSyncClass(record.lastSync)">
                  {{ record.lastSync }}
                </span>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="executeTask(record)"
                    :disabled="record.status === 'running'"
                  >
                    {{ record.status === 'paused' ? '继续' : '执行' }}
                  </a-button>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="pauseTask(record)"
                    :disabled="record.status !== 'running'"
                  >
                    暂停
                  </a-button>
                  <a-button type="link" size="small" @click="editTask(record)">
                    编辑
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewTaskDetail(record)">详情</a-menu-item>
                        <a-menu-item @click="viewTaskLogs(record)">日志</a-menu-item>
                        <a-menu-item @click="duplicateTask(record)">复制</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteTask(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 同步监控 -->
        <a-card title="同步监控" class="sync-card">
          <div class="monitor-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="syncTrendChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="dataVolumeChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时状态 -->
        <a-card title="实时状态" class="sync-card">
          <div class="realtime-status">
            <div v-for="status in realtimeStatus" :key="status.id" class="status-item">
              <div class="status-header">
                <span class="status-name">{{ status.name }}</span>
                <a-badge 
                  :status="getStatusBadge(status.status)" 
                  :text="status.status === 'running' ? '同步中' : getStatusText(status.status)" 
                />
              </div>
              <div class="status-progress">
                <a-progress 
                  :percent="status.progress" 
                  size="small" 
                  :status="status.status === 'failed' ? 'exception' : 'normal'"
                />
              </div>
              <div class="status-metrics">
                <div class="metric">
                  <span class="metric-label">数据量</span>
                  <span class="metric-value">{{ status.dataCount }}</span>
                </div>
                <div class="metric">
                  <span class="metric-label">耗时</span>
                  <span class="metric-value">{{ status.duration }}</span>
                </div>
              </div>
            </div>
            <a-empty v-if="realtimeStatus.length === 0" description="暂无运行任务" size="small" />
          </div>
        </a-card>

        <!-- 数据源管理 -->
        <a-card title="数据源" class="sync-card">
          <template #extra>
            <a-button size="small" @click="showDataSourceModal = true">
              <template #icon><PlusOutlined /></template>
              添加
            </a-button>
          </template>
          <div class="data-sources">
            <div v-for="source in dataSources" :key="source.id" class="source-item">
              <div class="source-header">
                <div class="source-info">
                  <component :is="getSourceIcon(source.type)" style="margin-right: 8px;" />
                  <span class="source-name">{{ source.name }}</span>
                </div>
                <a-badge 
                  :status="source.connected ? 'success' : 'error'" 
                  :text="source.connected ? '已连接' : '未连接'" 
                />
              </div>
              <div class="source-details">
                <div class="source-type">{{ source.type }}</div>
                <div class="source-url">{{ source.url }}</div>
              </div>
              <div class="source-actions">
                <a-button type="link" size="small" @click="testConnection(source)">
                  测试连接
                </a-button>
                <a-button type="link" size="small" @click="editDataSource(source)">
                  编辑
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 同步规则 -->
        <a-card title="同步规则" class="sync-card">
          <template #extra>
            <a-button size="small" @click="showRuleModal = true">
              <template #icon><PlusOutlined /></template>
              新建
            </a-button>
          </template>
          <div class="sync-rules">
            <div v-for="rule in syncRules" :key="rule.id" class="rule-item">
              <div class="rule-header">
                <span class="rule-name">{{ rule.name }}</span>
                <a-switch v-model:checked="rule.enabled" size="small" />
              </div>
              <div class="rule-description">{{ rule.description }}</div>
              <div class="rule-schedule">
                <ClockCircleOutlined style="margin-right: 4px;" />
                {{ rule.schedule }}
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 创建同步任务模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建同步任务"
      width="600px"
      @ok="createTask"
    >
      <a-form layout="vertical">
        <a-form-item label="任务名称" required>
          <a-input v-model:value="taskForm.name" placeholder="请输入任务名称" />
        </a-form-item>
        <a-form-item label="同步类型" required>
          <a-select v-model:value="taskForm.type" placeholder="选择同步类型">
            <a-select-option value="realtime">实时同步</a-select-option>
            <a-select-option value="scheduled">定时同步</a-select-option>
            <a-select-option value="manual">手动同步</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="源数据库" required>
          <a-select v-model:value="taskForm.sourceId" placeholder="选择源数据库">
            <a-select-option v-for="source in dataSources" :key="source.id" :value="source.id">
              {{ source.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="目标数据库" required>
          <a-select v-model:value="taskForm.targetId" placeholder="选择目标数据库">
            <a-select-option v-for="source in dataSources" :key="source.id" :value="source.id">
              {{ source.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="同步表" required>
          <a-select v-model:value="taskForm.tables" mode="multiple" placeholder="选择要同步的表">
            <a-select-option value="customers">客户信息表</a-select-option>
            <a-select-option value="cases">案件表</a-select-option>
            <a-select-option value="payments">还款记录表</a-select-option>
            <a-select-option value="contacts">联系人表</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="调度计划" v-if="taskForm.type === 'scheduled'">
          <a-input v-model:value="taskForm.schedule" placeholder="如: 0 0 2 * * ? (每天凌晨2点)" />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:value="taskForm.description" placeholder="任务描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 数据源配置模态框 -->
    <a-modal
      v-model:open="showDataSourceModal"
      title="数据源配置"
      width="500px"
      @ok="saveDataSource"
    >
      <a-form layout="vertical">
        <a-form-item label="数据源名称" required>
          <a-input v-model:value="dataSourceForm.name" placeholder="请输入数据源名称" />
        </a-form-item>
        <a-form-item label="数据源类型" required>
          <a-select v-model:value="dataSourceForm.type" placeholder="选择数据源类型">
            <a-select-option value="mysql">MySQL</a-select-option>
            <a-select-option value="postgresql">PostgreSQL</a-select-option>
            <a-select-option value="oracle">Oracle</a-select-option>
            <a-select-option value="sqlserver">SQL Server</a-select-option>
            <a-select-option value="mongodb">MongoDB</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="连接地址" required>
          <a-input v-model:value="dataSourceForm.url" placeholder="请输入连接地址" />
        </a-form-item>
        <a-form-item label="用户名">
          <a-input v-model:value="dataSourceForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="密码">
          <a-input-password v-model:value="dataSourceForm.password" placeholder="请输入密码" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 同步配置模态框 -->
    <a-modal
      v-model:open="showConfigModal"
      title="同步配置"
      @ok="saveConfig"
    >
      <a-form layout="vertical">
        <a-form-item label="批量大小">
          <a-input-number v-model:value="syncConfig.batchSize" :min="100" :max="10000" style="width: 100%" />
        </a-form-item>
        <a-form-item label="超时时间(秒)">
          <a-input-number v-model:value="syncConfig.timeout" :min="30" :max="3600" style="width: 100%" />
        </a-form-item>
        <a-form-item label="重试次数">
          <a-input-number v-model:value="syncConfig.retryCount" :min="0" :max="10" style="width: 100%" />
        </a-form-item>
        <a-form-item label="并发线程数">
          <a-input-number v-model:value="syncConfig.threadCount" :min="1" :max="20" style="width: 100%" />
        </a-form-item>
        <a-form-item label="错误处理策略">
          <a-select v-model:value="syncConfig.errorStrategy" style="width: 100%">
            <a-select-option value="skip">跳过错误记录</a-select-option>
            <a-select-option value="stop">停止同步</a-select-option>
            <a-select-option value="retry">重试处理</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  CloudSyncOutlined,
  ExclamationCircleOutlined,
  DownOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  CloudOutlined,
  HddOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showCreateModal = ref(false)
const showDataSourceModal = ref(false)
const showConfigModal = ref(false)
const showRuleModal = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')

// 图表引用
const syncTrendChart = ref(null)
const dataVolumeChart = ref(null)

// 同步统计
const syncStats = reactive({
  totalTasks: 12,
  runningTasks: 3,
  todaySync: 156,
  failedTasks: 1
})

// 同步任务列表
const syncTasks = ref([
  {
    id: 1,
    name: '客户信息同步',
    type: 'realtime',
    status: 'running',
    progress: 75,
    sourceDb: 'CRM系统',
    targetDb: '催收系统',
    lastSync: '2分钟前',
    schedule: '实时'
  },
  {
    id: 2,
    name: '案件数据同步',
    type: 'scheduled',
    status: 'success',
    progress: 100,
    sourceDb: '业务系统',
    targetDb: '催收系统',
    lastSync: '1小时前',
    schedule: '每小时'
  },
  {
    id: 3,
    name: '还款记录同步',
    type: 'scheduled',
    status: 'failed',
    progress: 45,
    sourceDb: '支付系统',
    targetDb: '催收系统',
    lastSync: '3小时前',
    schedule: '每30分钟'
  },
  {
    id: 4,
    name: '联系人信息同步',
    type: 'manual',
    status: 'paused',
    progress: 30,
    sourceDb: 'CRM系统',
    targetDb: '催收系统',
    lastSync: '昨天',
    schedule: '手动'
  }
])

// 实时状态
const realtimeStatus = ref([
  {
    id: 1,
    name: '客户信息同步',
    status: 'running',
    progress: 75,
    dataCount: '1.2万',
    duration: '5分钟'
  },
  {
    id: 2,
    name: '征信数据同步',
    status: 'running',
    progress: 45,
    dataCount: '8千',
    duration: '3分钟'
  }
])

// 数据源
const dataSources = ref([
  {
    id: 1,
    name: 'CRM主库',
    type: 'MySQL',
    url: 'mysql://**********:3306/crm',
    connected: true,
    username: 'crm_user'
  },
  {
    id: 2,
    name: '支付系统',
    type: 'PostgreSQL',
    url: 'postgresql://**********:5432/payment',
    connected: true,
    username: 'pay_user'
  },
  {
    id: 3,
    name: '数据仓库',
    type: 'Oracle',
    url: 'oracle://10.1.1.300:1521/dw',
    connected: false,
    username: 'dw_user'
  }
])

// 同步规则
const syncRules = ref([
  {
    id: 1,
    name: '客户信息增量同步',
    description: '每10分钟同步新增和修改的客户信息',
    schedule: '每10分钟',
    enabled: true
  },
  {
    id: 2,
    name: '案件状态全量同步',
    description: '每天凌晨2点进行案件状态全量同步',
    schedule: '每天 02:00',
    enabled: true
  },
  {
    id: 3,
    name: '还款记录实时同步',
    description: '实时同步还款记录变更',
    schedule: '实时',
    enabled: false
  }
])

// 表格列定义
const taskColumns = [
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name',
    width: 180
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 120
  },
  {
    title: '最后同步',
    dataIndex: 'lastSync',
    key: 'lastSync',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 180
  }
]

// 表单数据
const taskForm = reactive({
  name: '',
  type: '',
  sourceId: '',
  targetId: '',
  tables: [],
  schedule: '',
  description: ''
})

const dataSourceForm = reactive({
  name: '',
  type: '',
  url: '',
  username: '',
  password: ''
})

const syncConfig = reactive({
  batchSize: 1000,
  timeout: 300,
  retryCount: 3,
  threadCount: 5,
  errorStrategy: 'skip'
})

// 计算属性
const filteredTasks = computed(() => {
  let filtered = syncTasks.value
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(task => task.type === selectedType.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(task => task.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getTaskIcon = (type) => {
  const icons = {
    realtime: 'SyncOutlined',
    scheduled: 'ClockCircleOutlined',
    manual: 'PlayCircleOutlined'
  }
  return icons[type] || 'SyncOutlined'
}

const getTypeColor = (type) => {
  const colors = {
    realtime: 'blue',
    scheduled: 'green',
    manual: 'orange'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    realtime: '实时同步',
    scheduled: '定时同步',
    manual: '手动同步'
  }
  return texts[type] || type
}

const getStatusBadge = (status) => {
  const badges = {
    running: 'processing',
    success: 'success',
    failed: 'error',
    paused: 'warning'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    success: '成功',
    failed: '失败',
    paused: '已暂停'
  }
  return texts[status] || status
}

const getLastSyncClass = (lastSync) => {
  if (lastSync.includes('分钟前') || lastSync.includes('刚刚')) return 'sync-recent'
  if (lastSync.includes('小时前')) return 'sync-normal'
  return 'sync-old'
}

const getSourceIcon = (type) => {
  const icons = {
    'MySQL': 'DatabaseOutlined',
    'PostgreSQL': 'DatabaseOutlined',
    'Oracle': 'DatabaseOutlined',
    'SQL Server': 'HddOutlined',
    'MongoDB': 'CloudOutlined'
  }
  return icons[type] || 'DatabaseOutlined'
}

const refreshSyncStatus = () => {
  console.log('刷新同步状态')
  message.success('同步状态已刷新')
}

const filterTasks = () => {
  console.log('筛选任务:', selectedType.value, selectedStatus.value)
}

const executeTask = (record) => {
  console.log('执行任务:', record)
  record.status = 'running'
  record.progress = 0
  message.success(`任务 ${record.name} 开始执行`)
}

const pauseTask = (record) => {
  console.log('暂停任务:', record)
  record.status = 'paused'
  message.success(`任务 ${record.name} 已暂停`)
}

const editTask = (record) => {
  console.log('编辑任务:', record)
  Object.assign(taskForm, record)
  showCreateModal.value = true
}

const viewTaskDetail = (record) => {
  console.log('查看任务详情:', record)
}

const viewTaskLogs = (record) => {
  console.log('查看任务日志:', record)
}

const duplicateTask = (record) => {
  console.log('复制任务:', record)
  const newTask = {
    ...record,
    id: Date.now(),
    name: record.name + '_副本',
    status: 'paused'
  }
  syncTasks.value.push(newTask)
  message.success('任务复制成功')
}

const deleteTask = (record) => {
  const index = syncTasks.value.findIndex(task => task.id === record.id)
  if (index > -1) {
    syncTasks.value.splice(index, 1)
    message.success('任务删除成功')
  }
}

const createTask = () => {
  console.log('创建同步任务:', taskForm)
  const newTask = {
    id: Date.now(),
    ...taskForm,
    status: 'paused',
    progress: 0,
    lastSync: '从未'
  }
  syncTasks.value.push(newTask)
  message.success('同步任务创建成功')
  showCreateModal.value = false
  
  // 重置表单
  Object.keys(taskForm).forEach(key => {
    if (Array.isArray(taskForm[key])) {
      taskForm[key] = []
    } else {
      taskForm[key] = ''
    }
  })
}

const testConnection = (source) => {
  console.log('测试连接:', source)
  message.info(`正在测试 ${source.name} 连接...`)
  setTimeout(() => {
    source.connected = true
    message.success('连接测试成功')
  }, 1500)
}

const editDataSource = (source) => {
  console.log('编辑数据源:', source)
  Object.assign(dataSourceForm, source)
  showDataSourceModal.value = true
}

const saveDataSource = () => {
  console.log('保存数据源:', dataSourceForm)
  if (dataSourceForm.id) {
    // 编辑现有数据源
    const index = dataSources.value.findIndex(s => s.id === dataSourceForm.id)
    if (index > -1) {
      Object.assign(dataSources.value[index], dataSourceForm)
    }
  } else {
    // 添加新数据源
    const newSource = {
      id: Date.now(),
      ...dataSourceForm,
      connected: false
    }
    dataSources.value.push(newSource)
  }
  message.success('数据源保存成功')
  showDataSourceModal.value = false
  
  // 重置表单
  Object.keys(dataSourceForm).forEach(key => {
    dataSourceForm[key] = ''
  })
}

const saveConfig = () => {
  console.log('保存同步配置:', syncConfig)
  message.success('同步配置保存成功')
  showConfigModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 同步趋势图
    if (syncTrendChart.value) {
      const chart1 = echarts.init(syncTrendChart.value)
      chart1.setOption({
        title: { text: '同步数据量趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        },
        yAxis: { type: 'value', name: '记录数' },
        series: [{
          name: '同步记录数',
          type: 'line',
          data: [1200, 800, 2400, 3600, 4200, 3800, 2100],
          smooth: true,
          itemStyle: { color: '#52c41a' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }

    // 数据量分布图
    if (dataVolumeChart.value) {
      const chart2 = echarts.init(dataVolumeChart.value)
      chart2.setOption({
        title: { text: '数据源分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 35, name: 'CRM系统' },
            { value: 25, name: '支付系统' },
            { value: 20, name: '业务系统' },
            { value: 20, name: '数据仓库' }
          ]
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.data-sync {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.sync-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.sync-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sync-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-name {
  display: flex;
  align-items: center;
}

.sync-recent {
  color: #52c41a;
  font-weight: 500;
}

.sync-normal {
  color: #faad14;
  font-weight: 500;
}

.sync-old {
  color: #999;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.realtime-status {
  max-height: 300px;
  overflow-y: auto;
}

.status-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-name {
  font-weight: 500;
  color: #262626;
}

.status-progress {
  margin-bottom: 8px;
}

.status-metrics {
  display: flex;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.data-sources {
  max-height: 300px;
  overflow-y: auto;
}

.source-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.source-info {
  display: flex;
  align-items: center;
}

.source-name {
  font-weight: 500;
  color: #262626;
}

.source-details {
  margin-bottom: 8px;
}

.source-type {
  font-size: 12px;
  color: #1890ff;
  margin-bottom: 2px;
}

.source-url {
  font-size: 12px;
  color: #999;
  font-family: monospace;
}

.source-actions {
  display: flex;
  gap: 8px;
}

.sync-rules {
  max-height: 250px;
  overflow-y: auto;
}

.rule-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-name {
  font-weight: 500;
  color: #262626;
}

.rule-description {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 6px;
}

.rule-schedule {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}
</style>