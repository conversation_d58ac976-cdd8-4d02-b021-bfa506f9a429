<template>
  <div class="credit-interface">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="credit-info">
          <h2>征信接口</h2>
          <p class="credit-desc">对接各大征信机构，提供全面的信用数据查询服务</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshCreditData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showBatchQueryModal = true">
            <template #icon><FileSearchOutlined /></template>
            批量查询
          </a-button>
          <a-button type="primary" @click="showQueryModal = true">
            <template #icon><PlusOutlined /></template>
            征信查询
          </a-button>
        </div>
      </div>
    </div>

    <!-- 征信统计 -->
    <div class="credit-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日查询" 
              :value="creditStats.todayQueries" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><FileSearchOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="成功率" 
              :value="creditStats.successRate" 
              :value-style="{ color: '#52c41a' }"
              suffix="%"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="查询成本" 
              :value="creditStats.todayCost" 
              :value-style="{ color: '#722ed1' }"
              suffix="元"
            >
              <template #prefix><DollarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="待处理" 
              :value="creditStats.pending" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 查询记录 -->
        <a-card title="征信查询记录" class="credit-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedSource" style="width: 120px" @change="filterQueries">
                <a-select-option value="all">全部机构</a-select-option>
                <a-select-option value="pboc">人民银行</a-select-option>
                <a-select-option value="sesame">芝麻信用</a-select-option>
                <a-select-option value="tencent">腾讯征信</a-select-option>
                <a-select-option value="baiqian">百行征信</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterQueries">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="success">成功</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
                <a-select-option value="pending">处理中</a-select-option>
              </a-select>
              <a-date-picker v-model:value="selectedDate" @change="filterQueries" />
            </a-space>
          </template>
          
          <a-table 
            :columns="queryColumns" 
            :data-source="filteredQueries" 
            :pagination="{ pageSize: 10 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'customerInfo'">
                <div class="customer-info">
                  <div class="customer-name">{{ record.customerName }}</div>
                  <div class="customer-id">{{ record.idCard }}</div>
                </div>
              </template>
              <template v-if="column.key === 'source'">
                <a-tag :color="getSourceColor(record.source)">
                  <component :is="getSourceIcon(record.source)" style="margin-right: 4px;" />
                  {{ getSourceText(record.source) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'cost'">
                <span class="cost-text">¥{{ record.cost }}</span>
              </template>
              <template v-if="column.key === 'score'">
                <div class="score-display">
                  <a-progress 
                    :percent="record.score" 
                    size="small" 
                    :stroke-color="getScoreColor(record.score)"
                    :format="percent => `${percent}分`"
                  />
                </div>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewCreditReport(record)">
                    报告
                  </a-button>
                  <a-button type="link" size="small" @click="downloadReport(record)">
                    下载
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="shareReport(record)">分享</a-menu-item>
                        <a-menu-item @click="printReport(record)">打印</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteQuery(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 数据分析 -->
        <a-card title="征信数据分析" class="credit-card">
          <div class="analysis-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="queryTrendChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="sourceDistributionChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 接口状态 -->
        <a-card title="接口状态" class="credit-card">
          <div class="interface-status">
            <div v-for="item in interfaceStatus" :key="item.id" class="status-item">
              <div class="status-header">
                <div class="interface-name">
                  <component :is="getSourceIcon(item.source)" style="margin-right: 8px;" />
                  {{ item.name }}
                </div>
                <a-badge 
                  :status="getStatusBadge(item.status)" 
                  :text="item.responseTime + 'ms'" 
                />
              </div>
              <div class="status-metrics">
                <div class="metric">
                  <span class="metric-label">成功率</span>
                  <span class="metric-value">{{ item.successRate }}%</span>
                </div>
                <div class="metric">
                  <span class="metric-label">今日调用</span>
                  <span class="metric-value">{{ item.todayCount }}</span>
                </div>
                <div class="metric">
                  <span class="metric-label">费用/次</span>
                  <span class="metric-value cost">¥{{ item.unitCost }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 费用统计 -->
        <a-card title="费用统计" class="credit-card">
          <div class="cost-statistics">
            <div class="cost-overview">
              <div class="cost-item">
                <span class="cost-label">本月费用</span>
                <span class="cost-value total">¥{{ costStats.monthCost }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">平均费用</span>
                <span class="cost-value">¥{{ costStats.avgCost }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">预算余额</span>
                <span class="cost-value remaining">¥{{ costStats.remaining }}</span>
              </div>
            </div>
            <div class="cost-chart">
              <div ref="costTrendChart" class="mini-chart"></div>
            </div>
          </div>
        </a-card>

        <!-- 风险预警 -->
        <a-card title="风险预警" class="credit-card">
          <template #extra>
            <a-badge :count="riskAlerts.filter(a => !a.read).length" />
          </template>
          <div class="risk-alerts">
            <div v-for="alert in riskAlerts" :key="alert.id" class="alert-item" :class="{ 'unread': !alert.read }">
              <div class="alert-header">
                <div class="alert-type" :class="getAlertClass(alert.level)">
                  {{ getAlertLevelText(alert.level) }}
                </div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
              <div class="alert-content">{{ alert.content }}</div>
              <div class="alert-actions">
                <a-button v-if="!alert.read" type="link" size="small" @click="markAlertRead(alert)">
                  标记已读
                </a-button>
                <a-button type="link" size="small" @click="handleRiskAlert(alert)">
                  处理
                </a-button>
              </div>
            </div>
            <a-empty v-if="riskAlerts.length === 0" description="暂无风险预警" size="small" />
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 征信查询模态框 -->
    <a-modal
      v-model:open="showQueryModal"
      title="征信查询"
      width="600px"
      @ok="submitCreditQuery"
    >
      <a-form layout="vertical">
        <a-form-item label="客户姓名" required>
          <a-input v-model:value="queryForm.customerName" placeholder="请输入客户姓名" />
        </a-form-item>
        <a-form-item label="身份证号" required>
          <a-input v-model:value="queryForm.idCard" placeholder="请输入身份证号" />
        </a-form-item>
        <a-form-item label="手机号码">
          <a-input v-model:value="queryForm.phone" placeholder="请输入手机号码" />
        </a-form-item>
        <a-form-item label="征信机构" required>
          <a-checkbox-group v-model:value="queryForm.sources">
            <a-checkbox value="pboc">人民银行征信</a-checkbox>
            <a-checkbox value="sesame">芝麻信用</a-checkbox>
            <a-checkbox value="tencent">腾讯征信</a-checkbox>
            <a-checkbox value="baiqian">百行征信</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="查询用途">
          <a-select v-model:value="queryForm.purpose" placeholder="选择查询用途">
            <a-select-option value="loan">贷款审批</a-select-option>
            <a-select-option value="collection">催收评估</a-select-option>
            <a-select-option value="risk">风险评估</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量查询模态框 -->
    <a-modal
      v-model:open="showBatchQueryModal"
      title="批量征信查询"
      width="700px"
      @ok="submitBatchQuery"
    >
      <div class="batch-query-content">
        <a-alert 
          message="批量查询说明" 
          description="请上传包含客户信息的Excel文件，文件应包含姓名、身份证号等必要字段。" 
          type="info" 
          show-icon 
          style="margin-bottom: 16px;"
        />
        <a-form layout="vertical">
          <a-form-item label="上传文件">
            <a-upload
              v-model:file-list="batchFileList"
              :before-upload="beforeUpload"
              :remove="removeFile"
              accept=".xlsx,.xls"
            >
              <a-button>
                <template #icon><UploadOutlined /></template>
                选择文件
              </a-button>
            </a-upload>
          </a-form-item>
          <a-form-item label="征信机构">
            <a-checkbox-group v-model:value="batchQueryForm.sources">
              <a-checkbox value="pboc">人民银行征信</a-checkbox>
              <a-checkbox value="sesame">芝麻信用</a-checkbox>
              <a-checkbox value="tencent">腾讯征信</a-checkbox>
              <a-checkbox value="baiqian">百行征信</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
        <div v-if="batchPreview.length > 0" class="batch-preview">
          <h4>预览数据（前5条）:</h4>
          <a-table 
            :columns="previewColumns" 
            :data-source="batchPreview" 
            :pagination="false"
            size="small"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  FileSearchOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  DownOutlined,
  UploadOutlined,
  BankOutlined,
  CrownOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showQueryModal = ref(false)
const showBatchQueryModal = ref(false)
const selectedSource = ref('all')
const selectedStatus = ref('all')
const selectedDate = ref(null)

// 图表引用
const queryTrendChart = ref(null)
const sourceDistributionChart = ref(null)
const costTrendChart = ref(null)

// 征信统计
const creditStats = reactive({
  todayQueries: 156,
  successRate: 94.8,
  todayCost: 2340,
  pending: 8
})

// 查询记录列表
const queryRecords = ref([
  {
    id: 1,
    customerName: '张三',
    idCard: '110101199001011234',
    phone: '***********',
    source: 'pboc',
    status: 'success',
    cost: 15,
    score: 720,
    queryTime: '2023-07-28 10:30:00',
    purpose: '贷款审批'
  },
  {
    id: 2,
    customerName: '李四',
    idCard: '110101199002022345',
    phone: '13800138001',
    source: 'sesame',
    status: 'success',
    cost: 12,
    score: 680,
    queryTime: '2023-07-28 09:15:00',
    purpose: '催收评估'
  },
  {
    id: 3,
    customerName: '王五',
    idCard: '110101199003033456',
    phone: '13800138002',
    source: 'tencent',
    status: 'failed',
    cost: 0,
    score: 0,
    queryTime: '2023-07-28 08:45:00',
    purpose: '风险评估'
  },
  {
    id: 4,
    customerName: '赵六',
    idCard: '110101199004044567',
    phone: '13800138003',
    source: 'baiqian',
    status: 'pending',
    cost: 10,
    score: 0,
    queryTime: '2023-07-28 11:20:00',
    purpose: '贷款审批'
  }
])

// 接口状态
const interfaceStatus = ref([
  {
    id: 1,
    name: '人民银行征信',
    source: 'pboc',
    status: 'success',
    responseTime: 1200,
    successRate: 98.5,
    todayCount: 45,
    unitCost: 15
  },
  {
    id: 2,
    name: '芝麻信用',
    source: 'sesame',
    status: 'success',
    responseTime: 800,
    successRate: 96.2,
    todayCount: 62,
    unitCost: 12
  },
  {
    id: 3,
    name: '腾讯征信',
    source: 'tencent',
    status: 'error',
    responseTime: 3500,
    successRate: 85.6,
    todayCount: 23,
    unitCost: 8
  },
  {
    id: 4,
    name: '百行征信',
    source: 'baiqian',
    status: 'success',
    responseTime: 1500,
    successRate: 94.3,
    todayCount: 26,
    unitCost: 10
  }
])

// 费用统计
const costStats = reactive({
  monthCost: 45680,
  avgCost: 12.5,
  remaining: 24320
})

// 风险预警
const riskAlerts = ref([
  {
    id: 1,
    level: 'high',
    time: '10分钟前',
    content: '腾讯征信接口连续失败，建议检查配置',
    read: false
  },
  {
    id: 2,
    level: 'medium',
    time: '30分钟前',
    content: '本月征信查询费用已超预算80%',
    read: false
  },
  {
    id: 3,
    level: 'low',
    time: '1小时前',
    content: '百行征信接口响应时间较慢',
    read: true
  }
])

// 表格列定义
const queryColumns = [
  {
    title: '客户信息',
    key: 'customerInfo',
    width: 150
  },
  {
    title: '征信机构',
    dataIndex: 'source',
    key: 'source',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '费用',
    dataIndex: 'cost',
    key: 'cost',
    width: 80
  },
  {
    title: '信用分',
    dataIndex: 'score',
    key: 'score',
    width: 120
  },
  {
    title: '查询时间',
    dataIndex: 'queryTime',
    key: 'queryTime',
    width: 140
  },
  {
    title: '操作',
    key: 'action',
    width: 140
  }
]

const previewColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '身份证号', dataIndex: 'idCard', key: 'idCard' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' }
]

// 表单数据
const queryForm = reactive({
  customerName: '',
  idCard: '',
  phone: '',
  sources: [],
  purpose: 'loan'
})

const batchQueryForm = reactive({
  sources: []
})

const batchFileList = ref([])
const batchPreview = ref([])

// 计算属性
const filteredQueries = computed(() => {
  let filtered = queryRecords.value
  if (selectedSource.value !== 'all') {
    filtered = filtered.filter(item => item.source === selectedSource.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getSourceIcon = (source) => {
  const icons = {
    pboc: 'BankOutlined',
    sesame: 'CrownOutlined',
    tencent: 'GlobalOutlined',
    baiqian: 'SafetyCertificateOutlined'
  }
  return icons[source] || 'FileSearchOutlined'
}

const getSourceColor = (source) => {
  const colors = {
    pboc: 'red',
    sesame: 'orange',
    tencent: 'blue',
    baiqian: 'green'
  }
  return colors[source] || 'default'
}

const getSourceText = (source) => {
  const texts = {
    pboc: '人民银行',
    sesame: '芝麻信用',
    tencent: '腾讯征信',
    baiqian: '百行征信'
  }
  return texts[source] || source
}

const getStatusBadge = (status) => {
  const badges = {
    success: 'success',
    failed: 'error',
    pending: 'processing'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    pending: '处理中'
  }
  return texts[status] || status
}

const getScoreColor = (score) => {
  if (score >= 700) return '#52c41a'
  if (score >= 600) return '#faad14'
  return '#ff4d4f'
}

const getAlertClass = (level) => {
  return `alert-${level}`
}

const getAlertLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || level
}

const refreshCreditData = () => {
  console.log('刷新征信数据')
  message.success('征信数据已刷新')
}

const filterQueries = () => {
  console.log('筛选查询记录:', selectedSource.value, selectedStatus.value, selectedDate.value)
}

const viewCreditReport = (record) => {
  console.log('查看征信报告:', record)
}

const downloadReport = (record) => {
  console.log('下载报告:', record)
  message.success('报告下载中...')
}

const shareReport = (record) => {
  console.log('分享报告:', record)
}

const printReport = (record) => {
  console.log('打印报告:', record)
}

const deleteQuery = (record) => {
  const index = queryRecords.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    queryRecords.value.splice(index, 1)
    message.success('查询记录删除成功')
  }
}

const submitCreditQuery = () => {
  console.log('提交征信查询:', queryForm)
  const newQuery = {
    id: Date.now(),
    ...queryForm,
    status: 'pending',
    cost: 0,
    score: 0,
    queryTime: new Date().toLocaleString()
  }
  queryRecords.value.unshift(newQuery)
  message.success('征信查询已提交')
  showQueryModal.value = false
  
  // 重置表单
  Object.keys(queryForm).forEach(key => {
    if (key === 'sources') {
      queryForm[key] = []
    } else if (key === 'purpose') {
      queryForm[key] = 'loan'
    } else {
      queryForm[key] = ''
    }
  })
}

const beforeUpload = (file) => {
  console.log('上传文件:', file)
  // 模拟文件解析
  batchPreview.value = [
    { name: '张三', idCard: '110101199001011234', phone: '***********' },
    { name: '李四', idCard: '110101199002022345', phone: '13800138001' },
    { name: '王五', idCard: '110101199003033456', phone: '13800138002' }
  ]
  return false // 阻止自动上传
}

const removeFile = () => {
  batchPreview.value = []
}

const submitBatchQuery = () => {
  console.log('提交批量查询:', batchQueryForm, batchPreview.value)
  message.success('批量查询已提交')
  showBatchQueryModal.value = false
  batchFileList.value = []
  batchPreview.value = []
}

const markAlertRead = (alert) => {
  alert.read = true
}

const handleRiskAlert = (alert) => {
  console.log('处理风险预警:', alert)
  message.info('正在处理风险预警...')
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 查询趋势图
    if (queryTrendChart.value) {
      const chart1 = echarts.init(queryTrendChart.value)
      chart1.setOption({
        title: { text: '查询趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '查询次数',
          type: 'line',
          data: [8, 12, 25, 45, 38, 32, 18],
          smooth: true,
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }

    // 机构分布图
    if (sourceDistributionChart.value) {
      const chart2 = echarts.init(sourceDistributionChart.value)
      chart2.setOption({
        title: { text: '机构分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 45, name: '人民银行' },
            { value: 62, name: '芝麻信用' },
            { value: 23, name: '腾讯征信' },
            { value: 26, name: '百行征信' }
          ],
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          }
        }]
      })
    }

    // 费用趋势图
    if (costTrendChart.value) {
      const chart3 = echarts.init(costTrendChart.value)
      chart3.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          show: false
        },
        yAxis: { type: 'value', show: false },
        series: [{
          type: 'line',
          data: [280, 320, 450, 380, 520, 380, 420],
          smooth: true,
          symbol: 'none',
          itemStyle: { color: '#722ed1' },
          areaStyle: { opacity: 0.2 }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.credit-interface {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.credit-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.credit-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.credit-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.credit-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.customer-info {
  display: flex;
  flex-direction: column;
}

.customer-name {
  font-weight: 500;
  color: #262626;
}

.customer-id {
  font-size: 12px;
  color: #999;
}

.cost-text {
  color: #722ed1;
  font-weight: 500;
}

.score-display {
  width: 100px;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.mini-chart {
  height: 100px;
  width: 100%;
}

.interface-status {
  max-height: 300px;
  overflow-y: auto;
}

.status-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.interface-name {
  font-weight: 500;
  color: #262626;
  display: flex;
  align-items: center;
}

.status-metrics {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.metric-value.cost {
  color: #722ed1;
}

.cost-statistics {
  padding: 8px 0;
}

.cost-overview {
  margin-bottom: 16px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cost-item:last-child {
  border-bottom: none;
}

.cost-label {
  font-size: 13px;
  color: #666;
}

.cost-value {
  font-weight: 500;
}

.cost-value.total {
  color: #1890ff;
  font-size: 16px;
}

.cost-value.remaining {
  color: #52c41a;
}

.risk-alerts {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-left: 3px solid transparent;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item.unread {
  background: #f6f8ff;
  border-left-color: #1890ff;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.alert-type {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.alert-high {
  background: #fff2f0;
  color: #ff4d4f;
}

.alert-medium {
  background: #fff7e6;
  color: #faad14;
}

.alert-low {
  background: #f6f8ff;
  color: #1890ff;
}

.alert-time {
  font-size: 11px;
  color: #999;
}

.alert-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.batch-query-content {
  max-height: 500px;
  overflow-y: auto;
}

.batch-preview {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.batch-preview h4 {
  margin-bottom: 12px;
  color: #262626;
}
</style>