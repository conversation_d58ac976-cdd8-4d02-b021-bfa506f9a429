<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>数据管理</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="操作类型">
                <a-select v-model:value="searchForm.operationType" placeholder="请选择操作类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="backup">数据备份</a-select-option>
                  <a-select-option value="restore">数据恢复</a-select-option>
                  <a-select-option value="sync">数据同步</a-select-option>
                  <a-select-option value="clean">数据清理</a-select-option>
                  <a-select-option value="import">数据导入</a-select-option>
                  <a-select-option value="export">数据导出</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="数据表">
                <a-select v-model:value="searchForm.dataTable" placeholder="请选择数据表" allow-clear>
                  <a-select-option value="all">全部表</a-select-option>
                  <a-select-option value="cases">案件数据</a-select-option>
                  <a-select-option value="customers">客户数据</a-select-option>
                  <a-select-option value="collection_records">催收记录</a-select-option>
                  <a-select-option value="payment_records">还款记录</a-select-option>
                  <a-select-option value="users">用户数据</a-select-option>
                  <a-select-option value="system_logs">系统日志</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="执行状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="running">执行中</a-select-option>
                  <a-select-option value="success">成功</a-select-option>
                  <a-select-option value="failed">失败</a-select-option>
                  <a-select-option value="pending">等待中</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="执行人">
                  <a-input v-model:value="searchForm.executor" placeholder="请输入执行人" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="执行时间">
                  <a-range-picker 
                    v-model:value="searchForm.executeTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="数据大小">
                  <a-select v-model:value="searchForm.dataSize" placeholder="请选择数据大小" allow-clear>
                    <a-select-option value="small">小于100MB</a-select-option>
                    <a-select-option value="medium">100MB-1GB</a-select-option>
                    <a-select-option value="large">1GB-10GB</a-select-option>
                    <a-select-option value="huge">大于10GB</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="备注关键词">
                  <a-input v-model:value="searchForm.remark" placeholder="请输入关键词" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总数据量" 
              :value="statistics.totalDataSize" 
              :value-style="{ color: '#1890ff' }"
              suffix="GB"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="备份文件" 
              :value="statistics.backupFiles" 
              :value-style="{ color: '#52c41a' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="今日操作" 
              :value="statistics.todayOperations" 
              :value-style="{ color: '#faad14' }"
              suffix="次"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="存储空间" 
              :value="statistics.storageUsage" 
              :precision="1"
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space wrap>
            <a-button type="primary" @click="showBackupModal = true">
              <CloudDownloadOutlined />
              创建备份
            </a-button>
            <a-button @click="showRestoreModal = true">
              <CloudUploadOutlined />
              恢复数据
            </a-button>
            <a-button @click="showSyncModal = true">
              <SyncOutlined />
              数据同步
            </a-button>
            <a-button @click="showCleanModal = true">
              <DeleteOutlined />
              数据清理
            </a-button>
            <a-button @click="showImportModal = true">
              <ImportOutlined />
              数据导入
            </a-button>
            <a-button @click="exportData">
              <ExportOutlined />
              数据导出
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 数据管理操作标签页 -->
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="backup" tab="数据备份">
            <DataOperationTable 
              :operations="backupOperations" 
              operation-type="backup"
              @view="viewOperation"
              @download="downloadBackup"
              @delete="deleteOperation"
              @restore="restoreFromBackup"
            />
          </a-tab-pane>
          <a-tab-pane key="sync" tab="数据同步">
            <DataOperationTable 
              :operations="syncOperations" 
              operation-type="sync"
              @view="viewOperation"
              @retry="retryOperation"
              @stop="stopOperation"
            />
          </a-tab-pane>
          <a-tab-pane key="clean" tab="数据清理">
            <DataOperationTable 
              :operations="cleanOperations" 
              operation-type="clean"
              @view="viewOperation"
              @undo="undoClean"
            />
          </a-tab-pane>
          <a-tab-pane key="import" tab="数据导入">
            <DataOperationTable 
              :operations="importOperations" 
              operation-type="import"
              @view="viewOperation"
              @validate="validateImport"
              @rollback="rollbackImport"
            />
          </a-tab-pane>
          <a-tab-pane key="monitor" tab="实时监控">
            <DataMonitor />
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <!-- 创建备份弹窗 -->
      <a-modal
        v-model:open="showBackupModal"
        title="创建数据备份"
        width="700px"
        @ok="handleBackupSubmit"
        @cancel="showBackupModal = false"
      >
        <a-form ref="backupFormRef" :model="backupForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="备份名称" name="name" :rules="[{ required: true, message: '请输入备份名称' }]">
            <a-input v-model:value="backupForm.name" placeholder="请输入备份名称" />
          </a-form-item>
          
          <a-form-item label="备份类型" name="type" :rules="[{ required: true, message: '请选择备份类型' }]">
            <a-radio-group v-model:value="backupForm.type">
              <a-radio value="full">完整备份</a-radio>
              <a-radio value="incremental">增量备份</a-radio>
              <a-radio value="differential">差异备份</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="备份范围" name="scope" :rules="[{ required: true, message: '请选择备份范围' }]">
            <a-checkbox-group v-model:value="backupForm.scope">
              <a-checkbox value="cases">案件数据</a-checkbox>
              <a-checkbox value="customers">客户数据</a-checkbox>
              <a-checkbox value="collection_records">催收记录</a-checkbox>
              <a-checkbox value="payment_records">还款记录</a-checkbox>
              <a-checkbox value="users">用户数据</a-checkbox>
              <a-checkbox value="system_config">系统配置</a-checkbox>
              <a-checkbox value="logs">系统日志</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="压缩级别" name="compression">
            <a-select v-model:value="backupForm.compression" placeholder="请选择压缩级别">
              <a-select-option value="none">无压缩</a-select-option>
              <a-select-option value="low">低压缩</a-select-option>
              <a-select-option value="medium">中等压缩</a-select-option>
              <a-select-option value="high">高压缩</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="备份周期" name="schedule">
            <a-select v-model:value="backupForm.schedule" placeholder="请选择备份周期">
              <a-select-option value="once">仅一次</a-select-option>
              <a-select-option value="daily">每日备份</a-select-option>
              <a-select-option value="weekly">每周备份</a-select-option>
              <a-select-option value="monthly">每月备份</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="保留天数" name="retention">
            <a-input-number v-model:value="backupForm.retention" :min="1" :max="365" style="width: 100%" />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">备份文件保留天数，超期自动删除</div>
          </a-form-item>
          
          <a-form-item label="加密备份" name="encrypt">
            <a-switch v-model:checked="backupForm.encrypt" />
            <span style="margin-left: 8px; color: #666;">启用后备份文件将加密存储</span>
          </a-form-item>
          
          <a-form-item label="备份描述" name="description">
            <a-textarea v-model:value="backupForm.description" :rows="3" placeholder="请输入备份描述" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 数据恢复弹窗 -->
      <a-modal
        v-model:open="showRestoreModal"
        title="数据恢复"
        width="700px"
        @ok="handleRestoreSubmit"
        @cancel="showRestoreModal = false"
      >
        <a-form ref="restoreFormRef" :model="restoreForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选择备份" name="backupId" :rules="[{ required: true, message: '请选择备份文件' }]">
            <a-select v-model:value="restoreForm.backupId" placeholder="请选择备份文件">
              <a-select-option v-for="backup in availableBackups" :key="backup.id" :value="backup.id">
                {{ backup.name }} ({{ backup.createTime }})
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="恢复模式" name="mode" :rules="[{ required: true, message: '请选择恢复模式' }]">
            <a-radio-group v-model:value="restoreForm.mode">
              <a-radio value="full">完整恢复</a-radio>
              <a-radio value="selective">选择性恢复</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item v-if="restoreForm.mode === 'selective'" label="恢复范围" name="scope">
            <a-checkbox-group v-model:value="restoreForm.scope">
              <a-checkbox value="cases">案件数据</a-checkbox>
              <a-checkbox value="customers">客户数据</a-checkbox>
              <a-checkbox value="collection_records">催收记录</a-checkbox>
              <a-checkbox value="payment_records">还款记录</a-checkbox>
              <a-checkbox value="users">用户数据</a-checkbox>
              <a-checkbox value="system_config">系统配置</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="冲突处理" name="conflictStrategy">
            <a-select v-model:value="restoreForm.conflictStrategy" placeholder="请选择冲突处理策略">
              <a-select-option value="overwrite">覆盖现有数据</a-select-option>
              <a-select-option value="skip">跳过冲突数据</a-select-option>
              <a-select-option value="merge">合并数据</a-select-option>
              <a-select-option value="rename">重命名处理</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="数据验证" name="validate">
            <a-switch v-model:checked="restoreForm.validate" />
            <span style="margin-left: 8px; color: #666;">恢复后验证数据完整性</span>
          </a-form-item>
          
          <a-form-item label="创建快照" name="snapshot">
            <a-switch v-model:checked="restoreForm.snapshot" />
            <span style="margin-left: 8px; color: #666;">恢复前创建当前数据快照</span>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 数据同步弹窗 -->
      <a-modal
        v-model:open="showSyncModal"
        title="数据同步"
        width="700px"
        @ok="handleSyncSubmit"
        @cancel="showSyncModal = false"
      >
        <a-form ref="syncFormRef" :model="syncForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="同步方向" name="direction" :rules="[{ required: true, message: '请选择同步方向' }]">
            <a-radio-group v-model:value="syncForm.direction">
              <a-radio value="upload">上传到远程</a-radio>
              <a-radio value="download">从远程下载</a-radio>
              <a-radio value="bidirectional">双向同步</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="远程服务器" name="remoteServer" :rules="[{ required: true, message: '请选择远程服务器' }]">
            <a-select v-model:value="syncForm.remoteServer" placeholder="请选择远程服务器">
              <a-select-option value="backup_server">备份服务器</a-select-option>
              <a-select-option value="disaster_recovery">容灾服务器</a-select-option>
              <a-select-option value="data_warehouse">数据仓库</a-select-option>
              <a-select-option value="analytics_platform">分析平台</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="同步数据" name="dataTables" :rules="[{ required: true, message: '请选择同步数据' }]">
            <a-checkbox-group v-model:value="syncForm.dataTables">
              <a-checkbox value="cases">案件数据</a-checkbox>
              <a-checkbox value="customers">客户数据</a-checkbox>
              <a-checkbox value="collection_records">催收记录</a-checkbox>
              <a-checkbox value="payment_records">还款记录</a-checkbox>
              <a-checkbox value="reports">报表数据</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="同步模式" name="mode">
            <a-select v-model:value="syncForm.mode" placeholder="请选择同步模式">
              <a-select-option value="full">全量同步</a-select-option>
              <a-select-option value="incremental">增量同步</a-select-option>
              <a-select-option value="timestamp">时间戳同步</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="数据压缩" name="compress">
            <a-switch v-model:checked="syncForm.compress" />
            <span style="margin-left: 8px; color: #666;">压缩传输数据以节省带宽</span>
          </a-form-item>
          
          <a-form-item label="断点续传" name="resume">
            <a-switch v-model:checked="syncForm.resume" />
            <span style="margin-left: 8px; color: #666;">支持网络中断后继续传输</span>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 数据清理弹窗 -->
      <a-modal
        v-model:open="showCleanModal"
        title="数据清理"
        width="700px"
        @ok="handleCleanSubmit"
        @cancel="showCleanModal = false"
      >
        <a-alert 
          message="数据清理操作不可逆，请谨慎操作" 
          type="warning" 
          show-icon 
          style="margin-bottom: 16px;"
        />
        
        <a-form ref="cleanFormRef" :model="cleanForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="清理类型" name="type" :rules="[{ required: true, message: '请选择清理类型' }]">
            <a-checkbox-group v-model:value="cleanForm.type">
              <a-checkbox value="logs">系统日志</a-checkbox>
              <a-checkbox value="temp_files">临时文件</a-checkbox>
              <a-checkbox value="cache">缓存数据</a-checkbox>
              <a-checkbox value="expired_data">过期数据</a-checkbox>
              <a-checkbox value="duplicate_data">重复数据</a-checkbox>
              <a-checkbox value="archive_data">归档数据</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="时间范围" name="timeRange">
            <a-select v-model:value="cleanForm.timeRange" placeholder="请选择时间范围">
              <a-select-option value="7">7天前</a-select-option>
              <a-select-option value="30">30天前</a-select-option>
              <a-select-option value="90">90天前</a-select-option>
              <a-select-option value="180">180天前</a-select-option>
              <a-select-option value="365">1年前</a-select-option>
              <a-select-option value="custom">自定义</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item v-if="cleanForm.timeRange === 'custom'" label="自定义时间" name="customTimeRange">
            <a-range-picker v-model:value="cleanForm.customTimeRange" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="预估清理" name="preview">
            <a-button @click="previewClean" :loading="previewLoading">
              <EyeOutlined />
              预览清理内容
            </a-button>
            <div v-if="cleanPreview" style="margin-top: 8px;">
              <a-descriptions size="small" bordered>
                <a-descriptions-item label="清理文件数">{{ cleanPreview.fileCount }}</a-descriptions-item>
                <a-descriptions-item label="释放空间">{{ cleanPreview.spaceSize }}</a-descriptions-item>
                <a-descriptions-item label="预计耗时">{{ cleanPreview.estimatedTime }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </a-form-item>
          
          <a-form-item label="创建备份" name="backup">
            <a-switch v-model:checked="cleanForm.backup" />
            <span style="margin-left: 8px; color: #666;">清理前创建数据备份</span>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 数据导入弹窗 -->
      <a-modal
        v-model:open="showImportModal"
        title="数据导入"
        width="700px"
        @ok="handleImportSubmit"
        @cancel="showImportModal = false"
      >
        <a-form ref="importFormRef" :model="importForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="导入类型" name="type" :rules="[{ required: true, message: '请选择导入类型' }]">
            <a-select v-model:value="importForm.type" placeholder="请选择导入类型">
              <a-select-option value="excel">Excel文件</a-select-option>
              <a-select-option value="csv">CSV文件</a-select-option>
              <a-select-option value="json">JSON文件</a-select-option>
              <a-select-option value="sql">SQL脚本</a-select-option>
              <a-select-option value="database">数据库备份</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="目标表" name="targetTable" :rules="[{ required: true, message: '请选择目标表' }]">
            <a-select v-model:value="importForm.targetTable" placeholder="请选择目标表">
              <a-select-option value="cases">案件数据</a-select-option>
              <a-select-option value="customers">客户数据</a-select-option>
              <a-select-option value="collection_records">催收记录</a-select-option>
              <a-select-option value="payment_records">还款记录</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="上传文件" name="file">
            <a-upload
              v-model:file-list="importForm.fileList"
              :before-upload="beforeUpload"
              :on-remove="onRemove"
              accept=".xlsx,.xls,.csv,.json,.sql"
            >
              <a-button>
                <UploadOutlined />
                选择文件
              </a-button>
            </a-upload>
          </a-form-item>
          
          <a-form-item label="导入模式" name="mode">
            <a-radio-group v-model:value="importForm.mode">
              <a-radio value="insert">插入模式</a-radio>
              <a-radio value="update">更新模式</a-radio>
              <a-radio value="upsert">插入或更新</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="字段映射" name="fieldMapping">
            <a-button @click="showMappingModal = true">
              <SettingOutlined />
              配置字段映射
            </a-button>
          </a-form-item>
          
          <a-form-item label="数据验证" name="validation">
            <a-checkbox-group v-model:value="importForm.validation">
              <a-checkbox value="format">格式验证</a-checkbox>
              <a-checkbox value="uniqueness">唯一性验证</a-checkbox>
              <a-checkbox value="reference">引用完整性验证</a-checkbox>
              <a-checkbox value="business">业务规则验证</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="错误处理" name="errorHandling">
            <a-select v-model:value="importForm.errorHandling" placeholder="请选择错误处理策略">
              <a-select-option value="stop">遇错停止</a-select-option>
              <a-select-option value="skip">跳过错误</a-select-option>
              <a-select-option value="log">记录错误继续</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  CloudDownloadOutlined,
  CloudUploadOutlined,
  SyncOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  EyeOutlined,
  UploadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 数据操作表格组件
const DataOperationTable = {
  props: ['operations', 'operationType'],
  emits: ['view', 'download', 'delete', 'restore', 'retry', 'stop', 'undo', 'validate', 'rollback'],
  template: `
    <a-table 
      :columns="columns" 
      :data-source="operations" 
      :pagination="pagination"
      :loading="false"
      :scroll="{ x: 1200 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'progress'">
          <a-progress 
            :percent="record.progress" 
            size="small" 
            :status="getProgressStatus(record.status)"
          />
        </template>
        <template v-if="column.key === 'dataSize'">
          {{ formatDataSize(record.dataSize) }}
        </template>
        <template v-if="column.key === 'duration'">
          {{ formatDuration(record.duration) }}
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="$emit('view', record)">详情</a-button>
            <a-button v-if="operationType === 'backup'" type="link" size="small" @click="$emit('download', record)">下载</a-button>
            <a-button v-if="operationType === 'backup'" type="link" size="small" @click="$emit('restore', record)">恢复</a-button>
            <a-button v-if="record.status === 'failed'" type="link" size="small" @click="$emit('retry', record)">重试</a-button>
            <a-button v-if="record.status === 'running'" type="link" size="small" @click="$emit('stop', record)">停止</a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="operationType === 'clean'" @click="$emit('undo', record)">撤销清理</a-menu-item>
                  <a-menu-item v-if="operationType === 'import'" @click="$emit('validate', record)">验证数据</a-menu-item>
                  <a-menu-item v-if="operationType === 'import'" @click="$emit('rollback', record)">回滚导入</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="$emit('delete', record)" class="text-danger">删除</a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>
  `,
  setup() {
    const columns = [
      {
        title: '操作名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        fixed: 'left'
      },
      {
        title: '数据表',
        dataIndex: 'dataTable',
        key: 'dataTable',
        width: 120
      },
      {
        title: '数据大小',
        key: 'dataSize',
        width: 100
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '进度',
        key: 'progress',
        width: 120
      },
      {
        title: '耗时',
        key: 'duration',
        width: 100
      },
      {
        title: '执行人',
        dataIndex: 'executor',
        key: 'executor',
        width: 100
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        width: 150
      },
      {
        title: '完成时间',
        dataIndex: 'endTime',
        key: 'endTime',
        width: 150
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ]

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true
    })

    const getStatusColor = (status) => {
      const colors = {
        running: 'blue',
        success: 'green',
        failed: 'red',
        pending: 'orange'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        running: '执行中',
        success: '成功',
        failed: '失败',
        pending: '等待中'
      }
      return texts[status] || '未知'
    }

    const getProgressStatus = (status) => {
      if (status === 'failed') return 'exception'
      if (status === 'success') return 'success'
      if (status === 'running') return 'active'
      return 'normal'
    }

    const formatDataSize = (size) => {
      if (size >= 1024) {
        return (size / 1024).toFixed(1) + 'GB'
      }
      return size.toFixed(1) + 'MB'
    }

    const formatDuration = (duration) => {
      if (duration >= 3600) {
        return Math.floor(duration / 3600) + 'h' + Math.floor((duration % 3600) / 60) + 'm'
      } else if (duration >= 60) {
        return Math.floor(duration / 60) + 'm' + (duration % 60) + 's'
      }
      return duration + 's'
    }

    return {
      columns,
      pagination,
      getStatusColor,
      getStatusText,
      getProgressStatus,
      formatDataSize,
      formatDuration
    }
  }
}

// 数据监控组件
const DataMonitor = {
  template: `
    <div class="data-monitor">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="数据库性能" size="small">
            <div class="monitor-item">
              <span class="monitor-label">CPU使用率:</span>
              <a-progress :percent="85" size="small" status="active" />
            </div>
            <div class="monitor-item">
              <span class="monitor-label">内存使用率:</span>
              <a-progress :percent="72" size="small" status="normal" />
            </div>
            <div class="monitor-item">
              <span class="monitor-label">磁盘I/O:</span>
              <a-progress :percent="45" size="small" status="normal" />
            </div>
            <div class="monitor-item">
              <span class="monitor-label">连接数:</span>
              <span class="monitor-value">156/500</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="存储空间" size="small">
            <div class="monitor-item">
              <span class="monitor-label">数据文件:</span>
              <a-progress :percent="68" size="small" />
              <span class="monitor-value">68GB/100GB</span>
            </div>
            <div class="monitor-item">
              <span class="monitor-label">日志文件:</span>
              <a-progress :percent="45" size="small" />
              <span class="monitor-value">9GB/20GB</span>
            </div>
            <div class="monitor-item">
              <span class="monitor-label">备份文件:</span>
              <a-progress :percent="82" size="small" />
              <span class="monitor-value">41GB/50GB</span>
            </div>
            <div class="monitor-item">
              <span class="monitor-label">临时文件:</span>
              <a-progress :percent="25" size="small" />
              <span class="monitor-value">2.5GB/10GB</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card title="实时操作日志" size="small">
            <a-list size="small" :data-source="realtimeLogs">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-tag :color="getLogTypeColor(item.type)">{{ item.type }}</a-tag>
                    </template>
                    <template #title>{{ item.message }}</template>
                    <template #description>{{ item.time }} - 执行人: {{ item.user }}</template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  `,
  setup() {
    const realtimeLogs = ref([
      {
        id: 1,
        type: '备份',
        message: '客户数据备份已完成',
        time: '2024-01-27 15:30:25',
        user: '系统管理员'
      },
      {
        id: 2,
        type: '同步',
        message: '案件数据同步到备份服务器',
        time: '2024-01-27 15:25:12',
        user: '数据管理员'
      },
      {
        id: 3,
        type: '清理',
        message: '清理90天前的系统日志',
        time: '2024-01-27 15:20:08',
        user: '系统管理员'
      },
      {
        id: 4,
        type: '导入',
        message: '导入新增客户数据500条',
        time: '2024-01-27 15:15:33',
        user: '业务管理员'
      }
    ])

    const getLogTypeColor = (type) => {
      const colors = {
        '备份': 'blue',
        '恢复': 'green',
        '同步': 'purple',
        '清理': 'orange',
        '导入': 'cyan',
        '导出': 'magenta'
      }
      return colors[type] || 'default'
    }

    return {
      realtimeLogs,
      getLogTypeColor
    }
  }
}

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showBackupModal = ref(false)
const showRestoreModal = ref(false)
const showSyncModal = ref(false)
const showCleanModal = ref(false)
const showImportModal = ref(false)
const showMappingModal = ref(false)
const previewLoading = ref(false)
const activeTab = ref('backup')

// 搜索表单
const searchForm = reactive({
  operationType: undefined,
  dataTable: undefined,
  status: undefined,
  executor: '',
  executeTimeRange: [],
  dataSize: undefined,
  remark: ''
})

// 备份表单
const backupForm = reactive({
  name: '',
  type: 'full',
  scope: ['cases', 'customers'],
  compression: 'medium',
  schedule: 'once',
  retention: 30,
  encrypt: false,
  description: ''
})

// 恢复表单
const restoreForm = reactive({
  backupId: undefined,
  mode: 'full',
  scope: [],
  conflictStrategy: 'overwrite',
  validate: true,
  snapshot: true
})

// 同步表单
const syncForm = reactive({
  direction: 'upload',
  remoteServer: undefined,
  dataTables: ['cases', 'customers'],
  mode: 'incremental',
  compress: true,
  resume: true
})

// 清理表单
const cleanForm = reactive({
  type: ['logs', 'temp_files'],
  timeRange: '30',
  customTimeRange: [],
  backup: true
})

// 导入表单
const importForm = reactive({
  type: undefined,
  targetTable: undefined,
  fileList: [],
  mode: 'insert',
  validation: ['format', 'uniqueness'],
  errorHandling: 'log'
})

// 统计数据
const statistics = reactive({
  totalDataSize: 125.6,
  backupFiles: 28,
  todayOperations: 15,
  storageUsage: 68.5
})

// 清理预览数据
const cleanPreview = ref(null)

// 可用备份列表
const availableBackups = ref([
  {
    id: 1,
    name: '完整备份_20240127',
    createTime: '2024-01-27 02:00:00'
  },
  {
    id: 2,
    name: '增量备份_20240126',
    createTime: '2024-01-26 02:00:00'
  },
  {
    id: 3,
    name: '系统配置备份_20240125',
    createTime: '2024-01-25 14:30:00'
  }
])

// 操作数据
const allOperations = ref([
  {
    id: 1,
    name: '每日完整备份',
    type: 'backup',
    dataTable: '全部表',
    dataSize: 2048,
    status: 'success',
    progress: 100,
    duration: 3600,
    executor: '系统',
    startTime: '2024-01-27 02:00:00',
    endTime: '2024-01-27 03:00:00'
  },
  {
    id: 2,
    name: '客户数据同步',
    type: 'sync',
    dataTable: 'customers',
    dataSize: 256,
    status: 'running',
    progress: 65,
    duration: 1200,
    executor: '数据管理员',
    startTime: '2024-01-27 15:00:00',
    endTime: null
  },
  {
    id: 3,
    name: '系统日志清理',
    type: 'clean',
    dataTable: 'system_logs',
    dataSize: 512,
    status: 'success',
    progress: 100,
    duration: 600,
    executor: '系统管理员',
    startTime: '2024-01-27 01:00:00',
    endTime: '2024-01-27 01:10:00'
  }
])

// 计算属性 - 按类型过滤操作
const backupOperations = computed(() => 
  allOperations.value.filter(op => op.type === 'backup')
)

const syncOperations = computed(() => 
  allOperations.value.filter(op => op.type === 'sync')
)

const cleanOperations = computed(() => 
  allOperations.value.filter(op => op.type === 'clean')
)

const importOperations = computed(() => 
  allOperations.value.filter(op => op.type === 'import')
)

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('数据已更新')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = key === 'operationType' || key === 'dataTable' || key === 'status' || key === 'dataSize' ? undefined : ''
    }
  })
  handleSearch()
}

const handleTabChange = (key) => {
  activeTab.value = key
  console.log('切换到操作类型:', key)
}

const handleBackupSubmit = () => {
  console.log('备份配置:', backupForm)
  message.success('备份任务已创建')
  showBackupModal.value = false
}

const handleRestoreSubmit = () => {
  console.log('恢复配置:', restoreForm)
  message.success('数据恢复任务已启动')
  showRestoreModal.value = false
}

const handleSyncSubmit = () => {
  console.log('同步配置:', syncForm)
  message.success('数据同步任务已启动')
  showSyncModal.value = false
}

const handleCleanSubmit = () => {
  console.log('清理配置:', cleanForm)
  message.success('数据清理任务已启动')
  showCleanModal.value = false
}

const handleImportSubmit = () => {
  console.log('导入配置:', importForm)
  message.success('数据导入任务已启动')
  showImportModal.value = false
}

const previewClean = () => {
  previewLoading.value = true
  setTimeout(() => {
    cleanPreview.value = {
      fileCount: '2,456个文件',
      spaceSize: '1.2GB',
      estimatedTime: '约15分钟'
    }
    previewLoading.value = false
    message.success('预览完成')
  }, 2000)
}

const beforeUpload = (file) => {
  const isValidType = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                       'application/vnd.ms-excel', 
                       'text/csv', 
                       'application/json',
                       'text/plain'].includes(file.type)
  if (!isValidType) {
    message.error('只能上传 Excel、CSV、JSON 或 SQL 文件')
  }
  return false
}

const onRemove = (file) => {
  const index = importForm.fileList.indexOf(file)
  const newFileList = importForm.fileList.slice()
  newFileList.splice(index, 1)
  importForm.fileList = newFileList
}

const exportData = () => {
  message.success('数据导出任务已启动')
}

const viewOperation = (record) => {
  message.info(`查看操作 ${record.name} 的详细信息`)
}

const downloadBackup = (record) => {
  message.success(`备份文件 ${record.name} 下载已启动`)
}

const deleteOperation = (record) => {
  message.success(`操作记录 ${record.name} 已删除`)
}

const restoreFromBackup = (record) => {
  message.success(`从备份 ${record.name} 恢复数据任务已启动`)
}

const retryOperation = (record) => {
  message.success(`操作 ${record.name} 重试已启动`)
}

const stopOperation = (record) => {
  message.success(`操作 ${record.name} 已停止`)
}

const undoClean = (record) => {
  message.success(`清理操作 ${record.name} 已撤销`)
}

const validateImport = (record) => {
  message.success(`导入数据 ${record.name} 验证已完成`)
}

const rollbackImport = (record) => {
  message.success(`导入操作 ${record.name} 已回滚`)
}

// 组件注册
const components = {
  DataOperationTable,
  DataMonitor
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.data-monitor {
  padding: 16px 0;
}

.monitor-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.monitor-label {
  width: 100px;
  font-weight: 500;
  color: #666;
}

.monitor-value {
  margin-left: 8px;
  color: #333;
  font-weight: 500;
}

.text-danger {
  color: #ff4d4f;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>