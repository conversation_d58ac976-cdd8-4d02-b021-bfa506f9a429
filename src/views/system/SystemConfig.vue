<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>参数配置</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="配置分类">
                <a-select v-model:value="searchForm.category" placeholder="请选择配置分类" allow-clear>
                  <a-select-option value="all">全部分类</a-select-option>
                  <a-select-option value="system">系统配置</a-select-option>
                  <a-select-option value="business">业务配置</a-select-option>
                  <a-select-option value="security">安全配置</a-select-option>
                  <a-select-option value="notification">通知配置</a-select-option>
                  <a-select-option value="interface">接口配置</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="配置名称">
                <a-input v-model:value="searchForm.configName" placeholder="请输入配置名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="enabled">启用</a-select-option>
                  <a-select-option value="disabled">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="配置键名">
                  <a-input v-model:value="searchForm.configKey" placeholder="请输入配置键名" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="更新时间">
                  <a-range-picker 
                    v-model:value="searchForm.updateTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="更新人">
                  <a-input v-model:value="searchForm.updater" placeholder="请输入更新人" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="是否敏感">
                  <a-select v-model:value="searchForm.sensitive" placeholder="请选择" allow-clear>
                    <a-select-option value="true">敏感配置</a-select-option>
                    <a-select-option value="false">普通配置</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总配置项" 
              :value="statistics.totalConfigs" 
              :value-style="{ color: '#1890ff' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="启用配置" 
              :value="statistics.enabledConfigs" 
              :value-style="{ color: '#52c41a' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="敏感配置" 
              :value="statistics.sensitiveConfigs" 
              :value-style="{ color: '#faad14' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="今日更新" 
              :value="statistics.todayUpdates" 
              :value-style="{ color: '#722ed1' }"
              suffix="次"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              新增配置
            </a-button>
            <a-button @click="batchImport">
              <UploadOutlined />
              批量导入
            </a-button>
            <a-button @click="exportConfigs">
              <DownloadOutlined />
              导出配置
            </a-button>
            <a-button @click="syncConfigs">
              <SyncOutlined />
              同步配置
            </a-button>
            <a-button @click="showBackupModal = true">
              <SaveOutlined />
              备份配置
            </a-button>
            <a-button @click="validateConfigs">
              <CheckCircleOutlined />
              配置校验
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 配置分类标签页 -->
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="system" tab="系统配置">
            <ConfigTable :configs="systemConfigs" @edit="editConfig" @delete="deleteConfig" @toggle="toggleStatus" />
          </a-tab-pane>
          <a-tab-pane key="business" tab="业务配置">
            <ConfigTable :configs="businessConfigs" @edit="editConfig" @delete="deleteConfig" @toggle="toggleStatus" />
          </a-tab-pane>
          <a-tab-pane key="security" tab="安全配置">
            <ConfigTable :configs="securityConfigs" @edit="editConfig" @delete="deleteConfig" @toggle="toggleStatus" />
          </a-tab-pane>
          <a-tab-pane key="notification" tab="通知配置">
            <ConfigTable :configs="notificationConfigs" @edit="editConfig" @delete="deleteConfig" @toggle="toggleStatus" />
          </a-tab-pane>
          <a-tab-pane key="interface" tab="接口配置">
            <ConfigTable :configs="interfaceConfigs" @edit="editConfig" @delete="deleteConfig" @toggle="toggleStatus" />
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <!-- 新增/编辑配置弹窗 -->
      <a-modal
        v-model:open="showCreateModal"
        :title="editingConfig ? '编辑配置' : '新增配置'"
        width="700px"
        @ok="handleConfigSubmit"
        @cancel="handleConfigCancel"
      >
        <a-form ref="configFormRef" :model="configForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="配置分类" name="category" :rules="[{ required: true, message: '请选择配置分类' }]">
            <a-select v-model:value="configForm.category" placeholder="请选择配置分类">
              <a-select-option value="system">系统配置</a-select-option>
              <a-select-option value="business">业务配置</a-select-option>
              <a-select-option value="security">安全配置</a-select-option>
              <a-select-option value="notification">通知配置</a-select-option>
              <a-select-option value="interface">接口配置</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="配置名称" name="name" :rules="[{ required: true, message: '请输入配置名称' }]">
            <a-input v-model:value="configForm.name" placeholder="请输入配置名称" />
          </a-form-item>
          
          <a-form-item label="配置键名" name="key" :rules="[{ required: true, message: '请输入配置键名' }]">
            <a-input v-model:value="configForm.key" placeholder="请输入配置键名" />
          </a-form-item>
          
          <a-form-item label="数据类型" name="dataType" :rules="[{ required: true, message: '请选择数据类型' }]">
            <a-select v-model:value="configForm.dataType" placeholder="请选择数据类型">
              <a-select-option value="string">字符串</a-select-option>
              <a-select-option value="number">数字</a-select-option>
              <a-select-option value="boolean">布尔值</a-select-option>
              <a-select-option value="json">JSON对象</a-select-option>
              <a-select-option value="array">数组</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="配置值" name="value" :rules="[{ required: true, message: '请输入配置值' }]">
            <a-textarea v-if="configForm.dataType === 'json' || configForm.dataType === 'array'" 
              v-model:value="configForm.value" 
              :rows="4" 
              placeholder="请输入配置值"
            />
            <a-input-number v-else-if="configForm.dataType === 'number'" 
              v-model:value="configForm.value" 
              style="width: 100%"
              placeholder="请输入数字"
            />
            <a-switch v-else-if="configForm.dataType === 'boolean'" 
              v-model:checked="configForm.value"
            />
            <a-input v-else 
              v-model:value="configForm.value" 
              placeholder="请输入配置值"
            />
          </a-form-item>
          
          <a-form-item label="默认值" name="defaultValue">
            <a-input v-model:value="configForm.defaultValue" placeholder="请输入默认值" />
          </a-form-item>
          
          <a-form-item label="配置描述" name="description">
            <a-textarea v-model:value="configForm.description" :rows="3" placeholder="请输入配置描述" />
          </a-form-item>
          
          <a-form-item label="是否敏感" name="sensitive">
            <a-radio-group v-model:value="configForm.sensitive">
              <a-radio :value="false">普通配置</a-radio>
              <a-radio :value="true">敏感配置</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="配置状态" name="status">
            <a-radio-group v-model:value="configForm.status">
              <a-radio value="enabled">启用</a-radio>
              <a-radio value="disabled">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="排序" name="sort">
            <a-input-number v-model:value="configForm.sort" :min="0" :max="999" style="width: 100%" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 备份配置弹窗 -->
      <a-modal
        v-model:open="showBackupModal"
        title="配置备份"
        width="600px"
        @ok="handleBackup"
        @cancel="showBackupModal = false"
      >
        <a-space direction="vertical" style="width: 100%;">
          <a-alert message="备份将包含所有系统配置信息，请妥善保管备份文件" type="info" show-icon />
          
          <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="备份名称">
              <a-input v-model:value="backupForm.name" placeholder="请输入备份名称" />
            </a-form-item>
            
            <a-form-item label="备份类型">
              <a-radio-group v-model:value="backupForm.type">
                <a-radio value="full">完整备份</a-radio>
                <a-radio value="incremental">增量备份</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item label="包含分类">
              <a-checkbox-group v-model:value="backupForm.categories">
                <a-checkbox value="system">系统配置</a-checkbox>
                <a-checkbox value="business">业务配置</a-checkbox>
                <a-checkbox value="security">安全配置</a-checkbox>
                <a-checkbox value="notification">通知配置</a-checkbox>
                <a-checkbox value="interface">接口配置</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="备份说明">
              <a-textarea v-model:value="backupForm.description" :rows="3" placeholder="请输入备份说明" />
            </a-form-item>
          </a-form>
          
          <a-divider />
          
          <h4>历史备份</h4>
          <a-list size="small" :data-source="backupHistory">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>{{ item.name }}</template>
                  <template #description>{{ item.createTime }} | {{ item.size }}</template>
                </a-list-item-meta>
                <a-space>
                  <a-button type="link" size="small" @click="downloadBackup(item)">下载</a-button>
                  <a-button type="link" size="small" @click="restoreBackup(item)">恢复</a-button>
                  <a-button type="link" size="small" danger @click="deleteBackup(item)">删除</a-button>
                </a-space>
              </a-list-item>
            </template>
          </a-list>
        </a-space>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, defineComponent } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  SyncOutlined,
  SaveOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 配置表格组件
const ConfigTable = defineComponent({
  name: 'ConfigTable',
  props: ['configs', 'edit', 'delete', 'toggle'],
  emits: ['edit', 'delete', 'toggle'],
  template: `
    <a-table 
      :columns="columns" 
      :data-source="configs" 
      :pagination="pagination"
      :loading="false"
      :scroll="{ x: 1200 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'sensitive'">
          <a-tag :color="record.sensitive ? 'red' : 'green'">
            {{ record.sensitive ? '敏感' : '普通' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'value'">
          <span v-if="record.sensitive">******</span>
          <span v-else>{{ formatValue(record.value, record.dataType) }}</span>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="$emit('edit', record)">编辑</a-button>
            <a-button type="link" size="small" @click="viewHistory(record)">历史</a-button>
            <a-button type="link" size="small" @click="$emit('toggle', record)">
              {{ record.status === 'enabled' ? '禁用' : '启用' }}
            </a-button>
            <a-button type="link" size="small" danger @click="$emit('delete', record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  `,
  setup() {
    const columns = [
      {
        title: '配置名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        fixed: 'left'
      },
      {
        title: '配置键名',
        dataIndex: 'key',
        key: 'key',
        width: 180
      },
      {
        title: '配置值',
        key: 'value',
        width: 200
      },
      {
        title: '数据类型',
        dataIndex: 'dataType',
        key: 'dataType',
        width: 100
      },
      {
        title: '状态',
        key: 'status',
        width: 80
      },
      {
        title: '敏感性',
        key: 'sensitive',
        width: 80
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 200
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 150
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ]

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true
    })

    const getStatusColor = (status) => {
      return status === 'enabled' ? 'green' : 'red'
    }

    const getStatusText = (status) => {
      return status === 'enabled' ? '启用' : '禁用'
    }

    const formatValue = (value, dataType) => {
      if (dataType === 'boolean') {
        return value ? '是' : '否'
      }
      if (dataType === 'json' || dataType === 'array') {
        return typeof value === 'string' ? value : JSON.stringify(value)
      }
      return value
    }

    const viewHistory = (record) => {
      message.info(`查看配置 ${record.name} 的修改历史`)
    }

    return {
      columns,
      pagination,
      getStatusColor,
      getStatusText,
      formatValue,
      viewHistory
    }
  }
})

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showCreateModal = ref(false)
const showBackupModal = ref(false)
const editingConfig = ref(null)
const activeTab = ref('system')
const configFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  category: undefined,
  configName: '',
  status: undefined,
  configKey: '',
  updateTimeRange: [],
  updater: '',
  sensitive: undefined
})

// 配置表单
const configForm = reactive({
  category: undefined,
  name: '',
  key: '',
  dataType: 'string',
  value: '',
  defaultValue: '',
  description: '',
  sensitive: false,
  status: 'enabled',
  sort: 0
})

// 备份表单
const backupForm = reactive({
  name: '',
  type: 'full',
  categories: ['system', 'business', 'security', 'notification', 'interface'],
  description: ''
})

// 统计数据
const statistics = reactive({
  totalConfigs: 126,
  enabledConfigs: 118,
  sensitiveConfigs: 23,
  todayUpdates: 8
})

// 配置数据
const allConfigs = ref([
  {
    id: 1,
    category: 'system',
    name: '系统名称',
    key: 'system.name',
    value: '催收管理系统',
    dataType: 'string',
    defaultValue: 'Collection System',
    description: '系统显示名称',
    sensitive: false,
    status: 'enabled',
    sort: 1,
    updateTime: '2024-01-15 10:30',
    updater: '管理员'
  },
  {
    id: 2,
    category: 'system',
    name: '会话超时时间',
    key: 'system.session.timeout',
    value: 7200,
    dataType: 'number',
    defaultValue: '3600',
    description: '用户会话超时时间（秒）',
    sensitive: false,
    status: 'enabled',
    sort: 2,
    updateTime: '2024-01-15 09:00',
    updater: '管理员'
  },
  {
    id: 3,
    category: 'business',
    name: '催收提醒间隔',
    key: 'business.reminder.interval',
    value: 24,
    dataType: 'number',
    defaultValue: '24',
    description: '催收提醒间隔时间（小时）',
    sensitive: false,
    status: 'enabled',
    sort: 1,
    updateTime: '2024-01-14 16:20',
    updater: '业务主管'
  },
  {
    id: 4,
    category: 'security',
    name: '密码复杂度要求',
    key: 'security.password.complexity',
    value: true,
    dataType: 'boolean',
    defaultValue: 'false',
    description: '是否启用密码复杂度验证',
    sensitive: false,
    status: 'enabled',
    sort: 1,
    updateTime: '2024-01-15 08:00',
    updater: '安全管理员'
  },
  {
    id: 5,
    category: 'security',
    name: 'API密钥',
    key: 'security.api.secret',
    value: 'sk-1234567890abcdef',
    dataType: 'string',
    defaultValue: '',
    description: '系统API访问密钥',
    sensitive: true,
    status: 'enabled',
    sort: 10,
    updateTime: '2024-01-10 14:30',
    updater: '系统管理员'
  },
  {
    id: 6,
    category: 'notification',
    name: '短信通知开关',
    key: 'notification.sms.enabled',
    value: true,
    dataType: 'boolean',
    defaultValue: 'true',
    description: '是否启用短信通知功能',
    sensitive: false,
    status: 'enabled',
    sort: 1,
    updateTime: '2024-01-14 10:15',
    updater: '系统管理员'
  },
  {
    id: 7,
    category: 'notification',
    name: '邮件服务器地址',
    key: 'notification.email.smtp.host',
    value: 'smtp.company.com',
    dataType: 'string',
    defaultValue: '',
    description: 'SMTP邮件服务器地址',
    sensitive: false,
    status: 'enabled',
    sort: 2,
    updateTime: '2024-01-12 16:45',
    updater: '系统管理员'
  },
  {
    id: 8,
    category: 'notification',
    name: '通知发送频率限制',
    key: 'notification.rate.limit',
    value: 100,
    dataType: 'number',
    defaultValue: '50',
    description: '每小时最大通知发送数量',
    sensitive: false,
    status: 'enabled',
    sort: 3,
    updateTime: '2024-01-13 11:20',
    updater: '业务主管'
  },
  {
    id: 9,
    category: 'interface',
    name: '第三方支付接口地址',
    key: 'interface.payment.gateway.url',
    value: 'https://api.payment.com/gateway',
    dataType: 'string',
    defaultValue: '',
    description: '第三方支付网关接口地址',
    sensitive: false,
    status: 'enabled',
    sort: 1,
    updateTime: '2024-01-11 14:30',
    updater: '技术主管'
  },
  {
    id: 10,
    category: 'interface',
    name: '征信接口密钥',
    key: 'interface.credit.api.key',
    value: 'ck-9876543210fedcba',
    dataType: 'string',
    defaultValue: '',
    description: '征信查询接口访问密钥',
    sensitive: true,
    status: 'enabled',
    sort: 2,
    updateTime: '2024-01-09 09:15',
    updater: '系统管理员'
  },
  {
    id: 11,
    category: 'interface',
    name: '接口超时时间',
    key: 'interface.timeout',
    value: 30000,
    dataType: 'number',
    defaultValue: '15000',
    description: '第三方接口调用超时时间（毫秒）',
    sensitive: false,
    status: 'enabled',
    sort: 3,
    updateTime: '2024-01-14 15:30',
    updater: '技术主管'
  }
])

// 备份历史
const backupHistory = ref([
  {
    id: 1,
    name: '系统配置备份_20240115',
    type: 'full',
    size: '2.5MB',
    createTime: '2024-01-15 10:00',
    creator: '管理员'
  },
  {
    id: 2,
    name: '业务配置备份_20240110',
    type: 'incremental',
    size: '1.2MB',
    createTime: '2024-01-10 15:30',
    creator: '管理员'
  }
])

// 计算属性 - 按分类过滤配置
const systemConfigs = computed(() => 
  allConfigs.value.filter(config => config.category === 'system')
)

const businessConfigs = computed(() => 
  allConfigs.value.filter(config => config.category === 'business')
)

const securityConfigs = computed(() => 
  allConfigs.value.filter(config => config.category === 'security')
)

const notificationConfigs = computed(() => 
  allConfigs.value.filter(config => config.category === 'notification')
)

const interfaceConfigs = computed(() => 
  allConfigs.value.filter(config => config.category === 'interface')
)

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('配置数据已更新')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = key === 'category' || key === 'status' || key === 'sensitive' ? undefined : ''
    }
  })
  handleSearch()
}

const handleTabChange = (key) => {
  activeTab.value = key
  console.log('切换到分类:', key)
}

const handleConfigSubmit = async () => {
  try {
    await configFormRef.value.validate()
    console.log('配置信息:', configForm)
    message.success(editingConfig.value ? '配置更新成功' : '配置创建成功')
    showCreateModal.value = false
    handleConfigCancel()
  } catch (error) {
    message.error('请检查表单信息')
  }
}

const handleConfigCancel = () => {
  editingConfig.value = null
  Object.keys(configForm).forEach(key => {
    if (key === 'dataType') {
      configForm[key] = 'string'
    } else if (key === 'sensitive') {
      configForm[key] = false
    } else if (key === 'status') {
      configForm[key] = 'enabled'
    } else if (key === 'sort') {
      configForm[key] = 0
    } else {
      configForm[key] = ''
    }
  })
}

const editConfig = (record) => {
  editingConfig.value = record
  Object.keys(configForm).forEach(key => {
    if (record[key] !== undefined) {
      configForm[key] = record[key]
    }
  })
  showCreateModal.value = true
}

const deleteConfig = (record) => {
  if (record.sensitive) {
    message.error('敏感配置不允许删除，请联系系统管理员')
    return
  }
  message.success(`配置 ${record.name} 删除成功`)
}

const toggleStatus = (record) => {
  const newStatus = record.status === 'enabled' ? 'disabled' : 'enabled'
  message.success(`配置 ${record.name} 已${newStatus === 'enabled' ? '启用' : '禁用'}`)
}

const batchImport = () => {
  message.info('批量导入配置功能')
}

const exportConfigs = () => {
  message.success('配置数据导出成功')
}

const syncConfigs = () => {
  message.success('配置同步成功')
}

const validateConfigs = () => {
  message.success('配置校验完成，所有配置项格式正确')
}

const handleBackup = () => {
  console.log('备份配置:', backupForm)
  message.success('配置备份创建成功')
  showBackupModal.value = false
}

const downloadBackup = (backup) => {
  message.success(`备份文件 ${backup.name} 下载成功`)
}

const restoreBackup = (backup) => {
  message.success(`从备份 ${backup.name} 恢复配置成功`)
}

const deleteBackup = (backup) => {
  message.success(`备份 ${backup.name} 删除成功`)
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
