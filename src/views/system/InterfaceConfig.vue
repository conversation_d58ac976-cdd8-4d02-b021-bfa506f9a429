<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>接口配置</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="接口类型">
                <a-select v-model:value="searchForm.interfaceType" placeholder="请选择接口类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="rest">REST API</a-select-option>
                  <a-select-option value="graphql">GraphQL</a-select-option>
                  <a-select-option value="websocket">WebSocket</a-select-option>
                  <a-select-option value="rpc">RPC接口</a-select-option>
                  <a-select-option value="mq">消息队列</a-select-option>
                  <a-select-option value="file">文件传输</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="接口名称">
                <a-input v-model:value="searchForm.interfaceName" placeholder="请输入接口名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="enabled">启用</a-select-option>
                  <a-select-option value="disabled">禁用</a-select-option>
                  <a-select-option value="testing">测试中</a-select-option>
                  <a-select-option value="error">异常</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="服务分组">
                  <a-select v-model:value="searchForm.serviceGroup" placeholder="请选择服务分组" allow-clear>
                    <a-select-option value="internal">内部服务</a-select-option>
                    <a-select-option value="external">外部服务</a-select-option>
                    <a-select-option value="third_party">第三方服务</a-select-option>
                    <a-select-option value="payment">支付服务</a-select-option>
                    <a-select-option value="sms">短信服务</a-select-option>
                    <a-select-option value="email">邮件服务</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建时间">
                  <a-range-picker 
                    v-model:value="searchForm.createTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建人">
                  <a-input v-model:value="searchForm.creator" placeholder="请输入创建人" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="版本号">
                  <a-input v-model:value="searchForm.version" placeholder="请输入版本号" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总接口数" 
              :value="statistics.totalInterfaces" 
              :value-style="{ color: '#1890ff' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="正常运行" 
              :value="statistics.normalInterfaces" 
              :value-style="{ color: '#52c41a' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="异常接口" 
              :value="statistics.errorInterfaces" 
              :value-style="{ color: '#ff4d4f' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="今日调用" 
              :value="statistics.todayCalls" 
              :value-style="{ color: '#722ed1' }"
              suffix="次"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              新增接口
            </a-button>
            <a-button @click="batchTest">
              <ExperimentOutlined />
              批量测试
            </a-button>
            <a-button @click="importInterfaces">
              <UploadOutlined />
              导入配置
            </a-button>
            <a-button @click="exportInterfaces">
              <DownloadOutlined />
              导出配置
            </a-button>
            <a-button @click="showDocModal = true">
              <FileTextOutlined />
              接口文档
            </a-button>
            <a-button @click="showMonitorModal = true">
              <MonitorOutlined />
              监控面板
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 接口分类标签页 -->
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="rest" tab="REST API">
            <InterfaceTable 
              :interfaces="restInterfaces" 
              interface-type="rest"
              @edit="editInterface" 
              @delete="deleteInterface" 
              @toggle="toggleStatus"
              @test="testInterface"
              @copy="copyInterface"
              @view-doc="viewDocument"
            />
          </a-tab-pane>
          <a-tab-pane key="graphql" tab="GraphQL">
            <InterfaceTable 
              :interfaces="graphqlInterfaces" 
              interface-type="graphql"
              @edit="editInterface" 
              @delete="deleteInterface" 
              @toggle="toggleStatus"
              @test="testInterface"
              @copy="copyInterface"
              @view-doc="viewDocument"
            />
          </a-tab-pane>
          <a-tab-pane key="websocket" tab="WebSocket">
            <InterfaceTable 
              :interfaces="websocketInterfaces" 
              interface-type="websocket"
              @edit="editInterface" 
              @delete="deleteInterface" 
              @toggle="toggleStatus"
              @test="testInterface"
              @copy="copyInterface"
              @view-doc="viewDocument"
            />
          </a-tab-pane>
          <a-tab-pane key="rpc" tab="RPC接口">
            <InterfaceTable 
              :interfaces="rpcInterfaces" 
              interface-type="rpc"
              @edit="editInterface" 
              @delete="deleteInterface" 
              @toggle="toggleStatus"
              @test="testInterface"
              @copy="copyInterface"
              @view-doc="viewDocument"
            />
          </a-tab-pane>
          <a-tab-pane key="monitor" tab="性能监控">
            <InterfaceMonitor />
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <!-- 新增/编辑接口弹窗 -->
      <a-modal
        v-model:open="showCreateModal"
        :title="editingInterface ? '编辑接口' : '新增接口'"
        width="900px"
        @ok="handleInterfaceSubmit"
        @cancel="handleInterfaceCancel"
      >
        <a-form ref="interfaceFormRef" :model="interfaceForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="接口名称" name="name" :rules="[{ required: true, message: '请输入接口名称' }]">
                <a-input v-model:value="interfaceForm.name" placeholder="请输入接口名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="接口类型" name="type" :rules="[{ required: true, message: '请选择接口类型' }]">
                <a-select v-model:value="interfaceForm.type" placeholder="请选择接口类型">
                  <a-select-option value="rest">REST API</a-select-option>
                  <a-select-option value="graphql">GraphQL</a-select-option>
                  <a-select-option value="websocket">WebSocket</a-select-option>
                  <a-select-option value="rpc">RPC接口</a-select-option>
                  <a-select-option value="mq">消息队列</a-select-option>
                  <a-select-option value="file">文件传输</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="请求方法" name="method" :rules="[{ required: true, message: '请选择请求方法' }]">
                <a-select v-model:value="interfaceForm.method" placeholder="请选择请求方法">
                  <a-select-option value="GET">GET</a-select-option>
                  <a-select-option value="POST">POST</a-select-option>
                  <a-select-option value="PUT">PUT</a-select-option>
                  <a-select-option value="DELETE">DELETE</a-select-option>
                  <a-select-option value="PATCH">PATCH</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="服务分组" name="serviceGroup">
                <a-select v-model:value="interfaceForm.serviceGroup" placeholder="请选择服务分组">
                  <a-select-option value="internal">内部服务</a-select-option>
                  <a-select-option value="external">外部服务</a-select-option>
                  <a-select-option value="third_party">第三方服务</a-select-option>
                  <a-select-option value="payment">支付服务</a-select-option>
                  <a-select-option value="sms">短信服务</a-select-option>
                  <a-select-option value="email">邮件服务</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="接口地址" name="url" :rules="[{ required: true, message: '请输入接口地址' }]">
            <a-input v-model:value="interfaceForm.url" placeholder="请输入接口地址" />
          </a-form-item>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="超时时间" name="timeout">
                <a-input-number v-model:value="interfaceForm.timeout" :min="1" :max="300" style="width: 100%" placeholder="秒" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="重试次数" name="retryTimes">
                <a-input-number v-model:value="interfaceForm.retryTimes" :min="0" :max="10" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="请求头">
            <a-textarea 
              v-model:value="interfaceForm.headers" 
              :rows="4" 
              placeholder="请输入请求头（JSON格式）"
            />
          </a-form-item>
          
          <a-form-item label="请求参数">
            <a-textarea 
              v-model:value="interfaceForm.requestParams" 
              :rows="4" 
              placeholder="请输入请求参数（JSON格式）"
            />
          </a-form-item>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="版本号" name="version">
                <a-input v-model:value="interfaceForm.version" placeholder="请输入版本号" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="接口状态" name="status">
                <a-radio-group v-model:value="interfaceForm.status">
                  <a-radio value="enabled">启用</a-radio>
                  <a-radio value="disabled">禁用</a-radio>
                  <a-radio value="testing">测试中</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="接口描述" name="description">
            <a-textarea v-model:value="interfaceForm.description" :rows="3" placeholder="请输入接口描述" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 接口测试弹窗 -->
      <a-modal
        v-model:open="showTestModal"
        title="接口测试"
        width="800px"
        @ok="executeTest"
        @cancel="showTestModal = false"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="测试配置" size="small">
              <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <a-form-item label="测试环境">
                  <a-select v-model:value="testForm.environment" placeholder="请选择测试环境">
                    <a-select-option value="dev">开发环境</a-select-option>
                    <a-select-option value="test">测试环境</a-select-option>
                    <a-select-option value="staging">预发布环境</a-select-option>
                    <a-select-option value="prod">生产环境</a-select-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item label="测试数据">
                  <a-textarea 
                    v-model:value="testForm.testData" 
                    :rows="6" 
                    placeholder="请输入测试数据（JSON格式）"
                  />
                </a-form-item>
                
                <a-form-item label="断言规则">
                  <a-textarea 
                    v-model:value="testForm.assertions" 
                    :rows="3" 
                    placeholder="请输入断言规则"
                  />
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="测试结果" size="small">
              <div v-if="testResult" class="test-result">
                <a-descriptions size="small" bordered>
                  <a-descriptions-item label="状态码">
                    <a-tag :color="getStatusColor(testResult.statusCode)">
                      {{ testResult.statusCode }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="响应时间">{{ testResult.responseTime }}ms</a-descriptions-item>
                  <a-descriptions-item label="数据大小">{{ testResult.dataSize }}KB</a-descriptions-item>
                </a-descriptions>
                
                <a-divider />
                
                <h4>响应数据</h4>
                <pre class="response-data">{{ testResult.responseData }}</pre>
              </div>
              <a-empty v-else description="暂无测试结果" />
            </a-card>
          </a-col>
        </a-row>
      </a-modal>

      <!-- 接口文档弹窗 -->
      <a-modal
        v-model:open="showDocModal"
        title="接口文档"
        width="1200px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="swagger" tab="Swagger文档">
            <div class="doc-content">
              <iframe src="/api/swagger-ui" width="100%" height="600px" frameborder="0"></iframe>
            </div>
          </a-tab-pane>
          <a-tab-pane key="postman" tab="Postman集合">
            <div class="doc-actions">
              <a-space>
                <a-button type="primary" @click="exportPostman">
                  <DownloadOutlined />
                  导出Postman集合
                </a-button>
                <a-button @click="generateCurl">
                  <CodeOutlined />
                  生成cURL命令
                </a-button>
              </a-space>
            </div>
            <a-divider />
            <div class="postman-collection">
              <pre>{{ postmanCollection }}</pre>
            </div>
          </a-tab-pane>
          <a-tab-pane key="sdk" tab="SDK示例">
            <a-select v-model:value="selectedLanguage" style="width: 200px; margin-bottom: 16px;">
              <a-select-option value="javascript">JavaScript</a-select-option>
              <a-select-option value="python">Python</a-select-option>
              <a-select-option value="java">Java</a-select-option>
              <a-select-option value="php">PHP</a-select-option>
            </a-select>
            <pre class="sdk-example">{{ getSdkExample(selectedLanguage) }}</pre>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 监控面板弹窗 -->
      <a-modal
        v-model:open="showMonitorModal"
        title="接口监控面板"
        width="1200px"
        :footer="null"
      >
        <InterfaceMonitor :is-modal="true" />
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  PlusOutlined,
  ExperimentOutlined,
  UploadOutlined,
  DownloadOutlined,
  FileTextOutlined,
  MonitorOutlined,
  CodeOutlined
} from '@ant-design/icons-vue'

// 接口表格组件
const InterfaceTable = {
  props: ['interfaces', 'interfaceType'],
  emits: ['edit', 'delete', 'toggle', 'test', 'copy', 'view-doc'],
  template: `
    <a-table 
      :columns="columns" 
      :data-source="interfaces" 
      :pagination="pagination"
      :loading="false"
      :scroll="{ x: 1400 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'method'">
          <a-tag :color="getMethodColor(record.method)">
            {{ record.method }}
          </a-tag>
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'responseTime'">
          <span :class="getResponseTimeClass(record.responseTime)">
            {{ record.responseTime }}ms
          </span>
        </template>
        <template v-if="column.key === 'successRate'">
          <a-progress 
            :percent="record.successRate" 
            size="small" 
            :stroke-color="getSuccessRateColor(record.successRate)"
          />
        </template>
        <template v-if="column.key === 'url'">
          <a-tooltip :title="record.url">
            <span class="url-text">{{ record.url.substring(0, 50) }}{{ record.url.length > 50 ? '...' : '' }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="$emit('test', record)">测试</a-button>
            <a-button type="link" size="small" @click="$emit('edit', record)">编辑</a-button>
            <a-button type="link" size="small" @click="$emit('copy', record)">复制</a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="$emit('view-doc', record)">查看文档</a-menu-item>
                  <a-menu-item @click="viewHistory(record)">调用历史</a-menu-item>
                  <a-menu-item @click="viewMetrics(record)">性能指标</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="$emit('toggle', record)" :class="{ 'text-danger': record.status === 'enabled' }">
                    {{ record.status === 'enabled' ? '禁用' : '启用' }}
                  </a-menu-item>
                  <a-menu-item @click="$emit('delete', record)" class="text-danger">删除</a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>
  `,
  setup() {
    const columns = [
      {
        title: '接口名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        fixed: 'left'
      },
      {
        title: '请求方法',
        key: 'method',
        width: 80
      },
      {
        title: '接口地址',
        key: 'url',
        width: 200
      },
      {
        title: '服务分组',
        dataIndex: 'serviceGroup',
        key: 'serviceGroup',
        width: 100
      },
      {
        title: '状态',
        key: 'status',
        width: 80
      },
      {
        title: '响应时间',
        key: 'responseTime',
        width: 100
      },
      {
        title: '成功率',
        key: 'successRate',
        width: 120
      },
      {
        title: '今日调用',
        dataIndex: 'todayCalls',
        key: 'todayCalls',
        width: 100
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 80
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 150
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ]

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true
    })

    const getMethodColor = (method) => {
      const colors = {
        GET: 'blue',
        POST: 'green',
        PUT: 'orange',
        DELETE: 'red',
        PATCH: 'purple'
      }
      return colors[method] || 'default'
    }

    const getStatusColor = (status) => {
      const colors = {
        enabled: 'green',
        disabled: 'red',
        testing: 'orange',
        error: 'red'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        enabled: '启用',
        disabled: '禁用',
        testing: '测试中',
        error: '异常'
      }
      return texts[status] || '未知'
    }

    const getResponseTimeClass = (time) => {
      if (time <= 100) return 'response-time-good'
      if (time <= 500) return 'response-time-normal'
      return 'response-time-slow'
    }

    const getSuccessRateColor = (rate) => {
      if (rate >= 95) return '#52c41a'
      if (rate >= 90) return '#faad14'
      return '#ff4d4f'
    }

    const viewHistory = (record) => {
      message.info(`查看接口 ${record.name} 的调用历史`)
    }

    const viewMetrics = (record) => {
      message.info(`查看接口 ${record.name} 的性能指标`)
    }

    return {
      columns,
      pagination,
      getMethodColor,
      getStatusColor,
      getStatusText,
      getResponseTimeClass,
      getSuccessRateColor,
      viewHistory,
      viewMetrics
    }
  }
}

// 接口监控组件
const InterfaceMonitor = {
  props: ['isModal'],
  template: `
    <div class="interface-monitor">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="实时性能指标" size="small">
            <div class="monitor-item">
              <span class="monitor-label">平均响应时间:</span>
              <span class="monitor-value">156ms</span>
              <a-progress :percent="75" size="small" stroke-color="#1890ff" />
            </div>
            <div class="monitor-item">
              <span class="monitor-label">QPS (每秒请求):</span>
              <span class="monitor-value">1,256</span>
              <a-progress :percent="63" size="small" stroke-color="#52c41a" />
            </div>
            <div class="monitor-item">
              <span class="monitor-label">错误率:</span>
              <span class="monitor-value">0.8%</span>
              <a-progress :percent="8" size="small" stroke-color="#ff4d4f" />
            </div>
            <div class="monitor-item">
              <span class="monitor-label">活跃连接:</span>
              <span class="monitor-value">2,468</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="资源使用情况" size="small">
            <div class="monitor-item">
              <span class="monitor-label">CPU使用率:</span>
              <a-progress :percent="45" size="small" />
              <span class="monitor-value">45%</span>
            </div>
            <div class="monitor-item">
              <span class="monitor-label">内存使用率:</span>
              <a-progress :percent="68" size="small" />
              <span class="monitor-value">68%</span>
            </div>
            <div class="monitor-item">
              <span class="monitor-label">网络IO:</span>
              <a-progress :percent="32" size="small" />
              <span class="monitor-value">32MB/s</span>
            </div>
            <div class="monitor-item">
              <span class="monitor-label">磁盘IO:</span>
              <a-progress :percent="23" size="small" />
              <span class="monitor-value">23MB/s</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card title="接口调用统计" size="small">
            <a-table 
              :columns="statisticsColumns" 
              :data-source="interfaceStats" 
              :pagination="{ pageSize: 8 }"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 'normal' ? 'green' : 'red'">
                    {{ record.status === 'normal' ? '正常' : '异常' }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'responseTime'">
                  <span :class="record.responseTime > 500 ? 'text-danger' : ''">
                    {{ record.responseTime }}ms
                  </span>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </div>
  `,
  setup() {
    const statisticsColumns = [
      { title: '接口名称', dataIndex: 'name', key: 'name' },
      { title: '调用次数', dataIndex: 'calls', key: 'calls' },
      { title: '响应时间', key: 'responseTime' },
      { title: '成功率', dataIndex: 'successRate', key: 'successRate' },
      { title: '状态', key: 'status' }
    ]

    const interfaceStats = ref([
      {
        id: 1,
        name: '用户登录接口',
        calls: 15678,
        responseTime: 89,
        successRate: '99.8%',
        status: 'normal'
      },
      {
        id: 2,
        name: '案件查询接口',
        calls: 23456,
        responseTime: 156,
        successRate: '99.5%',
        status: 'normal'
      },
      {
        id: 3,
        name: '短信发送接口',
        calls: 8976,
        responseTime: 234,
        successRate: '98.2%',
        status: 'normal'
      },
      {
        id: 4,
        name: '支付回调接口',
        calls: 3456,
        responseTime: 678,
        successRate: '95.6%',
        status: 'error'
      }
    ])

    return {
      statisticsColumns,
      interfaceStats
    }
  }
}

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showCreateModal = ref(false)
const showTestModal = ref(false)
const showDocModal = ref(false)
const showMonitorModal = ref(false)
const editingInterface = ref(null)
const activeTab = ref('rest')
const selectedLanguage = ref('javascript')

// 搜索表单
const searchForm = reactive({
  interfaceType: undefined,
  interfaceName: '',
  status: undefined,
  serviceGroup: undefined,
  createTimeRange: [],
  creator: '',
  version: ''
})

// 接口表单
const interfaceForm = reactive({
  name: '',
  type: 'rest',
  method: 'GET',
  url: '',
  serviceGroup: undefined,
  timeout: 30,
  retryTimes: 3,
  headers: '',
  requestParams: '',
  version: '1.0',
  status: 'enabled',
  description: ''
})

// 测试表单
const testForm = reactive({
  environment: 'test',
  testData: '',
  assertions: ''
})

// 测试结果
const testResult = ref(null)

// 统计数据
const statistics = reactive({
  totalInterfaces: 156,
  normalInterfaces: 142,
  errorInterfaces: 8,
  todayCalls: 125678
})

// 所有接口数据
const allInterfaces = ref([
  {
    id: 1,
    name: '用户登录接口',
    type: 'rest',
    method: 'POST',
    url: '/api/auth/login',
    serviceGroup: 'internal',
    status: 'enabled',
    responseTime: 89,
    successRate: 99.8,
    todayCalls: 15678,
    version: '1.0',
    timeout: 30,
    retryTimes: 3,
    headers: '{"Content-Type": "application/json"}',
    requestParams: '{"username": "string", "password": "string"}',
    description: '用户登录验证接口',
    updateTime: '2024-01-27 10:30',
    creator: '张开发'
  },
  {
    id: 2,
    name: '案件查询接口',
    type: 'rest',
    method: 'GET',
    url: '/api/cases/list',
    serviceGroup: 'internal',
    status: 'enabled',
    responseTime: 156,
    successRate: 99.5,
    todayCalls: 23456,
    version: '2.1',
    timeout: 60,
    retryTimes: 2,
    headers: '{"Authorization": "Bearer token"}',
    requestParams: '{"page": "number", "size": "number", "status": "string"}',
    description: '案件信息查询接口',
    updateTime: '2024-01-26 14:20',
    creator: '李架构'
  },
  {
    id: 3,
    name: '短信发送接口',
    type: 'rest',
    method: 'POST',
    url: '/api/sms/send',
    serviceGroup: 'third_party',
    status: 'enabled',
    responseTime: 234,
    successRate: 98.2,
    todayCalls: 8976,
    version: '1.5',
    timeout: 15,
    retryTimes: 5,
    headers: '{"Content-Type": "application/json", "X-API-Key": "key"}',
    requestParams: '{"mobile": "string", "content": "string", "template": "string"}',
    description: '短信发送服务接口',
    updateTime: '2024-01-25 09:15',
    creator: '王集成'
  },
  {
    id: 4,
    name: 'GraphQL查询接口',
    type: 'graphql',
    method: 'POST',
    url: '/graphql',
    serviceGroup: 'internal',
    status: 'enabled',
    responseTime: 123,
    successRate: 99.1,
    todayCalls: 5432,
    version: '1.0',
    timeout: 45,
    retryTimes: 2,
    headers: '{"Content-Type": "application/json"}',
    requestParams: '{"query": "string", "variables": "object"}',
    description: 'GraphQL数据查询接口',
    updateTime: '2024-01-24 16:45',
    creator: '赵前端'
  }
])

// 计算属性 - 按类型过滤接口
const restInterfaces = computed(() => 
  allInterfaces.value.filter(item => item.type === 'rest')
)

const graphqlInterfaces = computed(() => 
  allInterfaces.value.filter(item => item.type === 'graphql')
)

const websocketInterfaces = computed(() => 
  allInterfaces.value.filter(item => item.type === 'websocket')
)

const rpcInterfaces = computed(() => 
  allInterfaces.value.filter(item => item.type === 'rpc')
)

// Postman集合数据
const postmanCollection = ref(`{
  "info": {
    "name": "催收管理系统API",
    "description": "催收管理系统接口集合",
    "version": "1.0.0"
  },
  "item": [
    {
      "name": "用户登录",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\\"username\\": \\"admin\\", \\"password\\": \\"123456\\"}"
        },
        "url": {
          "raw": "{{baseUrl}}/api/auth/login",
          "host": ["{{baseUrl}}"],
          "path": ["api", "auth", "login"]
        }
      }
    }
  ]
}`)

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('接口数据已更新')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = key === 'interfaceType' || key === 'status' || key === 'serviceGroup' ? undefined : ''
    }
  })
  handleSearch()
}

const handleTabChange = (key) => {
  activeTab.value = key
  console.log('切换到接口类型:', key)
}

const handleInterfaceSubmit = () => {
  console.log('接口配置:', interfaceForm)
  message.success(editingInterface.value ? '接口配置更新成功' : '接口配置创建成功')
  showCreateModal.value = false
  handleInterfaceCancel()
}

const handleInterfaceCancel = () => {
  editingInterface.value = null
  Object.keys(interfaceForm).forEach(key => {
    if (key === 'type') {
      interfaceForm[key] = 'rest'
    } else if (key === 'method') {
      interfaceForm[key] = 'GET'
    } else if (key === 'timeout') {
      interfaceForm[key] = 30
    } else if (key === 'retryTimes') {
      interfaceForm[key] = 3
    } else if (key === 'version') {
      interfaceForm[key] = '1.0'
    } else if (key === 'status') {
      interfaceForm[key] = 'enabled'
    } else {
      interfaceForm[key] = ''
    }
  })
}

const editInterface = (record) => {
  editingInterface.value = record
  Object.keys(interfaceForm).forEach(key => {
    if (record[key] !== undefined) {
      interfaceForm[key] = record[key]
    }
  })
  showCreateModal.value = true
}

const deleteInterface = (record) => {
  message.success(`接口 ${record.name} 删除成功`)
}

const toggleStatus = (record) => {
  const newStatus = record.status === 'enabled' ? 'disabled' : 'enabled'
  message.success(`接口 ${record.name} 已${newStatus === 'enabled' ? '启用' : '禁用'}`)
}

const testInterface = (record) => {
  testForm.testData = record.requestParams || ''
  showTestModal.value = true
}

const copyInterface = (record) => {
  message.success(`接口 ${record.name} 复制成功`)
}

const viewDocument = (record) => {
  message.info(`查看接口 ${record.name} 的文档`)
}

const executeTest = () => {
  console.log('执行测试:', testForm)
  
  // 模拟测试结果
  setTimeout(() => {
    testResult.value = {
      statusCode: 200,
      responseTime: 156,
      dataSize: 2.5,
      responseData: JSON.stringify({
        code: 200,
        message: '成功',
        data: {
          result: '测试数据'
        }
      }, null, 2)
    }
    message.success('接口测试完成')
  }, 2000)
}

const getStatusColor = (code) => {
  if (code >= 200 && code < 300) return 'green'
  if (code >= 400 && code < 500) return 'orange'
  if (code >= 500) return 'red'
  return 'default'
}

const batchTest = () => {
  message.info('批量测试功能正在执行')
}

const importInterfaces = () => {
  message.success('接口配置导入成功')
}

const exportInterfaces = () => {
  message.success('接口配置导出成功')
}

const exportPostman = () => {
  message.success('Postman集合导出成功')
}

const generateCurl = () => {
  message.success('cURL命令生成成功')
}

const getSdkExample = (language) => {
  const examples = {
    javascript: `// JavaScript SDK 示例
const api = require('collection-system-sdk');

// 配置API客户端
const client = new api.Client({
  baseUrl: 'https://api.example.com',
  apiKey: 'your-api-key'
});

// 调用接口
client.auth.login({
  username: 'admin',
  password: '123456'
}).then(response => {
  console.log('登录成功:', response.data);
}).catch(error => {
  console.error('登录失败:', error);
});`,
    python: `# Python SDK 示例
from collection_system_sdk import Client

# 配置API客户端
client = Client(
    base_url='https://api.example.com',
    api_key='your-api-key'
)

# 调用接口
try:
    response = client.auth.login(
        username='admin',
        password='123456'
    )
    print('登录成功:', response.data)
except Exception as error:
    print('登录失败:', error)`,
    java: `// Java SDK 示例
import com.example.CollectionSystemClient;
import com.example.model.LoginRequest;

// 配置API客户端
CollectionSystemClient client = new CollectionSystemClient.Builder()
    .baseUrl("https://api.example.com")
    .apiKey("your-api-key")
    .build();

// 调用接口
LoginRequest request = new LoginRequest()
    .setUsername("admin")
    .setPassword("123456");

try {
    LoginResponse response = client.auth().login(request);
    System.out.println("登录成功: " + response.getData());
} catch (Exception e) {
    System.err.println("登录失败: " + e.getMessage());
}`,
    php: `<?php
// PHP SDK 示例
require_once 'vendor/autoload.php';

use CollectionSystem\\Client;

// 配置API客户端
$client = new Client([
    'base_url' => 'https://api.example.com',
    'api_key' => 'your-api-key'
]);

// 调用接口
try {
    $response = $client->auth->login([
        'username' => 'admin',
        'password' => '123456'
    ]);
    echo '登录成功: ' . json_encode($response->data);
} catch (Exception $e) {
    echo '登录失败: ' . $e->getMessage();
}
?>`
  }
  
  return examples[language] || '// 代码示例'
}

// 组件注册
const components = {
  InterfaceTable,
  InterfaceMonitor
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.url-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #666;
}

.response-time-good {
  color: #52c41a;
  font-weight: 500;
}

.response-time-normal {
  color: #faad14;
  font-weight: 500;
}

.response-time-slow {
  color: #ff4d4f;
  font-weight: 500;
}

.test-result {
  padding: 16px 0;
}

.response-data {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.doc-content {
  padding: 16px 0;
}

.doc-actions {
  padding: 16px 0;
}

.postman-collection {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.sdk-example {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-size: 13px;
  max-height: 500px;
  overflow-y: auto;
  line-height: 1.5;
}

.interface-monitor {
  padding: 16px 0;
}

.monitor-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.monitor-label {
  width: 120px;
  font-weight: 500;
  color: #666;
}

.monitor-value {
  margin-left: 8px;
  color: #333;
  font-weight: 500;
  min-width: 60px;
}

.text-danger {
  color: #ff4d4f;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>