<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>模板管理</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="模板类型">
                <a-select v-model:value="searchForm.templateType" placeholder="请选择模板类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="sms">短信模板</a-select-option>
                  <a-select-option value="email">邮件模板</a-select-option>
                  <a-select-option value="document">文档模板</a-select-option>
                  <a-select-option value="report">报表模板</a-select-option>
                  <a-select-option value="contract">合同模板</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="模板名称">
                <a-input v-model:value="searchForm.templateName" placeholder="请输入模板名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="active">启用</a-select-option>
                  <a-select-option value="inactive">禁用</a-select-option>
                  <a-select-option value="draft">草稿</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="创建人">
                  <a-input v-model:value="searchForm.creator" placeholder="请输入创建人" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建时间">
                  <a-range-picker 
                    v-model:value="searchForm.createTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="使用频率">
                  <a-select v-model:value="searchForm.usageFrequency" placeholder="请选择使用频率" allow-clear>
                    <a-select-option value="high">高频使用</a-select-option>
                    <a-select-option value="medium">中频使用</a-select-option>
                    <a-select-option value="low">低频使用</a-select-option>
                    <a-select-option value="unused">未使用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="模板标签">
                  <a-input v-model:value="searchForm.tags" placeholder="请输入标签" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总模板数" 
              :value="statistics.totalTemplates" 
              :value-style="{ color: '#1890ff' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="启用模板" 
              :value="statistics.activeTemplates" 
              :value-style="{ color: '#52c41a' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="高频使用" 
              :value="statistics.highUsageTemplates" 
              :value-style="{ color: '#faad14' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="今日使用" 
              :value="statistics.todayUsage" 
              :value-style="{ color: '#722ed1' }"
              suffix="次"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              新增模板
            </a-button>
            <a-button @click="batchImport">
              <UploadOutlined />
              批量导入
            </a-button>
            <a-button @click="exportTemplates">
              <DownloadOutlined />
              导出模板
            </a-button>
            <a-button @click="showPreviewModal = true">
              <EyeOutlined />
              模板预览
            </a-button>
            <a-button @click="showTestModal = true">
              <ExperimentOutlined />
              效果测试
            </a-button>
            <a-button @click="optimizeTemplates">
              <ThunderboltOutlined />
              智能优化
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 模板分类标签页 -->
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="sms" tab="短信模板">
            <TemplateTable 
              :templates="smsTemplates" 
              template-type="sms"
              @edit="editTemplate" 
              @delete="deleteTemplate" 
              @toggle="toggleStatus"
              @preview="showPreviewTemplate"
              @test="testTemplate"
            />
          </a-tab-pane>
          <a-tab-pane key="email" tab="邮件模板">
            <TemplateTable 
              :templates="emailTemplates" 
              template-type="email"
              @edit="editTemplate" 
              @delete="deleteTemplate" 
              @toggle="toggleStatus"
              @preview="showPreviewTemplate"
              @test="testTemplate"
            />
          </a-tab-pane>
          <a-tab-pane key="document" tab="文档模板">
            <TemplateTable 
              :templates="documentTemplates" 
              template-type="document"
              @edit="editTemplate" 
              @delete="deleteTemplate" 
              @toggle="toggleStatus"
              @preview="showPreviewTemplate"
              @test="testTemplate"
            />
          </a-tab-pane>
          <a-tab-pane key="report" tab="报表模板">
            <TemplateTable 
              :templates="reportTemplates" 
              template-type="report"
              @edit="editTemplate" 
              @delete="deleteTemplate" 
              @toggle="toggleStatus"
              @preview="showPreviewTemplate"
              @test="testTemplate"
            />
          </a-tab-pane>
          <a-tab-pane key="contract" tab="合同模板">
            <TemplateTable 
              :templates="contractTemplates" 
              template-type="contract"
              @edit="editTemplate" 
              @delete="deleteTemplate" 
              @toggle="toggleStatus"
              @preview="showPreviewTemplate"
              @test="testTemplate"
            />
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <!-- 新增/编辑模板弹窗 -->
      <a-modal
        v-model:open="showCreateModal"
        :title="editingTemplate ? '编辑模板' : '新增模板'"
        width="900px"
        @ok="handleTemplateSubmit"
        @cancel="handleTemplateCancel"
      >
        <a-form ref="templateFormRef" :model="templateForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板名称" name="name" :rules="[{ required: true, message: '请输入模板名称' }]">
                <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="模板类型" name="type" :rules="[{ required: true, message: '请选择模板类型' }]">
                <a-select v-model:value="templateForm.type" placeholder="请选择模板类型">
                  <a-select-option value="sms">短信模板</a-select-option>
                  <a-select-option value="email">邮件模板</a-select-option>
                  <a-select-option value="document">文档模板</a-select-option>
                  <a-select-option value="report">报表模板</a-select-option>
                  <a-select-option value="contract">合同模板</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板编码" name="code" :rules="[{ required: true, message: '请输入模板编码' }]">
                <a-input v-model:value="templateForm.code" placeholder="请输入模板编码" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="模板分类" name="category">
                <a-select v-model:value="templateForm.category" placeholder="请选择分类">
                  <a-select-option value="催收通知">催收通知</a-select-option>
                  <a-select-option value="还款提醒">还款提醒</a-select-option>
                  <a-select-option value="逾期警告">逾期警告</a-select-option>
                  <a-select-option value="法务通知">法务通知</a-select-option>
                  <a-select-option value="协商沟通">协商沟通</a-select-option>
                  <a-select-option value="系统通知">系统通知</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item v-if="templateForm.type === 'email'" label="邮件主题" name="subject">
            <a-input v-model:value="templateForm.subject" placeholder="请输入邮件主题" />
          </a-form-item>
          
          <a-form-item label="模板内容" name="content" :rules="[{ required: true, message: '请输入模板内容' }]">
            <a-textarea 
              v-model:value="templateForm.content" 
              :rows="8" 
              placeholder="请输入模板内容，支持变量：{客户姓名}、{欠款金额}、{逾期天数}等"
            />
            <div class="template-variables">
              <span class="variable-label">可用变量：</span>
              <a-tag 
                v-for="variable in templateVariables" 
                :key="variable.key"
                @click="insertVariable(variable.key)"
                style="cursor: pointer; margin: 2px;"
              >
                {{ variable.label }}
              </a-tag>
            </div>
          </a-form-item>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板标签" name="tags">
                <a-select 
                  v-model:value="templateForm.tags" 
                  mode="tags" 
                  placeholder="请输入标签"
                  :tokenSeparators="[',']"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="使用场景" name="scenario">
                <a-select v-model:value="templateForm.scenario" placeholder="请选择使用场景">
                  <a-select-option value="初次催收">初次催收</a-select-option>
                  <a-select-option value="再次催收">再次催收</a-select-option>
                  <a-select-option value="最终催收">最终催收</a-select-option>
                  <a-select-option value="法务前置">法务前置</a-select-option>
                  <a-select-option value="协商沟通">协商沟通</a-select-option>
                  <a-select-option value="还款确认">还款确认</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板状态" name="status">
                <a-radio-group v-model:value="templateForm.status">
                  <a-radio value="active">启用</a-radio>
                  <a-radio value="inactive">禁用</a-radio>
                  <a-radio value="draft">草稿</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="排序" name="sort">
                <a-input-number v-model:value="templateForm.sort" :min="0" :max="999" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="模板说明" name="description">
            <a-textarea v-model:value="templateForm.description" :rows="3" placeholder="请输入模板说明" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 模板预览弹窗 -->
      <a-modal
        v-model:open="showPreviewModal"
        title="模板预览"
        width="700px"
        :footer="null"
      >
        <div v-if="previewTemplate">
          <a-descriptions title="模板信息" bordered size="small">
            <a-descriptions-item label="模板名称">{{ previewTemplate.name }}</a-descriptions-item>
            <a-descriptions-item label="模板类型">{{ getTemplateTypeText(previewTemplate.type) }}</a-descriptions-item>
            <a-descriptions-item label="使用次数">{{ previewTemplate.usageCount }}次</a-descriptions-item>
            <a-descriptions-item label="创建时间" span="2">{{ previewTemplate.createTime }}</a-descriptions-item>
            <a-descriptions-item label="最后修改">{{ previewTemplate.updateTime }}</a-descriptions-item>
          </a-descriptions>
          
          <a-divider />
          
          <h4>模板内容预览</h4>
          <div class="template-preview">
            <div v-if="previewTemplate.type === 'email'" class="email-preview">
              <div class="email-subject"><strong>主题：</strong>{{ previewTemplate.subject || '无主题' }}</div>
              <div class="email-content">{{ previewTemplate.content }}</div>
            </div>
            <div v-else class="content-preview">
              {{ previewTemplate.content }}
            </div>
          </div>
          
          <a-divider />
          
          <h4>效果预览（模拟数据）</h4>
          <div class="template-render">
            <div v-if="previewTemplate.type === 'email'" class="email-render">
              <div class="email-subject"><strong>主题：</strong>{{ renderTemplateContent(previewTemplate.subject) }}</div>
              <div class="email-content">{{ renderTemplateContent(previewTemplate.content) }}</div>
            </div>
            <div v-else class="content-render">
              {{ renderTemplateContent(previewTemplate.content) }}
            </div>
          </div>
        </div>
      </a-modal>

      <!-- A/B测试弹窗 -->
      <a-modal
        v-model:open="showTestModal"
        title="模板效果测试"
        width="800px"
        @ok="handleTestSubmit"
        @cancel="showTestModal = false"
      >
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="测试类型">
            <a-radio-group v-model:value="testForm.testType">
              <a-radio value="single">单模板测试</a-radio>
              <a-radio value="ab">A/B对比测试</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="测试模板A">
            <a-select v-model:value="testForm.templateA" placeholder="请选择模板A">
              <a-select-option v-for="template in allTemplates" :key="template.id" :value="template.id">
                {{ template.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item v-if="testForm.testType === 'ab'" label="测试模板B">
            <a-select v-model:value="testForm.templateB" placeholder="请选择模板B">
              <a-select-option v-for="template in allTemplates" :key="template.id" :value="template.id">
                {{ template.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="测试样本数">
            <a-input-number v-model:value="testForm.sampleSize" :min="10" :max="10000" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="测试时长">
            <a-select v-model:value="testForm.duration" placeholder="请选择测试时长">
              <a-select-option value="1">1天</a-select-option>
              <a-select-option value="3">3天</a-select-option>
              <a-select-option value="7">7天</a-select-option>
              <a-select-option value="14">14天</a-select-option>
              <a-select-option value="30">30天</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="评估指标">
            <a-checkbox-group v-model:value="testForm.metrics">
              <a-checkbox value="open_rate">打开率</a-checkbox>
              <a-checkbox value="response_rate">回复率</a-checkbox>
              <a-checkbox value="conversion_rate">转化率</a-checkbox>
              <a-checkbox value="payment_rate">还款率</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="测试说明">
            <a-textarea v-model:value="testForm.description" :rows="3" placeholder="请输入测试说明" />
          </a-form-item>
        </a-form>
        
        <a-divider />
        
        <h4>历史测试结果</h4>
        <a-table 
          :columns="testResultColumns" 
          :data-source="testResults" 
          :pagination="{ pageSize: 5 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'result'">
              <a-tag :color="getTestResultColor(record.result)">
                {{ getTestResultText(record.result) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-button type="link" size="small" @click="viewTestDetail(record)">查看详情</a-button>
            </template>
          </template>
        </a-table>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
  ExperimentOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue'

// 模板表格组件
const TemplateTable = {
  props: ['templates', 'templateType'],
  emits: ['edit', 'delete', 'toggle', 'preview', 'test'],
  template: `
    <a-table 
      :columns="columns" 
      :data-source="templates" 
      :pagination="pagination"
      :loading="false"
      :scroll="{ x: 1300 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <a-tag :color="getTemplateTypeColor(record.type)">
            {{ getTemplateTypeText(record.type) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'usageFrequency'">
          <a-progress 
            :percent="getUsagePercent(record.usageCount)" 
            size="small" 
            :stroke-color="getUsageColor(record.usageCount)"
          />
          <span style="margin-left: 8px;">{{ record.usageCount }}次</span>
        </template>
        <template v-if="column.key === 'content'">
          <div class="content-preview">
            {{ record.content.substring(0, 50) }}{{ record.content.length > 50 ? '...' : '' }}
          </div>
        </template>
        <template v-if="column.key === 'tags'">
          <a-tag v-for="tag in record.tags" :key="tag" size="small">{{ tag }}</a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="$emit('preview', record)">预览</a-button>
            <a-button type="link" size="small" @click="$emit('edit', record)">编辑</a-button>
            <a-button type="link" size="small" @click="copyTemplate(record)">复制</a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="$emit('test', record)">效果测试</a-menu-item>
                  <a-menu-item @click="exportTemplate(record)">导出模板</a-menu-item>
                  <a-menu-item @click="viewHistory(record)">修改历史</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="$emit('toggle', record)" :class="{ 'text-danger': record.status === 'active' }">
                    {{ record.status === 'active' ? '禁用' : '启用' }}
                  </a-menu-item>
                  <a-menu-item @click="$emit('delete', record)" class="text-danger">删除</a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>
  `,
  setup() {
    const columns = [
      {
        title: '模板名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        fixed: 'left'
      },
      {
        title: '模板编码',
        dataIndex: 'code',
        key: 'code',
        width: 120
      },
      {
        title: '类型',
        key: 'type',
        width: 100
      },
      {
        title: '分类',
        dataIndex: 'category',
        key: 'category',
        width: 100
      },
      {
        title: '模板内容',
        key: 'content',
        width: 200
      },
      {
        title: '使用频率',
        key: 'usageFrequency',
        width: 120
      },
      {
        title: '状态',
        key: 'status',
        width: 80
      },
      {
        title: '标签',
        key: 'tags',
        width: 120
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 150
      },
      {
        title: '操作',
        key: 'action',
        width: 180,
        fixed: 'right'
      }
    ]

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true
    })

    const getTemplateTypeColor = (type) => {
      const colors = {
        sms: 'blue',
        email: 'green',
        document: 'orange',
        report: 'purple',
        contract: 'red'
      }
      return colors[type] || 'default'
    }

    const getTemplateTypeText = (type) => {
      const texts = {
        sms: '短信模板',
        email: '邮件模板',
        document: '文档模板',
        report: '报表模板',
        contract: '合同模板'
      }
      return texts[type] || '未知'
    }

    const getStatusColor = (status) => {
      const colors = {
        active: 'green',
        inactive: 'red',
        draft: 'orange'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        active: '启用',
        inactive: '禁用',
        draft: '草稿'
      }
      return texts[status] || '未知'
    }

    const getUsagePercent = (count) => {
      return Math.min((count / 100) * 100, 100)
    }

    const getUsageColor = (count) => {
      if (count >= 50) return '#52c41a'
      if (count >= 20) return '#faad14'
      return '#ff4d4f'
    }

    const copyTemplate = (record) => {
      message.success(`模板 ${record.name} 复制成功`)
    }

    const exportTemplate = (record) => {
      message.success(`模板 ${record.name} 导出成功`)
    }

    const viewHistory = (record) => {
      message.info(`查看模板 ${record.name} 的修改历史`)
    }

    return {
      columns,
      pagination,
      getTemplateTypeColor,
      getTemplateTypeText,
      getStatusColor,
      getStatusText,
      getUsagePercent,
      getUsageColor,
      copyTemplate,
      exportTemplate,
      viewHistory
    }
  }
}

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showCreateModal = ref(false)
const showPreviewModal = ref(false)
const showTestModal = ref(false)
const editingTemplate = ref(null)
const previewTemplate = ref(null)
const activeTab = ref('sms')

// 搜索表单
const searchForm = reactive({
  templateType: undefined,
  templateName: '',
  status: undefined,
  creator: '',
  createTimeRange: [],
  usageFrequency: undefined,
  tags: ''
})

// 模板表单
const templateForm = reactive({
  name: '',
  type: 'sms',
  code: '',
  category: undefined,
  subject: '',
  content: '',
  tags: [],
  scenario: undefined,
  status: 'active',
  sort: 0,
  description: ''
})

// 测试表单
const testForm = reactive({
  testType: 'single',
  templateA: undefined,
  templateB: undefined,
  sampleSize: 100,
  duration: '7',
  metrics: ['response_rate', 'conversion_rate'],
  description: ''
})

// 统计数据
const statistics = reactive({
  totalTemplates: 156,
  activeTemplates: 142,
  highUsageTemplates: 23,
  todayUsage: 387
})

// 模板变量
const templateVariables = ref([
  { key: '{客户姓名}', label: '客户姓名' },
  { key: '{欠款金额}', label: '欠款金额' },
  { key: '{逾期天数}', label: '逾期天数' },
  { key: '{还款日期}', label: '还款日期' },
  { key: '{联系电话}', label: '联系电话' },
  { key: '{案件编号}', label: '案件编号' },
  { key: '{催收员姓名}', label: '催收员姓名' },
  { key: '{公司名称}', label: '公司名称' },
  { key: '{当前日期}', label: '当前日期' },
  { key: '{还款账户}', label: '还款账户' }
])

// 所有模板数据
const allTemplates = ref([
  {
    id: 1,
    name: '首次催收短信',
    type: 'sms',
    code: 'SMS_FIRST_COLLECTION',
    category: '催收通知',
    content: '尊敬的{客户姓名}，您的账户存在{欠款金额}元逾期款项，逾期{逾期天数}天，请尽快还款。咨询电话：{联系电话}',
    tags: ['催收', '首次', '通知'],
    scenario: '初次催收',
    status: 'active',
    sort: 1,
    usageCount: 89,
    createTime: '2024-01-15 09:00',
    updateTime: '2024-01-20 14:30',
    creator: '张催收',
    description: '用于首次催收的短信模板'
  },
  {
    id: 2,
    name: '逾期警告邮件',
    type: 'email',
    code: 'EMAIL_OVERDUE_WARNING',
    category: '逾期警告',
    subject: '重要提醒：您的账户已逾期{逾期天数}天',
    content: '亲爱的{客户姓名}，\n\n您好！\n\n我们发现您的账户存在逾期情况：\n- 欠款金额：{欠款金额}元\n- 逾期天数：{逾期天数}天\n- 案件编号：{案件编号}\n\n请您尽快处理逾期款项，避免产生额外费用。\n\n如有疑问，请联系我们：{联系电话}\n\n{公司名称}\n{当前日期}',
    tags: ['逾期', '警告', '邮件'],
    scenario: '再次催收',
    status: 'active',
    sort: 2,
    usageCount: 45,
    createTime: '2024-01-16 10:00',
    updateTime: '2024-01-22 16:45',
    creator: '李主管',
    description: '用于逾期警告的邮件模板'
  },
  {
    id: 3,
    name: '法务通知书',
    type: 'document',
    code: 'DOC_LEGAL_NOTICE',
    category: '法务通知',
    content: '催收通知书\n\n{客户姓名}：\n\n根据相关法律法规和合同约定，您的账户存在逾期情况如下：\n\n欠款金额：{欠款金额}元\n逾期天数：{逾期天数}天\n案件编号：{案件编号}\n\n请您在收到本通知书后7日内主动联系我司处理相关事宜，否则我司将依法采取进一步措施。\n\n联系方式：{联系电话}\n\n{公司名称}\n{当前日期}',
    tags: ['法务', '通知', '正式'],
    scenario: '法务前置',
    status: 'active',
    sort: 3,
    usageCount: 12,
    createTime: '2024-01-18 11:00',
    updateTime: '2024-01-25 09:15',
    creator: '王法务',
    description: '用于法务通知的文档模板'
  }
])

// 测试结果数据
const testResults = ref([
  {
    id: 1,
    testName: '短信模板A/B测试',
    templateA: '首次催收短信',
    templateB: '优化催收短信',
    sampleSize: 500,
    duration: 7,
    result: 'success',
    improvement: '+15.2%',
    createTime: '2024-01-20 14:00'
  },
  {
    id: 2,
    testName: '邮件主题测试',
    templateA: '逾期警告邮件',
    templateB: '紧急逾期通知',
    sampleSize: 200,
    duration: 3,
    result: 'failed',
    improvement: '-3.1%',
    createTime: '2024-01-18 16:30'
  }
])

// 测试结果表格列
const testResultColumns = [
  {
    title: '测试名称',
    dataIndex: 'testName',
    key: 'testName'
  },
  {
    title: '样本数',
    dataIndex: 'sampleSize',
    key: 'sampleSize'
  },
  {
    title: '测试结果',
    key: 'result'
  },
  {
    title: '效果提升',
    dataIndex: 'improvement',
    key: 'improvement'
  },
  {
    title: '测试时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'action'
  }
]

// 计算属性 - 按类型过滤模板
const smsTemplates = computed(() => 
  allTemplates.value.filter(template => template.type === 'sms')
)

const emailTemplates = computed(() => 
  allTemplates.value.filter(template => template.type === 'email')
)

const documentTemplates = computed(() => 
  allTemplates.value.filter(template => template.type === 'document')
)

const reportTemplates = computed(() => 
  allTemplates.value.filter(template => template.type === 'report')
)

const contractTemplates = computed(() => 
  allTemplates.value.filter(template => template.type === 'contract')
)

// 辅助方法
const getTemplateTypeText = (type) => {
  const texts = {
    sms: '短信模板',
    email: '邮件模板',
    document: '文档模板',
    report: '报表模板',
    contract: '合同模板'
  }
  return texts[type] || '未知'
}

const getTestResultColor = (result) => {
  const colors = {
    success: 'green',
    failed: 'red',
    pending: 'orange'
  }
  return colors[result] || 'default'
}

const getTestResultText = (result) => {
  const texts = {
    success: '测试成功',
    failed: '测试失败',
    pending: '测试中'
  }
  return texts[result] || '未知'
}

// 模板内容渲染（模拟数据替换）
const renderTemplateContent = (content) => {
  if (!content) return ''
  
  const mockData = {
    '{客户姓名}': '张三',
    '{欠款金额}': '5,000',
    '{逾期天数}': '15',
    '{还款日期}': '2024-02-01',
    '{联系电话}': '400-123-4567',
    '{案件编号}': 'CS2024010001',
    '{催收员姓名}': '李催收',
    '{公司名称}': '某某金融公司',
    '{当前日期}': '2024-01-27',
    '{还款账户}': '6222081234567890'
  }
  
  let result = content
  Object.keys(mockData).forEach(key => {
    result = result.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'), mockData[key])
  })
  
  return result
}

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('模板数据已更新')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = key === 'templateType' || key === 'status' || key === 'usageFrequency' ? undefined : ''
    }
  })
  handleSearch()
}

const handleTabChange = (key) => {
  activeTab.value = key
  console.log('切换到模板类型:', key)
}

const handleTemplateSubmit = () => {
  console.log('模板信息:', templateForm)
  message.success(editingTemplate.value ? '模板更新成功' : '模板创建成功')
  showCreateModal.value = false
  handleTemplateCancel()
}

const handleTemplateCancel = () => {
  editingTemplate.value = null
  Object.keys(templateForm).forEach(key => {
    if (Array.isArray(templateForm[key])) {
      templateForm[key] = []
    } else if (key === 'type') {
      templateForm[key] = 'sms'
    } else if (key === 'status') {
      templateForm[key] = 'active'
    } else if (key === 'sort') {
      templateForm[key] = 0
    } else {
      templateForm[key] = ''
    }
  })
}

const editTemplate = (record) => {
  editingTemplate.value = record
  Object.keys(templateForm).forEach(key => {
    if (record[key] !== undefined) {
      templateForm[key] = record[key]
    }
  })
  showCreateModal.value = true
}

const deleteTemplate = (record) => {
  message.success(`模板 ${record.name} 删除成功`)
}

const toggleStatus = (record) => {
  const newStatus = record.status === 'active' ? 'inactive' : 'active'
  message.success(`模板 ${record.name} 已${newStatus === 'active' ? '启用' : '禁用'}`)
}

const showPreviewTemplate = (record) => {
  previewTemplate.value = record
  showPreviewModal.value = true
}

const testTemplate = (record) => {
  testForm.templateA = record.id
  showTestModal.value = true
}

const insertVariable = (variable) => {
  const textarea = document.querySelector('textarea[placeholder*="模板内容"]')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = templateForm.content
    templateForm.content = text.substring(0, start) + variable + text.substring(end)
    
    // 设置光标位置
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variable.length, start + variable.length)
    }, 0)
  }
}

const batchImport = () => {
  message.info('批量导入模板功能')
}

const exportTemplates = () => {
  message.success('模板数据导出成功')
}

const optimizeTemplates = () => {
  message.success('智能优化建议已生成')
}

const handleTestSubmit = () => {
  console.log('测试配置:', testForm)
  message.success('模板效果测试已启动')
  showTestModal.value = false
}

const viewTestDetail = (record) => {
  message.info(`查看测试 ${record.testName} 的详细结果`)
}

// 组件注册
const components = {
  TemplateTable
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.template-variables {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.variable-label {
  font-weight: 500;
  margin-right: 8px;
  color: #666;
}

.template-preview {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin: 16px 0;
}

.template-render {
  padding: 16px;
  background-color: #e6f7ff;
  border-radius: 4px;
  margin: 16px 0;
}

.email-preview .email-subject,
.email-render .email-subject {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.email-preview .email-content,
.email-render .email-content {
  white-space: pre-line;
  line-height: 1.6;
}

.content-preview {
  white-space: pre-line;
  line-height: 1.6;
}

.content-render {
  white-space: pre-line;
  line-height: 1.6;
}

.text-danger {
  color: #ff4d4f;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
EOF < /dev/null