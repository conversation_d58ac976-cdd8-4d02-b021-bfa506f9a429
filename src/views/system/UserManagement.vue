<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>用户管理</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="用户姓名">
                <a-input v-model:value="searchForm.userName" placeholder="请输入用户姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="all">全部部门</a-select-option>
                  <a-select-option value="collection1">催收一部</a-select-option>
                  <a-select-option value="collection2">催收二部</a-select-option>
                  <a-select-option value="legal">法务部</a-select-option>
                  <a-select-option value="customer">客户服务部</a-select-option>
                  <a-select-option value="admin">管理部门</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="用户状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="active">正常</a-select-option>
                  <a-select-option value="inactive">停用</a-select-option>
                  <a-select-option value="locked">锁定</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="用户角色">
                  <a-select v-model:value="searchForm.role" placeholder="请选择角色" allow-clear>
                    <a-select-option value="admin">管理员</a-select-option>
                    <a-select-option value="manager">主管</a-select-option>
                    <a-select-option value="collector">催收员</a-select-option>
                    <a-select-option value="legal">法务专员</a-select-option>
                    <a-select-option value="customer_service">客服专员</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建时间">
                  <a-range-picker 
                    v-model:value="searchForm.createTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="最后登录">
                  <a-range-picker 
                    v-model:value="searchForm.lastLoginRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="工号">
                  <a-input v-model:value="searchForm.employeeId" placeholder="请输入工号" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总用户数" 
              :value="statistics.totalUsers" 
              :value-style="{ color: '#1890ff' }"
              suffix="人"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="在线用户" 
              :value="statistics.onlineUsers" 
              :value-style="{ color: '#52c41a' }"
              suffix="人"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="今日新增" 
              :value="statistics.todayNew" 
              :value-style="{ color: '#faad14' }"
              suffix="人"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="活跃用户" 
              :value="statistics.activeUsers" 
              :value-style="{ color: '#722ed1' }"
              suffix="人"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space>
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              新增用户
            </a-button>
            <a-button @click="showBatchImportModal = true">
              <UploadOutlined />
              批量导入
            </a-button>
            <a-button @click="exportUsers">
              <DownloadOutlined />
              导出用户
            </a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu @click="({ key }) => handleBatchAction(key)">
                  <a-menu-item key="enable">
                    <CheckCircleOutlined />
                    批量启用
                  </a-menu-item>
                  <a-menu-item key="disable">
                    <CloseCircleOutlined />
                    批量禁用
                  </a-menu-item>
                  <a-menu-item key="resetPassword">
                    <KeyOutlined />
                    批量重置密码
                  </a-menu-item>
                  <a-menu-item key="assignRole">
                    <UserOutlined />
                    批量分配角色
                  </a-menu-item>
                  <a-menu-item key="delete" danger>
                    <DeleteOutlined />
                    批量删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>
                批量操作 ({{ selectedRowKeys.length }})
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
          
          <a-space>
            <a-button @click="showOnlineUsersModal = true">
              <TeamOutlined />
              在线用户
            </a-button>
            <a-button @click="showDepartmentModal = true">
              <ApartmentOutlined />
              部门管理
            </a-button>
            <a-button @click="showUserStatModal = true">
              <BarChartOutlined />
              用户统计
            </a-button>
            <a-button @click="syncUsers">
              <SyncOutlined />
              同步用户
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 用户列表 -->
      <a-card title="用户列表">
        <a-table 
          :columns="columns" 
          :data-source="userList" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1500 }"
          :row-selection="rowSelection"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar :src="record.avatar" :size="32">
                {{ record.name.charAt(0) }}
              </a-avatar>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'role'">
              <a-tag :color="getRoleColor(record.role)">
                {{ getRoleText(record.role) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'onlineStatus'">
              <a-badge 
                :status="record.onlineStatus === 'online' ? 'processing' : 'default'" 
                :text="record.onlineStatus === 'online' ? '在线' : '离线'"
              />
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewUser(record)">查看</a-button>
                <a-button type="link" size="small" @click="editUser(record)">编辑</a-button>
                <a-button type="link" size="small" @click="resetPassword(record)">重置密码</a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMenuAction(key, record)">
                      <a-menu-item key="role">
                        <UserOutlined />
                        分配角色
                      </a-menu-item>
                      <a-menu-item key="permission">
                        <SafetyCertificateOutlined />
                        权限设置
                      </a-menu-item>
                      <a-menu-item key="history">
                        <FileTextOutlined />
                        登录历史
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="lock" v-if="record.status === 'active'">
                        <LockOutlined />
                        锁定账号
                      </a-menu-item>
                      <a-menu-item key="unlock" v-if="record.status === 'locked'">
                        <UnlockOutlined />
                        解锁账号
                      </a-menu-item>
                      <a-menu-item key="toggle">
                        {{ record.status === 'active' ? '停用' : '启用' }}
                      </a-menu-item>
                      <a-menu-item key="delete" danger>
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 新增/编辑用户弹窗 -->
      <a-modal
        v-model:open="showCreateModal"
        :title="editingUser ? '编辑用户' : '新增用户'"
        width="800px"
        @ok="handleUserSubmit"
        @cancel="handleUserCancel"
      >
        <a-form ref="userFormRef" :model="userForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="用户姓名" name="name" :rules="[{ required: true, message: '请输入用户姓名' }]">
                <a-input v-model:value="userForm.name" placeholder="请输入用户姓名" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="工号" name="employeeId" :rules="[{ required: true, message: '请输入工号' }]">
                <a-input v-model:value="userForm.employeeId" placeholder="请输入工号" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="手机号" name="phone" :rules="[{ required: true, message: '请输入手机号' }]">
                <a-input v-model:value="userForm.phone" placeholder="请输入手机号" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="邮箱" name="email">
                <a-input v-model:value="userForm.email" placeholder="请输入邮箱" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="所属部门" name="department" :rules="[{ required: true, message: '请选择部门' }]">
                <a-select v-model:value="userForm.department" placeholder="请选择部门">
                  <a-select-option value="collection1">催收一部</a-select-option>
                  <a-select-option value="collection2">催收二部</a-select-option>
                  <a-select-option value="legal">法务部</a-select-option>
                  <a-select-option value="customer">客户服务部</a-select-option>
                  <a-select-option value="admin">管理部门</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="用户角色" name="role" :rules="[{ required: true, message: '请选择角色' }]">
                <a-select v-model:value="userForm.role" placeholder="请选择角色">
                  <a-select-option value="admin">管理员</a-select-option>
                  <a-select-option value="manager">主管</a-select-option>
                  <a-select-option value="collector">催收员</a-select-option>
                  <a-select-option value="legal">法务专员</a-select-option>
                  <a-select-option value="customer_service">客服专员</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="直属上级" name="supervisor">
                <a-select v-model:value="userForm.supervisor" placeholder="请选择直属上级" allow-clear>
                  <a-select-option value="user1">张主管</a-select-option>
                  <a-select-option value="user2">李经理</a-select-option>
                  <a-select-option value="user3">王总监</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="入职时间" name="hireDate">
                <a-date-picker v-model:value="userForm.hireDate" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="用户状态" name="status">
            <a-radio-group v-model:value="userForm.status">
              <a-radio value="active">正常</a-radio>
              <a-radio value="inactive">停用</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="userForm.remark" :rows="3" placeholder="请输入备注信息" />
          </a-form-item>
          
          <a-form-item label="权限设置">
            <a-checkbox-group v-model:value="userForm.permissions">
              <a-checkbox value="case_view">案件查看</a-checkbox>
              <a-checkbox value="case_edit">案件编辑</a-checkbox>
              <a-checkbox value="customer_view">客户查看</a-checkbox>
              <a-checkbox value="customer_edit">客户编辑</a-checkbox>
              <a-checkbox value="report_view">报表查看</a-checkbox>
              <a-checkbox value="system_config">系统配置</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量导入弹窗 -->
      <a-modal
        v-model:open="showBatchImportModal"
        title="批量导入用户"
        width="600px"
        @ok="handleBatchImport"
        @cancel="showBatchImportModal = false"
      >
        <a-space direction="vertical" style="width: 100%;">
          <a-alert message="请按照模板格式填写用户信息" type="info" show-icon />
          
          <a-button @click="downloadTemplate">
            <DownloadOutlined />
            下载导入模板
          </a-button>
          
          <a-upload
            v-model:file-list="importFileList"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls"
          >
            <a-button>
              <UploadOutlined />
              选择文件
            </a-button>
          </a-upload>
          
          <div v-if="importResult">
            <a-divider />
            <h4>导入结果</h4>
            <a-descriptions size="small" bordered>
              <a-descriptions-item label="总计">{{ importResult.total }}</a-descriptions-item>
              <a-descriptions-item label="成功">{{ importResult.success }}</a-descriptions-item>
              <a-descriptions-item label="失败">{{ importResult.failed }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </a-space>
      </a-modal>

      <!-- 用户统计弹窗 -->
      <a-modal
        v-model:open="showUserStatModal"
        title="用户统计分析"
        width="1000px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="overview" tab="总体概览">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="departmentChart" style="height: 300px;"></div>
              </a-col>
              <a-col :span="12">
                <div ref="roleChart" style="height: 300px;"></div>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <div ref="statusChart" style="height: 300px;"></div>
              </a-col>
              <a-col :span="12">
                <div ref="joinChart" style="height: 300px;"></div>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="activity" tab="活跃度分析">
            <div ref="activityChart" style="height: 400px;"></div>
          </a-tab-pane>
          <a-tab-pane key="performance" tab="绩效分析">
            <div ref="performanceChart" style="height: 400px;"></div>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 权限设置弹窗 -->
      <a-modal
        v-model:open="showPermissionModal"
        title="权限设置"
        width="800px"
        @ok="handlePermissionSubmit"
      >
        <div v-if="currentUser">
          <a-alert :message="`为用户 ${currentUser.name} 设置权限`" type="info" show-icon style="margin-bottom: 16px;" />
          <a-tabs>
            <a-tab-pane key="menu" tab="菜单权限">
              <a-tree
                v-model:checked-keys="checkedMenuKeys"
                :tree-data="menuTreeData"
                checkable
                :selectable="false"
                default-expand-all
              />
            </a-tab-pane>
            <a-tab-pane key="data" tab="数据权限">
              <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <a-form-item label="数据范围">
                  <a-radio-group v-model:value="dataPermission.scope">
                    <a-radio value="all">全部数据</a-radio>
                    <a-radio value="department">本部门</a-radio>
                    <a-radio value="self">仅本人</a-radio>
                    <a-radio value="custom">自定义</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="特殊权限">
                  <a-checkbox-group v-model:value="dataPermission.special">
                    <a-checkbox value="export">导出权限</a-checkbox>
                    <a-checkbox value="import">导入权限</a-checkbox>
                    <a-checkbox value="delete">删除权限</a-checkbox>
                    <a-checkbox value="audit">审核权限</a-checkbox>
                  </a-checkbox-group>
                </a-form-item>
              </a-form>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-modal>

      <!-- 登录历史弹窗 -->
      <a-modal
        v-model:open="showLoginHistoryModal"
        title="登录历史"
        width="900px"
        :footer="null"
      >
        <a-table
          :columns="loginHistoryColumns"
          :data-source="loginHistoryData"
          :pagination="{ pageSize: 10 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'success' ? 'green' : 'red'">
                {{ record.status === 'success' ? '成功' : '失败' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'device'">
              <span>
                <laptop-outlined v-if="record.device === 'PC'" />
                <mobile-outlined v-else />
                {{ record.device }}
              </span>
            </template>
          </template>
        </a-table>
      </a-modal>

      <!-- 在线用户弹窗 -->
      <a-modal
        v-model:open="showOnlineUsersModal"
        title="在线用户"
        width="1000px"
        :footer="null"
      >
        <a-table
          :columns="onlineUserColumns"
          :data-source="onlineUserData"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'duration'">
              {{ formatDuration(record.loginTime) }}
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="sendMessage(record)">
                  <MailOutlined />
                  发送消息
                </a-button>
                <a-button type="link" size="small" danger @click="forceOffline(record)">
                  <LogoutOutlined />
                  强制下线
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-modal>

      <!-- 部门管理弹窗 -->
      <a-modal
        v-model:open="showDepartmentModal"
        title="部门管理"
        width="800px"
        :footer="null"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="组织架构" size="small">
              <template #extra>
                <a-button type="link" size="small" @click="addDepartment">
                  <PlusOutlined />
                  新增
                </a-button>
              </template>
              <a-tree
                :tree-data="departmentTreeData"
                :selected-keys="selectedDeptKeys"
                @select="selectDepartment"
                show-line
              >
                <template #title="{ title, key }">
                  <span>{{ title }}</span>
                  <a-space style="float: right;">
                    <EditOutlined @click.stop="editDepartment(key)" />
                    <DeleteOutlined @click.stop="deleteDepartment(key)" style="color: #ff4d4f;" />
                  </a-space>
                </template>
              </a-tree>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="部门信息" size="small" v-if="selectedDepartment">
              <a-descriptions size="small" :column="1">
                <a-descriptions-item label="部门名称">{{ selectedDepartment.name }}</a-descriptions-item>
                <a-descriptions-item label="部门编码">{{ selectedDepartment.code }}</a-descriptions-item>
                <a-descriptions-item label="上级部门">{{ selectedDepartment.parentName || '无' }}</a-descriptions-item>
                <a-descriptions-item label="部门主管">{{ selectedDepartment.manager }}</a-descriptions-item>
                <a-descriptions-item label="员工数量">{{ selectedDepartment.employeeCount }}</a-descriptions-item>
                <a-descriptions-item label="创建时间">{{ selectedDepartment.createTime }}</a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  EditOutlined,
  BarChartOutlined,
  SyncOutlined,
  UserOutlined,
  KeyOutlined,
  TeamOutlined,
  ApartmentOutlined,
  SafetyCertificateOutlined,
  FileTextOutlined,
  LoginOutlined,
  LogoutOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LockOutlined,
  UnlockOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  DeleteOutlined,
  LaptopOutlined,
  MobileOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showCreateModal = ref(false)
const showBatchImportModal = ref(false)
const showUserStatModal = ref(false)
const showPermissionModal = ref(false)
const showLoginHistoryModal = ref(false)
const showOnlineUsersModal = ref(false)
const showDepartmentModal = ref(false)
const editingUser = ref(null)
const importFileList = ref([])
const importResult = ref(null)
const currentUser = ref(null)
const userFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  userName: '',
  department: undefined,
  status: undefined,
  role: undefined,
  createTimeRange: [],
  lastLoginRange: [],
  employeeId: ''
})

// 用户表单
const userForm = reactive({
  name: '',
  employeeId: '',
  phone: '',
  email: '',
  department: undefined,
  role: undefined,
  supervisor: undefined,
  hireDate: null,
  status: 'active',
  remark: '',
  permissions: []
})

// 统计数据
const statistics = reactive({
  totalUsers: 156,
  onlineUsers: 89,
  todayNew: 3,
  activeUsers: 134
})

// 表格配置
const columns = [
  {
    title: '头像',
    key: 'avatar',
    width: 60
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    fixed: 'left'
  },
  {
    title: '工号',
    dataIndex: 'employeeId',
    key: 'employeeId',
    width: 100
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 120
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 120
  },
  {
    title: '角色',
    key: 'role',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '在线状态',
    key: 'onlineStatus',
    width: 100
  },
  {
    title: '最后登录',
    dataIndex: 'lastLogin',
    key: 'lastLogin',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 用户数据
const userList = ref([
  {
    id: 1,
    name: '张催收',
    employeeId: 'CS001',
    phone: '13800138001',
    email: '<EMAIL>',
    department: 'collection1',
    departmentName: '催收一部',
    role: 'collector',
    status: 'active',
    onlineStatus: 'online',
    lastLogin: '2024-01-15 10:30',
    createTime: '2023-06-01 09:00',
    avatar: ''
  },
  {
    id: 2,
    name: '李主管',
    employeeId: 'MG001',
    phone: '13800138002',
    email: '<EMAIL>',
    department: 'collection1',
    departmentName: '催收一部',
    role: 'manager',
    status: 'active',
    onlineStatus: 'offline',
    lastLogin: '2024-01-15 09:15',
    createTime: '2023-03-15 09:00',
    avatar: ''
  },
  {
    id: 3,
    name: '王法务',
    employeeId: 'LG001',
    phone: '13800138003',
    email: '<EMAIL>',
    department: 'legal',
    departmentName: '法务部',
    role: 'legal',
    status: 'active',
    onlineStatus: 'online',
    lastLogin: '2024-01-15 11:20',
    createTime: '2023-08-10 09:00',
    avatar: ''
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: userList.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = reactive({
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
})

// 状态相关方法
const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    inactive: 'red',
    locked: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    inactive: '停用',
    locked: '锁定'
  }
  return texts[status] || '未知'
}

const getRoleColor = (role) => {
  const colors = {
    admin: 'red',
    manager: 'blue',
    collector: 'green',
    legal: 'purple',
    customer_service: 'orange'
  }
  return colors[role] || 'default'
}

const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    manager: '主管',
    collector: '催收员',
    legal: '法务专员',
    customer_service: '客服专员'
  }
  return texts[role] || '未知'
}

// 事件处理方法
const handleSearch = async () => {
  try {
    loading.value = true
    
    // 根据搜索条件过滤用户数据
    let filteredUsers = [...userList.value]
    
    // 用户姓名过滤
    if (searchForm.userName.trim()) {
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(searchForm.userName.toLowerCase())
      )
    }
    
    // 部门过滤
    if (searchForm.department && searchForm.department !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.department === searchForm.department)
    }
    
    // 状态过滤
    if (searchForm.status && searchForm.status !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.status === searchForm.status)
    }
    
    // 角色过滤
    if (searchForm.role) {
      filteredUsers = filteredUsers.filter(user => user.role === searchForm.role)
    }
    
    // 工号过滤
    if (searchForm.employeeId.trim()) {
      filteredUsers = filteredUsers.filter(user => 
        user.employeeId && user.employeeId.includes(searchForm.employeeId)
      )
    }
    
    // 创建时间范围过滤
    if (searchForm.createTimeRange && searchForm.createTimeRange.length === 2) {
      const [startDate, endDate] = searchForm.createTimeRange
      filteredUsers = filteredUsers.filter(user => {
        const createTime = new Date(user.createTime)
        return createTime >= startDate && createTime <= endDate
      })
    }
    
    // 最后登录时间范围过滤
    if (searchForm.lastLoginRange && searchForm.lastLoginRange.length === 2) {
      const [startDate, endDate] = searchForm.lastLoginRange
      filteredUsers = filteredUsers.filter(user => {
        if (user.lastLoginTime === '未登录') return false
        const lastLoginTime = new Date(user.lastLoginTime)
        return lastLoginTime >= startDate && lastLoginTime <= endDate
      })
    }
    
    // 更新过滤后的用户列表（这里应该是一个响应式的过滤结果）
    // 实际项目中可能需要将filteredUsers赋值给一个新的响应式变量
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    message.success(`找到 ${filteredUsers.length} 条用户记录`)
  } catch (error) {
    message.error('搜索失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = key === 'department' || key === 'status' || key === 'role' ? undefined : ''
    }
  })
  handleSearch()
}

const handleUserSubmit = async () => {
  try {
    loading.value = true
    
    // 表单验证
    await userFormRef.value.validate()
    
    if (editingUser.value) {
      // 编辑用户
      const userIndex = userList.value.findIndex(user => user.id === editingUser.value.id)
      if (userIndex !== -1) {
        userList.value[userIndex] = {
          ...userList.value[userIndex],
          ...userForm,
          updateTime: new Date().toLocaleString()
        }
        message.success('用户信息更新成功')
      }
    } else {
      // 新增用户
      const newUser = {
        id: Date.now(),
        ...userForm,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
        lastLoginTime: '未登录',
        onlineStatus: 'offline'
      }
      userList.value.unshift(newUser)
      // 更新统计数据
      statistics.totalUsers++
      if (userForm.status === 'active') {
        statistics.activeUsers++
      }
      message.success('用户创建成功')
    }
    
    showCreateModal.value = false
    handleUserCancel()
    await handleSearch() // 刷新列表
  } catch (error) {
    message.error('操作失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const handleUserCancel = () => {
  editingUser.value = null
  Object.keys(userForm).forEach(key => {
    if (Array.isArray(userForm[key])) {
      userForm[key] = []
    } else if (key === 'status') {
      userForm[key] = 'active'
    } else {
      userForm[key] = ''
    }
  })
}

const viewUser = (record) => {
  message.info(`查看用户 ${record.name} 的详细信息`)
}

const editUser = (record) => {
  editingUser.value = record
  Object.keys(userForm).forEach(key => {
    if (record[key] !== undefined) {
      userForm[key] = record[key]
    }
  })
  showCreateModal.value = true
}

const resetPassword = (record) => {
  message.success(`已重置用户 ${record.name} 的密码`)
}

const assignRole = (record) => {
  message.info(`为用户 ${record.name} 分配角色`)
}

const viewPermissions = (record) => {
  message.info(`查看用户 ${record.name} 的权限`)
}

const loginHistory = (record) => {
  message.info(`查看用户 ${record.name} 的登录历史`)
}

const toggleStatus = (record) => {
  const newStatus = record.status === 'active' ? 'inactive' : 'active'
  const actionText = newStatus === 'active' ? '启用' : '停用'
  
  Modal.confirm({
    title: `${actionText}用户`,
    content: `确定要${actionText}用户 "${record.name}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      try {
        // 更新用户状态
        const userIndex = userList.value.findIndex(user => user.id === record.id)
        if (userIndex !== -1) {
          userList.value[userIndex].status = newStatus
          userList.value[userIndex].updateTime = new Date().toLocaleString()
          
          // 更新统计数据
          if (newStatus === 'active') {
            statistics.activeUsers++
          } else {
            statistics.activeUsers--
            // 停用用户时，如果在线则强制下线
            if (record.onlineStatus === 'online') {
              userList.value[userIndex].onlineStatus = 'offline'
              statistics.onlineUsers--
            }
          }
          
          message.success(`用户 ${record.name} 已${actionText}`)
        }
      } catch (error) {
        message.error(`${actionText}失败：` + error.message)
      }
    }
  })
}

const deleteUser = (record) => {
  Modal.confirm({
    title: '删除用户',
    content: `确定要删除用户 "${record.name}" 吗？删除后无法恢复！`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      try {
        // 从列表中删除
        const userIndex = userList.value.findIndex(user => user.id === record.id)
        if (userIndex !== -1) {
          userList.value.splice(userIndex, 1)
          
          // 更新统计数据
          statistics.totalUsers--
          if (record.status === 'active') {
            statistics.activeUsers--
          }
          if (record.onlineStatus === 'online') {
            statistics.onlineUsers--
          }
          
          message.success(`用户 ${record.name} 删除成功`)
          
          // 如果当前选中的行包含被删除的用户，更新选中状态
          selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== record.id)
        }
      } catch (error) {
        message.error('删除失败：' + error.message)
      }
    }
  })
}

const exportUsers = () => {
  try {
    // 获取要导出的用户数据
    const selectedUsers = selectedRowKeys.value.length > 0 
      ? userList.value.filter(user => selectedRowKeys.value.includes(user.id))
      : userList.value
    
    // 准备导出数据
    const exportData = selectedUsers.map(user => ({
      '工号': user.employeeId || '',
      '姓名': user.name,
      '用户名': user.username,
      '邮箱': user.email,
      '手机号': user.phone || '',
      '部门': getDepartmentText(user.department),
      '角色': getRoleText(user.role),
      '状态': getStatusText(user.status),
      '创建时间': user.createTime,
      '最后登录': user.lastLoginTime
    }))
    
    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n')
    
    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `用户数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    const count = selectedRowKeys.value.length > 0 ? selectedRowKeys.value.length : userList.value.length
    message.success(`已导出 ${count} 条用户数据`)
  } catch (error) {
    message.error('导出失败：' + error.message)
  }
}


const syncUsers = async () => {
  try {
    Modal.confirm({
      title: '同步用户数据',
      content: '确定要从企业系统同步用户数据吗？这可能需要几分钟时间。',
      onOk: async () => {
        try {
          loading.value = true
          
          // 模拟从企业系统同步用户数据
          await new Promise(resolve => setTimeout(resolve, 2000))
          
          // 模拟新增一些同步的用户
          const syncedUsers = [
            {
              id: Date.now() + 1,
              name: '新员工A',
              username: 'newuserA',
              email: '<EMAIL>',
              phone: '13800138001',
              department: 'collection1',
              role: 'collector',
              status: 'active',
              employeeId: 'EMP001',
              avatar: '',
              createTime: new Date().toLocaleString(),
              updateTime: new Date().toLocaleString(),
              lastLoginTime: '未登录',
              onlineStatus: 'offline'
            },
            {
              id: Date.now() + 2,
              name: '新员工B',
              username: 'newuserB',
              email: '<EMAIL>',
              phone: '13800138002',
              department: 'legal',
              role: 'legal',
              status: 'active',
              employeeId: 'EMP002',
              avatar: '',
              createTime: new Date().toLocaleString(),
              updateTime: new Date().toLocaleString(),
              lastLoginTime: '未登录',
              onlineStatus: 'offline'
            }
          ]
          
          // 添加到用户列表
          syncedUsers.forEach(user => {
            userList.value.unshift(user)
            statistics.totalUsers++
            statistics.activeUsers++
          })
          
          message.success(`同步成功！新增 ${syncedUsers.length} 个用户`)
        } catch (error) {
          message.error('同步失败：' + error.message)
        } finally {
          loading.value = false
        }
      }
    })
  } catch (error) {
    message.error('操作失败：' + error.message)
  }
}

const downloadTemplate = () => {
  message.success('用户导入模板下载成功')
}

const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    message.error('只能上传 Excel 文件')
  }
  return false // 阻止自动上传
}

const handleBatchImport = () => {
  if (importFileList.value.length === 0) {
    message.warning('请选择要导入的文件')
    return
  }
  
  // 模拟导入结果
  importResult.value = {
    total: 50,
    success: 47,
    failed: 3
  }
  
  message.success('用户批量导入完成')
}

// 图表引用
const departmentChart = ref()
const roleChart = ref()
const statusChart = ref()
const joinChart = ref()
const activityChart = ref()
const performanceChart = ref()

// 权限相关数据
const checkedMenuKeys = ref([])
const menuTreeData = ref([
  {
    title: '案件管理',
    key: 'case',
    children: [
      { title: '案件列表', key: 'case:list' },
      { title: '案件分配', key: 'case:assign' },
      { title: '案件跟进', key: 'case:follow' }
    ]
  },
  {
    title: '客户管理',
    key: 'customer',
    children: [
      { title: '客户档案', key: 'customer:list' },
      { title: '联系人管理', key: 'customer:contact' },
      { title: '客户分群', key: 'customer:segment' }
    ]
  },
  {
    title: '催收管理',
    key: 'collection',
    children: [
      { title: '通话记录', key: 'collection:call' },
      { title: '短信记录', key: 'collection:sms' },
      { title: '外访记录', key: 'collection:visit' }
    ]
  },
  {
    title: '系统管理',
    key: 'system',
    children: [
      { title: '用户管理', key: 'system:user' },
      { title: '角色管理', key: 'system:role' },
      { title: '系统配置', key: 'system:config' }
    ]
  }
])

const dataPermission = reactive({
  scope: 'department',
  special: ['export']
})

// 登录历史列配置
const loginHistoryColumns = [
  { title: '登录时间', dataIndex: 'loginTime', key: 'loginTime', width: 160 },
  { title: '登录IP', dataIndex: 'ip', key: 'ip', width: 120 },
  { title: '登录地点', dataIndex: 'location', key: 'location', width: 150 },
  { title: '设备类型', key: 'device', width: 100 },
  { title: '浏览器', dataIndex: 'browser', key: 'browser', width: 120 },
  { title: '状态', key: 'status', width: 80 },
  { title: '备注', dataIndex: 'remark', key: 'remark' }
]

const loginHistoryData = ref([
  {
    id: 1,
    loginTime: '2024-01-20 14:30:00',
    ip: '*************',
    location: '北京市海淀区',
    device: 'PC',
    browser: 'Chrome 120',
    status: 'success',
    remark: '正常登录'
  },
  {
    id: 2,
    loginTime: '2024-01-20 09:15:00',
    ip: '*************',
    location: '北京市朝阳区',
    device: 'Mobile',
    browser: 'Safari',
    status: 'success',
    remark: '移动端登录'
  },
  {
    id: 3,
    loginTime: '2024-01-19 18:45:00',
    ip: '*************',
    location: '北京市西城区',
    device: 'PC',
    browser: 'Firefox',
    status: 'failed',
    remark: '密码错误'
  }
])

// 在线用户列配置
const onlineUserColumns = [
  { title: '用户名', dataIndex: 'name', key: 'name', width: 100 },
  { title: '部门', dataIndex: 'departmentName', key: 'departmentName', width: 120 },
  { title: '角色', dataIndex: 'roleName', key: 'roleName', width: 100 },
  { title: '登录IP', dataIndex: 'ip', key: 'ip', width: 120 },
  { title: '登录时间', dataIndex: 'loginTime', key: 'loginTime', width: 160 },
  { title: '在线时长', key: 'duration', width: 100 },
  { title: '当前页面', dataIndex: 'currentPage', key: 'currentPage', width: 150 },
  { title: '操作', key: 'action', width: 180 }
]

const onlineUserData = ref([
  {
    id: 1,
    name: '张催收',
    departmentName: '催收一部',
    roleName: '催收员',
    ip: '*************',
    loginTime: '2024-01-20 08:00:00',
    currentPage: '案件列表'
  },
  {
    id: 2,
    name: '李主管',
    departmentName: '催收一部',
    roleName: '主管',
    ip: '*************',
    loginTime: '2024-01-20 09:15:00',
    currentPage: '数据统计'
  }
])

// 部门树数据
const departmentTreeData = ref([
  {
    title: '总公司',
    key: '1',
    children: [
      {
        title: '催收一部',
        key: '2',
        children: [
          { title: '一组', key: '5' },
          { title: '二组', key: '6' }
        ]
      },
      {
        title: '催收二部',
        key: '3',
        children: [
          { title: '三组', key: '7' },
          { title: '四组', key: '8' }
        ]
      },
      { title: '法务部', key: '4' },
      { title: '客服部', key: '9' },
      { title: '风控部', key: '10' }
    ]
  }
])

const selectedDeptKeys = ref([])
const selectedDepartment = ref(null)

// 新增方法
const handleMenuAction = (action, record) => {
  currentUser.value = record
  const actions = {
    role: () => message.info(`分配角色: ${record.name}`),
    permission: () => {
      checkedMenuKeys.value = ['case:list', 'case:follow', 'customer:list']
      showPermissionModal.value = true
    },
    history: () => {
      showLoginHistoryModal.value = true
    },
    lock: () => {
      Modal.confirm({
        title: '锁定账号',
        content: `确定要锁定用户 ${record.name} 的账号吗？`,
        onOk() {
          message.success('账号已锁定')
        }
      })
    },
    unlock: () => {
      message.success(`账号 ${record.name} 已解锁`)
    },
    toggle: () => toggleStatus(record),
    delete: () => deleteUser(record)
  }
  actions[action]?.()
}

const handleBatchAction = (action) => {
  const count = selectedRowKeys.value.length
  if (count === 0) {
    message.warning('请先选择要操作的用户')
    return
  }
  
  const selectedUsers = userList.value.filter(user => selectedRowKeys.value.includes(user.id))
  
  const actions = {
    enable: () => {
      Modal.confirm({
        title: '批量启用',
        content: `确定要启用 ${count} 个用户吗？`,
        onOk() {
          try {
            let enabledCount = 0
            selectedUsers.forEach(user => {
              const userIndex = userList.value.findIndex(u => u.id === user.id)
              if (userIndex !== -1 && userList.value[userIndex].status === 'inactive') {
                userList.value[userIndex].status = 'active'
                userList.value[userIndex].updateTime = new Date().toLocaleString()
                statistics.activeUsers++
                enabledCount++
              }
            })
            selectedRowKeys.value = []
            message.success(`已启用 ${enabledCount} 个用户`)
          } catch (error) {
            message.error('批量启用失败：' + error.message)
          }
        }
      })
    },
    disable: () => {
      Modal.confirm({
        title: '批量禁用',
        content: `确定要禁用 ${count} 个用户吗？`,
        onOk() {
          try {
            let disabledCount = 0
            selectedUsers.forEach(user => {
              const userIndex = userList.value.findIndex(u => u.id === user.id)
              if (userIndex !== -1 && userList.value[userIndex].status === 'active') {
                userList.value[userIndex].status = 'inactive'
                userList.value[userIndex].updateTime = new Date().toLocaleString()
                statistics.activeUsers--
                // 禁用时如果在线则强制下线
                if (userList.value[userIndex].onlineStatus === 'online') {
                  userList.value[userIndex].onlineStatus = 'offline'
                  statistics.onlineUsers--
                }
                disabledCount++
              }
            })
            selectedRowKeys.value = []
            message.success(`已禁用 ${disabledCount} 个用户`)
          } catch (error) {
            message.error('批量禁用失败：' + error.message)
          }
        }
      })
    },
    resetPassword: () => {
      Modal.confirm({
        title: '批量重置密码',
        content: `确定要重置 ${count} 个用户的密码吗？重置后密码将为：123456`,
        onOk() {
          try {
            selectedUsers.forEach(user => {
              const userIndex = userList.value.findIndex(u => u.id === user.id)
              if (userIndex !== -1) {
                userList.value[userIndex].updateTime = new Date().toLocaleString()
              }
            })
            selectedRowKeys.value = []
            message.success(`已重置 ${count} 个用户的密码`)
          } catch (error) {
            message.error('批量重置密码失败：' + error.message)
          }
        }
      })
    },
    assignRole: () => {
      // 这里应该打开角色分配弹窗
      message.info(`批量分配角色功能 - 选中 ${count} 个用户`)
    },
    delete: () => {
      Modal.confirm({
        title: '批量删除',
        content: `确定要删除 ${count} 个用户吗？删除后无法恢复！`,
        okType: 'danger',
        onOk() {
          try {
            // 从列表中删除选中的用户
            selectedRowKeys.value.forEach(userId => {
              const userIndex = userList.value.findIndex(user => user.id === userId)
              if (userIndex !== -1) {
                const user = userList.value[userIndex]
                // 更新统计数据
                statistics.totalUsers--
                if (user.status === 'active') {
                  statistics.activeUsers--
                }
                if (user.onlineStatus === 'online') {
                  statistics.onlineUsers--
                }
                // 删除用户
                userList.value.splice(userIndex, 1)
              }
            })
            const deletedCount = selectedRowKeys.value.length
            selectedRowKeys.value = []
            message.success(`已删除 ${deletedCount} 个用户`)
          } catch (error) {
            message.error('批量删除失败：' + error.message)
          }
        }
      })
    }
  }
  actions[action]?.()
}

const handlePermissionSubmit = () => {
  showPermissionModal.value = false
  message.success('权限设置成功')
}

const formatDuration = (loginTime) => {
  const now = dayjs()
  const login = dayjs(loginTime)
  const hours = now.diff(login, 'hour')
  const minutes = now.diff(login, 'minute') % 60
  return `${hours}小时${minutes}分钟`
}

const sendMessage = (user) => {
  message.info(`向 ${user.name} 发送消息`)
}

const forceOffline = (user) => {
  Modal.confirm({
    title: '强制下线',
    content: `确定要将用户 ${user.name} 强制下线吗？`,
    onOk() {
      message.success('已强制下线')
    }
  })
}

const selectDepartment = (keys, { node }) => {
  selectedDeptKeys.value = keys
  if (keys.length > 0) {
    selectedDepartment.value = {
      name: node.title,
      code: `DEPT00${node.key}`,
      parentName: node.parent?.title,
      manager: '张经理',
      employeeCount: Math.floor(Math.random() * 50) + 10,
      createTime: '2023-01-01 09:00:00'
    }
  }
}

const addDepartment = () => {
  message.info('新增部门')
}

const editDepartment = (key) => {
  message.info(`编辑部门: ${key}`)
}

const deleteDepartment = (key) => {
  Modal.confirm({
    title: '删除部门',
    content: '确定要删除该部门吗？',
    onOk() {
      message.success('删除成功')
    }
  })
}

// 初始化图表
const initCharts = () => {
  // 部门分布图
  const deptChart = echarts.init(departmentChart.value)
  const deptOption = {
    title: { text: '部门用户分布', left: 'center' },
    tooltip: { trigger: 'item' },
    series: [{
      name: '用户数',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 45, name: '催收一部' },
        { value: 38, name: '催收二部' },
        { value: 25, name: '法务部' },
        { value: 20, name: '客服部' },
        { value: 15, name: '风控部' },
        { value: 13, name: '管理部门' }
      ]
    }]
  }
  deptChart.setOption(deptOption)

  // 角色分布图
  const roleChartInstance = echarts.init(roleChart.value)
  const roleOption = {
    title: { text: '角色分布', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['管理员', '主管', '催收员', '法务专员', '客服专员', '风控专员']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '人数',
      type: 'bar',
      data: [5, 12, 89, 25, 15, 10],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#83bff6' },
          { offset: 0.5, color: '#188df0' },
          { offset: 1, color: '#188df0' }
        ])
      }
    }]
  }
  roleChartInstance.setOption(roleOption)

  // 状态分布图
  const statusChartInstance = echarts.init(statusChart.value)
  const statusOption = {
    title: { text: '用户状态分布', left: 'center' },
    tooltip: { trigger: 'item' },
    legend: { bottom: 0 },
    series: [{
      name: '状态',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      labelLine: { show: false },
      data: [
        { value: 134, name: '正常', itemStyle: { color: '#52c41a' } },
        { value: 15, name: '停用', itemStyle: { color: '#ff4d4f' } },
        { value: 7, name: '锁定', itemStyle: { color: '#faad14' } }
      ]
    }]
  }
  statusChartInstance.setOption(statusOption)

  // 入职趋势图
  const joinChartInstance = echarts.init(joinChart.value)
  const joinOption = {
    title: { text: '入职趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '入职人数',
      type: 'line',
      smooth: true,
      data: [12, 15, 8, 20, 18, 25],
      areaStyle: {
        opacity: 0.3
      }
    }]
  }
  joinChartInstance.setOption(joinOption)

  // 活跃度趋势图
  const activityChartInstance = echarts.init(activityChart.value)
  const activityOption = {
    title: { text: '用户活跃度趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { bottom: 0 },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '登录次数',
        type: 'line',
        data: [450, 480, 490, 510, 520, 280, 210]
      },
      {
        name: '活跃用户',
        type: 'line',
        data: [120, 125, 130, 135, 140, 85, 65]
      }
    ]
  }
  activityChartInstance.setOption(activityOption)

  // 绩效分析图
  const performanceChartInstance = echarts.init(performanceChart.value)
  const performanceOption = {
    title: { text: '部门绩效对比', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { bottom: 0 },
    radar: {
      indicator: [
        { name: '案件处理', max: 100 },
        { name: '回款率', max: 100 },
        { name: '客户满意度', max: 100 },
        { name: '工作效率', max: 100 },
        { name: '团队协作', max: 100 }
      ]
    },
    series: [{
      name: '部门绩效',
      type: 'radar',
      data: [
        {
          value: [85, 78, 90, 82, 88],
          name: '催收一部'
        },
        {
          value: [82, 80, 85, 78, 85],
          name: '催收二部'
        },
        {
          value: [78, 75, 88, 85, 82],
          name: '法务部'
        }
      ]
    }]
  }
  performanceChartInstance.setOption(performanceOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    deptChart.resize()
    roleChartInstance.resize()
    statusChartInstance.resize()
    joinChartInstance.resize()
    activityChartInstance.resize()
    performanceChartInstance.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  
  // 初始化图表需要等待DOM渲染完成
  showUserStatModal.value = true
  nextTick(() => {
    initCharts()
    showUserStatModal.value = false
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.text-danger {
  color: #ff4d4f;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
