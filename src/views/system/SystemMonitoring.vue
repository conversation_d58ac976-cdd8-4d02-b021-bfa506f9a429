<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>系统监控</h2>
      
      <!-- 系统状态概览 -->
      <a-row :gutter="16" class="status-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="系统状态" 
              :value="systemStatus.status"
              :value-style="{ color: getStatusColor(systemStatus.status) }"
            >
              <template #prefix>
                <component :is="getStatusIcon(systemStatus.status)" />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>运行时间 <span class="stat-time">{{ systemStatus.uptime }}</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="在线用户" 
              :value="systemStatus.onlineUsers" 
              suffix="人"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>峰值 <span class="stat-peak">{{ systemStatus.peakUsers }}人</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="CPU使用率" 
              :value="systemStatus.cpuUsage" 
              suffix="%"
              :value-style="{ color: getCpuColor(systemStatus.cpuUsage) }"
            >
              <template #prefix>
                <DashboardOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>内存 <span class="stat-memory">{{ systemStatus.memoryUsage }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="磁盘使用率" 
              :value="systemStatus.diskUsage" 
              suffix="%"
              :value-style="{ color: getDiskColor(systemStatus.diskUsage) }"
            >
              <template #prefix>
                <DatabaseOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>可用 <span class="stat-available">{{ systemStatus.diskAvailable }}GB</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-button type="primary" @click="refreshData">
            <ReloadOutlined />
            刷新数据
          </a-button>
          <a-button @click="showPerformanceModal">
            <LineChartOutlined />
            性能分析
          </a-button>
          <a-button @click="showLogModal">
            <FileTextOutlined />
            系统日志
          </a-button>
          <a-button @click="showAlertModal">
            <AlertOutlined />
            告警管理
          </a-button>
          <a-button @click="showConfigModal">
            <SettingOutlined />
            监控配置
          </a-button>
          <a-button @click="exportReport">
            <DownloadOutlined />
            导出报告
          </a-button>
        </div>
      </a-card>

      <!-- 实时监控图表 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="系统资源使用趋势" :bordered="false">
            <div id="resource-trend-chart" style="height: 350px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="用户活跃度统计" :bordered="false">
            <div id="user-activity-chart" style="height: 350px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="8">
          <a-card title="服务状态监控" :bordered="false">
            <div class="service-status">
              <div v-for="service in services" :key="service.name" class="service-item">
                <div class="service-info">
                  <span class="service-name">{{ service.name }}</span>
                  <a-tag :color="service.status === 'running' ? 'green' : 'red'">
                    {{ service.status === 'running' ? '运行中' : '已停止' }}
                  </a-tag>
                </div>
                <div class="service-metrics">
                  <span>响应时间: {{ service.responseTime }}ms</span>
                  <span>CPU: {{ service.cpu }}%</span>
                  <span>内存: {{ service.memory }}MB</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="数据库监控" :bordered="false">
            <div class="database-info">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="连接数">
                  {{ dbStatus.connections }}/{{ dbStatus.maxConnections }}
                </a-descriptions-item>
                <a-descriptions-item label="查询/秒">
                  {{ dbStatus.queriesPerSecond }}
                </a-descriptions-item>
                <a-descriptions-item label="慢查询">
                  {{ dbStatus.slowQueries }}
                </a-descriptions-item>
                <a-descriptions-item label="缓存命中率">
                  {{ dbStatus.cacheHitRate }}%
                </a-descriptions-item>
                <a-descriptions-item label="数据库大小">
                  {{ dbStatus.databaseSize }}GB
                </a-descriptions-item>
              </a-descriptions>
              <div class="db-performance">
                <div id="db-performance-chart" style="height: 200px;"></div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="网络监控" :bordered="false">
            <div class="network-info">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="入站流量">
                  {{ networkStatus.inboundTraffic }}MB/s
                </a-descriptions-item>
                <a-descriptions-item label="出站流量">
                  {{ networkStatus.outboundTraffic }}MB/s
                </a-descriptions-item>
                <a-descriptions-item label="并发连接">
                  {{ networkStatus.connections }}
                </a-descriptions-item>
                <a-descriptions-item label="错误率">
                  {{ networkStatus.errorRate }}%
                </a-descriptions-item>
                <a-descriptions-item label="平均延迟">
                  {{ networkStatus.avgLatency }}ms
                </a-descriptions-item>
              </a-descriptions>
              <div class="network-chart">
                <div id="network-chart" style="height: 200px;"></div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 告警信息 -->
      <a-card title="实时告警" class="alert-card">
        <a-list
          :data-source="alerts"
          :split="false"
          :pagination="{ pageSize: 5 }"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: getAlertColor(item.level) }">
                    <template #icon>
                      <component :is="getAlertIcon(item.level)" />
                    </template>
                  </a-avatar>
                </template>
                <template #title>
                  <span :style="{ color: getAlertColor(item.level) }">{{ item.title }}</span>
                  <a-tag :color="getAlertColor(item.level)" style="margin-left: 8px;">
                    {{ getAlertLevelText(item.level) }}
                  </a-tag>
                </template>
                <template #description>
                  <div>{{ item.description }}</div>
                  <div class="alert-time">{{ item.time }}</div>
                </template>
              </a-list-item-meta>
              <template #actions>
                <a-button type="link" size="small" @click="handleAlert(item)">处理</a-button>
                <a-button type="link" size="small" @click="ignoreAlert(item)">忽略</a-button>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </a-card>

      <!-- 性能分析弹窗 -->
      <a-modal
        v-model:open="performanceModalVisible"
        title="系统性能分析"
        width="1200px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="cpu" tab="CPU分析">
            <div id="cpu-analysis-chart" style="height: 400px;"></div>
          </a-tab-pane>
          <a-tab-pane key="memory" tab="内存分析">
            <div id="memory-analysis-chart" style="height: 400px;"></div>
          </a-tab-pane>
          <a-tab-pane key="disk" tab="磁盘I/O">
            <div id="disk-io-chart" style="height: 400px;"></div>
          </a-tab-pane>
          <a-tab-pane key="network" tab="网络分析">
            <div id="network-analysis-chart" style="height: 400px;"></div>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 系统日志弹窗 -->
      <a-modal
        v-model:open="logModalVisible"
        title="系统日志"
        width="1000px"
        :footer="null"
      >
        <div class="log-filters">
          <a-space>
            <a-select v-model:value="logFilter.level" placeholder="日志级别" style="width: 120px;">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="error">错误</a-select-option>
              <a-select-option value="warn">警告</a-select-option>
              <a-select-option value="info">信息</a-select-option>
              <a-select-option value="debug">调试</a-select-option>
            </a-select>
            <a-select v-model:value="logFilter.source" placeholder="日志来源" style="width: 150px;">
              <a-select-option value="">全部来源</a-select-option>
              <a-select-option value="system">系统</a-select-option>
              <a-select-option value="database">数据库</a-select-option>
              <a-select-option value="api">API</a-select-option>
              <a-select-option value="auth">认证</a-select-option>
            </a-select>
            <a-input v-model:value="logFilter.keyword" placeholder="关键词搜索" style="width: 200px;" />
            <a-button type="primary" @click="filterLogs">筛选</a-button>
            <a-button @click="clearLogFilter">清空</a-button>
          </a-space>
        </div>
        <div class="log-content">
          <a-table
            :columns="logColumns"
            :data-source="filteredLogs"
            :pagination="{ pageSize: 10 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'level'">
                <a-tag :color="getLogLevelColor(record.level)">
                  {{ record.level.toUpperCase() }}
                </a-tag>
              </template>
              <template v-if="column.key === 'message'">
                <div class="log-message" :title="record.message">
                  {{ record.message }}
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </a-modal>

      <!-- 告警管理弹窗 -->
      <a-modal
        v-model:open="alertModalVisible"
        title="告警管理"
        width="800px"
        @ok="handleAlertManagement"
        @cancel="alertModalVisible = false"
      >
        <a-table
          :columns="alertColumns"
          :data-source="alerts"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'level'">
              <a-tag :color="getAlertColor(record.level)">
                {{ getAlertLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'active' ? 'red' : 'green'">
                {{ record.status === 'active' ? '未处理' : '已处理' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleAlert(record)">
                  {{ record.status === 'active' ? '处理' : '查看' }}
                </a-button>
                <a-button type="link" size="small" danger @click="deleteAlert(record)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-modal>

      <!-- 监控配置弹窗 -->
      <a-modal
        v-model:open="configModalVisible"
        title="监控配置"
        width="600px"
        @ok="handleConfigSave"
        @cancel="configModalVisible = false"
      >
        <a-form :model="monitorConfig" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="刷新间隔">
            <a-select v-model:value="monitorConfig.refreshInterval">
              <a-select-option :value="5">5秒</a-select-option>
              <a-select-option :value="10">10秒</a-select-option>
              <a-select-option :value="30">30秒</a-select-option>
              <a-select-option :value="60">1分钟</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="CPU告警阈值">
            <a-input-number
              v-model:value="monitorConfig.cpuThreshold"
              :min="0"
              :max="100"
              addon-after="%"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="内存告警阈值">
            <a-input-number
              v-model:value="monitorConfig.memoryThreshold"
              :min="0"
              :max="100"
              addon-after="%"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="磁盘告警阈值">
            <a-input-number
              v-model:value="monitorConfig.diskThreshold"
              :min="0"
              :max="100"
              addon-after="%"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="告警通知">
            <a-checkbox-group v-model:value="monitorConfig.notifications">
              <a-checkbox value="email">邮件通知</a-checkbox>
              <a-checkbox value="sms">短信通知</a-checkbox>
              <a-checkbox value="webhook">Webhook</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item label="数据保留期">
            <a-select v-model:value="monitorConfig.dataRetention">
              <a-select-option :value="7">7天</a-select-option>
              <a-select-option :value="30">30天</a-select-option>
              <a-select-option :value="90">90天</a-select-option>
              <a-select-option :value="365">1年</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, h } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  LineChartOutlined,
  FileTextOutlined,
  AlertOutlined,
  SettingOutlined,
  DownloadOutlined,
  UserOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const refreshTimer = ref(null)

// 系统状态
const systemStatus = reactive({
  status: '正常',
  uptime: '15天12小时',
  onlineUsers: 128,
  peakUsers: 256,
  cpuUsage: 45,
  memoryUsage: 68,
  diskUsage: 72,
  diskAvailable: 850
})

// 服务状态
const services = ref([
  {
    name: 'Web服务',
    status: 'running',
    responseTime: 120,
    cpu: 25,
    memory: 512
  },
  {
    name: 'API服务',
    status: 'running',
    responseTime: 85,
    cpu: 18,
    memory: 384
  },
  {
    name: '数据库服务',
    status: 'running',
    responseTime: 45,
    cpu: 35,
    memory: 1024
  },
  {
    name: '缓存服务',
    status: 'running',
    responseTime: 15,
    cpu: 8,
    memory: 256
  },
  {
    name: '消息队列',
    status: 'running',
    responseTime: 32,
    cpu: 12,
    memory: 128
  },
  {
    name: '文件服务',
    status: 'running',
    responseTime: 95,
    cpu: 22,
    memory: 192
  }
])

// 数据库状态
const dbStatus = reactive({
  connections: 45,
  maxConnections: 100,
  queriesPerSecond: 1250,
  slowQueries: 3,
  cacheHitRate: 92.5,
  databaseSize: 125.6
})

// 网络状态
const networkStatus = reactive({
  inboundTraffic: 15.2,
  outboundTraffic: 8.6,
  connections: 2456,
  errorRate: 0.12,
  avgLatency: 35
})

// 告警信息
const alerts = ref([
  {
    id: 1,
    title: 'CPU使用率过高',
    description: '服务器CPU使用率达到85%，建议检查资源使用情况',
    level: 'warning',
    time: '2024-01-15 14:30:00',
    status: 'active'
  },
  {
    id: 2,
    title: '磁盘空间不足',
    description: '系统磁盘使用率超过90%，请及时清理或扩容',
    level: 'critical',
    time: '2024-01-15 13:45:00',
    status: 'active'
  },
  {
    id: 3,
    title: '数据库连接异常',
    description: '检测到多次数据库连接失败，请检查数据库状态',
    level: 'error',
    time: '2024-01-15 12:20:00',
    status: 'resolved'
  },
  {
    id: 4,
    title: '内存使用率告警',
    description: '系统内存使用率达到75%',
    level: 'warning',
    time: '2024-01-15 11:15:00',
    status: 'active'
  },
  {
    id: 5,
    title: '登录失败次数异常',
    description: '检测到异常的登录失败尝试，可能存在安全风险',
    level: 'info',
    time: '2024-01-15 10:30:00',
    status: 'resolved'
  }
])

// 弹窗状态
const performanceModalVisible = ref(false)
const logModalVisible = ref(false)
const alertModalVisible = ref(false)
const configModalVisible = ref(false)

// 日志数据
const logs = ref([
  {
    id: 1,
    time: '2024-01-15 14:30:15',
    level: 'error',
    source: 'api',
    message: 'API请求超时: /api/cases/list'
  },
  {
    id: 2,
    time: '2024-01-15 14:29:45',
    level: 'warn',
    source: 'database',
    message: '慢查询检测: SELECT * FROM cases WHERE status = "active" - 执行时间: 2.5s'
  },
  {
    id: 3,
    time: '2024-01-15 14:28:30',
    level: 'info',
    source: 'auth',
    message: '用户登录成功: admin'
  },
  {
    id: 4,
    time: '2024-01-15 14:27:12',
    level: 'debug',
    source: 'system',
    message: '定时任务执行: 数据备份任务开始'
  },
  {
    id: 5,
    time: '2024-01-15 14:26:58',
    level: 'error',
    source: 'system',
    message: '磁盘空间不足警告: /var/log 分区使用率 92%'
  }
])

// 日志筛选
const logFilter = reactive({
  level: '',
  source: '',
  keyword: ''
})

const filteredLogs = ref([...logs.value])

// 日志表格列
const logColumns = [
  { title: '时间', dataIndex: 'time', key: 'time', width: 180 },
  { title: '级别', key: 'level', width: 80 },
  { title: '来源', dataIndex: 'source', key: 'source', width: 100 },
  { title: '消息', key: 'message' }
]

// 告警表格列
const alertColumns = [
  { title: '标题', dataIndex: 'title', key: 'title' },
  { title: '级别', key: 'level', width: 100 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 180 },
  { title: '状态', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 120 }
]

// 监控配置
const monitorConfig = reactive({
  refreshInterval: 30,
  cpuThreshold: 80,
  memoryThreshold: 85,
  diskThreshold: 90,
  notifications: ['email'],
  dataRetention: 30
})

// 状态相关方法
const getStatusColor = (status) => {
  return status === '正常' ? '#52c41a' : '#ff4d4f'
}

const getStatusIcon = (status) => {
  return status === '正常' ? 'CheckCircleOutlined' : 'ExclamationCircleOutlined'
}

const getCpuColor = (usage) => {
  if (usage < 50) return '#52c41a'
  if (usage < 80) return '#fa8c16'
  return '#ff4d4f'
}

const getDiskColor = (usage) => {
  if (usage < 70) return '#52c41a'
  if (usage < 90) return '#fa8c16'
  return '#ff4d4f'
}

const getAlertColor = (level) => {
  const colors = {
    info: '#1890ff',
    warning: '#fa8c16',
    error: '#ff4d4f',
    critical: '#722ed1'
  }
  return colors[level] || '#666'
}

const getAlertIcon = (level) => {
  const icons = {
    info: 'ExclamationCircleOutlined',
    warning: 'WarningOutlined',
    error: 'CloseCircleOutlined',
    critical: 'CloseCircleOutlined'
  }
  return icons[level] || 'ExclamationCircleOutlined'
}

const getAlertLevelText = (level) => {
  const texts = {
    info: '信息',
    warning: '警告',
    error: '错误',
    critical: '严重'
  }
  return texts[level] || '未知'
}

const getLogLevelColor = (level) => {
  const colors = {
    debug: 'blue',
    info: 'green',
    warn: 'orange',
    error: 'red'
  }
  return colors[level] || 'default'
}

// 事件处理方法
const refreshData = () => {
  // 模拟数据刷新
  systemStatus.cpuUsage = Math.floor(Math.random() * 30) + 30
  systemStatus.memoryUsage = Math.floor(Math.random() * 20) + 60
  systemStatus.onlineUsers = Math.floor(Math.random() * 50) + 100
  
  // 更新服务状态
  services.value.forEach(service => {
    service.responseTime = Math.floor(Math.random() * 100) + 50
    service.cpu = Math.floor(Math.random() * 30) + 10
  })
  
  message.success('数据已刷新')
  initCharts()
}

const showPerformanceModal = () => {
  performanceModalVisible.value = true
  setTimeout(() => {
    initPerformanceCharts()
  }, 100)
}

const showLogModal = () => {
  logModalVisible.value = true
}

const showAlertModal = () => {
  alertModalVisible.value = true
}

const showConfigModal = () => {
  configModalVisible.value = true
}

const exportReport = () => {
  message.success('正在生成监控报告...')
}

const handleAlert = (alert) => {
  alert.status = 'resolved'
  message.success(`告警 "${alert.title}" 已处理`)
}

const ignoreAlert = (alert) => {
  const index = alerts.value.findIndex(a => a.id === alert.id)
  if (index > -1) {
    alerts.value.splice(index, 1)
    message.success('告警已忽略')
  }
}

const deleteAlert = (alert) => {
  const index = alerts.value.findIndex(a => a.id === alert.id)
  if (index > -1) {
    alerts.value.splice(index, 1)
    message.success('告警已删除')
  }
}

const handleAlertManagement = () => {
  alertModalVisible.value = false
  message.success('告警管理设置已保存')
}

const filterLogs = () => {
  filteredLogs.value = logs.value.filter(log => {
    const levelMatch = !logFilter.level || log.level === logFilter.level
    const sourceMatch = !logFilter.source || log.source === logFilter.source
    const keywordMatch = !logFilter.keyword || 
      log.message.toLowerCase().includes(logFilter.keyword.toLowerCase())
    
    return levelMatch && sourceMatch && keywordMatch
  })
}

const clearLogFilter = () => {
  logFilter.level = ''
  logFilter.source = ''
  logFilter.keyword = ''
  filteredLogs.value = [...logs.value]
}

const handleConfigSave = () => {
  configModalVisible.value = false
  message.success('监控配置已保存')
  
  // 重新设置定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
  refreshTimer.value = setInterval(refreshData, monitorConfig.refreshInterval * 1000)
}

// 图表初始化
const initCharts = () => {
  // 资源使用趋势图
  const resourceChart = echarts.init(document.getElementById('resource-trend-chart'))
  const resourceOption = {
    title: {
      text: '最近24小时资源使用情况',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['CPU使用率', '内存使用率', '磁盘使用率'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 80,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: { formatter: '{value}%' }
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 60) + 20),
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '内存使用率',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 40) + 50),
        smooth: true,
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '磁盘使用率',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 20) + 65),
        smooth: true,
        itemStyle: { color: '#fa8c16' }
      }
    ]
  }
  resourceChart.setOption(resourceOption)

  // 用户活跃度统计
  const userChart = echarts.init(document.getElementById('user-activity-chart'))
  const userOption = {
    title: {
      text: '用户活跃度统计',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 60,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value',
      name: '用户数'
    },
    series: [
      {
        name: '在线用户',
        type: 'bar',
        data: [45, 32, 89, 156, 128, 98],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#1890ff' },
            { offset: 1, color: '#69c0ff' }
          ])
        }
      }
    ]
  }
  userChart.setOption(userOption)

  // 数据库性能图
  const dbChart = echarts.init(document.getElementById('db-performance-chart'))
  const dbOption = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['10:00', '11:00', '12:00', '13:00', '14:00']
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '查询/秒',
        type: 'line',
        data: [1200, 1350, 1100, 1250, 1180],
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  dbChart.setOption(dbOption)

  // 网络流量图
  const networkChart = echarts.init(document.getElementById('network-chart'))
  const networkOption = {
    tooltip: { trigger: 'axis' },
    legend: { data: ['入站', '出站'] },
    xAxis: {
      type: 'category',
      data: ['10:00', '11:00', '12:00', '13:00', '14:00']
    },
    yAxis: { type: 'value', name: 'MB/s' },
    series: [
      {
        name: '入站',
        type: 'line',
        data: [12, 15, 18, 14, 16],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '出站',
        type: 'line',
        data: [8, 10, 12, 9, 11],
        itemStyle: { color: '#fa8c16' }
      }
    ]
  }
  networkChart.setOption(networkOption)

  // 响应式调整
  window.addEventListener('resize', () => {
    resourceChart.resize()
    userChart.resize()
    dbChart.resize()
    networkChart.resize()
  })
}

// 性能分析图表
const initPerformanceCharts = () => {
  // CPU分析图
  const cpuChart = echarts.init(document.getElementById('cpu-analysis-chart'))
  cpuChart.setOption({
    title: { text: 'CPU使用率详细分析' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 60 }, (_, i) => `${i}分钟前`)
    },
    yAxis: { type: 'value', max: 100 },
    series: [{
      type: 'line',
      data: Array.from({ length: 60 }, () => Math.floor(Math.random() * 50) + 30),
      smooth: true,
      areaStyle: { opacity: 0.3 }
    }]
  })

  // 内存分析图
  const memoryChart = echarts.init(document.getElementById('memory-analysis-chart'))
  memoryChart.setOption({
    title: { text: '内存使用情况分析' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['已使用', '缓存', '空闲'] },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '已使用',
        type: 'bar',
        stack: 'total',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 40) + 30)
      },
      {
        name: '缓存',
        type: 'bar',
        stack: 'total',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 20) + 10)
      },
      {
        name: '空闲',
        type: 'bar',
        stack: 'total',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 30) + 20)
      }
    ]
  })

  // 磁盘I/O图
  const diskChart = echarts.init(document.getElementById('disk-io-chart'))
  diskChart.setOption({
    title: { text: '磁盘I/O性能分析' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['读取', '写入'] },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 30 }, (_, i) => `${i * 2}分钟前`)
    },
    yAxis: { type: 'value', name: 'MB/s' },
    series: [
      {
        name: '读取',
        type: 'line',
        data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 20),
        smooth: true
      },
      {
        name: '写入',
        type: 'line',
        data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 30) + 10),
        smooth: true
      }
    ]
  })

  // 网络分析图
  const networkAnalysisChart = echarts.init(document.getElementById('network-analysis-chart'))
  networkAnalysisChart.setOption({
    title: { text: '网络连接数分析' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: { type: 'value' },
    series: [{
      name: '连接数',
      type: 'line',
      data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 1000) + 2000),
      smooth: true,
      areaStyle: { opacity: 0.3 }
    }]
  })
}

// 生命周期
onMounted(() => {
  setTimeout(() => {
    initCharts()
  }, 100)
  
  // 设置定时刷新
  refreshTimer.value = setInterval(refreshData, monitorConfig.refreshInterval * 1000)
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.status-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-time,
.stat-peak,
.stat-memory,
.stat-available {
  color: #1890ff;
  font-weight: 500;
}

.service-status {
  max-height: 300px;
  overflow-y: auto;
}

.service-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.service-name {
  font-weight: 500;
}

.service-metrics {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.database-info,
.network-info {
  padding: 16px 0;
}

.db-performance,
.network-chart {
  margin-top: 16px;
}

.alert-card {
  margin-top: 16px;
}

.alert-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.log-filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.log-content {
  max-height: 400px;
  overflow-y: auto;
}

.log-message {
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-wrapper {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .status-cards .ant-col {
    margin-bottom: 16px;
  }
  
  .service-metrics {
    flex-direction: column;
    gap: 4px;
  }
}
</style>