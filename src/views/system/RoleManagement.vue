<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>角色权限</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="角色名称">
                <a-input v-model:value="searchForm.roleName" placeholder="请输入角色名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="角色类型">
                <a-select v-model:value="searchForm.roleType" placeholder="请选择角色类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="system">系统角色</a-select-option>
                  <a-select-option value="business">业务角色</a-select-option>
                  <a-select-option value="custom">自定义角色</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="角色状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="active">启用</a-select-option>
                  <a-select-option value="inactive">停用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="权限模块">
                  <a-select v-model:value="searchForm.module" placeholder="请选择权限模块" allow-clear>
                    <a-select-option value="case">案件管理</a-select-option>
                    <a-select-option value="customer">客户管理</a-select-option>
                    <a-select-option value="collection">催收记录</a-select-option>
                    <a-select-option value="payment">还款管理</a-select-option>
                    <a-select-option value="report">报表统计</a-select-option>
                    <a-select-option value="system">系统设置</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建时间">
                  <a-range-picker 
                    v-model:value="searchForm.createTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建人">
                  <a-input v-model:value="searchForm.creator" placeholder="请输入创建人" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="角色级别">
                  <a-select v-model:value="searchForm.level" placeholder="请选择角色级别" allow-clear>
                    <a-select-option value="1">高级管理</a-select-option>
                    <a-select-option value="2">中级管理</a-select-option>
                    <a-select-option value="3">普通员工</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总角色数" 
              :value="statistics.totalRoles" 
              :value-style="{ color: '#1890ff' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="启用角色" 
              :value="statistics.activeRoles" 
              :value-style="{ color: '#52c41a' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="自定义角色" 
              :value="statistics.customRoles" 
              :value-style="{ color: '#faad14' }"
              suffix="个"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="权限节点" 
              :value="statistics.permissionNodes" 
              :value-style="{ color: '#722ed1' }"
              suffix="个"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              新增角色
            </a-button>
            <a-button @click="showPermissionModal = true">
              <SafetyOutlined />
              权限管理
            </a-button>
            <a-button @click="batchAssign">
              <TeamOutlined />
              批量分配
            </a-button>
            <a-button @click="exportRoles">
              <DownloadOutlined />
              导出配置
            </a-button>
            <a-button @click="importRoles">
              <UploadOutlined />
              导入配置
            </a-button>
            <a-button @click="syncPermissions">
              <SyncOutlined />
              权限同步
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 角色列表 -->
      <a-card title="角色列表">
        <a-table 
          :columns="columns" 
          :data-source="roleList" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'roleType'">
              <a-tag :color="getRoleTypeColor(record.roleType)">
                {{ getRoleTypeText(record.roleType) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'userCount'">
              <a-button type="link" @click="viewRoleUsers(record)">
                {{ record.userCount }}人
              </a-button>
            </template>
            <template v-if="column.key === 'permissions'">
              <a-button type="link" @click="viewPermissions(record)">
                查看权限({{ record.permissionCount }})
              </a-button>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editRole(record)">编辑</a-button>
                <a-button type="link" size="small" @click="copyRole(record)">复制</a-button>
                <a-button type="link" size="small" @click="assignUsers(record)">分配用户</a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="setPermissions(record)">设置权限</a-menu-item>
                      <a-menu-item @click="exportRole(record)">导出角色</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="toggleStatus(record)" :class="{ 'text-danger': record.status === 'active' }">
                        {{ record.status === 'active' ? '停用' : '启用' }}
                      </a-menu-item>
                      <a-menu-item @click="deleteRole(record)" class="text-danger">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 新增/编辑角色弹窗 -->
      <a-modal
        v-model:open="showCreateModal"
        :title="editingRole ? '编辑角色' : '新增角色'"
        width="600px"
        @ok="handleRoleSubmit"
        @cancel="handleRoleCancel"
      >
        <a-form ref="roleFormRef" :model="roleForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="角色名称" name="name" :rules="[{ required: true, message: '请输入角色名称' }]">
            <a-input v-model:value="roleForm.name" placeholder="请输入角色名称" />
          </a-form-item>
          
          <a-form-item label="角色编码" name="code" :rules="[{ required: true, message: '请输入角色编码' }]">
            <a-input v-model:value="roleForm.code" placeholder="请输入角色编码" />
          </a-form-item>
          
          <a-form-item label="角色类型" name="roleType" :rules="[{ required: true, message: '请选择角色类型' }]">
            <a-select v-model:value="roleForm.roleType" placeholder="请选择角色类型">
              <a-select-option value="system">系统角色</a-select-option>
              <a-select-option value="business">业务角色</a-select-option>
              <a-select-option value="custom">自定义角色</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="角色级别" name="level">
            <a-select v-model:value="roleForm.level" placeholder="请选择角色级别">
              <a-select-option value="1">高级管理</a-select-option>
              <a-select-option value="2">中级管理</a-select-option>
              <a-select-option value="3">普通员工</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="排序" name="sort">
            <a-input-number v-model:value="roleForm.sort" :min="0" :max="999" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="角色状态" name="status">
            <a-radio-group v-model:value="roleForm.status">
              <a-radio value="active">启用</a-radio>
              <a-radio value="inactive">停用</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="描述" name="description">
            <a-textarea v-model:value="roleForm.description" :rows="3" placeholder="请输入角色描述" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 权限管理弹窗 -->
      <a-modal
        v-model:open="showPermissionModal"
        title="权限管理"
        width="1200px"
        @ok="handlePermissionSubmit"
        @cancel="showPermissionModal = false"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card title="角色列表" size="small">
              <a-list 
                :data-source="roleList" 
                size="small"
                :style="{ height: '400px', overflowY: 'auto' }"
              >
                <template #renderItem="{ item }">
                  <a-list-item 
                    :class="{ 'selected-role': selectedRole?.id === item.id }"
                    @click="selectRole(item)"
                    style="cursor: pointer;"
                  >
                    <a-list-item-meta>
                      <template #title>{{ item.name }}</template>
                      <template #description>{{ item.description }}</template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
          <a-col :span="16">
            <a-card title="权限配置" size="small">
              <div v-if="selectedRole">
                <a-tree
                  v-model:checkedKeys="checkedKeys"
                  :tree-data="permissionTree"
                  checkable
                  :style="{ height: '400px', overflowY: 'auto' }"
                />
              </div>
              <a-empty v-else description="请选择角色进行权限配置" />
            </a-card>
          </a-col>
        </a-row>
      </a-modal>

      <!-- 用户分配弹窗 -->
      <a-modal
        v-model:open="showUserAssignModal"
        title="分配用户"
        width="800px"
        @ok="handleUserAssign"
        @cancel="showUserAssignModal = false"
      >
        <a-transfer
          v-model:target-keys="targetKeys"
          :data-source="allUsers"
          :titles="['可选用户', '已分配用户']"
          :render="item => item.name"
          show-search
        />
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  PlusOutlined,
  SafetyOutlined,
  TeamOutlined,
  DownloadOutlined,
  UploadOutlined,
  SyncOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showCreateModal = ref(false)
const showPermissionModal = ref(false)
const showUserAssignModal = ref(false)
const editingRole = ref(null)
const selectedRole = ref(null)
const checkedKeys = ref([])
const targetKeys = ref([])

// 搜索表单
const searchForm = reactive({
  roleName: '',
  roleType: undefined,
  status: undefined,
  module: undefined,
  createTimeRange: [],
  creator: '',
  level: undefined
})

// 角色表单
const roleForm = reactive({
  name: '',
  code: '',
  roleType: undefined,
  level: undefined,
  sort: 0,
  status: 'active',
  description: ''
})

// 统计数据
const statistics = reactive({
  totalRoles: 12,
  activeRoles: 10,
  customRoles: 5,
  permissionNodes: 86
})

// 表格配置
const columns = [
  {
    title: '角色名称',
    dataIndex: 'name',
    key: 'name',
    width: 120,
    fixed: 'left'
  },
  {
    title: '角色编码',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '角色类型',
    key: 'roleType',
    width: 100
  },
  {
    title: '用户数量',
    key: 'userCount',
    width: 100
  },
  {
    title: '权限数量',
    key: 'permissions',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 角色数据
const roleList = ref([
  {
    id: 1,
    name: '超级管理员',
    code: 'super_admin',
    roleType: 'system',
    userCount: 2,
    permissionCount: 86,
    status: 'active',
    sort: 1,
    creator: '系统',
    createTime: '2023-01-01 00:00',
    description: '系统超级管理员，拥有所有权限'
  },
  {
    id: 2,
    name: '催收主管',
    code: 'collection_manager',
    roleType: 'business',
    userCount: 8,
    permissionCount: 45,
    status: 'active',
    sort: 2,
    creator: '管理员',
    createTime: '2023-03-15 09:00',
    description: '催收部门主管，负责催收业务管理'
  },
  {
    id: 3,
    name: '催收专员',
    code: 'collector',
    roleType: 'business',
    userCount: 25,
    permissionCount: 28,
    status: 'active',
    sort: 3,
    creator: '管理员',
    createTime: '2023-03-15 09:00',
    description: '催收专员，负责具体催收工作'
  },
  {
    id: 4,
    name: '法务专员',
    code: 'legal_specialist',
    roleType: 'business',
    userCount: 5,
    permissionCount: 32,
    status: 'active',
    sort: 4,
    creator: '管理员',
    createTime: '2023-03-15 09:00',
    description: '法务专员，负责法律事务处理'
  }
])

// 权限树数据
const permissionTree = ref([
  {
    title: '案件管理',
    key: 'case',
    children: [
      { title: '案件查看', key: 'case:view' },
      { title: '案件新增', key: 'case:create' },
      { title: '案件编辑', key: 'case:edit' },
      { title: '案件删除', key: 'case:delete' },
      { title: '案件分配', key: 'case:assign' },
      { title: '案件转移', key: 'case:transfer' }
    ]
  },
  {
    title: '客户管理',
    key: 'customer',
    children: [
      { title: '客户查看', key: 'customer:view' },
      { title: '客户新增', key: 'customer:create' },
      { title: '客户编辑', key: 'customer:edit' },
      { title: '客户删除', key: 'customer:delete' },
      { title: '客户分析', key: 'customer:analysis' }
    ]
  },
  {
    title: '催收记录',
    key: 'collection',
    children: [
      { title: '记录查看', key: 'collection:view' },
      { title: '记录新增', key: 'collection:create' },
      { title: '记录编辑', key: 'collection:edit' },
      { title: '记录删除', key: 'collection:delete' }
    ]
  },
  {
    title: '还款管理',
    key: 'payment',
    children: [
      { title: '还款查看', key: 'payment:view' },
      { title: '还款录入', key: 'payment:create' },
      { title: '还款计划', key: 'payment:plan' },
      { title: '减免管理', key: 'payment:reduction' }
    ]
  },
  {
    title: '报表统计',
    key: 'report',
    children: [
      { title: '报表查看', key: 'report:view' },
      { title: '报表导出', key: 'report:export' },
      { title: '数据分析', key: 'report:analysis' }
    ]
  },
  {
    title: '系统设置',
    key: 'system',
    children: [
      { title: '用户管理', key: 'system:user' },
      { title: '角色管理', key: 'system:role' },
      { title: '系统配置', key: 'system:config' },
      { title: '数据管理', key: 'system:data' }
    ]
  }
])

// 所有用户数据（用于分配）
const allUsers = ref([
  { key: '1', name: '张催收', chosen: false },
  { key: '2', name: '李主管', chosen: true },
  { key: '3', name: '王法务', chosen: false },
  { key: '4', name: '赵客服', chosen: false },
  { key: '5', name: '钱催收', chosen: false }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: roleList.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 状态相关方法
const getRoleTypeColor = (type) => {
  const colors = {
    system: 'red',
    business: 'blue',
    custom: 'green'
  }
  return colors[type] || 'default'
}

const getRoleTypeText = (type) => {
  const texts = {
    system: '系统角色',
    business: '业务角色',
    custom: '自定义角色'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    inactive: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '启用',
    inactive: '停用'
  }
  return texts[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('角色数据已更新')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = key === 'roleType' || key === 'status' || key === 'module' || key === 'level' ? undefined : ''
    }
  })
  handleSearch()
}

const handleRoleSubmit = async () => {
  try {
    loading.value = true
    
    // 表单验证
    await roleFormRef.value.validate()
    
    // 检查角色编码是否已存在
    const existingRole = roleList.value.find(role => 
      role.code === roleForm.code && (!editingRole.value || role.id !== editingRole.value.id)
    )
    
    if (existingRole) {
      message.error('角色编码已存在，请使用其他编码')
      return
    }
    
    if (editingRole.value) {
      // 编辑角色
      const roleIndex = roleList.value.findIndex(role => role.id === editingRole.value.id)
      if (roleIndex !== -1) {
        roleList.value[roleIndex] = {
          ...roleList.value[roleIndex],
          ...roleForm,
          updateTime: new Date().toLocaleString()
        }
        message.success('角色信息更新成功')
      }
    } else {
      // 新增角色
      const newRole = {
        id: Date.now(),
        ...roleForm,
        userCount: 0,
        permissionCount: 0,
        creator: '当前用户',
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      roleList.value.unshift(newRole)
      
      // 更新统计数据
      statistics.totalRoles++
      if (roleForm.status === 'active') {
        statistics.activeRoles++
      }
      if (roleForm.roleType === 'custom') {
        statistics.customRoles++
      }
      
      message.success('角色创建成功')
    }
    
    showCreateModal.value = false
    handleRoleCancel()
  } catch (error) {
    message.error('操作失败：' + (error.message || '请检查表单信息'))
  } finally {
    loading.value = false
  }
}

const handleRoleCancel = () => {
  editingRole.value = null
  Object.keys(roleForm).forEach(key => {
    if (key === 'status') {
      roleForm[key] = 'active'
    } else if (key === 'sort') {
      roleForm[key] = 0
    } else {
      roleForm[key] = ''
    }
  })
}

const editRole = (record) => {
  editingRole.value = record
  Object.keys(roleForm).forEach(key => {
    if (record[key] !== undefined) {
      roleForm[key] = record[key]
    }
  })
  showCreateModal.value = true
}

const copyRole = (record) => {
  message.success(`角色 ${record.name} 复制成功`)
}

const assignUsers = (record) => {
  selectedRole.value = record
  // 设置已分配的用户
  targetKeys.value = allUsers.value.filter(user => user.chosen).map(user => user.key)
  showUserAssignModal.value = true
}

const setPermissions = (record) => {
  selectedRole.value = record
  // 模拟设置当前角色的权限
  checkedKeys.value = ['case:view', 'case:create', 'customer:view', 'collection:view']
  showPermissionModal.value = true
}

const viewRoleUsers = (record) => {
  message.info(`查看角色 ${record.name} 的用户列表`)
}

const viewPermissions = (record) => {
  message.info(`查看角色 ${record.name} 的权限详情`)
}

const exportRole = (record) => {
  message.success(`角色 ${record.name} 配置导出成功`)
}

const toggleStatus = (record) => {
  const newStatus = record.status === 'active' ? 'inactive' : 'active'
  const actionText = newStatus === 'active' ? '启用' : '停用'
  
  if (newStatus === 'inactive' && record.userCount > 0) {
    Modal.confirm({
      title: `${actionText}角色`,
      content: `角色 "${record.name}" 下有 ${record.userCount} 个用户，${actionText}后这些用户将无法使用该角色权限，确定要继续吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        updateRoleStatus(record, newStatus, actionText)
      }
    })
  } else {
    updateRoleStatus(record, newStatus, actionText)
  }
}

const updateRoleStatus = (record, newStatus, actionText) => {
  try {
    // 更新角色状态
    const roleIndex = roleList.value.findIndex(role => role.id === record.id)
    if (roleIndex !== -1) {
      roleList.value[roleIndex].status = newStatus
      roleList.value[roleIndex].updateTime = new Date().toLocaleString()
      
      // 更新统计数据
      if (newStatus === 'active') {
        statistics.activeRoles++
      } else {
        statistics.activeRoles--
      }
      
      message.success(`角色 ${record.name} 已${actionText}`)
    }
  } catch (error) {
    message.error(`${actionText}失败：` + error.message)
  }
}

const deleteRole = (record) => {
  if (record.roleType === 'system') {
    message.error('系统角色不允许删除')
    return
  }
  
  if (record.userCount > 0) {
    message.error(`角色 ${record.name} 下还有 ${record.userCount} 个用户，请先移除用户后再删除`)
    return
  }
  
  Modal.confirm({
    title: '删除角色',
    content: `确定要删除角色 "${record.name}" 吗？删除后无法恢复！`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      try {
        // 从列表中删除
        const roleIndex = roleList.value.findIndex(role => role.id === record.id)
        if (roleIndex !== -1) {
          roleList.value.splice(roleIndex, 1)
          
          // 更新统计数据
          statistics.totalRoles--
          if (record.status === 'active') {
            statistics.activeRoles--
          }
          if (record.roleType === 'custom') {
            statistics.customRoles--
          }
          
          message.success(`角色 ${record.name} 删除成功`)
        }
      } catch (error) {
        message.error('删除失败：' + error.message)
      }
    }
  })
}

const selectRole = (role) => {
  selectedRole.value = role
  // 模拟加载角色权限
  checkedKeys.value = ['case:view', 'case:create', 'customer:view']
}

const handlePermissionSubmit = () => {
  if (!selectedRole.value) {
    message.warning('请选择角色')
    return
  }
  
  try {
    // 更新角色权限
    const roleIndex = roleList.value.findIndex(role => role.id === selectedRole.value.id)
    if (roleIndex !== -1) {
      // 保存权限配置
      roleList.value[roleIndex].permissions = [...checkedKeys.value]
      roleList.value[roleIndex].permissionCount = checkedKeys.value.length
      roleList.value[roleIndex].updateTime = new Date().toLocaleString()
      
      message.success(`角色 ${selectedRole.value.name} 权限配置保存成功`)
      showPermissionModal.value = false
    }
  } catch (error) {
    message.error('权限配置保存失败：' + error.message)
  }
}

const handleUserAssign = () => {
  console.log('分配用户:', targetKeys.value)
  message.success(`用户分配成功`)
  showUserAssignModal.value = false
}

const batchAssign = () => {
  message.info('批量分配用户功能')
}

const exportRoles = () => {
  message.success('角色配置导出成功')
}

const importRoles = () => {
  message.success('角色配置导入成功')
}

const syncPermissions = () => {
  message.success('权限同步成功')
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.text-danger {
  color: #ff4d4f;
}

.selected-role {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
