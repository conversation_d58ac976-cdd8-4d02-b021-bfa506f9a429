<template>
  <div class="strategy-engine">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="strategy-info">
          <h2>策略引擎</h2>
          <p class="strategy-desc">智能催收策略执行引擎，自动化决策流程优化</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshStrategies">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showExecuteModal = true">
            <template #icon><PlayCircleOutlined /></template>
            执行策略
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建策略
          </a-button>
        </div>
      </div>
    </div>

    <!-- 策略统计 -->
    <div class="strategy-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="策略总数" 
              :value="strategyStats.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ThunderboltOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="运行中" 
              :value="strategyStats.running" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日执行" 
              :value="strategyStats.todayExecutions" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><RocketOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="成功率" 
              :value="strategyStats.successRate" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 策略列表 -->
        <a-card title="策略列表" class="strategy-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedType" style="width: 120px" @change="filterStrategies">
                <a-select-option value="all">全部类型</a-select-option>
                <a-select-option value="rule">规则策略</a-select-option>
                <a-select-option value="ml">机器学习</a-select-option>
                <a-select-option value="decision_tree">决策树</a-select-option>
                <a-select-option value="workflow">工作流</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterStrategies">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="active">运行中</a-select-option>
                <a-select-option value="paused">已暂停</a-select-option>
                <a-select-option value="stopped">已停止</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <a-table 
            :columns="strategyColumns" 
            :data-source="filteredStrategies" 
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="strategy-name">
                  <component :is="getStrategyIcon(record.type)" style="margin-right: 8px;" />
                  <span>{{ record.name }}</span>
                </div>
              </template>
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'successRate'">
                <a-progress 
                  :percent="record.successRate" 
                  size="small" 
                  :stroke-color="getSuccessRateColor(record.successRate)"
                />
              </template>
              <template v-if="column.key === 'priority'">
                <a-tag :color="getPriorityColor(record.priority)">
                  {{ getPriorityText(record.priority) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="executeStrategy(record)" :disabled="record.status !== 'active'">
                    执行
                  </a-button>
                  <a-button type="link" size="small" @click="viewStrategy(record)">
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="editStrategy(record)">
                    编辑
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewExecutionHistory(record)">执行历史</a-menu-item>
                        <a-menu-item @click="cloneStrategy(record)">克隆</a-menu-item>
                        <a-menu-item @click="exportStrategy(record)">导出</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="toggleStrategy(record)">
                          {{ record.status === 'active' ? '暂停' : '启动' }}
                        </a-menu-item>
                        <a-menu-item @click="deleteStrategy(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 执行监控 -->
        <a-card title="执行监控" class="strategy-card">
          <div class="execution-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="executionTrendChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="strategyPerformanceChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时执行 -->
        <a-card title="实时执行" class="strategy-card">
          <div class="realtime-execution">
            <div v-for="execution in realtimeExecutions" :key="execution.id" class="execution-item">
              <div class="execution-header">
                <span class="execution-strategy">{{ execution.strategyName }}</span>
                <a-badge 
                  :status="getExecutionStatusBadge(execution.status)" 
                  :text="getExecutionStatusText(execution.status)" 
                />
              </div>
              <div class="execution-progress">
                <a-progress 
                  :percent="execution.progress" 
                  :status="execution.status === 'running' ? 'active' : execution.status === 'completed' ? 'success' : 'exception'"
                  size="small"
                />
              </div>
              <div class="execution-details">
                <div class="detail-row">
                  <span class="detail-label">处理数</span>
                  <span class="detail-value">{{ execution.processed }}/{{ execution.total }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">耗时</span>
                  <span class="detail-value">{{ execution.duration }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">成功率</span>
                  <span class="detail-value">{{ execution.successRate }}%</span>
                </div>
              </div>
            </div>
            <a-empty v-if="realtimeExecutions.length === 0" description="暂无执行任务" size="small" />
          </div>
        </a-card>

        <!-- 策略推荐 -->
        <a-card title="策略推荐" class="strategy-card">
          <div class="strategy-recommendations">
            <div v-for="rec in strategyRecommendations" :key="rec.id" class="rec-item">
              <div class="rec-header">
                <div class="rec-title">{{ rec.title }}</div>
                <div class="rec-confidence">{{ rec.confidence }}%</div>
              </div>
              <div class="rec-content">{{ rec.description }}</div>
              <div class="rec-actions">
                <a-button type="link" size="small" @click="applyRecommendation(rec)">
                  应用
                </a-button>
                <a-button type="link" size="small" @click="viewRecDetail(rec)">
                  详情
                </a-button>
              </div>
            </div>
            <a-empty v-if="strategyRecommendations.length === 0" description="暂无推荐" size="small" />
          </div>
        </a-card>

        <!-- 规则引擎 -->
        <a-card title="规则引擎" class="strategy-card">
          <div class="rule-engine-panel">
            <div class="rule-stats">
              <div class="rule-stat">
                <span class="stat-label">活跃规则</span>
                <span class="stat-value">{{ ruleEngineStats.activeRules }}</span>
              </div>
              <div class="rule-stat">
                <span class="stat-label">今日触发</span>
                <span class="stat-value">{{ ruleEngineStats.todayTriggers }}</span>
              </div>
              <div class="rule-stat">
                <span class="stat-label">命中率</span>
                <span class="stat-value">{{ ruleEngineStats.hitRate }}%</span>
              </div>
            </div>
            <div class="rule-actions">
              <a-button type="primary" block @click="showRuleConfigModal = true">
                规则配置
              </a-button>
              <a-button block @click="showRuleTestModal = true" style="margin-top: 8px;">
                规则测试
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 创建策略模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建策略"
      width="700px"
      @ok="createStrategy"
    >
      <a-form layout="vertical">
        <a-form-item label="策略名称" required>
          <a-input v-model:value="strategyForm.name" placeholder="请输入策略名称" />
        </a-form-item>
        <a-form-item label="策略类型" required>
          <a-select v-model:value="strategyForm.type" placeholder="选择策略类型">
            <a-select-option value="rule">规则策略</a-select-option>
            <a-select-option value="ml">机器学习</a-select-option>
            <a-select-option value="decision_tree">决策树</a-select-option>
            <a-select-option value="workflow">工作流</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" required>
          <a-select v-model:value="strategyForm.priority" placeholder="选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="适用场景">
          <a-checkbox-group v-model:value="strategyForm.scenarios">
            <a-checkbox value="overdue">逾期催收</a-checkbox>
            <a-checkbox value="risk">风险评估</a-checkbox>
            <a-checkbox value="payment">还款提醒</a-checkbox>
            <a-checkbox value="follow">跟进策略</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="策略描述">
          <a-textarea v-model:value="strategyForm.description" placeholder="策略用途和规则说明" :rows="4" />
        </a-form-item>
        <a-form-item label="规则配置" v-if="strategyForm.type === 'rule'">
          <div class="rule-config">
            <a-button @click="addRule" style="margin-bottom: 8px;">
              <template #icon><PlusOutlined /></template>
              添加规则
            </a-button>
            <div v-for="(rule, index) in strategyForm.rules" :key="index" class="rule-item">
              <a-row :gutter="8">
                <a-col :span="6">
                  <a-select v-model:value="rule.field" placeholder="字段">
                    <a-select-option value="amount">金额</a-select-option>
                    <a-select-option value="overdue_days">逾期天数</a-select-option>
                    <a-select-option value="risk_level">风险等级</a-select-option>
                    <a-select-option value="contact_count">联系次数</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="rule.operator" placeholder="条件">
                    <a-select-option value=">">&gt;</a-select-option>
                    <a-select-option value="<">&lt;</a-select-option>
                    <a-select-option value="=">=</a-select-option>
                    <a-select-option value=">=">>=</a-select-option>
                    <a-select-option value="<="><=</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-input v-model:value="rule.value" placeholder="值" />
                </a-col>
                <a-col :span="6">
                  <a-select v-model:value="rule.action" placeholder="动作">
                    <a-select-option value="call">电话催收</a-select-option>
                    <a-select-option value="sms">短信提醒</a-select-option>
                    <a-select-option value="visit">外访</a-select-option>
                    <a-select-option value="legal">法务处理</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="2">
                  <a-button type="text" @click="removeRule(index)" danger>
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 执行策略模态框 -->
    <a-modal
      v-model:open="showExecuteModal"
      title="执行策略"
      width="500px"
      @ok="submitExecution"
    >
      <a-form layout="vertical">
        <a-form-item label="选择策略">
          <a-select v-model:value="executeForm.strategyId" placeholder="选择要执行的策略">
            <a-select-option v-for="strategy in strategies.filter(s => s.status === 'active')" :key="strategy.id" :value="strategy.id">
              {{ strategy.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="执行范围">
          <a-radio-group v-model:value="executeForm.scope">
            <a-radio value="all">全部数据</a-radio>
            <a-radio value="selected">指定条件</a-radio>
            <a-radio value="batch">批量数据</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="执行模式">
          <a-select v-model:value="executeForm.mode" placeholder="选择执行模式">
            <a-select-option value="immediate">立即执行</a-select-option>
            <a-select-option value="scheduled">定时执行</a-select-option>
            <a-select-option value="manual">手动触发</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="并发数量">
          <a-input-number v-model:value="executeForm.concurrency" :min="1" :max="100" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 规则配置模态框 -->
    <a-modal
      v-model:open="showRuleConfigModal"
      title="规则配置"
      width="800px"
      @ok="saveRuleConfig"
    >
      <div class="rule-config-content">
        <a-alert 
          message="规则配置说明" 
          description="配置规则引擎的全局参数和执行策略，规则按优先级从高到低执行。" 
          type="info" 
          show-icon 
          style="margin-bottom: 16px;"
        />
        <a-form layout="vertical">
          <a-form-item label="执行模式">
            <a-radio-group v-model:value="ruleConfig.executionMode">
              <a-radio value="sequential">顺序执行</a-radio>
              <a-radio value="parallel">并行执行</a-radio>
              <a-radio value="priority">优先级执行</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="最大执行时间(秒)">
            <a-input-number v-model:value="ruleConfig.maxExecutionTime" :min="1" :max="3600" style="width: 100%" />
          </a-form-item>
          <a-form-item label="失败重试次数">
            <a-input-number v-model:value="ruleConfig.retryCount" :min="0" :max="5" style="width: 100%" />
          </a-form-item>
          <a-form-item label="启用规则缓存">
            <a-switch v-model:checked="ruleConfig.enableCache" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 规则测试模态框 -->
    <a-modal
      v-model:open="showRuleTestModal"
      title="规则测试"
      width="600px"
      @ok="runRuleTest"
    >
      <a-form layout="vertical">
        <a-form-item label="测试数据">
          <a-textarea v-model:value="ruleTestForm.testData" placeholder="输入JSON格式的测试数据" :rows="6" />
        </a-form-item>
        <a-form-item label="测试规则">
          <a-select v-model:value="ruleTestForm.ruleId" placeholder="选择要测试的规则">
            <a-select-option v-for="strategy in strategies.filter(s => s.type === 'rule')" :key="strategy.id" :value="strategy.id">
              {{ strategy.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
      <div v-if="testResult" class="test-result">
        <h4>测试结果：</h4>
        <a-alert 
          :type="testResult.success ? 'success' : 'error'" 
          :message="testResult.message" 
          :description="testResult.details"
          show-icon 
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  ThunderboltOutlined,
  RocketOutlined,
  TrophyOutlined,
  DownOutlined,
  DeleteOutlined,
  BranchesOutlined,
  NodeIndexOutlined,
  ClusterOutlined,
  PartitionOutlined,
  FunctionOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showCreateModal = ref(false)
const showExecuteModal = ref(false)
const showRuleConfigModal = ref(false)
const showRuleTestModal = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')

// 图表引用
const executionTrendChart = ref(null)
const strategyPerformanceChart = ref(null)

// 策略统计
const strategyStats = reactive({
  total: 18,
  running: 12,
  todayExecutions: 2845,
  successRate: 94.2
})

// 策略列表
const strategies = ref([
  {
    id: 1,
    name: '高风险客户催收策略',
    type: 'rule',
    status: 'active',
    priority: 'high',
    successRate: 92.5,
    executionCount: 1245,
    createTime: '2023-07-20',
    lastExecuted: '2023-07-28 10:30:00'
  },
  {
    id: 2,
    name: '智能还款提醒策略',
    type: 'ml',
    status: 'active',
    priority: 'medium',
    successRate: 96.8,
    executionCount: 3256,
    createTime: '2023-07-18',
    lastExecuted: '2023-07-28 09:15:00'
  },
  {
    id: 3,
    name: '逾期分级处理策略',
    type: 'decision_tree',
    status: 'active',
    priority: 'high',
    successRate: 88.3,
    executionCount: 856,
    createTime: '2023-07-22',
    lastExecuted: '2023-07-28 11:20:00'
  },
  {
    id: 4,
    name: '客户跟进工作流',
    type: 'workflow',
    status: 'paused',
    priority: 'low',
    successRate: 85.7,
    executionCount: 432,
    createTime: '2023-07-15',
    lastExecuted: '2023-07-27 16:45:00'
  },
  {
    id: 5,
    name: '风险预警自动策略',
    type: 'rule',
    status: 'active',
    priority: 'high',
    successRate: 91.2,
    executionCount: 2156,
    createTime: '2023-07-12',
    lastExecuted: '2023-07-28 08:30:00'
  }
])

// 实时执行
const realtimeExecutions = ref([
  {
    id: 1,
    strategyName: '高风险客户催收策略',
    status: 'running',
    progress: 68,
    processed: 680,
    total: 1000,
    duration: '15分钟',
    successRate: 92.5
  },
  {
    id: 2,
    strategyName: '智能还款提醒策略',
    status: 'completed',
    progress: 100,
    processed: 1500,
    total: 1500,
    duration: '8分钟',
    successRate: 96.8
  },
  {
    id: 3,
    strategyName: '逾期分级处理策略',
    status: 'failed',
    progress: 45,
    processed: 225,
    total: 500,
    duration: '12分钟',
    successRate: 88.3
  }
])

// 策略推荐
const strategyRecommendations = ref([
  {
    id: 1,
    title: '优化高风险客户策略',
    confidence: 95,
    description: '建议调整风险阈值，可提升策略效果8-12%'
  },
  {
    id: 2,
    title: '增加机器学习模型',
    confidence: 88,
    description: '结合历史数据训练新模型，提高催收精准度'
  },
  {
    id: 3,
    title: '调整执行时机',
    confidence: 82,
    description: '根据客户活跃时间优化策略执行时间'
  }
])

// 规则引擎统计
const ruleEngineStats = reactive({
  activeRules: 45,
  todayTriggers: 2845,
  hitRate: 94.2
})

// 表格列定义
const strategyColumns = [
  {
    title: '策略名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 100
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate',
    width: 120
  },
  {
    title: '执行次数',
    dataIndex: 'executionCount',
    key: 'executionCount',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 表单数据
const strategyForm = reactive({
  name: '',
  type: '',
  priority: 'medium',
  scenarios: [],
  description: '',
  rules: []
})

const executeForm = reactive({
  strategyId: null,
  scope: 'all',
  mode: 'immediate',
  concurrency: 10
})

const ruleConfig = reactive({
  executionMode: 'priority',
  maxExecutionTime: 300,
  retryCount: 3,
  enableCache: true
})

const ruleTestForm = reactive({
  testData: '',
  ruleId: null
})

const testResult = ref(null)

// 计算属性
const filteredStrategies = computed(() => {
  let filtered = strategies.value
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(item => item.type === selectedType.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getStrategyIcon = (type) => {
  const icons = {
    rule: 'BranchesOutlined',
    ml: 'NodeIndexOutlined',
    decision_tree: 'PartitionOutlined',
    workflow: 'ClusterOutlined'
  }
  return icons[type] || 'FunctionOutlined'
}

const getTypeColor = (type) => {
  const colors = {
    rule: 'blue',
    ml: 'green',
    decision_tree: 'orange',
    workflow: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    rule: '规则策略',
    ml: '机器学习',
    decision_tree: '决策树',
    workflow: '工作流'
  }
  return texts[type] || type
}

const getStatusBadge = (status) => {
  const badges = {
    active: 'success',
    paused: 'warning',
    stopped: 'default'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '运行中',
    paused: '已暂停',
    stopped: '已停止'
  }
  return texts[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getSuccessRateColor = (rate) => {
  if (rate >= 95) return '#52c41a'
  if (rate >= 85) return '#faad14'
  return '#ff4d4f'
}

const getExecutionStatusBadge = (status) => {
  const badges = {
    running: 'processing',
    completed: 'success',
    failed: 'error'
  }
  return badges[status] || 'default'
}

const getExecutionStatusText = (status) => {
  const texts = {
    running: '执行中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

const refreshStrategies = () => {
  console.log('刷新策略列表')
  message.success('策略列表已刷新')
}

const filterStrategies = () => {
  console.log('筛选策略:', selectedType.value, selectedStatus.value)
}

const executeStrategy = (record) => {
  executeForm.strategyId = record.id
  showExecuteModal.value = true
}

const viewStrategy = (record) => {
  console.log('查看策略:', record)
}

const editStrategy = (record) => {
  console.log('编辑策略:', record)
  Object.assign(strategyForm, record)
  showCreateModal.value = true
}

const viewExecutionHistory = (record) => {
  console.log('查看执行历史:', record)
}

const cloneStrategy = (record) => {
  console.log('克隆策略:', record)
  message.success('策略克隆成功')
}

const exportStrategy = (record) => {
  console.log('导出策略:', record)
  message.success('策略导出成功')
}

const toggleStrategy = (record) => {
  const newStatus = record.status === 'active' ? 'paused' : 'active'
  record.status = newStatus
  message.success(`策略已${newStatus === 'active' ? '启动' : '暂停'}`)
}

const deleteStrategy = (record) => {
  const index = strategies.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    strategies.value.splice(index, 1)
    message.success('策略删除成功')
  }
}

const addRule = () => {
  strategyForm.rules.push({
    field: '',
    operator: '',
    value: '',
    action: ''
  })
}

const removeRule = (index) => {
  strategyForm.rules.splice(index, 1)
}

const createStrategy = () => {
  console.log('创建策略:', strategyForm)
  const newStrategy = {
    id: Date.now(),
    ...strategyForm,
    status: 'active',
    successRate: 0,
    executionCount: 0,
    createTime: new Date().toISOString().split('T')[0],
    lastExecuted: ''
  }
  strategies.value.push(newStrategy)
  message.success('策略创建成功')
  showCreateModal.value = false
  
  // 重置表单
  Object.keys(strategyForm).forEach(key => {
    if (key === 'scenarios' || key === 'rules') {
      strategyForm[key] = []
    } else if (key === 'priority') {
      strategyForm[key] = 'medium'
    } else {
      strategyForm[key] = ''
    }
  })
}

const submitExecution = () => {
  console.log('提交执行:', executeForm)
  message.success('策略执行任务已提交')
  showExecuteModal.value = false
}

const saveRuleConfig = () => {
  console.log('保存规则配置:', ruleConfig)
  message.success('规则配置保存成功')
  showRuleConfigModal.value = false
}

const runRuleTest = () => {
  console.log('运行规则测试:', ruleTestForm)
  
  // 模拟测试结果
  setTimeout(() => {
    testResult.value = {
      success: Math.random() > 0.3,
      message: Math.random() > 0.3 ? '规则测试通过' : '规则测试失败',
      details: Math.random() > 0.3 ? '规则逻辑正确，测试数据符合预期' : '规则条件不匹配，请检查测试数据'
    }
  }, 1000)
}

const applyRecommendation = (rec) => {
  console.log('应用推荐:', rec)
  message.success('推荐已应用')
}

const viewRecDetail = (rec) => {
  console.log('查看推荐详情:', rec)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 执行趋势图
    if (executionTrendChart.value) {
      const chart1 = echarts.init(executionTrendChart.value)
      chart1.setOption({
        title: { text: '策略执行趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        },
        yAxis: { type: 'value', name: '执行次数' },
        series: [
          {
            name: '成功',
            type: 'line',
            data: [45, 38, 125, 285, 352, 248, 153],
            smooth: true,
            itemStyle: { color: '#52c41a' }
          },
          {
            name: '失败',
            type: 'line',
            data: [5, 8, 15, 25, 18, 12, 8],
            smooth: true,
            itemStyle: { color: '#ff4d4f' }
          }
        ]
      })
    }

    // 策略性能图
    if (strategyPerformanceChart.value) {
      const chart2 = echarts.init(strategyPerformanceChart.value)
      chart2.setOption({
        title: { text: '策略性能对比', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        xAxis: {
          type: 'value',
          name: '成功率(%)'
        },
        yAxis: {
          type: 'category',
          data: ['高风险催收', '智能提醒', '逾期分级', '客户跟进', '风险预警']
        },
        series: [{
          type: 'bar',
          data: [92.5, 96.8, 88.3, 85.7, 91.2],
          itemStyle: {
            color: function(params) {
              const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
              return colors[params.dataIndex]
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.strategy-engine {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strategy-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.strategy-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.strategy-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.strategy-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.strategy-name {
  display: flex;
  align-items: center;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.realtime-execution {
  max-height: 300px;
  overflow-y: auto;
}

.execution-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.execution-strategy {
  font-weight: 500;
  color: #262626;
}

.execution-progress {
  margin-bottom: 8px;
}

.execution-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.detail-label {
  color: #999;
}

.detail-value {
  color: #666;
}

.strategy-recommendations {
  max-height: 300px;
  overflow-y: auto;
}

.rec-item {
  padding: 12px;
  border-left: 3px solid #1890ff;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.rec-item:last-child {
  border-bottom: none;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.rec-title {
  font-weight: 500;
  color: #262626;
}

.rec-confidence {
  color: #1890ff;
  font-weight: 500;
}

.rec-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.rec-actions {
  display: flex;
  gap: 8px;
}

.rule-engine-panel {
  padding: 8px 0;
}

.rule-stats {
  margin-bottom: 16px;
}

.rule-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.rule-stat:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 13px;
  color: #666;
}

.stat-value {
  font-weight: 500;
  color: #262626;
}

.rule-config {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.rule-item {
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: white;
}

.rule-config-content {
  max-height: 500px;
  overflow-y: auto;
}

.test-result {
  margin-top: 16px;
}

.test-result h4 {
  margin-bottom: 8px;
}
</style>