<template>
  <div class="effect-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="effect-info">
          <h2>效果监控</h2>
          <p class="effect-desc">实时监控决策效果，量化分析策略执行结果与业务指标</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showReportModal = true">
            <template #icon><FileTextOutlined /></template>
            生成报告
          </a-button>
          <a-button type="primary" @click="showConfigModal = true">
            <template #icon><SettingOutlined /></template>
            监控配置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 效果统计 -->
    <div class="effect-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="总执行次数" 
              :value="effectStats.totalExecutions" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ThunderboltOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="成功率" 
              :value="effectStats.successRate" 
              :value-style="{ color: '#52c41a' }"
              suffix="%"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="平均响应时间" 
              :value="effectStats.avgResponseTime" 
              :value-style="{ color: '#faad14' }"
              suffix="ms"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="ROI提升" 
              :value="effectStats.roiImprovement" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><RiseOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 效果趋势 -->
        <a-card title="效果趋势分析" class="effect-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedTimeRange" style="width: 120px" @change="updateTrendChart">
                <a-select-option value="7d">近7天</a-select-option>
                <a-select-option value="30d">近30天</a-select-option>
                <a-select-option value="90d">近90天</a-select-option>
                <a-select-option value="1y">近1年</a-select-option>
              </a-select>
              <a-select v-model:value="selectedMetric" style="width: 120px" @change="updateTrendChart">
                <a-select-option value="success_rate">成功率</a-select-option>
                <a-select-option value="response_time">响应时间</a-select-option>
                <a-select-option value="roi">ROI</a-select-option>
                <a-select-option value="conversion">转化率</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <div class="trend-dashboard">
            <div ref="effectTrendChart" class="chart-container"></div>
          </div>
        </a-card>

        <!-- 策略效果对比 -->
        <a-card title="策略效果对比" class="effect-card">
          <div class="comparison-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="strategyComparisonChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="metricDistributionChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 详细数据表格 -->
        <a-card title="执行详情" class="effect-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedStrategy" style="width: 150px" @change="filterExecutions">
                <a-select-option value="all">全部策略</a-select-option>
                <a-select-option value="collection">催收策略</a-select-option>
                <a-select-option value="risk">风险策略</a-select-option>
                <a-select-option value="payment">还款策略</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterExecutions">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="success">成功</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
                <a-select-option value="timeout">超时</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <a-table 
            :columns="executionColumns" 
            :data-source="filteredExecutions" 
            :pagination="{ pageSize: 10 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'strategyName'">
                <div class="strategy-name">
                  <component :is="getStrategyIcon(record.strategyType)" style="margin-right: 8px;" />
                  {{ record.strategyName }}
                </div>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'responseTime'">
                <span :class="getResponseTimeClass(record.responseTime)">
                  {{ record.responseTime }}ms
                </span>
              </template>
              <template v-if="column.key === 'effectScore'">
                <a-progress 
                  :percent="record.effectScore" 
                  size="small" 
                  :stroke-color="getEffectScoreColor(record.effectScore)"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewExecutionDetail(record)">
                    详情
                  </a-button>
                  <a-button type="link" size="small" @click="analyzeExecution(record)">
                    分析
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时监控 -->
        <a-card title="实时监控" class="effect-card">
          <div class="realtime-monitor">
            <div class="monitor-item">
              <div class="monitor-label">当前QPS</div>
              <div class="monitor-value primary">{{ realtimeData.currentQPS }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">活跃连接</div>
              <div class="monitor-value success">{{ realtimeData.activeConnections }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">错误率</div>
              <div class="monitor-value error">{{ realtimeData.errorRate }}%</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">内存使用</div>
              <div class="monitor-value warning">{{ realtimeData.memoryUsage }}%</div>
            </div>
          </div>
          <div class="realtime-chart">
            <div ref="realtimeChart" class="mini-chart"></div>
          </div>
        </a-card>

        <!-- 异常告警 -->
        <a-card title="异常告警" class="effect-card">
          <template #extra>
            <a-badge :count="alerts.filter(a => !a.read).length" />
          </template>
          <div class="alert-list">
            <div v-for="alert in alerts" :key="alert.id" class="alert-item" :class="{ 'unread': !alert.read }">
              <div class="alert-header">
                <div class="alert-type" :class="getAlertClass(alert.level)">
                  {{ getAlertLevelText(alert.level) }}
                </div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
              <div class="alert-content">{{ alert.message }}</div>
              <div class="alert-actions">
                <a-button v-if="!alert.read" type="link" size="small" @click="markAlertRead(alert)">
                  标记已读
                </a-button>
                <a-button type="link" size="small" @click="handleAlert(alert)">
                  处理
                </a-button>
              </div>
            </div>
            <a-empty v-if="alerts.length === 0" description="暂无告警" size="small" />
          </div>
        </a-card>

        <!-- 关键指标 -->
        <a-card title="关键指标" class="effect-card">
          <div class="key-metrics">
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">催收成功率</span>
                <span class="metric-value">{{ keyMetrics.collectionRate }}%</span>
              </div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="getTrendClass(keyMetrics.collectionTrend)">
                  {{ keyMetrics.collectionTrend > 0 ? '↗' : '↘' }} {{ Math.abs(keyMetrics.collectionTrend) }}%
                </span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">平均回收金额</span>
                <span class="metric-value">¥{{ formatNumber(keyMetrics.avgRecovery) }}</span>
              </div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="getTrendClass(keyMetrics.recoveryTrend)">
                  {{ keyMetrics.recoveryTrend > 0 ? '↗' : '↘' }} {{ Math.abs(keyMetrics.recoveryTrend) }}%
                </span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">客户满意度</span>
                <span class="metric-value">{{ keyMetrics.satisfaction }}分</span>
              </div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="getTrendClass(keyMetrics.satisfactionTrend)">
                  {{ keyMetrics.satisfactionTrend > 0 ? '↗' : '↘' }} {{ Math.abs(keyMetrics.satisfactionTrend) }}
                </span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">处理时长</span>
                <span class="metric-value">{{ keyMetrics.avgDuration }}min</span>
              </div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="getTrendClass(-keyMetrics.durationTrend)">
                  {{ keyMetrics.durationTrend > 0 ? '↗' : '↘' }} {{ Math.abs(keyMetrics.durationTrend) }}%
                </span>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 性能建议 -->
        <a-card title="性能建议" class="effect-card">
          <div class="performance-suggestions">
            <div v-for="suggestion in suggestions" :key="suggestion.id" class="suggestion-item">
              <div class="suggestion-header">
                <div class="suggestion-priority" :class="getPriorityClass(suggestion.priority)">
                  {{ getPriorityText(suggestion.priority) }}
                </div>
                <div class="suggestion-impact">+{{ suggestion.impact }}%</div>
              </div>
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-description">{{ suggestion.description }}</div>
              <div class="suggestion-actions">
                <a-button type="link" size="small" @click="applySuggestion(suggestion)">
                  应用
                </a-button>
                <a-button type="link" size="small" @click="ignoreSuggestion(suggestion)">
                  忽略
                </a-button>
              </div>
            </div>
            <a-empty v-if="suggestions.length === 0" description="暂无建议" size="small" />
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 生成报告模态框 -->
    <a-modal
      v-model:open="showReportModal"
      title="生成效果监控报告"
      width="600px"
      @ok="generateReport"
    >
      <a-form layout="vertical">
        <a-form-item label="报告类型">
          <a-select v-model:value="reportForm.type" placeholder="选择报告类型">
            <a-select-option value="daily">日报</a-select-option>
            <a-select-option value="weekly">周报</a-select-option>
            <a-select-option value="monthly">月报</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时间范围">
          <a-range-picker 
            v-model:value="reportForm.dateRange" 
            style="width: 100%"
            :disabled="reportForm.type !== 'custom'"
          />
        </a-form-item>
        <a-form-item label="包含内容">
          <a-checkbox-group v-model:value="reportForm.contents">
            <a-checkbox value="summary">执行概要</a-checkbox>
            <a-checkbox value="trends">趋势分析</a-checkbox>
            <a-checkbox value="comparison">策略对比</a-checkbox>
            <a-checkbox value="metrics">关键指标</a-checkbox>
            <a-checkbox value="alerts">异常告警</a-checkbox>
            <a-checkbox value="suggestions">优化建议</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="输出格式">
          <a-radio-group v-model:value="reportForm.format">
            <a-radio value="pdf">PDF</a-radio>
            <a-radio value="excel">Excel</a-radio>
            <a-radio value="html">HTML</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 监控配置模态框 -->
    <a-modal
      v-model:open="showConfigModal"
      title="监控配置"
      width="500px"
      @ok="saveMonitorConfig"
    >
      <a-form layout="vertical">
        <a-form-item label="监控频率(秒)">
          <a-input-number v-model:value="monitorConfig.frequency" :min="10" :max="3600" style="width: 100%" />
        </a-form-item>
        <a-form-item label="告警阈值">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="错误率(%)">
                <a-input-number v-model:value="monitorConfig.errorThreshold" :min="0" :max="100" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="响应时间(ms)">
                <a-input-number v-model:value="monitorConfig.responseThreshold" :min="100" :max="10000" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="数据保留天数">
          <a-input-number v-model:value="monitorConfig.retentionDays" :min="7" :max="365" style="width: 100%" />
        </a-form-item>
        <a-form-item label="启用自动告警">
          <a-switch v-model:checked="monitorConfig.autoAlert" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  FileTextOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  PhoneOutlined,
  SafetyOutlined,
  DollarOutlined,
  UserOutlined,
  BranchesOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showReportModal = ref(false)
const showConfigModal = ref(false)
const selectedTimeRange = ref('30d')
const selectedMetric = ref('success_rate')
const selectedStrategy = ref('all')
const selectedStatus = ref('all')

// 图表引用
const effectTrendChart = ref(null)
const strategyComparisonChart = ref(null)
const metricDistributionChart = ref(null)
const realtimeChart = ref(null)

// 效果统计
const effectStats = reactive({
  totalExecutions: 28456,
  successRate: 94.2,
  avgResponseTime: 245,
  roiImprovement: 23.5
})

// 实时监控数据
const realtimeData = reactive({
  currentQPS: 125,
  activeConnections: 256,
  errorRate: 2.1,
  memoryUsage: 68
})

// 执行记录
const executionRecords = ref([
  {
    id: 1,
    strategyName: '高风险客户催收策略',
    strategyType: 'collection',
    status: 'success',
    responseTime: 156,
    effectScore: 92,
    executionTime: '2023-07-28 10:30:00',
    processedCount: 125,
    successCount: 115
  },
  {
    id: 2,
    strategyName: '智能还款提醒策略',
    strategyType: 'payment',
    status: 'success',
    responseTime: 89,
    effectScore: 96,
    executionTime: '2023-07-28 09:15:00',
    processedCount: 256,
    successCount: 246
  },
  {
    id: 3,
    strategyName: '风险评估模型',
    strategyType: 'risk',
    status: 'failed',
    responseTime: 3200,
    effectScore: 45,
    executionTime: '2023-07-28 08:45:00',
    processedCount: 89,
    successCount: 40
  },
  {
    id: 4,
    strategyName: '客户分群策略',
    strategyType: 'collection',
    status: 'timeout',
    responseTime: 5000,
    effectScore: 38,
    executionTime: '2023-07-28 11:20:00',
    processedCount: 68,
    successCount: 26
  }
])

// 告警列表
const alerts = ref([
  {
    id: 1,
    level: 'high',
    time: '5分钟前',
    message: '风险评估模型响应时间超过3秒，建议检查系统负载',
    read: false
  },
  {
    id: 2,
    level: 'medium',
    time: '15分钟前',
    message: '催收策略成功率低于95%，建议优化规则配置',
    read: false
  },
  {
    id: 3,
    level: 'low',
    time: '1小时前',
    message: '系统内存使用率较高，建议关注资源消耗',
    read: true
  }
])

// 关键指标
const keyMetrics = reactive({
  collectionRate: 94.2,
  collectionTrend: 2.3,
  avgRecovery: 15680,
  recoveryTrend: 5.1,
  satisfaction: 4.2,
  satisfactionTrend: 0.3,
  avgDuration: 12.5,
  durationTrend: -1.2
})

// 性能建议
const suggestions = ref([
  {
    id: 1,
    priority: 'high',
    impact: 15,
    title: '优化规则执行顺序',
    description: '调整高优先级规则的执行顺序，可提升整体响应速度'
  },
  {
    id: 2,
    priority: 'medium',
    impact: 8,
    title: '增加缓存机制',
    description: '为频繁查询的数据增加缓存，减少数据库访问次数'
  },
  {
    id: 3,
    priority: 'low',
    impact: 3,
    title: '优化数据库查询',
    description: '优化复杂查询语句，提升数据检索效率'
  }
])

// 表格列定义
const executionColumns = [
  {
    title: '策略名称',
    dataIndex: 'strategyName',
    key: 'strategyName',
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '响应时间',
    dataIndex: 'responseTime',
    key: 'responseTime',
    width: 100
  },
  {
    title: '效果评分',
    dataIndex: 'effectScore',
    key: 'effectScore',
    width: 120
  },
  {
    title: '处理数量',
    dataIndex: 'processedCount',
    key: 'processedCount',
    width: 100
  },
  {
    title: '执行时间',
    dataIndex: 'executionTime',
    key: 'executionTime',
    width: 140
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
]

// 表单数据
const reportForm = reactive({
  type: 'weekly',
  dateRange: null,
  contents: ['summary', 'trends', 'metrics'],
  format: 'pdf'
})

const monitorConfig = reactive({
  frequency: 60,
  errorThreshold: 5,
  responseThreshold: 1000,
  retentionDays: 30,
  autoAlert: true
})

// 计算属性
const filteredExecutions = computed(() => {
  let filtered = executionRecords.value
  
  if (selectedStrategy.value !== 'all') {
    filtered = filtered.filter(item => item.strategyType === selectedStrategy.value)
  }
  
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === selectedStatus.value)
  }
  
  return filtered
})

// 方法定义
const getStrategyIcon = (type) => {
  const icons = {
    collection: 'PhoneOutlined',
    risk: 'SafetyOutlined',
    payment: 'DollarOutlined',
    follow: 'UserOutlined'
  }
  return icons[type] || 'BranchesOutlined'
}

const getStatusBadge = (status) => {
  const badges = {
    success: 'success',
    failed: 'error',
    timeout: 'warning'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    timeout: '超时'
  }
  return texts[status] || status
}

const getResponseTimeClass = (time) => {
  if (time < 200) return 'response-fast'
  if (time < 1000) return 'response-normal'
  return 'response-slow'
}

const getEffectScoreColor = (score) => {
  if (score >= 90) return '#52c41a'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getAlertClass = (level) => {
  return `alert-${level}`
}

const getAlertLevelText = (level) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[level] || level
}

const getTrendClass = (trend) => {
  return trend > 0 ? 'trend-up' : 'trend-down'
}

const getPriorityClass = (priority) => {
  return `priority-${priority}`
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const formatNumber = (num) => {
  return num.toLocaleString()
}

const refreshData = () => {
  console.log('刷新监控数据')
  message.success('监控数据已刷新')
}

const updateTrendChart = () => {
  console.log('更新趋势图:', selectedTimeRange.value, selectedMetric.value)
  initCharts()
}

const filterExecutions = () => {
  console.log('筛选执行记录:', selectedStrategy.value, selectedStatus.value)
}

const viewExecutionDetail = (record) => {
  console.log('查看执行详情:', record)
}

const analyzeExecution = (record) => {
  console.log('分析执行结果:', record)
}

const markAlertRead = (alert) => {
  alert.read = true
}

const handleAlert = (alert) => {
  console.log('处理告警:', alert)
  message.info('正在处理告警...')
}

const applySuggestion = (suggestion) => {
  console.log('应用建议:', suggestion)
  message.success('建议已应用')
}

const ignoreSuggestion = (suggestion) => {
  const index = suggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    suggestions.value.splice(index, 1)
    message.info('建议已忽略')
  }
}

const generateReport = () => {
  console.log('生成报告:', reportForm)
  message.success('报告生成中，请稍后下载')
  showReportModal.value = false
}

const saveMonitorConfig = () => {
  console.log('保存监控配置:', monitorConfig)
  message.success('监控配置保存成功')
  showConfigModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 效果趋势图
    if (effectTrendChart.value) {
      const chart1 = echarts.init(effectTrendChart.value)
      chart1.setOption({
        title: { text: '效果趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['7/22', '7/23', '7/24', '7/25', '7/26', '7/27', '7/28']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '成功率',
            type: 'line',
            data: [91.2, 92.8, 94.1, 93.5, 94.8, 95.2, 94.2],
            smooth: true,
            itemStyle: { color: '#52c41a' }
          },
          {
            name: '响应时间',
            type: 'line',
            data: [280, 245, 220, 235, 210, 195, 245],
            smooth: true,
            yAxisIndex: 1,
            itemStyle: { color: '#1890ff' }
          }
        ],
        yAxis: [
          { type: 'value', name: '成功率(%)' },
          { type: 'value', name: '响应时间(ms)' }
        ]
      })
    }

    // 策略对比图
    if (strategyComparisonChart.value) {
      const chart2 = echarts.init(strategyComparisonChart.value)
      chart2.setOption({
        title: { text: '策略效果对比', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        xAxis: {
          type: 'value',
          name: '成功率(%)'
        },
        yAxis: {
          type: 'category',
          data: ['催收策略', '还款策略', '风险策略', '跟进策略']
        },
        series: [{
          type: 'bar',
          data: [94.2, 96.8, 88.3, 91.5],
          itemStyle: {
            color: function(params) {
              const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
              return colors[params.dataIndex]
            }
          }
        }]
      })
    }

    // 指标分布图
    if (metricDistributionChart.value) {
      const chart3 = echarts.init(metricDistributionChart.value)
      chart3.setOption({
        title: { text: '执行状态分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 94.2, name: '成功' },
            { value: 3.8, name: '失败' },
            { value: 2.0, name: '超时' }
          ],
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          }
        }]
      })
    }

    // 实时监控图
    if (realtimeChart.value) {
      const chart4 = echarts.init(realtimeChart.value)
      chart4.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['10:20', '10:25', '10:30', '10:35', '10:40', '10:45', '10:50'],
          show: false
        },
        yAxis: { type: 'value', show: false },
        series: [{
          type: 'line',
          data: [120, 135, 125, 145, 130, 125, 140],
          smooth: true,
          symbol: 'none',
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.effect-monitoring {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.effect-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.effect-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.effect-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.effect-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.strategy-name {
  display: flex;
  align-items: center;
}

.response-fast {
  color: #52c41a;
  font-weight: 500;
}

.response-normal {
  color: #faad14;
  font-weight: 500;
}

.response-slow {
  color: #ff4d4f;
  font-weight: 500;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.mini-chart {
  height: 80px;
  width: 100%;
}

.realtime-monitor {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.monitor-item {
  text-align: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.monitor-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.monitor-value {
  font-size: 18px;
  font-weight: 600;
}

.monitor-value.primary {
  color: #1890ff;
}

.monitor-value.success {
  color: #52c41a;
}

.monitor-value.error {
  color: #ff4d4f;
}

.monitor-value.warning {
  color: #faad14;
}

.alert-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-left: 3px solid transparent;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item.unread {
  background: #f6f8ff;
  border-left-color: #1890ff;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.alert-type {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.alert-high {
  background: #fff2f0;
  color: #ff4d4f;
}

.alert-medium {
  background: #fff7e6;
  color: #faad14;
}

.alert-low {
  background: #f6f8ff;
  color: #1890ff;
}

.alert-time {
  font-size: 11px;
  color: #999;
}

.alert-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.key-metrics {
  space-y: 12px;
}

.metric-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.metric-name {
  font-size: 13px;
  color: #666;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.metric-trend {
  text-align: right;
}

.trend-indicator {
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.performance-suggestions {
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-priority {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.priority-high {
  background: #fff2f0;
  color: #ff4d4f;
}

.priority-medium {
  background: #fff7e6;
  color: #faad14;
}

.priority-low {
  background: #f6f8ff;
  color: #1890ff;
}

.suggestion-impact {
  color: #52c41a;
  font-weight: 500;
  font-size: 12px;
}

.suggestion-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.suggestion-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}
</style>