<template>
  <div class="strategy-optimization">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="optimization-info">
          <h2>策略优化</h2>
          <p class="optimization-desc">基于AI分析的策略智能优化与效果评估</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showTestModal = true">
            <template #icon><ExperimentOutlined /></template>
            A/B测试
          </a-button>
          <a-button type="primary" @click="startOptimization">
            <template #icon><ThunderboltOutlined /></template>
            开始优化
          </a-button>
        </div>
      </div>
    </div>

    <!-- 优化统计 -->
    <div class="optimization-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="优化任务" 
              :value="optimizationStats.totalTasks" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><BulbOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="成功优化" 
              :value="optimizationStats.successTasks" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><RiseOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="平均提升" 
              :value="optimizationStats.avgImprovement" 
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="节省成本" 
              :value="optimizationStats.costSavings" 
              :value-style="{ color: '#722ed1' }"
              suffix="万"
            >
              <template #prefix><DollarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 优化建议 -->
        <a-card title="智能优化建议" class="optimization-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedCategory" style="width: 120px" @change="filterSuggestions">
                <a-select-option value="all">全部分类</a-select-option>
                <a-select-option value="strategy">策略优化</a-select-option>
                <a-select-option value="process">流程优化</a-select-option>
                <a-select-option value="resource">资源优化</a-select-option>
                <a-select-option value="cost">成本优化</a-select-option>
              </a-select>
              <a-select v-model:value="selectedPriority" style="width: 100px" @change="filterSuggestions">
                <a-select-option value="all">全部优先级</a-select-option>
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <div class="suggestions-list">
            <div v-for="suggestion in filteredSuggestions" :key="suggestion.id" class="suggestion-item">
              <div class="suggestion-header">
                <div class="suggestion-title">
                  <component :is="getCategoryIcon(suggestion.category)" class="category-icon" />
                  <span>{{ suggestion.title }}</span>
                </div>
                <div class="suggestion-meta">
                  <a-tag :color="getPriorityColor(suggestion.priority)">
                    {{ getPriorityText(suggestion.priority) }}
                  </a-tag>
                  <a-tag color="blue">{{ getImpactText(suggestion.impact) }}</a-tag>
                </div>
              </div>
              <div class="suggestion-content">
                <p>{{ suggestion.description }}</p>
                <div class="suggestion-metrics">
                  <div class="metric-item">
                    <span class="metric-label">预期提升：</span>
                    <span class="metric-value success">{{ suggestion.expectedImprovement }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">实施难度：</span>
                    <span class="metric-value" :class="getDifficultyClass(suggestion.difficulty)">{{ suggestion.difficulty }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">预估耗时：</span>
                    <span class="metric-value">{{ suggestion.estimatedTime }}</span>
                  </div>
                </div>
              </div>
              <div class="suggestion-actions">
                <a-button type="primary" size="small" @click="applySuggestion(suggestion)">
                  采纳建议
                </a-button>
                <a-button size="small" @click="analyzeSuggestion(suggestion)">
                  详细分析
                </a-button>
                <a-button size="small" @click="simulateSuggestion(suggestion)">
                  效果模拟
                </a-button>
                <a-button type="link" size="small" @click="dismissSuggestion(suggestion)">
                  忽略
                </a-button>
              </div>
            </div>
            <a-empty v-if="filteredSuggestions.length === 0" description="暂无优化建议" />
          </div>
        </a-card>

        <!-- A/B测试结果 -->
        <a-card title="A/B测试结果" class="optimization-card">
          <div class="ab-test-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="conversionChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="performanceChart" class="chart-container"></div>
              </a-col>
            </a-row>
            <div class="test-summary">
              <a-table 
                :columns="testColumns" 
                :data-source="abTestResults" 
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getTestStatusBadge(record.status)" 
                      :text="getTestStatusText(record.status)" 
                    />
                  </template>
                  <template v-if="column.key === 'improvement'">
                    <span :class="record.improvement > 0 ? 'improvement-positive' : 'improvement-negative'">
                      {{ record.improvement > 0 ? '+' : '' }}{{ record.improvement }}%
                    </span>
                  </template>
                  <template v-if="column.key === 'confidence'">
                    <a-progress 
                      :percent="record.confidence" 
                      size="small" 
                      :stroke-color="getConfidenceColor(record.confidence)"
                    />
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="viewTestDetail(record)">
                        详情
                      </a-button>
                      <a-button type="link" size="small" @click="rolloutStrategy(record)" :disabled="record.status !== 'completed'">
                        全量推广
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 优化进度 -->
        <a-card title="优化进度" class="optimization-card">
          <div class="optimization-progress">
            <div v-for="task in optimizationTasks" :key="task.id" class="progress-item">
              <div class="progress-header">
                <span class="task-name">{{ task.name }}</span>
                <a-tag :color="task.status === 'running' ? 'processing' : task.status === 'completed' ? 'success' : 'default'">
                  {{ getTaskStatusText(task.status) }}
                </a-tag>
              </div>
              <div class="progress-bar">
                <a-progress 
                  :percent="task.progress" 
                  :status="task.status === 'running' ? 'active' : task.status === 'completed' ? 'success' : 'normal'"
                  size="small"
                />
              </div>
              <div class="progress-details">
                <div class="detail-row">
                  <span class="detail-label">阶段</span>
                  <span class="detail-value">{{ task.currentStage }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">预期提升</span>
                  <span class="detail-value">{{ task.expectedGain }}%</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">剩余时间</span>
                  <span class="detail-value">{{ task.remainingTime }}</span>
                </div>
              </div>
            </div>
            <a-empty v-if="optimizationTasks.length === 0" description="暂无优化任务" size="small" />
          </div>
        </a-card>

        <!-- 效果对比 -->
        <a-card title="效果对比" class="optimization-card">
          <div class="effect-comparison">
            <div class="comparison-tabs">
              <a-radio-group v-model:value="selectedPeriod" button-style="solid" size="small">
                <a-radio-button value="today">今日</a-radio-button>
                <a-radio-button value="week">本周</a-radio-button>
                <a-radio-button value="month">本月</a-radio-button>
              </a-radio-group>
            </div>
            <div class="comparison-metrics">
              <div v-for="metric in comparisonMetrics" :key="metric.key" class="metric-comparison">
                <div class="metric-header">
                  <span class="metric-name">{{ metric.name }}</span>
                  <span class="metric-change" :class="metric.change > 0 ? 'positive' : 'negative'">
                    {{ metric.change > 0 ? '+' : '' }}{{ metric.change }}%
                  </span>
                </div>
                <div class="metric-values">
                  <div class="value-item">
                    <span class="value-label">优化前</span>
                    <span class="value-number">{{ metric.before }}</span>
                  </div>
                  <div class="value-divider">→</div>
                  <div class="value-item">
                    <span class="value-label">优化后</span>
                    <span class="value-number highlight">{{ metric.after }}</span>
                  </div>
                </div>
                <div class="metric-progress">
                  <a-progress 
                    :percent="Math.abs(metric.change) * 10" 
                    size="small" 
                    :stroke-color="metric.change > 0 ? '#52c41a' : '#ff4d4f'"
                    :show-info="false"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 快速操作 -->
        <a-card title="快速操作" class="optimization-card">
          <div class="quick-actions">
            <a-button type="primary" block @click="showBatchOptimizeModal = true" style="margin-bottom: 8px;">
              批量优化
            </a-button>
            <a-button block @click="showStrategyCompareModal = true" style="margin-bottom: 8px;">
              策略对比
            </a-button>
            <a-button block @click="exportOptimizationReport">
              导出报告
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- A/B测试模态框 -->
    <a-modal
      v-model:open="showTestModal"
      title="创建A/B测试"
      width="600px"
      @ok="createABTest"
    >
      <a-form layout="vertical">
        <a-form-item label="测试名称" required>
          <a-input v-model:value="testForm.name" placeholder="请输入测试名称" />
        </a-form-item>
        <a-form-item label="测试策略" required>
          <a-select v-model:value="testForm.strategy" placeholder="选择测试策略">
            <a-select-option value="conversion">转化率优化</a-select-option>
            <a-select-option value="cost">成本优化</a-select-option>
            <a-select-option value="efficiency">效率优化</a-select-option>
            <a-select-option value="satisfaction">满意度优化</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="流量分配">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="对照组" style="margin-bottom: 0;">
                <a-input-number v-model:value="testForm.controlTraffic" :min="10" :max="90" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="实验组" style="margin-bottom: 0;">
                <a-input-number v-model:value="testForm.testTraffic" :min="10" :max="90" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="测试时长（天）">
          <a-input-number v-model:value="testForm.duration" :min="1" :max="30" style="width: 100%" />
        </a-form-item>
        <a-form-item label="测试描述">
          <a-textarea v-model:value="testForm.description" placeholder="描述测试目标和预期效果" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  ExperimentOutlined,
  ThunderboltOutlined,
  BulbOutlined,
  RiseOutlined,
  TrophyOutlined,
  DollarOutlined,
  SettingOutlined,
  AimOutlined,
  FundOutlined,
  TeamOutlined,
  SaveOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showTestModal = ref(false)
const showBatchOptimizeModal = ref(false)
const showStrategyCompareModal = ref(false)
const selectedCategory = ref('all')
const selectedPriority = ref('all')
const selectedPeriod = ref('today')

// 图表引用
const conversionChart = ref(null)
const performanceChart = ref(null)

// 优化统计
const optimizationStats = reactive({
  totalTasks: 24,
  successTasks: 18,
  avgImprovement: 15.8,
  costSavings: 45.2
})

// 优化建议列表
const optimizationSuggestions = ref([
  {
    id: 1,
    title: '优化催收时间策略',
    category: 'strategy',
    priority: 'high',
    impact: 'high',
    description: '基于客户行为分析，建议在上午9-11点和下午2-4点进行催收，可提升15%的联系成功率',
    expectedImprovement: 15.2,
    difficulty: '简单',
    estimatedTime: '3天'
  },
  {
    id: 2,
    title: '智能客户分层策略',
    category: 'strategy',
    priority: 'high',
    impact: 'high',
    description: '采用机器学习算法对客户进行精准分层，针对不同层级制定差异化催收策略',
    expectedImprovement: 22.5,
    difficulty: '中等',
    estimatedTime: '2周'
  },
  {
    id: 3,
    title: '自动化流程优化',
    category: 'process',
    priority: 'medium',
    impact: 'medium',
    description: '将重复性工作自动化，减少人工操作，提升工作效率和准确性',
    expectedImprovement: 18.7,
    difficulty: '中等',
    estimatedTime: '1周'
  },
  {
    id: 4,
    title: '人员配置优化',
    category: 'resource',
    priority: 'medium',
    impact: 'medium',
    description: '根据案件量和难度动态调整人员配置，提升整体催收效率',
    expectedImprovement: 12.3,
    difficulty: '简单',
    estimatedTime: '5天'
  },
  {
    id: 5,
    title: '沟通成本控制',
    category: 'cost',
    priority: 'low',
    impact: 'low',
    description: '优化沟通渠道选择，减少不必要的通信成本',
    expectedImprovement: 8.9,
    difficulty: '简单',
    estimatedTime: '2天'
  }
])

// A/B测试结果
const abTestResults = ref([
  {
    id: 1,
    name: '催收话术A/B测试',
    strategy: '话术优化',
    status: 'completed',
    improvement: 12.5,
    confidence: 95,
    startDate: '2023-07-15',
    endDate: '2023-07-25'
  },
  {
    id: 2,
    name: '联系时间优化测试',
    strategy: '时间策略',
    status: 'running',
    improvement: 8.3,
    confidence: 78,
    startDate: '2023-07-20',
    endDate: '2023-07-30'
  },
  {
    id: 3,
    name: '渠道组合测试',
    strategy: '渠道优化',
    status: 'completed',
    improvement: -2.1,
    confidence: 88,
    startDate: '2023-07-10',
    endDate: '2023-07-20'
  }
])

// 优化任务
const optimizationTasks = ref([
  {
    id: 1,
    name: '智能分层模型训练',
    status: 'running',
    progress: 75,
    currentStage: '模型验证',
    expectedGain: 22.5,
    remainingTime: '2天'
  },
  {
    id: 2,
    name: '流程自动化部署',
    status: 'completed',
    progress: 100,
    currentStage: '已完成',
    expectedGain: 18.7,
    remainingTime: '0'
  },
  {
    id: 3,
    name: '话术模板优化',
    status: 'pending',
    progress: 0,
    currentStage: '等待开始',
    expectedGain: 15.2,
    remainingTime: '5天'
  }
])

// 效果对比指标
const comparisonMetrics = ref([
  {
    key: 'contactRate',
    name: '联系成功率',
    before: '65.2%',
    after: '78.8%',
    change: 13.6
  },
  {
    key: 'recoveryRate',
    name: '回收率',
    before: '28.5%',
    after: '34.7%',
    change: 6.2
  },
  {
    key: 'efficiency',
    name: '人均效率',
    before: '15.2',
    after: '18.9',
    change: 3.7
  },
  {
    key: 'cost',
    name: '单案成本',
    before: '45.8元',
    after: '38.2元',
    change: -7.6
  }
])

// 表格列定义
const testColumns = [
  {
    title: '测试名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '效果提升',
    dataIndex: 'improvement',
    key: 'improvement',
    width: 80
  },
  {
    title: '置信度',
    dataIndex: 'confidence',
    key: 'confidence',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
]

// 表单数据
const testForm = reactive({
  name: '',
  strategy: '',
  controlTraffic: 50,
  testTraffic: 50,
  duration: 7,
  description: ''
})

// 计算属性
const filteredSuggestions = computed(() => {
  let filtered = optimizationSuggestions.value
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(item => item.category === selectedCategory.value)
  }
  if (selectedPriority.value !== 'all') {
    filtered = filtered.filter(item => item.priority === selectedPriority.value)
  }
  return filtered
})

// 方法定义
const getCategoryIcon = (category) => {
  const icons = {
    strategy: 'AimOutlined',
    process: 'SettingOutlined',
    resource: 'TeamOutlined',
    cost: 'FundOutlined'
  }
  return icons[category] || 'BulbOutlined'
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getImpactText = (impact) => {
  const texts = {
    high: '高影响',
    medium: '中影响',
    low: '低影响'
  }
  return texts[impact] || impact
}

const getDifficultyClass = (difficulty) => {
  const classes = {
    '简单': 'difficulty-easy',
    '中等': 'difficulty-medium',
    '困难': 'difficulty-hard'
  }
  return classes[difficulty] || ''
}

const getTestStatusBadge = (status) => {
  const badges = {
    running: 'processing',
    completed: 'success',
    pending: 'default'
  }
  return badges[status] || 'default'
}

const getTestStatusText = (status) => {
  const texts = {
    running: '运行中',
    completed: '已完成',
    pending: '待开始'
  }
  return texts[status] || status
}

const getTaskStatusText = (status) => {
  const texts = {
    running: '进行中',
    completed: '已完成',
    pending: '待开始'
  }
  return texts[status] || status
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 95) return '#52c41a'
  if (confidence >= 80) return '#faad14'
  return '#ff4d4f'
}

const refreshData = () => {
  console.log('刷新数据')
  message.success('数据已刷新')
}

const startOptimization = () => {
  console.log('开始优化')
  message.success('优化任务已启动')
}

const filterSuggestions = () => {
  console.log('筛选建议:', selectedCategory.value, selectedPriority.value)
}

const applySuggestion = (suggestion) => {
  console.log('采纳建议:', suggestion)
  message.success(`已采纳建议: ${suggestion.title}`)
}

const analyzeSuggestion = (suggestion) => {
  console.log('分析建议:', suggestion)
}

const simulateSuggestion = (suggestion) => {
  console.log('模拟建议:', suggestion)
  message.info('正在进行效果模拟...')
}

const dismissSuggestion = (suggestion) => {
  const index = optimizationSuggestions.value.findIndex(item => item.id === suggestion.id)
  if (index > -1) {
    optimizationSuggestions.value.splice(index, 1)
    message.success('建议已忽略')
  }
}

const createABTest = () => {
  console.log('创建A/B测试:', testForm)
  const newTest = {
    id: Date.now(),
    ...testForm,
    status: 'pending',
    improvement: 0,
    confidence: 0,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + testForm.duration * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  }
  abTestResults.value.push(newTest)
  message.success('A/B测试创建成功')
  showTestModal.value = false
  
  // 重置表单
  Object.keys(testForm).forEach(key => {
    if (typeof testForm[key] === 'string') {
      testForm[key] = ''
    } else if (typeof testForm[key] === 'number') {
      testForm[key] = key === 'controlTraffic' || key === 'testTraffic' ? 50 : key === 'duration' ? 7 : 0
    }
  })
}

const viewTestDetail = (record) => {
  console.log('查看测试详情:', record)
}

const rolloutStrategy = (record) => {
  console.log('全量推广策略:', record)
  message.success('策略已全量推广')
}

const exportOptimizationReport = () => {
  console.log('导出优化报告')
  message.success('报告导出成功')
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 转化率对比图
    if (conversionChart.value) {
      const chart1 = echarts.init(conversionChart.value)
      chart1.setOption({
        title: { text: '转化率对比', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['第1天', '第2天', '第3天', '第4天', '第5天', '第6天', '第7天']
        },
        yAxis: { type: 'value', name: '转化率(%)' },
        series: [
          {
            name: '对照组',
            type: 'line',
            data: [65, 67, 64, 68, 66, 69, 67],
            smooth: true,
            itemStyle: { color: '#faad14' }
          },
          {
            name: '实验组',
            type: 'line',
            data: [68, 72, 75, 78, 76, 80, 82],
            smooth: true,
            itemStyle: { color: '#52c41a' }
          }
        ]
      })
    }

    // 性能提升图
    if (performanceChart.value) {
      const chart2 = echarts.init(performanceChart.value)
      chart2.setOption({
        title: { text: '性能提升分析', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['联系成功率', '回收率', '人均效率', '客户满意度']
        },
        yAxis: { type: 'value', name: '提升幅度(%)' },
        series: [{
          name: '性能提升',
          type: 'bar',
          data: [13.6, 6.2, 3.7, 9.5],
          itemStyle: {
            color: function(params) {
              const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
              return colors[params.dataIndex]
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.strategy-optimization {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.optimization-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.optimization-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.optimization-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.optimization-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.suggestions-list {
  max-height: 600px;
  overflow-y: auto;
}

.suggestion-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
  transition: all 0.3s;
}

.suggestion-item:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #262626;
}

.category-icon {
  margin-right: 8px;
  color: #1890ff;
}

.suggestion-meta {
  display: flex;
  gap: 8px;
}

.suggestion-content p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.suggestion-metrics {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-label {
  color: #999;
  font-size: 12px;
}

.metric-value {
  font-weight: 500;
  font-size: 13px;
}

.metric-value.success {
  color: #52c41a;
}

.difficulty-easy {
  color: #52c41a;
}

.difficulty-medium {
  color: #faad14;
}

.difficulty-hard {
  color: #ff4d4f;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.test-summary {
  margin-top: 16px;
}

.improvement-positive {
  color: #52c41a;
  font-weight: 500;
}

.improvement-negative {
  color: #ff4d4f;
  font-weight: 500;
}

.optimization-progress {
  max-height: 400px;
  overflow-y: auto;
}

.progress-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-name {
  font-weight: 500;
  color: #262626;
}

.progress-bar {
  margin-bottom: 8px;
}

.progress-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.detail-label {
  color: #999;
}

.detail-value {
  color: #666;
}

.effect-comparison {
  padding: 8px 0;
}

.comparison-tabs {
  text-align: center;
  margin-bottom: 16px;
}

.comparison-metrics {
  max-height: 300px;
  overflow-y: auto;
}

.metric-comparison {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-name {
  font-weight: 500;
  color: #262626;
}

.metric-change {
  font-weight: 500;
  font-size: 13px;
}

.metric-change.positive {
  color: #52c41a;
}

.metric-change.negative {
  color: #ff4d4f;
}

.metric-values {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.value-label {
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
}

.value-number {
  font-size: 14px;
  color: #666;
}

.value-number.highlight {
  color: #1890ff;
  font-weight: 500;
}

.value-divider {
  color: #ccc;
  font-size: 16px;
}

.metric-progress {
  margin-top: 4px;
}

.quick-actions {
  padding: 8px 0;
}
</style>