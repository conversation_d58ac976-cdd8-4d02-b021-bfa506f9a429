<template>
  <div class="decision-model">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="model-info">
          <h2>决策模型</h2>
          <p class="model-desc">AI驱动的智能决策模型管理，优化催收策略和效果</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshModels">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showTrainModal = true">
            <template #icon><ExperimentOutlined /></template>
            模型训练
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建模型
          </a-button>
        </div>
      </div>
    </div>

    <!-- 模型统计 -->
    <div class="model-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="模型总数" 
              :value="modelStats.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><FunctionOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="运行中" 
              :value="modelStats.active" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="训练中" 
              :value="modelStats.training" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><LoadingOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="平均准确率" 
              :value="modelStats.avgAccuracy" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 模型列表 -->
        <a-card title="决策模型列表" class="model-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedType" style="width: 120px" @change="filterModels">
                <a-select-option value="all">全部类型</a-select-option>
                <a-select-option value="classification">分类模型</a-select-option>
                <a-select-option value="regression">回归模型</a-select-option>
                <a-select-option value="clustering">聚类模型</a-select-option>
                <a-select-option value="recommendation">推荐模型</a-select-option>
              </a-select>
              <a-select v-model:value="selectedStatus" style="width: 100px" @change="filterModels">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="active">运行中</a-select-option>
                <a-select-option value="training">训练中</a-select-option>
                <a-select-option value="stopped">已停止</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <a-table 
            :columns="modelColumns" 
            :data-source="filteredModels" 
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="model-name">
                  <component :is="getModelIcon(record.type)" style="margin-right: 8px;" />
                  <span>{{ record.name }}</span>
                </div>
              </template>
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-badge 
                  :status="getStatusBadge(record.status)" 
                  :text="getStatusText(record.status)" 
                />
              </template>
              <template v-if="column.key === 'accuracy'">
                <a-progress 
                  :percent="record.accuracy" 
                  size="small" 
                  :stroke-color="getAccuracyColor(record.accuracy)"
                />
              </template>
              <template v-if="column.key === 'version'">
                <a-tag color="blue">v{{ record.version }}</a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewModel(record)">
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="testModel(record)" :disabled="record.status !== 'active'">
                    测试
                  </a-button>
                  <a-button type="link" size="small" @click="trainModel(record)">
                    训练
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewMetrics(record)">指标</a-menu-item>
                        <a-menu-item @click="exportModel(record)">导出</a-menu-item>
                        <a-menu-item @click="cloneModel(record)">克隆</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="toggleModel(record)">
                          {{ record.status === 'active' ? '停止' : '启动' }}
                        </a-menu-item>
                        <a-menu-item @click="deleteModel(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 模型性能 -->
        <a-card title="模型性能分析" class="model-card">
          <div class="performance-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="accuracyTrendChart" class="chart-container"></div>
              </a-col>
              <a-col :span="12">
                <div ref="modelComparisonChart" class="chart-container"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 训练状态 -->
        <a-card title="训练状态" class="model-card">
          <div class="training-status">
            <div v-for="training in trainingJobs" :key="training.id" class="training-item">
              <div class="training-header">
                <span class="training-name">{{ training.modelName }}</span>
                <a-tag :color="training.status === 'running' ? 'processing' : 'success'">
                  {{ training.status === 'running' ? '训练中' : '已完成' }}
                </a-tag>
              </div>
              <div class="training-progress">
                <a-progress 
                  :percent="training.progress" 
                  :status="training.status === 'running' ? 'active' : 'success'"
                  size="small"
                />
              </div>
              <div class="training-details">
                <div class="detail-row">
                  <span class="detail-label">轮次</span>
                  <span class="detail-value">{{ training.currentEpoch }}/{{ training.totalEpochs }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">损失</span>
                  <span class="detail-value">{{ training.loss }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">耗时</span>
                  <span class="detail-value">{{ training.duration }}</span>
                </div>
              </div>
            </div>
            <a-empty v-if="trainingJobs.length === 0" description="暂无训练任务" size="small" />
          </div>
        </a-card>

        <!-- 模型推荐 -->
        <a-card title="模型推荐" class="model-card">
          <div class="model-recommendations">
            <div v-for="rec in recommendations" :key="rec.id" class="rec-item">
              <div class="rec-header">
                <div class="rec-title">{{ rec.title }}</div>
                <div class="rec-score">{{ rec.score }}分</div>
              </div>
              <div class="rec-content">{{ rec.description }}</div>
              <div class="rec-actions">
                <a-button type="link" size="small" @click="applyRecommendation(rec)">
                  应用
                </a-button>
                <a-button type="link" size="small" @click="viewRecDetail(rec)">
                  详情
                </a-button>
              </div>
            </div>
            <a-empty v-if="recommendations.length === 0" description="暂无推荐" size="small" />
          </div>
        </a-card>

        <!-- 快速操作 -->
        <a-card title="快速操作" class="model-card">
          <div class="quick-actions">
            <a-button type="primary" block @click="showBatchTrainModal = true" style="margin-bottom: 8px;">
              批量训练
            </a-button>
            <a-button block @click="showModelCompareModal = true" style="margin-bottom: 8px;">
              模型对比
            </a-button>
            <a-button block @click="showAutoMLModal = true">
              AutoML训练
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 创建模型模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建决策模型"
      width="600px"
      @ok="createModel"
    >
      <a-form layout="vertical">
        <a-form-item label="模型名称" required>
          <a-input v-model:value="modelForm.name" placeholder="请输入模型名称" />
        </a-form-item>
        <a-form-item label="模型类型" required>
          <a-select v-model:value="modelForm.type" placeholder="选择模型类型">
            <a-select-option value="classification">分类模型</a-select-option>
            <a-select-option value="regression">回归模型</a-select-option>
            <a-select-option value="clustering">聚类模型</a-select-option>
            <a-select-option value="recommendation">推荐模型</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="算法选择" required>
          <a-select v-model:value="modelForm.algorithm" placeholder="选择使用的算法">
            <a-select-option value="random_forest">随机森林</a-select-option>
            <a-select-option value="xgboost">XGBoost</a-select-option>
            <a-select-option value="neural_network">神经网络</a-select-option>
            <a-select-option value="svm">支持向量机</a-select-option>
            <a-select-option value="logistic_regression">逻辑回归</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="数据源">
          <a-select v-model:value="modelForm.dataSource" placeholder="选择训练数据源">
            <a-select-option value="customer_data">客户数据</a-select-option>
            <a-select-option value="payment_history">还款历史</a-select-option>
            <a-select-option value="collection_records">催收记录</a-select-option>
            <a-select-option value="credit_info">征信信息</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="模型描述">
          <a-textarea v-model:value="modelForm.description" placeholder="模型用途和说明" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 模型训练模态框 -->
    <a-modal
      v-model:open="showTrainModal"
      title="模型训练配置"
      width="500px"
      @ok="startTraining"
    >
      <a-form layout="vertical">
        <a-form-item label="选择模型">
          <a-select v-model:value="trainForm.modelId" placeholder="选择要训练的模型">
            <a-select-option v-for="model in decisionModels" :key="model.id" :value="model.id">
              {{ model.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="训练轮次">
          <a-input-number v-model:value="trainForm.epochs" :min="10" :max="1000" style="width: 100%" />
        </a-form-item>
        <a-form-item label="学习率">
          <a-input-number v-model:value="trainForm.learningRate" :min="0.0001" :max="1" :step="0.0001" style="width: 100%" />
        </a-form-item>
        <a-form-item label="批次大小">
          <a-input-number v-model:value="trainForm.batchSize" :min="16" :max="512" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  ExperimentOutlined,
  PlusOutlined,
  FunctionOutlined,
  PlayCircleOutlined,
  LoadingOutlined,
  TrophyOutlined,
  DownOutlined,
  BranchesOutlined,
  ClusterOutlined,
  NodeIndexOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showCreateModal = ref(false)
const showTrainModal = ref(false)
const showBatchTrainModal = ref(false)
const showModelCompareModal = ref(false)
const showAutoMLModal = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')

// 图表引用
const accuracyTrendChart = ref(null)
const modelComparisonChart = ref(null)

// 模型统计
const modelStats = reactive({
  total: 12,
  active: 8,
  training: 2,
  avgAccuracy: 89.5
})

// 决策模型列表
const decisionModels = ref([
  {
    id: 1,
    name: '客户风险评估模型',
    type: 'classification',
    algorithm: 'random_forest',
    status: 'active',
    accuracy: 92.3,
    version: '2.1',
    createTime: '2023-07-20',
    lastTrained: '2023-07-25'
  },
  {
    id: 2,
    name: '还款能力预测模型',
    type: 'regression',
    algorithm: 'xgboost',
    status: 'active',
    accuracy: 88.7,
    version: '1.8',
    createTime: '2023-07-18',
    lastTrained: '2023-07-24'
  },
  {
    id: 3,
    name: '催收策略推荐模型',
    type: 'recommendation',
    algorithm: 'neural_network',
    status: 'training',
    accuracy: 85.2,
    version: '3.0',
    createTime: '2023-07-22',
    lastTrained: '2023-07-28'
  },
  {
    id: 4,
    name: '客户分群模型',
    type: 'clustering',
    algorithm: 'kmeans',
    status: 'active',
    accuracy: 91.6,
    version: '1.5',
    createTime: '2023-07-15',
    lastTrained: '2023-07-23'
  },
  {
    id: 5,
    name: '逾期概率预测模型',
    type: 'classification',
    algorithm: 'logistic_regression',
    status: 'stopped',
    accuracy: 87.4,
    version: '2.3',
    createTime: '2023-07-12',
    lastTrained: '2023-07-20'
  }
])

// 训练任务
const trainingJobs = ref([
  {
    id: 1,
    modelName: '催收策略推荐模型',
    status: 'running',
    progress: 65,
    currentEpoch: 65,
    totalEpochs: 100,
    loss: 0.245,
    duration: '2h 15m'
  },
  {
    id: 2,
    modelName: '客户行为分析模型',
    status: 'completed',
    progress: 100,
    currentEpoch: 80,
    totalEpochs: 80,
    loss: 0.189,
    duration: '1h 45m'
  }
])

// 推荐列表
const recommendations = ref([
  {
    id: 1,
    title: '优化风险评估模型',
    score: 95,
    description: '建议增加征信数据特征，可提升模型准确率3-5%'
  },
  {
    id: 2,
    title: '启用集成学习',
    score: 88,
    description: '结合多个模型的预测结果，提高整体决策准确性'
  },
  {
    id: 3,
    title: '更新训练数据',
    score: 82,
    description: '使用最近3个月的数据重新训练模型，提升时效性'
  }
])

// 表格列定义
const modelColumns = [
  {
    title: '模型名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '准确率',
    dataIndex: 'accuracy',
    key: 'accuracy',
    width: 120
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    width: 80
  },
  {
    title: '最后训练',
    dataIndex: 'lastTrained',
    key: 'lastTrained',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 表单数据
const modelForm = reactive({
  name: '',
  type: '',
  algorithm: '',
  dataSource: '',
  description: ''
})

const trainForm = reactive({
  modelId: null,
  epochs: 100,
  learningRate: 0.001,
  batchSize: 32
})

// 计算属性
const filteredModels = computed(() => {
  let filtered = decisionModels.value
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(item => item.type === selectedType.value)
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === selectedStatus.value)
  }
  return filtered
})

// 方法定义
const getModelIcon = (type) => {
  const icons = {
    classification: 'BranchesOutlined',
    regression: 'NodeIndexOutlined',
    clustering: 'ClusterOutlined',
    recommendation: 'ThunderboltOutlined'
  }
  return icons[type] || 'FunctionOutlined'
}

const getTypeColor = (type) => {
  const colors = {
    classification: 'blue',
    regression: 'green',
    clustering: 'orange',
    recommendation: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    classification: '分类模型',
    regression: '回归模型',
    clustering: '聚类模型',
    recommendation: '推荐模型'
  }
  return texts[type] || type
}

const getStatusBadge = (status) => {
  const badges = {
    active: 'success',
    training: 'processing',
    stopped: 'default'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '运行中',
    training: '训练中',
    stopped: '已停止'
  }
  return texts[status] || status
}

const getAccuracyColor = (accuracy) => {
  if (accuracy >= 90) return '#52c41a'
  if (accuracy >= 80) return '#faad14'
  return '#ff4d4f'
}

const refreshModels = () => {
  console.log('刷新模型列表')
  message.success('模型列表已刷新')
}

const filterModels = () => {
  console.log('筛选模型:', selectedType.value, selectedStatus.value)
}

const viewModel = (record) => {
  console.log('查看模型:', record)
}

const testModel = (record) => {
  console.log('测试模型:', record)
  message.info(`正在测试模型: ${record.name}`)
}

const trainModel = (record) => {
  trainForm.modelId = record.id
  showTrainModal.value = true
}

const viewMetrics = (record) => {
  console.log('查看指标:', record)
}

const exportModel = (record) => {
  console.log('导出模型:', record)
  message.success('模型导出成功')
}

const cloneModel = (record) => {
  console.log('克隆模型:', record)
  message.success('模型克隆成功')
}

const toggleModel = (record) => {
  const newStatus = record.status === 'active' ? 'stopped' : 'active'
  record.status = newStatus
  message.success(`模型已${newStatus === 'active' ? '启动' : '停止'}`)
}

const deleteModel = (record) => {
  const index = decisionModels.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    decisionModels.value.splice(index, 1)
    message.success('模型删除成功')
  }
}

const createModel = () => {
  console.log('创建模型:', modelForm)
  const newModel = {
    id: Date.now(),
    ...modelForm,
    status: 'training',
    accuracy: 0,
    version: '1.0',
    createTime: new Date().toISOString().split('T')[0],
    lastTrained: new Date().toISOString().split('T')[0]
  }
  decisionModels.value.push(newModel)
  message.success('模型创建成功')
  showCreateModal.value = false
  
  // 重置表单
  Object.keys(modelForm).forEach(key => {
    modelForm[key] = ''
  })
}

const startTraining = () => {
  console.log('开始训练:', trainForm)
  message.success('训练任务已启动')
  showTrainModal.value = false
}

const applyRecommendation = (rec) => {
  console.log('应用推荐:', rec)
  message.success('推荐已应用')
}

const viewRecDetail = (rec) => {
  console.log('查看推荐详情:', rec)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 准确率趋势图
    if (accuracyTrendChart.value) {
      const chart1 = echarts.init(accuracyTrendChart.value)
      chart1.setOption({
        title: { text: '模型准确率趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
        },
        yAxis: { type: 'value', name: '准确率(%)' },
        series: [
          {
            name: '风险评估',
            type: 'line',
            data: [85, 87, 89, 91, 90, 92, 92.3],
            smooth: true
          },
          {
            name: '还款预测',
            type: 'line',
            data: [82, 84, 86, 87, 88, 88.5, 88.7],
            smooth: true
          }
        ]
      })
    }

    // 模型对比图
    if (modelComparisonChart.value) {
      const chart2 = echarts.init(modelComparisonChart.value)
      chart2.setOption({
        title: { text: '模型性能对比', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        xAxis: {
          type: 'value',
          name: '准确率(%)'
        },
        yAxis: {
          type: 'category',
          data: ['风险评估', '还款预测', '策略推荐', '客户分群', '逾期预测']
        },
        series: [{
          type: 'bar',
          data: [92.3, 88.7, 85.2, 91.6, 87.4],
          itemStyle: {
            color: function(params) {
              const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
              return colors[params.dataIndex]
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.decision-model {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.model-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.model-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.model-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.model-name {
  display: flex;
  align-items: center;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.training-status {
  max-height: 300px;
  overflow-y: auto;
}

.training-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.training-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.training-name {
  font-weight: 500;
  color: #262626;
}

.training-progress {
  margin-bottom: 8px;
}

.training-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.detail-label {
  color: #999;
}

.detail-value {
  color: #666;
}

.model-recommendations {
  max-height: 300px;
  overflow-y: auto;
}

.rec-item {
  padding: 12px;
  border-left: 3px solid #1890ff;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.rec-item:last-child {
  border-bottom: none;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.rec-title {
  font-weight: 500;
  color: #262626;
}

.rec-score {
  color: #1890ff;
  font-weight: 500;
}

.rec-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.rec-actions {
  display: flex;
  gap: 8px;
}

.quick-actions {
  padding: 8px 0;
}
</style>