<template>
  <div class="online-service">
    <!-- 客服工作台顶部 -->
    <div class="service-header">
      <div class="status-panel">
        <a-space size="large">
          <div class="status-item">
            <span>服务状态：</span>
            <a-badge :status="serviceStatus.type" :text="serviceStatus.text" />
            <a-button 
              size="small" 
              :type="serviceStatus.online ? 'danger' : 'primary'"
              @click="toggleServiceStatus"
            >
              {{ serviceStatus.online ? '下线' : '上线' }}
            </a-button>
          </div>
          <div class="status-item">
            <span>当前队列：</span>
            <a-tag color="orange">{{ waitingQueue.length }}人</a-tag>
          </div>
          <div class="status-item">
            <span>今日服务：</span>
            <a-tag color="blue">{{ todayStats.served }}人</a-tag>
          </div>
          <div class="status-item">
            <span>平均响应：</span>
            <a-tag color="green">{{ todayStats.avgResponse }}秒</a-tag>
          </div>
        </a-space>
      </div>
      <div class="quick-actions">
        <a-space>
          <a-button @click="showSettingsModal = true">
            <SettingOutlined />
            设置
          </a-button>
          <a-button @click="showHistoryModal = true">
            <HistoryOutlined />
            历史记录
          </a-button>
          <a-button @click="showTemplateModal = true">
            <FileTextOutlined />
            话术模板
          </a-button>
        </a-space>
      </div>
    </div>

    <div class="service-content">
      <!-- 左侧客户队列 -->
      <div class="customer-queue">
        <div class="queue-header">
          <h4>客户队列</h4>
          <a-badge :count="waitingQueue.length" />
        </div>
        <div class="queue-tabs">
          <a-tabs v-model:activeKey="activeQueueTab">
            <a-tab-pane key="waiting" tab="等待中">
              <div class="customer-list">
                <div 
                  v-for="customer in waitingQueue" 
                  :key="customer.id"
                  class="customer-item"
                  :class="{ active: currentCustomer?.id === customer.id }"
                  @click="selectCustomer(customer)"
                >
                  <div class="customer-info">
                    <div class="customer-name">{{ customer.name }}</div>
                    <div class="customer-meta">
                      <span class="wait-time">等待：{{ customer.waitTime }}</span>
                      <a-tag size="small" :color="getPriorityColor(customer.priority)">
                        {{ customer.priority }}
                      </a-tag>
                    </div>
                    <div class="customer-issue">{{ customer.issue }}</div>
                  </div>
                  <div class="customer-actions">
                    <a-button 
                      type="primary" 
                      size="small"
                      @click.stop="acceptCustomer(customer)"
                    >
                      接入
                    </a-button>
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="serving" tab="服务中">
              <div class="customer-list">
                <div 
                  v-for="customer in servingQueue" 
                  :key="customer.id"
                  class="customer-item"
                  :class="{ active: currentCustomer?.id === customer.id }"
                  @click="selectCustomer(customer)"
                >
                  <div class="customer-info">
                    <div class="customer-name">{{ customer.name }}</div>
                    <div class="customer-meta">
                      <span class="serve-time">服务：{{ customer.serveTime }}</span>
                      <a-tag size="small" color="green">进行中</a-tag>
                    </div>
                    <div class="customer-issue">{{ customer.issue }}</div>
                  </div>
                  <div class="customer-actions">
                    <a-button 
                      size="small"
                      @click.stop="endService(customer)"
                    >
                      结束
                    </a-button>
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>

      <!-- 中间聊天区域 -->
      <div class="chat-area">
        <div v-if="!currentCustomer" class="no-customer">
          <a-empty description="请选择客户开始服务" />
        </div>
        <div v-else class="chat-container">
          <!-- 客户信息栏 -->
          <div class="customer-header">
            <div class="customer-profile">
              <a-avatar :size="40">{{ currentCustomer.name.charAt(0) }}</a-avatar>
              <div class="profile-info">
                <div class="name">{{ currentCustomer.name }}</div>
                <div class="details">
                  <span>{{ currentCustomer.phone }}</span>
                  <span>{{ currentCustomer.email }}</span>
                </div>
              </div>
            </div>
            <div class="customer-tags">
              <a-tag color="blue">{{ currentCustomer.type }}</a-tag>
              <a-tag :color="getPriorityColor(currentCustomer.priority)">
                {{ currentCustomer.priority }}
              </a-tag>
            </div>
            <div class="chat-actions">
              <a-space>
                <a-button size="small" @click="showCustomerInfo = true">
                  <UserOutlined />
                  详情
                </a-button>
                <a-button size="small" @click="transferCustomer">
                  <SwapOutlined />
                  转接
                </a-button>
                <a-button size="small" danger @click="endService(currentCustomer)">
                  <CloseOutlined />
                  结束
                </a-button>
              </a-space>
            </div>
          </div>

          <!-- 聊天消息区 -->
          <div class="chat-messages" ref="messagesContainer">
            <div 
              v-for="message in currentMessages" 
              :key="message.id"
              class="message-item"
              :class="{ 'own-message': message.sender === 'agent' }"
            >
              <div class="message-avatar">
                <a-avatar v-if="message.sender === 'customer'" :size="32">
                  {{ currentCustomer.name.charAt(0) }}
                </a-avatar>
                <a-avatar v-else :size="32" style="background-color: #1890ff">
                  客
                </a-avatar>
              </div>
              <div class="message-content">
                <div class="message-info">
                  <span class="sender-name">
                    {{ message.sender === 'customer' ? currentCustomer.name : '客服' }}
                  </span>
                  <span class="message-time">{{ message.time }}</span>
                </div>
                <div class="message-text">{{ message.content }}</div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <div class="input-toolbar">
              <a-space>
                <a-button size="small" @click="insertTemplate">
                  <FileTextOutlined />
                  模板
                </a-button>
                <a-button size="small" @click="sendFile">
                  <PaperClipOutlined />
                  文件
                </a-button>
                <a-button size="small" @click="sendImage">
                  <PictureOutlined />
                  图片
                </a-button>
                <a-button size="small" @click="showEmoji = !showEmoji">
                  <SmileOutlined />
                  表情
                </a-button>
              </a-space>
            </div>
            <div class="input-area">
              <a-textarea
                v-model:value="messageInput"
                :rows="3"
                placeholder="输入消息内容..."
                @press-enter="sendMessage"
              />
              <div class="send-actions">
                <a-space>
                  <a-button @click="messageInput = ''">清空</a-button>
                  <a-button type="primary" @click="sendMessage">
                    发送 (Ctrl+Enter)
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧客户信息和工具 -->
      <div class="customer-panel">
        <a-tabs v-model:activeKey="activePanelTab">
          <a-tab-pane key="info" tab="客户信息">
            <div v-if="currentCustomer" class="customer-details">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="姓名">
                  {{ currentCustomer.name }}
                </a-descriptions-item>
                <a-descriptions-item label="电话">
                  {{ currentCustomer.phone }}
                </a-descriptions-item>
                <a-descriptions-item label="邮箱">
                  {{ currentCustomer.email }}
                </a-descriptions-item>
                <a-descriptions-item label="客户类型">
                  {{ currentCustomer.type }}
                </a-descriptions-item>
                <a-descriptions-item label="问题分类">
                  {{ currentCustomer.category }}
                </a-descriptions-item>
                <a-descriptions-item label="历史咨询">
                  {{ currentCustomer.historyCount }}次
                </a-descriptions-item>
                <a-descriptions-item label="满意度">
                  <a-rate :value="currentCustomer.satisfaction" disabled allow-half />
                </a-descriptions-item>
              </a-descriptions>
              
              <div class="customer-notes">
                <h5>客户备注</h5>
                <a-textarea 
                  v-model:value="customerNotes"
                  :rows="4"
                  placeholder="添加客户备注..."
                />
                <a-button type="primary" size="small" style="margin-top: 8px">
                  保存备注
                </a-button>
              </div>
            </div>
            <div v-else class="no-selection">
              <a-empty description="未选择客户" />
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="templates" tab="话术模板">
            <div class="template-list">
              <a-input
                v-model:value="templateSearch"
                placeholder="搜索模板..."
                style="margin-bottom: 16px"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
              <div class="template-categories">
                <a-collapse v-model:activeKey="activeTemplateKeys">
                  <a-collapse-panel 
                    v-for="category in templateCategories" 
                    :key="category.key"
                    :header="category.name"
                  >
                    <div 
                      v-for="template in category.templates"
                      :key="template.id"
                      class="template-item"
                      @click="useTemplate(template)"
                    >
                      <div class="template-title">{{ template.title }}</div>
                      <div class="template-content">{{ template.content }}</div>
                    </div>
                  </a-collapse-panel>
                </a-collapse>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="knowledge" tab="知识库">
            <div class="knowledge-search">
              <a-input
                v-model:value="knowledgeSearch"
                placeholder="搜索知识库..."
                @press-enter="searchKnowledge"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
              <div class="knowledge-results">
                <div 
                  v-for="article in knowledgeResults"
                  :key="article.id"
                  class="knowledge-item"
                  @click="viewKnowledge(article)"
                >
                  <div class="knowledge-title">{{ article.title }}</div>
                  <div class="knowledge-summary">{{ article.summary }}</div>
                  <div class="knowledge-tags">
                    <a-tag v-for="tag in article.tags" :key="tag" size="small">
                      {{ tag }}
                    </a-tag>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettingsModal"
      title="客服设置"
      width="600px"
      @ok="saveSettings"
    >
      <a-form :model="settings" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="自动接入">
          <a-switch v-model:checked="settings.autoAccept" />
          <span style="margin-left: 8px">自动接入等待客户</span>
        </a-form-item>
        <a-form-item label="最大并发">
          <a-input-number v-model:value="settings.maxConcurrent" :min="1" :max="10" />
        </a-form-item>
        <a-form-item label="响应提醒">
          <a-switch v-model:checked="settings.responseAlert" />
          <span style="margin-left: 8px">超时未响应提醒</span>
        </a-form-item>
        <a-form-item label="提醒时间">
          <a-input-number v-model:value="settings.alertTime" :min="10" :max="300" />
          <span style="margin-left: 8px">秒</span>
        </a-form-item>
        <a-form-item label="声音提醒">
          <a-switch v-model:checked="settings.soundAlert" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 历史记录模态框 -->
    <a-modal
      v-model:open="showHistoryModal"
      title="服务历史"
      width="800px"
      :footer="null"
    >
      <a-table
        :columns="historyColumns"
        :data-source="serviceHistory"
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'satisfaction'">
            <a-rate :value="record.satisfaction" disabled allow-half />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="link" size="small" @click="viewHistoryDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { 
  SettingOutlined,
  HistoryOutlined,
  FileTextOutlined,
  UserOutlined,
  SwapOutlined,
  CloseOutlined,
  PaperClipOutlined,
  PictureOutlined,
  SmileOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const serviceStatus = reactive({
  online: false,
  type: 'default',
  text: '离线'
})

const todayStats = reactive({
  served: 23,
  avgResponse: 15
})

const activeQueueTab = ref('waiting')
const activePanelTab = ref('info')
const currentCustomer = ref(null)
const messageInput = ref('')
const customerNotes = ref('')
const templateSearch = ref('')
const knowledgeSearch = ref('')
const showSettingsModal = ref(false)
const showHistoryModal = ref(false)
const showTemplateModal = ref(false)
const showCustomerInfo = ref(false)
const showEmoji = ref(false)
const messagesContainer = ref(null)

// 设置
const settings = reactive({
  autoAccept: false,
  maxConcurrent: 3,
  responseAlert: true,
  alertTime: 30,
  soundAlert: true
})

// 等待队列
const waitingQueue = ref([
  {
    id: 1,
    name: '张三',
    phone: '13812345678',
    email: '<EMAIL>',
    issue: '还款计划咨询',
    priority: '高',
    waitTime: '3分25秒',
    type: '老客户',
    category: '还款咨询',
    historyCount: 5,
    satisfaction: 4.5
  },
  {
    id: 2,
    name: '李四',
    phone: '13987654321',
    email: '<EMAIL>',
    issue: '投诉服务态度',
    priority: '紧急',
    waitTime: '1分12秒',
    type: '新客户',
    category: '投诉建议',
    historyCount: 1,
    satisfaction: 2.5
  },
  {
    id: 3,
    name: '王五',
    phone: '13765432109',
    email: '<EMAIL>',
    issue: '账单查询',
    priority: '普通',
    waitTime: '0分45秒',
    type: 'VIP客户',
    category: '账单查询',
    historyCount: 12,
    satisfaction: 4.8
  }
])

// 服务中队列
const servingQueue = ref([])

// 当前消息
const currentMessages = ref([])

// 所有消息记录
const allMessages = reactive({})

// 话术模板
const templateCategories = ref([
  {
    key: 'greeting',
    name: '问候语',
    templates: [
      {
        id: 1,
        title: '标准问候',
        content: '您好！我是客服小王，很高兴为您服务，请问有什么可以帮助您的吗？'
      },
      {
        id: 2,
        title: 'VIP问候',
        content: '尊敬的VIP客户您好！我是您的专属客服，非常荣幸为您提供服务。'
      }
    ]
  },
  {
    key: 'payment',
    name: '还款相关',
    templates: [
      {
        id: 3,
        title: '还款计划说明',
        content: '根据您的情况，我们为您制定了灵活的还款计划，您可以根据自己的实际情况选择合适的还款方式。'
      },
      {
        id: 4,
        title: '逾期提醒',
        content: '我们注意到您的账单已经逾期，为了避免产生额外费用，建议您尽快安排还款。'
      }
    ]
  },
  {
    key: 'complaint',
    name: '投诉处理',
    templates: [
      {
        id: 5,
        title: '投诉受理',
        content: '非常抱歉给您带来了不便，我们会认真对待您的投诉，并在24小时内给您回复处理结果。'
      },
      {
        id: 6,
        title: '道歉话术',
        content: '对于我们服务中的不足，我向您表示诚挚的歉意，我们会立即改进并确保类似问题不再发生。'
      }
    ]
  }
])

const activeTemplateKeys = ref(['greeting'])

// 知识库搜索结果
const knowledgeResults = ref([
  {
    id: 1,
    title: '还款计划制定流程',
    summary: '详细介绍如何为客户制定个性化还款计划',
    tags: ['还款', '计划', '流程']
  },
  {
    id: 2,
    title: '投诉处理标准',
    summary: '客户投诉处理的标准流程和注意事项',
    tags: ['投诉', '处理', '标准']
  }
])

// 服务历史
const serviceHistory = ref([
  {
    id: 1,
    customerName: '张三',
    issue: '还款计划咨询',
    startTime: '2023-11-28 09:30:00',
    endTime: '2023-11-28 09:45:00',
    duration: '15分钟',
    satisfaction: 5,
    status: '已完成'
  },
  {
    id: 2,
    customerName: '李四',
    issue: '账单查询',
    startTime: '2023-11-28 10:15:00',
    endTime: '2023-11-28 10:25:00',
    duration: '10分钟',
    satisfaction: 4,
    status: '已完成'
  }
])

const historyColumns = [
  { title: '客户姓名', dataIndex: 'customerName', key: 'customerName' },
  { title: '咨询问题', dataIndex: 'issue', key: 'issue' },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
  { title: '服务时长', dataIndex: 'duration', key: 'duration' },
  { title: '满意度', dataIndex: 'satisfaction', key: 'satisfaction' },
  { title: '操作', key: 'action' }
]

// 计算属性
const onlineCustomers = computed(() => {
  return servingQueue.value.length
})

// 方法定义
const toggleServiceStatus = () => {
  serviceStatus.online = !serviceStatus.online
  if (serviceStatus.online) {
    serviceStatus.type = 'processing'
    serviceStatus.text = '在线'
    message.success('已上线，开始接受客户咨询')
  } else {
    serviceStatus.type = 'default'
    serviceStatus.text = '离线'
    message.info('已下线，停止接受新客户')
  }
}

const getPriorityColor = (priority) => {
  const colors = {
    '紧急': 'red',
    '高': 'orange',
    '普通': 'green'
  }
  return colors[priority] || 'blue'
}

const selectCustomer = (customer) => {
  currentCustomer.value = customer
  // 加载该客户的消息历史
  if (!allMessages[customer.id]) {
    allMessages[customer.id] = [
      {
        id: 1,
        sender: 'customer',
        content: customer.issue,
        time: '09:30:25'
      },
      {
        id: 2,
        sender: 'agent',
        content: '您好！我是客服小王，很高兴为您服务，请问具体需要什么帮助吗？',
        time: '09:30:30'
      }
    ]
  }
  currentMessages.value = allMessages[customer.id]
  nextTick(() => {
    scrollToBottom()
  })
}

const acceptCustomer = (customer) => {
  if (!serviceStatus.online) {
    message.error('请先上线后再接入客户')
    return
  }
  
  if (servingQueue.value.length >= settings.maxConcurrent) {
    message.error(`最多同时服务${settings.maxConcurrent}个客户`)
    return
  }
  
  // 从等待队列移到服务队列
  const index = waitingQueue.value.findIndex(c => c.id === customer.id)
  if (index > -1) {
    waitingQueue.value.splice(index, 1)
    customer.serveTime = '0分0秒'
    servingQueue.value.push(customer)
    selectCustomer(customer)
    message.success(`已接入客户：${customer.name}`)
  }
}

const endService = (customer) => {
  const index = servingQueue.value.findIndex(c => c.id === customer.id)
  if (index > -1) {
    servingQueue.value.splice(index, 1)
    if (currentCustomer.value?.id === customer.id) {
      currentCustomer.value = null
      currentMessages.value = []
    }
    message.success(`已结束对客户${customer.name}的服务`)
    
    // 更新统计
    todayStats.served++
  }
}

const sendMessage = () => {
  if (!messageInput.value.trim()) {
    message.error('请输入消息内容')
    return
  }
  
  if (!currentCustomer.value) {
    message.error('请先选择客户')
    return
  }
  
  const newMessage = {
    id: Date.now(),
    sender: 'agent',
    content: messageInput.value.trim(),
    time: new Date().toLocaleTimeString('zh-CN', { hour12: false }).slice(0, 8)
  }
  
  currentMessages.value.push(newMessage)
  allMessages[currentCustomer.value.id] = currentMessages.value
  messageInput.value = ''
  
  nextTick(() => {
    scrollToBottom()
  })
  
  // 模拟客户回复
  setTimeout(() => {
    const customerReply = {
      id: Date.now() + 1,
      sender: 'customer',
      content: '好的，谢谢您的回复。',
      time: new Date().toLocaleTimeString('zh-CN', { hour12: false }).slice(0, 8)
    }
    currentMessages.value.push(customerReply)
    allMessages[currentCustomer.value.id] = currentMessages.value
    nextTick(() => {
      scrollToBottom()
    })
  }, 2000)
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const insertTemplate = () => {
  activePanelTab.value = 'templates'
}

const useTemplate = (template) => {
  messageInput.value = template.content
  message.success('已插入模板内容')
}

const sendFile = () => {
  message.info('文件发送功能开发中...')
}

const sendImage = () => {
  message.info('图片发送功能开发中...')
}

const transferCustomer = () => {
  message.info('客户转接功能开发中...')
}

const searchKnowledge = () => {
  message.success('知识库搜索完成')
}

const viewKnowledge = (article) => {
  message.info(`查看知识：${article.title}`)
}

const saveSettings = () => {
  message.success('设置保存成功')
  showSettingsModal.value = false
}

const viewHistoryDetail = (record) => {
  message.info(`查看历史详情：${record.customerName}`)
}

// 组件挂载
onMounted(() => {
  // 模拟定时更新等待时间
  setInterval(() => {
    waitingQueue.value.forEach(customer => {
      const parts = customer.waitTime.split('分')
      const minutes = parseInt(parts[0])
      const seconds = parseInt(parts[1].replace('秒', ''))
      const totalSeconds = minutes * 60 + seconds + 1
      const newMinutes = Math.floor(totalSeconds / 60)
      const newSeconds = totalSeconds % 60
      customer.waitTime = `${newMinutes}分${newSeconds}秒`
    })
    
    servingQueue.value.forEach(customer => {
      const parts = customer.serveTime.split('分')
      const minutes = parseInt(parts[0])
      const seconds = parseInt(parts[1].replace('秒', ''))
      const totalSeconds = minutes * 60 + seconds + 1
      const newMinutes = Math.floor(totalSeconds / 60)
      const newSeconds = totalSeconds % 60
      customer.serveTime = `${newMinutes}分${newSeconds}秒`
    })
  }, 1000)
})
</script>

<style scoped>
.online-service {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-panel {
  display: flex;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.customer-queue {
  width: 300px;
  background: white;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.queue-header h4 {
  margin: 0;
}

.queue-tabs {
  flex: 1;
  overflow: hidden;
}

.customer-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.customer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.customer-item:hover {
  background-color: #f5f5f5;
}

.customer-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-weight: 500;
  color: #262626;
}

.customer-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 4px 0;
  font-size: 12px;
  color: #8c8c8c;
}

.customer-issue {
  font-size: 12px;
  color: #595959;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customer-actions {
  margin-left: 12px;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.no-customer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.customer-profile {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-info .name {
  font-weight: 500;
  color: #262626;
}

.profile-info .details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #8c8c8c;
}

.customer-tags {
  display: flex;
  gap: 8px;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f8f9fa;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 8px;
}

.message-content {
  max-width: 60%;
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.own-message .message-content {
  background: #1890ff;
  color: white;
}

.message-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.own-message .message-info {
  color: rgba(255,255,255,0.8);
}

.message-info {
  color: #8c8c8c;
}

.message-text {
  line-height: 1.4;
}

.chat-input {
  border-top: 1px solid #f0f0f0;
  background: white;
}

.input-toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.input-area {
  padding: 16px;
}

.send-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.customer-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #f0f0f0;
  overflow: hidden;
}

.customer-details {
  padding: 16px;
}

.customer-notes {
  margin-top: 24px;
}

.customer-notes h5 {
  margin-bottom: 8px;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.template-list {
  padding: 16px;
}

.template-categories {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.template-item {
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-item:hover {
  border-color: #1890ff;
  background-color: #f6ffed;
}

.template-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.template-content {
  font-size: 12px;
  color: #595959;
  line-height: 1.4;
}

.knowledge-search {
  padding: 16px;
}

.knowledge-results {
  margin-top: 16px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.knowledge-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.knowledge-item:hover {
  border-color: #1890ff;
  background-color: #f6ffed;
}

.knowledge-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.knowledge-summary {
  font-size: 12px;
  color: #595959;
  margin-bottom: 8px;
  line-height: 1.4;
}

.knowledge-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}
</style>