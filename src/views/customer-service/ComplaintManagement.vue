<template>
  <div class="complaint-management">
    <div class="header-section">
      <div class="title-area">
        <h2>投诉管理</h2>
        <p>客户投诉处理与跟踪</p>
      </div>
      <div class="action-area">
        <a-button type="primary" @click="showCreateModal = true">
          <PlusOutlined />
          新建投诉
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索投诉编号、客户姓名"
            @press-enter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="投诉状态"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option value="pending">待处理</a-select-option>
            <a-select-option value="processing">处理中</a-select-option>
            <a-select-option value="resolved">已解决</a-select-option>
            <a-select-option value="closed">已关闭</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.priority"
            placeholder="优先级"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.type"
            placeholder="投诉类型"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option value="service">服务态度</a-select-option>
            <a-select-option value="process">流程问题</a-select-option>
            <a-select-option value="fee">费用争议</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-range-picker
            v-model:value="searchForm.dateRange"
            @change="handleSearch"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总投诉数"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="待处理"
              :value="stats.pending"
              :value-style="{ color: '#f5222d' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="处理中"
              :value="stats.processing"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="解决率"
              :value="stats.resolveRate"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 投诉列表 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="complaintsData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>{{ getTypeText(record.type) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewComplaint(record)">
                查看详情
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleComplaint(record)"
                v-if="record.status !== 'closed'"
              >
                处理
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="editComplaint(record)">
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="transferComplaint(record)">
                      转派
                    </a-menu-item>
                    <a-menu-item @click="closeComplaint(record)">
                      关闭
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新建投诉模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="新建投诉"
      width="800px"
      @ok="handleCreateComplaint"
      @cancel="resetCreateForm"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="客户姓名" required>
          <a-input v-model:value="createForm.customerName" />
        </a-form-item>
        <a-form-item label="客户电话" required>
          <a-input v-model:value="createForm.customerPhone" />
        </a-form-item>
        <a-form-item label="投诉类型" required>
          <a-select v-model:value="createForm.type">
            <a-select-option value="service">服务态度</a-select-option>
            <a-select-option value="process">流程问题</a-select-option>
            <a-select-option value="fee">费用争议</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" required>
          <a-select v-model:value="createForm.priority">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="投诉内容" required>
          <a-textarea 
            v-model:value="createForm.content" 
            :rows="4"
            placeholder="请详细描述投诉内容..."
          />
        </a-form-item>
        <a-form-item label="处理人员">
          <a-select v-model:value="createForm.assignee" allow-clear>
            <a-select-option value="user1">张三</a-select-option>
            <a-select-option value="user2">李四</a-select-option>
            <a-select-option value="user3">王五</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="createForm.attachments"
            :before-upload="() => false"
            multiple
          >
            <a-button>
              <UploadOutlined />
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 投诉详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="投诉详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedComplaint" class="complaint-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="投诉编号">
            {{ selectedComplaint.complaintNo }}
          </a-descriptions-item>
          <a-descriptions-item label="客户姓名">
            {{ selectedComplaint.customerName }}
          </a-descriptions-item>
          <a-descriptions-item label="客户电话">
            {{ selectedComplaint.customerPhone }}
          </a-descriptions-item>
          <a-descriptions-item label="投诉时间">
            {{ selectedComplaint.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="投诉类型">
            {{ getTypeText(selectedComplaint.type) }}
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedComplaint.priority)">
              {{ getPriorityText(selectedComplaint.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(selectedComplaint.status)">
              {{ getStatusText(selectedComplaint.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="处理人员">
            {{ selectedComplaint.assignee }}
          </a-descriptions-item>
          <a-descriptions-item label="投诉内容" :span="2">
            {{ selectedComplaint.content }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 处理记录时间线 -->
        <div class="timeline-section">
          <h4>处理记录</h4>
          <a-timeline>
            <a-timeline-item
              v-for="record in selectedComplaint.processRecords"
              :key="record.id"
              :color="getTimelineColor(record.type)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="timeline-title">{{ record.title }}</span>
                  <span class="timeline-time">{{ record.time }}</span>
                </div>
                <div class="timeline-desc">{{ record.description }}</div>
                <div class="timeline-operator">操作人：{{ record.operator }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>

        <!-- 添加处理记录 -->
        <div class="add-record-section">
          <h4>添加处理记录</h4>
          <a-form layout="vertical">
            <a-form-item label="操作类型">
              <a-select v-model:value="newRecord.type">
                <a-select-option value="contact">联系客户</a-select-option>
                <a-select-option value="investigate">调查核实</a-select-option>
                <a-select-option value="solve">问题解决</a-select-option>
                <a-select-option value="feedback">客户反馈</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="处理内容">
              <a-textarea 
                v-model:value="newRecord.content" 
                :rows="3"
                placeholder="请输入处理内容..."
              />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="addProcessRecord">
                  添加记录
                </a-button>
                <a-button @click="updateComplaintStatus">
                  更新状态
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { 
  PlusOutlined, 
  SearchOutlined, 
  DownOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const selectedComplaint = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined,
  priority: undefined,
  type: undefined,
  dateRange: undefined
})

// 创建表单
const createForm = reactive({
  customerName: '',
  customerPhone: '',
  type: undefined,
  priority: undefined,
  content: '',
  assignee: undefined,
  attachments: []
})

// 新记录表单
const newRecord = reactive({
  type: undefined,
  content: ''
})

// 统计数据
const stats = reactive({
  total: 156,
  pending: 23,
  processing: 15,
  resolveRate: 87.5
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '投诉编号',
    dataIndex: 'complaintNo',
    key: 'complaintNo',
    width: 120
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '投诉类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '处理人员',
    dataIndex: 'assignee',
    key: 'assignee',
    width: 100
  },
  {
    title: '投诉时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模拟数据
const complaintsData = ref([
  {
    id: 1,
    complaintNo: 'CP20231128001',
    customerName: '张三',
    customerPhone: '13812345678',
    type: 'service',
    priority: 'high',
    status: 'pending',
    assignee: '李四',
    createTime: '2023-11-28 09:30:00',
    content: '客服态度恶劣，处理问题不及时',
    processRecords: [
      {
        id: 1,
        type: 'create',
        title: '投诉创建',
        description: '客户通过电话投诉，反映客服态度问题',
        operator: '系统',
        time: '2023-11-28 09:30:00'
      }
    ]
  },
  {
    id: 2,
    complaintNo: 'CP20231128002',
    customerName: '李四',
    customerPhone: '13987654321',
    type: 'process',
    priority: 'medium',
    status: 'processing',
    assignee: '王五',
    createTime: '2023-11-28 10:15:00',
    content: '催收流程不规范，存在违规操作',
    processRecords: [
      {
        id: 1,
        type: 'create',
        title: '投诉创建',
        description: '客户通过在线渠道投诉',
        operator: '系统',
        time: '2023-11-28 10:15:00'
      },
      {
        id: 2,
        type: 'contact',
        title: '联系客户',
        description: '已电话联系客户，了解具体情况',
        operator: '王五',
        time: '2023-11-28 11:00:00'
      }
    ]
  },
  {
    id: 3,
    complaintNo: 'CP20231127001',
    customerName: '王五',
    customerPhone: '13765432109',
    type: 'fee',
    priority: 'low',
    status: 'resolved',
    assignee: '赵六',
    createTime: '2023-11-27 14:20:00',
    content: '收费标准不明确，存在乱收费现象',
    processRecords: [
      {
        id: 1,
        type: 'create',
        title: '投诉创建',
        description: '客户投诉收费问题',
        operator: '系统',
        time: '2023-11-27 14:20:00'
      },
      {
        id: 2,
        type: 'investigate',
        title: '调查核实',
        description: '核查相关收费记录，确认存在问题',
        operator: '赵六',
        time: '2023-11-27 15:30:00'
      },
      {
        id: 3,
        type: 'solve',
        title: '问题解决',
        description: '已退还多收费用，客户表示满意',
        operator: '赵六',
        time: '2023-11-27 16:45:00'
      }
    ]
  }
])

// 方法定义
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'red',
    processing: 'orange',
    resolved: 'green',
    closed: 'gray'
  }
  return colors[status] || 'blue'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return texts[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'blue'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getTypeText = (type) => {
  const texts = {
    service: '服务态度',
    process: '流程问题',
    fee: '费用争议',
    other: '其他'
  }
  return texts[type] || type
}

const getTimelineColor = (type) => {
  const colors = {
    create: 'blue',
    contact: 'green',
    investigate: 'orange',
    solve: 'green',
    feedback: 'purple'
  }
  return colors[type] || 'blue'
}

const viewComplaint = (record) => {
  selectedComplaint.value = record
  showDetailModal.value = true
}

const handleComplaint = (record) => {
  viewComplaint(record)
}

const editComplaint = (record) => {
  message.info(`编辑投诉：${record.complaintNo}`)
}

const transferComplaint = (record) => {
  message.info(`转派投诉：${record.complaintNo}`)
}

const closeComplaint = (record) => {
  message.info(`关闭投诉：${record.complaintNo}`)
}

const handleCreateComplaint = () => {
  if (!createForm.customerName || !createForm.customerPhone || !createForm.type || !createForm.content) {
    message.error('请填写必填项')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    showCreateModal.value = false
    message.success('投诉创建成功')
    resetCreateForm()
    handleSearch()
  }, 1000)
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    customerName: '',
    customerPhone: '',
    type: undefined,
    priority: undefined,
    content: '',
    assignee: undefined,
    attachments: []
  })
}

const addProcessRecord = () => {
  if (!newRecord.type || !newRecord.content) {
    message.error('请填写记录类型和内容')
    return
  }
  
  const record = {
    id: Date.now(),
    type: newRecord.type,
    title: getRecordTitle(newRecord.type),
    description: newRecord.content,
    operator: '当前用户',
    time: new Date().toLocaleString()
  }
  
  selectedComplaint.value.processRecords.push(record)
  Object.assign(newRecord, { type: undefined, content: '' })
  message.success('处理记录添加成功')
}

const getRecordTitle = (type) => {
  const titles = {
    contact: '联系客户',
    investigate: '调查核实',
    solve: '问题解决',
    feedback: '客户反馈'
  }
  return titles[type] || type
}

const updateComplaintStatus = () => {
  message.info('状态更新功能开发中...')
}

// 组件挂载
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.complaint-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.title-area h2 {
  margin: 0;
  color: #262626;
}

.title-area p {
  margin: 4px 0 0;
  color: #8c8c8c;
}

.search-section {
  margin-bottom: 16px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-section {
  margin-bottom: 16px;
}

.table-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.complaint-detail {
  max-height: 600px;
  overflow-y: auto;
}

.timeline-section {
  margin-top: 24px;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.timeline-title {
  font-weight: 500;
  color: #262626;
}

.timeline-time {
  color: #8c8c8c;
  font-size: 12px;
}

.timeline-desc {
  margin-bottom: 4px;
  color: #595959;
}

.timeline-operator {
  font-size: 12px;
  color: #8c8c8c;
}

.add-record-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}
</style>