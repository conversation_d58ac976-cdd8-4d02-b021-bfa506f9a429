<template>
  <div class="satisfaction-survey">
    <div class="header-section">
      <div class="title-area">
        <h2>满意度调查</h2>
        <p>客户满意度问卷调查与分析</p>
      </div>
      <div class="action-area">
        <a-space>
          <a-button @click="exportSurvey">
            <DownloadOutlined />
            导出报告
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <PlusOutlined />
            新建调查
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 调查概览 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总调查数"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="本月完成"
              :value="stats.monthlyCompleted"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均满意度"
              :value="stats.avgSatisfaction"
              :precision="1"
              suffix="分"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="响应率"
              :value="stats.responseRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 调查列表 -->
        <a-tab-pane key="surveys" tab="调查管理">
          <div class="surveys-content">
            <!-- 搜索筛选 -->
            <div class="search-section">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-input
                    v-model:value="searchForm.keyword"
                    placeholder="搜索调查标题"
                    @press-enter="handleSearch"
                  >
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="searchForm.status"
                    placeholder="调查状态"
                    allow-clear
                    @change="handleSearch"
                  >
                    <a-select-option value="active">进行中</a-select-option>
                    <a-select-option value="completed">已完成</a-select-option>
                    <a-select-option value="draft">草稿</a-select-option>
                    <a-select-option value="paused">已暂停</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="searchForm.type"
                    placeholder="调查类型"
                    allow-clear
                    @change="handleSearch"
                  >
                    <a-select-option value="service">服务满意度</a-select-option>
                    <a-select-option value="product">产品体验</a-select-option>
                    <a-select-option value="support">支持质量</a-select-option>
                    <a-select-option value="overall">整体评价</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-range-picker
                    v-model:value="searchForm.dateRange"
                    @change="handleSearch"
                  />
                </a-col>
                <a-col :span="4">
                  <a-button type="primary" @click="handleSearch">
                    <SearchOutlined />
                    搜索
                  </a-button>
                </a-col>
              </a-row>
            </div>

            <!-- 调查列表 -->
            <div class="table-section">
              <a-table
                :columns="surveyColumns"
                :data-source="surveysData"
                :pagination="pagination"
                :loading="loading"
                row-key="id"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getStatusColor(record.status)">
                      {{ getStatusText(record.status) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'type'">
                    <span>{{ getTypeText(record.type) }}</span>
                  </template>
                  <template v-else-if="column.key === 'avgScore'">
                    <a-rate :value="record.avgScore" disabled allow-half />
                    <span style="margin-left: 8px">{{ record.avgScore }}</span>
                  </template>
                  <template v-else-if="column.key === 'progress'">
                    <a-progress 
                      :percent="record.responseRate" 
                      size="small"
                      :status="record.responseRate < 50 ? 'exception' : 'success'"
                    />
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="viewSurvey(record)">
                        查看
                      </a-button>
                      <a-button type="link" size="small" @click="viewResults(record)">
                        结果
                      </a-button>
                      <a-dropdown>
                        <template #overlay>
                          <a-menu>
                            <a-menu-item @click="editSurvey(record)">
                              编辑
                            </a-menu-item>
                            <a-menu-item @click="copySurvey(record)">
                              复制
                            </a-menu-item>
                            <a-menu-item @click="pauseSurvey(record)">
                              {{ record.status === 'active' ? '暂停' : '启用' }}
                            </a-menu-item>
                            <a-menu-divider />
                            <a-menu-item @click="deleteSurvey(record)" danger>
                              删除
                            </a-menu-item>
                          </a-menu>
                        </template>
                        <a-button type="link" size="small">
                          更多 <DownOutlined />
                        </a-button>
                      </a-dropdown>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 调查结果分析 -->
        <a-tab-pane key="analysis" tab="结果分析">
          <div class="analysis-content">
            <!-- 总体统计 -->
            <div class="analysis-overview">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-card title="满意度趋势">
                    <div id="satisfactionTrendChart" style="height: 300px;"></div>
                  </a-card>
                </a-col>
                <a-col :span="12">
                  <a-card title="满意度分布">
                    <div id="satisfactionDistChart" style="height: 300px;"></div>
                  </a-card>
                </a-col>
              </a-row>
            </div>

            <!-- 详细分析 -->
            <div class="detailed-analysis">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-card title="服务类型分析">
                    <div class="analysis-item" v-for="item in serviceAnalysis" :key="item.type">
                      <div class="analysis-header">
                        <span>{{ item.type }}</span>
                        <span class="score">{{ item.score }}分</span>
                      </div>
                      <a-progress :percent="item.score * 20" :show-info="false" />
                    </div>
                  </a-card>
                </a-col>
                <a-col :span="8">
                  <a-card title="客户评价关键词">
                    <div class="keywords-cloud">
                      <a-tag 
                        v-for="keyword in keywords" 
                        :key="keyword.text"
                        :style="{ fontSize: keyword.size + 'px' }"
                        :color="keyword.sentiment === 'positive' ? 'green' : keyword.sentiment === 'negative' ? 'red' : 'blue'"
                      >
                        {{ keyword.text }}
                      </a-tag>
                    </div>
                  </a-card>
                </a-col>
                <a-col :span="8">
                  <a-card title="改进建议">
                    <div class="suggestions">
                      <div v-for="suggestion in suggestions" :key="suggestion.id" class="suggestion-item">
                        <div class="suggestion-title">{{ suggestion.title }}</div>
                        <div class="suggestion-desc">{{ suggestion.description }}</div>
                        <div class="suggestion-priority">
                          <a-tag :color="getPriorityColor(suggestion.priority)">
                            {{ suggestion.priority }}优先级
                          </a-tag>
                        </div>
                      </div>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 问卷模板 -->
        <a-tab-pane key="templates" tab="问卷模板">
          <div class="templates-content">
            <div class="template-grid">
              <a-row :gutter="[16, 16]">
                <a-col 
                  v-for="template in templates" 
                  :key="template.id" 
                  :span="8"
                >
                  <a-card
                    :hoverable="true"
                    class="template-card"
                    @click="useTemplate(template)"
                  >
                    <template #cover>
                      <div class="template-cover">
                        <FileTextOutlined class="template-icon" />
                      </div>
                    </template>
                    <template #actions>
                      <EditOutlined @click.stop="editTemplate(template)" />
                      <EyeOutlined @click.stop="previewTemplate(template)" />
                      <DeleteOutlined @click.stop="deleteTemplate(template)" />
                    </template>
                    <a-card-meta :title="template.name">
                      <template #description>
                        <div class="template-desc">
                          <p>{{ template.description }}</p>
                          <div class="template-meta">
                            <span>{{ template.questions }}个问题</span>
                            <span>{{ template.category }}</span>
                          </div>
                        </div>
                      </template>
                    </a-card-meta>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 新建调查模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="新建满意度调查"
      width="800px"
      @ok="handleCreateSurvey"
      @cancel="resetCreateForm"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="调查标题" required>
          <a-input v-model:value="createForm.title" />
        </a-form-item>
        <a-form-item label="调查类型" required>
          <a-select v-model:value="createForm.type">
            <a-select-option value="service">服务满意度</a-select-option>
            <a-select-option value="product">产品体验</a-select-option>
            <a-select-option value="support">支持质量</a-select-option>
            <a-select-option value="overall">整体评价</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="调查描述">
          <a-textarea 
            v-model:value="createForm.description" 
            :rows="3"
            placeholder="请描述本次调查的目的和内容..."
          />
        </a-form-item>
        <a-form-item label="使用模板">
          <a-select v-model:value="createForm.templateId" allow-clear placeholder="选择问卷模板">
            <a-select-option 
              v-for="template in templates" 
              :key="template.id" 
              :value="template.id"
            >
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="调查时间">
          <a-range-picker
            v-model:value="createForm.dateRange"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
          />
        </a-form-item>
        <a-form-item label="目标客户">
          <a-checkbox-group v-model:value="createForm.targetGroups">
            <a-checkbox value="new">新客户</a-checkbox>
            <a-checkbox value="vip">VIP客户</a-checkbox>
            <a-checkbox value="regular">普通客户</a-checkbox>
            <a-checkbox value="complaint">投诉客户</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="发送方式">
          <a-checkbox-group v-model:value="createForm.sendMethods">
            <a-checkbox value="sms">短信</a-checkbox>
            <a-checkbox value="email">邮件</a-checkbox>
            <a-checkbox value="phone">电话</a-checkbox>
            <a-checkbox value="app">APP推送</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 调查详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      :title="selectedSurvey?.title"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedSurvey" class="survey-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="调查类型">
            {{ getTypeText(selectedSurvey.type) }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedSurvey.status)">
              {{ getStatusText(selectedSurvey.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedSurvey.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="有效期">
            {{ selectedSurvey.startTime }} - {{ selectedSurvey.endTime }}
          </a-descriptions-item>
          <a-descriptions-item label="目标人数">
            {{ selectedSurvey.targetCount }}
          </a-descriptions-item>
          <a-descriptions-item label="完成人数">
            {{ selectedSurvey.completedCount }}
          </a-descriptions-item>
          <a-descriptions-item label="平均得分">
            <a-rate :value="selectedSurvey.avgScore" disabled allow-half />
            {{ selectedSurvey.avgScore }}分
          </a-descriptions-item>
          <a-descriptions-item label="响应率">
            {{ selectedSurvey.responseRate }}%
          </a-descriptions-item>
          <a-descriptions-item label="调查描述" :span="2">
            {{ selectedSurvey.description }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 问题列表 -->
        <div class="questions-section">
          <h4>调查问题</h4>
          <div v-for="(question, index) in selectedSurvey.questions" :key="question.id" class="question-item">
            <div class="question-header">
              <span class="question-number">{{ index + 1 }}.</span>
              <span class="question-title">{{ question.title }}</span>
              <a-tag size="small">{{ question.type }}</a-tag>
            </div>
            <div class="question-options">
              <div v-if="question.type === 'single' || question.type === 'multiple'">
                <div v-for="option in question.options" :key="option.id" class="option-item">
                  <span>{{ option.text }}</span>
                  <span class="option-count">({{ option.count || 0 }}票)</span>
                </div>
              </div>
              <div v-else-if="question.type === 'rating'">
                <a-rate :value="question.avgRating || 0" disabled allow-half />
                <span style="margin-left: 8px">平均 {{ question.avgRating || 0 }} 分</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { 
  PlusOutlined,
  SearchOutlined, 
  DownloadOutlined,
  DownOutlined,
  FileTextOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const selectedSurvey = ref(null)
const activeTab = ref('surveys')

// 统计数据
const stats = reactive({
  total: 45,
  monthlyCompleted: 12,
  avgSatisfaction: 4.2,
  responseRate: 78.5
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined,
  type: undefined,
  dateRange: undefined
})

// 创建表单
const createForm = reactive({
  title: '',
  type: undefined,
  description: '',
  templateId: undefined,
  dateRange: undefined,
  targetGroups: [],
  sendMethods: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const surveyColumns = [
  {
    title: '调查标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '目标/完成',
    key: 'progress',
    width: 120
  },
  {
    title: '平均评分',
    dataIndex: 'avgScore',
    key: 'avgScore',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 调查数据
const surveysData = ref([
  {
    id: 1,
    title: '11月客户服务满意度调查',
    type: 'service',
    status: 'active',
    targetCount: 500,
    completedCount: 392,
    responseRate: 78.4,
    avgScore: 4.3,
    createTime: '2023-11-01 09:00:00',
    startTime: '2023-11-01 09:00:00',
    endTime: '2023-11-30 18:00:00',
    description: '本次调查旨在了解客户对我们服务质量的满意度',
    questions: [
      {
        id: 1,
        title: '您对我们的客服态度满意吗？',
        type: 'rating',
        avgRating: 4.2
      },
      {
        id: 2,
        title: '您认为我们的服务还有哪些需要改进的地方？',
        type: 'text'
      }
    ]
  },
  {
    id: 2,
    title: '产品体验反馈调查',
    type: 'product',
    status: 'completed',
    targetCount: 300,
    completedCount: 245,
    responseRate: 81.7,
    avgScore: 3.9,
    createTime: '2023-10-15 10:00:00',
    startTime: '2023-10-15 10:00:00',
    endTime: '2023-10-31 18:00:00',
    description: '收集客户对产品功能和体验的反馈意见',
    questions: [
      {
        id: 1,
        title: '产品功能是否满足您的需求？',
        type: 'single',
        options: [
          { id: 1, text: '完全满足', count: 98 },
          { id: 2, text: '基本满足', count: 112 },
          { id: 3, text: '部分满足', count: 25 },
          { id: 4, text: '不满足', count: 10 }
        ]
      }
    ]
  }
])

// 服务分析数据
const serviceAnalysis = ref([
  { type: '客服态度', score: 4.5 },
  { type: '响应速度', score: 4.1 },
  { type: '问题解决', score: 3.8 },
  { type: '专业能力', score: 4.2 },
  { type: '整体满意', score: 4.0 }
])

// 关键词数据
const keywords = ref([
  { text: '专业', size: 16, sentiment: 'positive' },
  { text: '热情', size: 14, sentiment: 'positive' },
  { text: '耐心', size: 18, sentiment: 'positive' },
  { text: '慢', size: 12, sentiment: 'negative' },
  { text: '满意', size: 20, sentiment: 'positive' },
  { text: '友好', size: 15, sentiment: 'positive' },
  { text: '等待时间长', size: 13, sentiment: 'negative' }
])

// 改进建议
const suggestions = ref([
  {
    id: 1,
    title: '提升响应速度',
    description: '客户反馈等待时间较长，建议增加客服人员或优化流程',
    priority: '高'
  },
  {
    id: 2,
    title: '完善FAQ系统',
    description: '建立更完善的自助服务系统，减少重复问题',
    priority: '中'
  },
  {
    id: 3,
    title: '提供多渠道服务',
    description: '增加在线聊天、视频通话等多种联系方式',
    priority: '中'
  }
])

// 问卷模板
const templates = ref([
  {
    id: 1,
    name: '服务满意度标准模板',
    description: '通用的客户服务满意度调查问卷',
    questions: 8,
    category: '服务类'
  },
  {
    id: 2,
    name: '产品体验调查模板',
    description: '针对产品功能和用户体验的调查问卷',
    questions: 12,
    category: '产品类'
  },
  {
    id: 3,
    name: '投诉处理满意度模板',
    description: '投诉处理后的满意度调查问卷',
    questions: 6,
    category: '投诉类'
  }
])

// 方法定义
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const getStatusColor = (status) => {
  const colors = {
    active: 'blue',
    completed: 'green',
    draft: 'orange',
    paused: 'gray'
  }
  return colors[status] || 'blue'
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    completed: '已完成',
    draft: '草稿',
    paused: '已暂停'
  }
  return texts[status] || status
}

const getTypeText = (type) => {
  const texts = {
    service: '服务满意度',
    product: '产品体验',
    support: '支持质量',
    overall: '整体评价'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    '高': 'red',
    '中': 'orange',
    '低': 'green'
  }
  return colors[priority] || 'blue'
}

const viewSurvey = (record) => {
  selectedSurvey.value = record
  showDetailModal.value = true
}

const viewResults = (record) => {
  activeTab.value = 'analysis'
  message.info(`查看调查结果：${record.title}`)
}

const editSurvey = (record) => {
  message.info(`编辑调查：${record.title}`)
}

const copySurvey = (record) => {
  message.info(`复制调查：${record.title}`)
}

const pauseSurvey = (record) => {
  const action = record.status === 'active' ? '暂停' : '启用'
  message.info(`${action}调查：${record.title}`)
}

const deleteSurvey = (record) => {
  message.warning(`删除调查：${record.title}`)
}

const exportSurvey = () => {
  message.info('正在导出调查报告...')
}

const handleCreateSurvey = () => {
  if (!createForm.title || !createForm.type) {
    message.error('请填写必填项')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    showCreateModal.value = false
    message.success('调查创建成功')
    resetCreateForm()
    handleSearch()
  }, 1000)
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    title: '',
    type: undefined,
    description: '',
    templateId: undefined,
    dateRange: undefined,
    targetGroups: [],
    sendMethods: []
  })
}

const useTemplate = (template) => {
  createForm.templateId = template.id
  showCreateModal.value = true
  message.success(`已选择模板：${template.name}`)
}

const editTemplate = (template) => {
  message.info(`编辑模板：${template.name}`)
}

const previewTemplate = (template) => {
  message.info(`预览模板：${template.name}`)
}

const deleteTemplate = (template) => {
  message.warning(`删除模板：${template.name}`)
}

// 组件挂载
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.satisfaction-survey {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.title-area h2 {
  margin: 0;
  color: #262626;
}

.title-area p {
  margin: 4px 0 0;
  color: #8c8c8c;
}

.overview-section {
  margin-bottom: 24px;
}

.content-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.surveys-content {
  padding: 24px;
}

.search-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.table-section {
  background: white;
}

.analysis-content {
  padding: 24px;
}

.analysis-overview {
  margin-bottom: 24px;
}

.detailed-analysis {
  margin-top: 24px;
}

.analysis-item {
  margin-bottom: 16px;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.score {
  font-weight: 500;
  color: #1890ff;
}

.keywords-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.suggestions {
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
}

.suggestion-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.suggestion-desc {
  color: #595959;
  margin-bottom: 8px;
  line-height: 1.4;
}

.templates-content {
  padding: 24px;
}

.template-grid {
  max-height: 600px;
  overflow-y: auto;
}

.template-card {
  height: 280px;
  cursor: pointer;
}

.template-cover {
  height: 100px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-icon {
  font-size: 36px;
  color: white;
}

.template-desc p {
  margin-bottom: 12px;
  color: #595959;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.survey-detail {
  max-height: 600px;
  overflow-y: auto;
}

.questions-section {
  margin-top: 24px;
}

.question-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.question-number {
  font-weight: 500;
  color: #1890ff;
}

.question-title {
  flex: 1;
  font-weight: 500;
  color: #262626;
}

.question-options {
  padding-left: 24px;
}

.option-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  color: #595959;
}

.option-count {
  color: #8c8c8c;
  font-size: 12px;
}
</style>