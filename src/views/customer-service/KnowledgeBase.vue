<template>
  <div class="knowledge-base">
    <div class="header-section">
      <div class="title-area">
        <h2>知识库</h2>
        <p>企业知识文档管理与检索</p>
      </div>
      <div class="action-area">
        <a-space>
          <a-button @click="importKnowledge">
            <UploadOutlined />
            批量导入
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <PlusOutlined />
            新建文档
          </a-button>
        </a-space>
      </div>
    </div>

    <div class="content-wrapper">
      <!-- 左侧分类树 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h4>知识分类</h4>
          <a-button 
            type="text" 
            size="small" 
            @click="showCategoryModal = true"
          >
            <SettingOutlined />
          </a-button>
        </div>
        <div class="category-tree">
          <a-tree
            v-model:selectedKeys="selectedCategoryKeys"
            :tree-data="categoryTree"
            @select="onCategorySelect"
            :field-names="{ children: 'children', title: 'title', key: 'key' }"
          >
            <template #title="{ title, count }">
              <span>{{ title }} <span class="count">({{ count || 0 }})</span></span>
            </template>
          </a-tree>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 搜索区域 -->
        <div class="search-section">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-input
                v-model:value="searchForm.keyword"
                placeholder="搜索标题、内容、标签..."
                @press-enter="handleSearch"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.status"
                placeholder="文档状态"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="published">已发布</a-select-option>
                <a-select-option value="draft">草稿</a-select-option>
                <a-select-option value="reviewing">审核中</a-select-option>
                <a-select-option value="archived">已归档</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.type"
                placeholder="文档类型"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="faq">常见问题</a-select-option>
                <a-select-option value="process">流程指南</a-select-option>
                <a-select-option value="policy">政策制度</a-select-option>
                <a-select-option value="template">模板文档</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.author"
                placeholder="创建人"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="user1">张三</a-select-option>
                <a-select-option value="user2">李四</a-select-option>
                <a-select-option value="user3">王五</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-button-group>
                <a-button 
                  :type="viewMode === 'list' ? 'primary' : 'default'"
                  @click="viewMode = 'list'"
                >
                  <UnorderedListOutlined />
                </a-button>
                <a-button 
                  :type="viewMode === 'card' ? 'primary' : 'default'"
                  @click="viewMode = 'card'"
                >
                  <AppstoreOutlined />
                </a-button>
              </a-button-group>
            </a-col>
          </a-row>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="总文档数"
                  :value="stats.total"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="今日阅读"
                  :value="stats.todayRead"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="待审核"
                  :value="stats.reviewing"
                  :value-style="{ color: '#fa8c16' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="更新率"
                  :value="stats.updateRate"
                  suffix="%"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 文档列表/卡片视图 -->
        <div class="document-section">
          <!-- 列表视图 -->
          <div v-if="viewMode === 'list'" class="list-view">
            <a-table
              :columns="columns"
              :data-source="documentsData"
              :pagination="pagination"
              :loading="loading"
              row-key="id"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'title'">
                  <a @click="viewDocument(record)" class="doc-title">
                    {{ record.title }}
                  </a>
                  <div class="doc-tags">
                    <a-tag 
                      v-for="tag in record.tags" 
                      :key="tag" 
                      size="small"
                    >
                      {{ tag }}
                    </a-tag>
                  </div>
                </template>
                <template v-else-if="column.key === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'type'">
                  <a-tag>{{ getTypeText(record.type) }}</a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewDocument(record)">
                      查看
                    </a-button>
                    <a-button type="link" size="small" @click="editDocument(record)">
                      编辑
                    </a-button>
                    <a-dropdown>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="copyDocument(record)">
                            复制
                          </a-menu-item>
                          <a-menu-item @click="moveDocument(record)">
                            移动
                          </a-menu-item>
                          <a-menu-item @click="versionHistory(record)">
                            版本历史
                          </a-menu-item>
                          <a-menu-divider />
                          <a-menu-item @click="deleteDocument(record)" danger>
                            删除
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button type="link" size="small">
                        更多 <DownOutlined />
                      </a-button>
                    </a-dropdown>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 卡片视图 -->
          <div v-else class="card-view">
            <a-row :gutter="[16, 16]">
              <a-col 
                v-for="doc in documentsData" 
                :key="doc.id" 
                :span="8"
              >
                <a-card
                  :hoverable="true"
                  @click="viewDocument(doc)"
                  class="doc-card"
                >
                  <template #cover>
                    <div class="card-cover">
                      <FileTextOutlined class="doc-icon" />
                      <div class="doc-type">{{ getTypeText(doc.type) }}</div>
                    </div>
                  </template>
                  <template #actions>
                    <EditOutlined @click.stop="editDocument(doc)" />
                    <ShareAltOutlined @click.stop="shareDocument(doc)" />
                    <EllipsisOutlined @click.stop="moreActions(doc)" />
                  </template>
                  <a-card-meta :title="doc.title">
                    <template #description>
                      <div class="card-desc">
                        <p>{{ doc.summary }}</p>
                        <div class="card-meta">
                          <span>{{ doc.author }}</span>
                          <span>{{ doc.updateTime }}</span>
                          <span>阅读 {{ doc.readCount }}</span>
                        </div>
                      </div>
                    </template>
                  </a-card-meta>
                  <div class="card-footer">
                    <div class="tags">
                      <a-tag 
                        v-for="tag in doc.tags.slice(0, 2)" 
                        :key="tag" 
                        size="small"
                      >
                        {{ tag }}
                      </a-tag>
                      <span v-if="doc.tags.length > 2">...</span>
                    </div>
                    <a-tag :color="getStatusColor(doc.status)" size="small">
                      {{ getStatusText(doc.status) }}
                    </a-tag>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建文档模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="新建文档"
      width="800px"
      @ok="handleCreateDocument"
      @cancel="resetCreateForm"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="文档标题" required>
          <a-input v-model:value="createForm.title" />
        </a-form-item>
        <a-form-item label="文档类型" required>
          <a-select v-model:value="createForm.type">
            <a-select-option value="faq">常见问题</a-select-option>
            <a-select-option value="process">流程指南</a-select-option>
            <a-select-option value="policy">政策制度</a-select-option>
            <a-select-option value="template">模板文档</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="所属分类" required>
          <a-tree-select
            v-model:value="createForm.categoryId"
            :tree-data="categoryTree"
            placeholder="选择分类"
            tree-default-expand-all
            :field-names="{ children: 'children', label: 'title', value: 'key' }"
          />
        </a-form-item>
        <a-form-item label="文档摘要">
          <a-textarea 
            v-model:value="createForm.summary" 
            :rows="3"
            placeholder="简要描述文档内容..."
          />
        </a-form-item>
        <a-form-item label="标签">
          <a-select
            v-model:value="createForm.tags"
            mode="tags"
            placeholder="添加标签"
            :options="tagOptions"
          />
        </a-form-item>
        <a-form-item label="文档内容" required>
          <a-textarea 
            v-model:value="createForm.content" 
            :rows="8"
            placeholder="输入文档内容..."
          />
        </a-form-item>
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="createForm.attachments"
            :before-upload="() => false"
            multiple
          >
            <a-button>
              <UploadOutlined />
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 分类管理模态框 -->
    <a-modal
      v-model:open="showCategoryModal"
      title="分类管理"
      width="600px"
      @ok="saveCategoryChanges"
    >
      <a-tree
        v-model:expanded-keys="expandedKeys"
        :tree-data="categoryTree"
        draggable
        block-node
        :field-names="{ children: 'children', title: 'title', key: 'key' }"
      >
        <template #title="{ title, key }">
          <div class="tree-node">
            <span>{{ title }}</span>
            <a-space>
              <a-button 
                type="text" 
                size="small" 
                @click="addCategory(key)"
              >
                <PlusOutlined />
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="editCategory(key)"
              >
                <EditOutlined />
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                danger
                @click="deleteCategory(key)"
              >
                <DeleteOutlined />
              </a-button>
            </a-space>
          </div>
        </template>
      </a-tree>
    </a-modal>

    <!-- 文档详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      :title="selectedDocument?.title"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedDocument" class="document-detail">
        <div class="detail-header">
          <a-descriptions :column="3" size="small">
            <a-descriptions-item label="类型">
              {{ getTypeText(selectedDocument.type) }}
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="getStatusColor(selectedDocument.status)">
                {{ getStatusText(selectedDocument.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="创建人">
              {{ selectedDocument.author }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ selectedDocument.createTime }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ selectedDocument.updateTime }}
            </a-descriptions-item>
            <a-descriptions-item label="阅读次数">
              {{ selectedDocument.readCount }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div class="detail-content">
          <h4>文档内容</h4>
          <div class="content-area">
            {{ selectedDocument.content }}
          </div>
        </div>
        <div class="detail-tags">
          <h4>标签</h4>
          <a-tag v-for="tag in selectedDocument.tags" :key="tag">
            {{ tag }}
          </a-tag>
        </div>
        <div class="detail-actions">
          <a-space>
            <a-button @click="likeDocument">
              <LikeOutlined />
              点赞 ({{ selectedDocument.likeCount }})
            </a-button>
            <a-button @click="collectDocument">
              <StarOutlined />
              收藏
            </a-button>
            <a-button @click="shareDocument(selectedDocument)">
              <ShareAltOutlined />
              分享
            </a-button>
            <a-button @click="downloadDocument">
              <DownloadOutlined />
              下载
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { 
  PlusOutlined, 
  SearchOutlined, 
  UploadOutlined,
  SettingOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
  DownOutlined,
  FileTextOutlined,
  EditOutlined,
  ShareAltOutlined,
  EllipsisOutlined,
  DeleteOutlined,
  LikeOutlined,
  StarOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showCategoryModal = ref(false)
const showDetailModal = ref(false)
const selectedDocument = ref(null)
const viewMode = ref('list')
const selectedCategoryKeys = ref(['all'])
const expandedKeys = ref(['cat1', 'cat2', 'cat3'])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined,
  type: undefined,
  author: undefined
})

// 创建表单
const createForm = reactive({
  title: '',
  type: undefined,
  categoryId: undefined,
  summary: '',
  tags: [],
  content: '',
  attachments: []
})

// 统计数据
const stats = reactive({
  total: 248,
  todayRead: 156,
  reviewing: 8,
  updateRate: 92.3
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 分类树数据
const categoryTree = ref([
  {
    title: '全部文档',
    key: 'all',
    count: 248,
    children: []
  },
  {
    title: '催收指南',
    key: 'cat1',
    count: 85,
    children: [
      { title: '电话催收', key: 'cat1-1', count: 32 },
      { title: '上门催收', key: 'cat1-2', count: 28 },
      { title: '法律催收', key: 'cat1-3', count: 25 }
    ]
  },
  {
    title: '客户服务',
    key: 'cat2',
    count: 67,
    children: [
      { title: '投诉处理', key: 'cat2-1', count: 23 },
      { title: '咨询服务', key: 'cat2-2', count: 22 },
      { title: '售后服务', key: 'cat2-3', count: 22 }
    ]
  },
  {
    title: '政策制度',
    key: 'cat3',
    count: 45,
    children: [
      { title: '操作规范', key: 'cat3-1', count: 18 },
      { title: '合规要求', key: 'cat3-2', count: 15 },
      { title: '风险控制', key: 'cat3-3', count: 12 }
    ]
  },
  {
    title: '模板文档',
    key: 'cat4',
    count: 51,
    children: [
      { title: '合同模板', key: 'cat4-1', count: 20 },
      { title: '通知模板', key: 'cat4-2', count: 18 },
      { title: '报告模板', key: 'cat4-3', count: 13 }
    ]
  }
])

// 标签选项
const tagOptions = ref([
  { label: '催收', value: '催收' },
  { label: '客服', value: '客服' },
  { label: '合规', value: '合规' },
  { label: '培训', value: '培训' },
  { label: '模板', value: '模板' }
])

// 表格列配置
const columns = [
  {
    title: '文档标题',
    dataIndex: 'title',
    key: 'title',
    width: 300
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '创建人',
    dataIndex: 'author',
    key: 'author',
    width: 100
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 150
  },
  {
    title: '阅读次数',
    dataIndex: 'readCount',
    key: 'readCount',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模拟数据
const documentsData = ref([
  {
    id: 1,
    title: '电话催收标准化流程指南',
    type: 'process',
    status: 'published',
    author: '张三',
    createTime: '2023-11-20 10:00:00',
    updateTime: '2023-11-28 14:30:00',
    readCount: 245,
    likeCount: 18,
    summary: '详细介绍电话催收的标准化操作流程和注意事项',
    content: '电话催收是催收工作中最重要的环节之一，本文档详细介绍了电话催收的标准化操作流程...',
    tags: ['催收', '流程', '电话'],
    categoryId: 'cat1-1'
  },
  {
    id: 2,
    title: '客户投诉处理应急预案',
    type: 'faq',
    status: 'published',
    author: '李四',
    createTime: '2023-11-18 09:15:00',
    updateTime: '2023-11-27 16:20:00',
    readCount: 189,
    likeCount: 12,
    summary: '客户投诉处理的应急预案和处理技巧',
    content: '面对客户投诉时，我们需要保持冷静，按照标准流程进行处理...',
    tags: ['客服', '投诉', '应急'],
    categoryId: 'cat2-1'
  },
  {
    id: 3,
    title: '催收合规要求及注意事项',
    type: 'policy',
    status: 'reviewing',
    author: '王五',
    createTime: '2023-11-25 11:30:00',
    updateTime: '2023-11-28 09:45:00',
    readCount: 67,
    likeCount: 8,
    summary: '催收行业最新合规要求和操作注意事项',
    content: '根据最新监管要求，催收行业需要严格遵守以下合规要求...',
    tags: ['合规', '政策', '监管'],
    categoryId: 'cat3-2'
  }
])

// 方法定义
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const onCategorySelect = (selectedKeys) => {
  selectedCategoryKeys.value = selectedKeys
  handleSearch()
}

const getStatusColor = (status) => {
  const colors = {
    published: 'green',
    draft: 'orange',
    reviewing: 'blue',
    archived: 'gray'
  }
  return colors[status] || 'blue'
}

const getStatusText = (status) => {
  const texts = {
    published: '已发布',
    draft: '草稿',
    reviewing: '审核中',
    archived: '已归档'
  }
  return texts[status] || status
}

const getTypeText = (type) => {
  const texts = {
    faq: '常见问题',
    process: '流程指南',
    policy: '政策制度',
    template: '模板文档'
  }
  return texts[type] || type
}

const viewDocument = (record) => {
  selectedDocument.value = record
  showDetailModal.value = true
  // 增加阅读次数
  record.readCount++
}

const editDocument = (record) => {
  message.info(`编辑文档：${record.title}`)
}

const copyDocument = (record) => {
  message.info(`复制文档：${record.title}`)
}

const moveDocument = (record) => {
  message.info(`移动文档：${record.title}`)
}

const versionHistory = (record) => {
  message.info(`查看版本历史：${record.title}`)
}

const deleteDocument = (record) => {
  message.warning(`删除文档：${record.title}`)
}

const shareDocument = (record) => {
  message.info(`分享文档：${record.title}`)
}

const moreActions = (record) => {
  message.info(`更多操作：${record.title}`)
}

const importKnowledge = () => {
  message.info('批量导入功能开发中...')
}

const handleCreateDocument = () => {
  if (!createForm.title || !createForm.type || !createForm.content) {
    message.error('请填写必填项')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    showCreateModal.value = false
    message.success('文档创建成功')
    resetCreateForm()
    handleSearch()
  }, 1000)
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    title: '',
    type: undefined,
    categoryId: undefined,
    summary: '',
    tags: [],
    content: '',
    attachments: []
  })
}

const addCategory = (parentKey) => {
  message.info(`添加子分类到：${parentKey}`)
}

const editCategory = (key) => {
  message.info(`编辑分类：${key}`)
}

const deleteCategory = (key) => {
  message.warning(`删除分类：${key}`)
}

const saveCategoryChanges = () => {
  message.success('分类保存成功')
  showCategoryModal.value = false
}

const likeDocument = () => {
  selectedDocument.value.likeCount++
  message.success('点赞成功')
}

const collectDocument = () => {
  message.success('收藏成功')
}

const downloadDocument = () => {
  message.info('下载功能开发中...')
}

// 组件挂载
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.knowledge-base {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.title-area h2 {
  margin: 0;
  color: #262626;
}

.title-area p {
  margin: 4px 0 0;
  color: #8c8c8c;
}

.content-wrapper {
  display: flex;
  gap: 16px;
  height: calc(100vh - 180px);
}

.sidebar {
  width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-header h4 {
  margin: 0;
}

.category-tree {
  padding: 16px;
  max-height: calc(100vh - 260px);
  overflow-y: auto;
}

.count {
  font-size: 12px;
  color: #8c8c8c;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 16px;
}

.stats-section {
  margin-bottom: 16px;
}

.document-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.list-view {
  padding: 24px;
}

.card-view {
  padding: 24px;
  max-height: calc(100vh - 380px);
  overflow-y: auto;
}

.doc-title {
  color: #1890ff;
  font-weight: 500;
}

.doc-tags {
  margin-top: 4px;
}

.doc-card {
  height: 280px;
  cursor: pointer;
}

.card-cover {
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.doc-icon {
  font-size: 32px;
  color: white;
}

.doc-type {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255,255,255,0.8);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.card-desc p {
  margin-bottom: 8px;
  color: #595959;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.document-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-content {
  margin-bottom: 24px;
}

.content-area {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  min-height: 200px;
  white-space: pre-wrap;
}

.detail-tags {
  margin-bottom: 24px;
}

.detail-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>