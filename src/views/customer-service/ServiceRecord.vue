<template>
  <div class="service-record">
    <div class="header-section">
      <div class="title-area">
        <h2>服务记录</h2>
        <p>客户服务记录查询与统计分析</p>
      </div>
      <div class="action-area">
        <a-space>
          <a-button @click="exportRecords">
            <DownloadOutlined />
            导出记录
          </a-button>
          <a-button @click="showStatistics">
            <BarChartOutlined />
            统计分析
          </a-button>
          <a-button type="primary" @click="showDetailModal = true">
            <SearchOutlined />
            高级搜索
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日服务"
              :value="stats.todayService"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="本月服务"
              :value="stats.monthlyService"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均时长"
              :value="stats.avgDuration"
              suffix="分钟"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="解决率"
              :value="stats.resolveRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card enhanced-search">
      <template #title>
        <div class="search-header">
          <span>服务记录搜索</span>
          <div class="search-stats">
            <a-statistic
              title="今日服务"
              :value="stats.todayService"
              :value-style="{ color: '#1890ff', fontSize: '16px' }"
            />
          </div>
        </div>
      </template>
      <template #extra>
        <a-space>
          <a-button @click="toggleAdvanced">
            <span>{{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}</span>
            <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-button type="primary" @click="handleQuickSearch">
            <template #icon><SearchOutlined /></template>
            快速搜索
          </a-button>
        </a-space>
      </template>

      <a-form :model="searchForm" @submit="handleSearch">
        <!-- 基础搜索 -->
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="客户姓名">
              <a-input
                v-model:value="searchForm.customerName"
                placeholder="请输入客户姓名"
                allow-clear
              >
                <template #prefix><UserOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户电话">
              <a-input
                v-model:value="searchForm.customerPhone"
                placeholder="请输入客户电话"
                allow-clear
              >
                <template #prefix><PhoneOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="服务类型">
              <a-select
                v-model:value="searchForm.serviceType"
                placeholder="请选择服务类型"
                allow-clear
              >
                <template #suffixIcon><CustomerServiceOutlined /></template>
                <a-select-option value="consultation">
                  <a-tag color="blue">咨询服务</a-tag>
                </a-select-option>
                <a-select-option value="complaint">
                  <a-tag color="orange">投诉处理</a-tag>
                </a-select-option>
                <a-select-option value="payment">
                  <a-tag color="green">还款相关</a-tag>
                </a-select-option>
                <a-select-option value="technical">
                  <a-tag color="purple">技术支持</a-tag>
                </a-select-option>
                <a-select-option value="other">
                  <a-tag color="default">其他服务</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="服务状态">
              <a-select
                v-model:value="searchForm.status"
                placeholder="请选择状态"
                allow-clear
              >
                <template #suffixIcon><CheckCircleOutlined /></template>
                <a-select-option value="completed">
                  <a-tag color="success">已完成</a-tag>
                </a-select-option>
                <a-select-option value="processing">
                  <a-tag color="processing">处理中</a-tag>
                </a-select-option>
                <a-select-option value="pending">
                  <a-tag color="warning">待处理</a-tag>
                </a-select-option>
                <a-select-option value="cancelled">
                  <a-tag color="error">已取消</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 高级搜索 -->
        <div v-if="showAdvanced">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客服人员">
                <a-select
                  v-model:value="searchForm.agent"
                  placeholder="请选择客服"
                  allow-clear
                >
                  <template #suffixIcon><UserOutlined /></template>
                  <a-select-option value="agent1">张三</a-select-option>
                  <a-select-option value="agent2">李四</a-select-option>
                  <a-select-option value="agent3">王五</a-select-option>
                  <a-select-option value="agent4">赵六</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="服务时间">
                <a-range-picker
                  v-model:value="searchForm.dateRange"
                  :presets="timePresets"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="服务渠道">
                <a-select
                  v-model:value="searchForm.channel"
                  placeholder="请选择渠道"
                  allow-clear
                >
                  <a-select-option value="phone">电话</a-select-option>
                  <a-select-option value="online">在线客服</a-select-option>
                  <a-select-option value="email">邮件</a-select-option>
                  <a-select-option value="visit">上门</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="满意度">
                <a-select
                  v-model:value="searchForm.satisfaction"
                  placeholder="请选择满意度"
                  allow-clear
                >
                  <a-select-option value="5">非常满意</a-select-option>
                  <a-select-option value="4">满意</a-select-option>
                  <a-select-option value="3">一般</a-select-option>
                  <a-select-option value="2">不满意</a-select-option>
                  <a-select-option value="1">非常不满意</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 搜索按钮 -->
        <a-row>
          <a-col :span="24">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
                <a-button @click="exportRecords">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 记录列表 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>服务记录列表</span>
            <a-space>
              <a-button size="small" @click="refreshData">
                <ReloadOutlined />
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="batchExport">
                      批量导出
                    </a-menu-item>
                    <a-menu-item @click="batchDelete">
                      批量删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  批量操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </div>
        </template>

        <a-table
          :columns="columns"
          :data-source="recordsData"
          :pagination="pagination"
          :loading="loading"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'customerInfo'">
              <div class="customer-info">
                <div class="customer-name">{{ record.customerName }}</div>
                <div class="customer-phone">{{ record.customerPhone }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'serviceType'">
              <a-tag :color="getServiceTypeColor(record.serviceType)">
                {{ getServiceTypeText(record.serviceType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'satisfaction'">
              <a-rate :value="record.satisfaction" disabled allow-half />
            </template>
            <template v-else-if="column.key === 'duration'">
              <span>{{ formatDuration(record.duration) }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewRecord(record)">
                  查看详情
                </a-button>
                <a-button type="link" size="small" @click="editRecord(record)">
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="copyRecord(record)">
                        复制记录
                      </a-menu-item>
                      <a-menu-item @click="exportSingleRecord(record)">
                        导出单条
                      </a-menu-item>
                      <a-menu-item @click="addFollowUp(record)">
                        添加跟进
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="deleteRecord(record)" danger>
                        删除记录
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 服务记录详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="服务记录详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord" class="record-detail">
        <!-- 基本信息 -->
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="服务编号">
            {{ selectedRecord.serviceNo }}
          </a-descriptions-item>
          <a-descriptions-item label="客户姓名">
            {{ selectedRecord.customerName }}
          </a-descriptions-item>
          <a-descriptions-item label="客户电话">
            {{ selectedRecord.customerPhone }}
          </a-descriptions-item>
          <a-descriptions-item label="客户邮箱">
            {{ selectedRecord.customerEmail }}
          </a-descriptions-item>
          <a-descriptions-item label="服务类型">
            <a-tag :color="getServiceTypeColor(selectedRecord.serviceType)">
              {{ getServiceTypeText(selectedRecord.serviceType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="服务状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="客服人员">
            {{ selectedRecord.agentName }}
          </a-descriptions-item>
          <a-descriptions-item label="服务时长">
            {{ formatDuration(selectedRecord.duration) }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ selectedRecord.startTime }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ selectedRecord.endTime }}
          </a-descriptions-item>
          <a-descriptions-item label="满意度评分">
            <a-rate :value="selectedRecord.satisfaction" disabled allow-half />
            <span style="margin-left: 8px">{{ selectedRecord.satisfaction }}分</span>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedRecord.createTime }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 服务内容 -->
        <div class="service-content">
          <h4>服务内容</h4>
          <div class="content-section">
            <div class="content-item">
              <strong>问题描述：</strong>
              <p>{{ selectedRecord.problemDescription }}</p>
            </div>
            <div class="content-item">
              <strong>处理过程：</strong>
              <p>{{ selectedRecord.processingSteps }}</p>
            </div>
            <div class="content-item">
              <strong>解决方案：</strong>
              <p>{{ selectedRecord.solution }}</p>
            </div>
            <div class="content-item">
              <strong>客户反馈：</strong>
              <p>{{ selectedRecord.customerFeedback }}</p>
            </div>
          </div>
        </div>

        <!-- 附件信息 -->
        <div class="attachments-section" v-if="selectedRecord.attachments?.length">
          <h4>相关附件</h4>
          <a-list size="small" :data-source="selectedRecord.attachments">
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a @click="downloadAttachment(item)">下载</a>
                  <a @click="previewAttachment(item)">预览</a>
                </template>
                <a-list-item-meta
                  :title="item.name"
                  :description="`大小：${item.size} | 上传时间：${item.uploadTime}`"
                >
                  <template #avatar>
                    <a-avatar :src="item.icon" />
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>

        <!-- 跟进记录 -->
        <div class="follow-up-section">
          <h4>跟进记录</h4>
          <a-timeline>
            <a-timeline-item
              v-for="followUp in selectedRecord.followUps"
              :key="followUp.id"
              :color="getFollowUpColor(followUp.type)"
            >
              <div class="follow-up-content">
                <div class="follow-up-header">
                  <span class="follow-up-type">{{ followUp.type }}</span>
                  <span class="follow-up-time">{{ followUp.time }}</span>
                </div>
                <div class="follow-up-desc">{{ followUp.description }}</div>
                <div class="follow-up-operator">操作人：{{ followUp.operator }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-modal>

    <!-- 统计分析模态框 -->
    <a-modal
      v-model:open="showStatsModal"
      title="服务记录统计分析"
      width="1200px"
      :footer="null"
    >
      <div class="stats-content">
        <!-- 图表区域 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="服务类型分布">
              <div id="serviceTypeChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="服务时长分布">
              <div id="durationChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="12">
            <a-card title="满意度趋势">
              <div id="satisfactionTrendChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="客服绩效对比">
              <div id="agentPerformanceChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 数据表格 -->
        <a-row style="margin-top: 16px;">
          <a-col :span="24">
            <a-card title="详细统计数据">
              <a-table
                :columns="statsColumns"
                :data-source="statsData"
                size="small"
                :pagination="false"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  SearchOutlined,
  DownloadOutlined,
  BarChartOutlined,
  ReloadOutlined,
  DownOutlined,
  UpOutlined,
  UserOutlined,
  PhoneOutlined,
  CustomerServiceOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const showDetailModal = ref(false)
const showStatsModal = ref(false)
const selectedRecord = ref(null)
const selectedRowKeys = ref([])
const showAdvanced = ref(false)

// 统计数据
const stats = reactive({
  todayService: 89,
  monthlyService: 1456,
  avgDuration: 18.5,
  resolveRate: 94.2
})

// 搜索表单
const searchForm = reactive({
  customerName: '',
  customerPhone: '',
  serviceType: undefined,
  status: undefined,
  agent: undefined,
  dateRange: undefined,
  channel: undefined,
  satisfaction: undefined
})

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const rowSelection = {
  selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 表格列配置
const columns = [
  {
    title: '服务编号',
    dataIndex: 'serviceNo',
    key: 'serviceNo',
    width: 120
  },
  {
    title: '客户信息',
    key: 'customerInfo',
    width: 150
  },
  {
    title: '服务类型',
    dataIndex: 'serviceType',
    key: 'serviceType',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '客服人员',
    dataIndex: 'agentName',
    key: 'agentName',
    width: 100
  },
  {
    title: '服务时长',
    dataIndex: 'duration',
    key: 'duration',
    width: 100
  },
  {
    title: '满意度',
    dataIndex: 'satisfaction',
    key: 'satisfaction',
    width: 120
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 统计表格列
const statsColumns = [
  { title: '统计项目', dataIndex: 'item', key: 'item' },
  { title: '今日', dataIndex: 'today', key: 'today' },
  { title: '本周', dataIndex: 'week', key: 'week' },
  { title: '本月', dataIndex: 'month', key: 'month' },
  { title: '增长率', dataIndex: 'growth', key: 'growth' }
]

// 模拟数据
const recordsData = ref([
  {
    id: 1,
    serviceNo: 'SVC20231128001',
    customerName: '张三',
    customerPhone: '13812345678',
    customerEmail: '<EMAIL>',
    serviceType: 'consultation',
    status: 'completed',
    agentName: '李四',
    duration: 1250, // 秒
    satisfaction: 4.5,
    startTime: '2023-11-28 09:30:00',
    endTime: '2023-11-28 09:50:50',
    createTime: '2023-11-28 09:30:00',
    problemDescription: '客户咨询还款计划相关问题，希望了解分期还款的具体流程和费用计算方式。',
    processingSteps: '1. 核实客户身份信息\n2. 查询客户账户状态\n3. 详细解释分期还款流程\n4. 提供费用计算示例\n5. 发送相关资料链接',
    solution: '为客户制定了个性化的分期还款方案，并详细说明了每期还款金额和时间安排。客户表示满意并确认执行该方案。',
    customerFeedback: '客服态度很好，解释很详细，对提供的方案很满意。',
    attachments: [
      {
        id: 1,
        name: '分期还款方案.pdf',
        size: '245KB',
        uploadTime: '2023-11-28 09:45:00',
        icon: '/icons/pdf.png'
      }
    ],
    followUps: [
      {
        id: 1,
        type: '创建记录',
        description: '客户通过在线客服咨询还款计划相关问题',
        time: '2023-11-28 09:30:00',
        operator: '系统'
      },
      {
        id: 2,
        type: '问题处理',
        description: '详细解答客户疑问，提供分期还款方案',
        time: '2023-11-28 09:45:00',
        operator: '李四'
      },
      {
        id: 3,
        type: '服务完成',
        description: '客户确认方案，服务圆满结束',
        time: '2023-11-28 09:50:00',
        operator: '李四'
      }
    ]
  },
  {
    id: 2,
    serviceNo: 'SVC20231128002',
    customerName: '王五',
    customerPhone: '13987654321',
    customerEmail: '<EMAIL>',
    serviceType: 'complaint',
    status: 'processing',
    agentName: '赵六',
    duration: 1800,
    satisfaction: 3.0,
    startTime: '2023-11-28 10:15:00',
    endTime: '2023-11-28 10:45:00',
    createTime: '2023-11-28 10:15:00',
    problemDescription: '客户投诉催收人员态度恶劣，要求道歉并更换负责人。',
    processingSteps: '1. 耐心倾听客户投诉\n2. 记录投诉具体内容\n3. 向客户道歉\n4. 承诺调查处理\n5. 安排更换负责人',
    solution: '已向客户诚恳道歉，安排更换催收负责人，并承诺加强内部培训。',
    customerFeedback: '希望这次能真正改善服务质量。',
    attachments: [],
    followUps: [
      {
        id: 1,
        type: '投诉受理',
        description: '客户投诉催收人员态度问题',
        time: '2023-11-28 10:15:00',
        operator: '系统'
      },
      {
        id: 2,
        type: '调查处理',
        description: '核实投诉内容，向客户道歉并承诺改进',
        time: '2023-11-28 10:30:00',
        operator: '赵六'
      }
    ]
  }
])

// 统计数据
const statsData = ref([
  { item: '服务总数', today: 89, week: 623, month: 1456, growth: '+12.5%' },
  { item: '客户满意度', today: '4.3分', week: '4.2分', month: '4.1分', growth: '****%' },
  { item: '平均时长', today: '18.5分钟', week: '19.2分钟', month: '18.8分钟', growth: '-1.6%' },
  { item: '解决率', today: '94.2%', week: '93.8%', month: '94.5%', growth: '+0.4%' }
])

// 方法定义
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    customerName: '',
    customerPhone: '',
    serviceType: undefined,
    status: undefined,
    agent: undefined,
    dateRange: undefined,
    channel: undefined,
    satisfaction: undefined
  })
  handleSearch()
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 快速搜索
const handleQuickSearch = () => {
  handleSearch()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const getServiceTypeColor = (type) => {
  const colors = {
    consultation: 'blue',
    complaint: 'red',
    payment: 'green',
    technical: 'orange',
    other: 'gray'
  }
  return colors[type] || 'blue'
}

const getServiceTypeText = (type) => {
  const texts = {
    consultation: '咨询服务',
    complaint: '投诉处理',
    payment: '还款相关',
    technical: '技术支持',
    other: '其他服务'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    completed: 'green',
    processing: 'blue',
    pending: 'orange',
    cancelled: 'gray'
  }
  return colors[status] || 'blue'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    pending: '待处理',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getFollowUpColor = (type) => {
  const colors = {
    '创建记录': 'blue',
    '问题处理': 'green',
    '服务完成': 'green',
    '投诉受理': 'red',
    '调查处理': 'orange'
  }
  return colors[type] || 'blue'
}

const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const viewRecord = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const editRecord = (record) => {
  message.info(`编辑记录：${record.serviceNo}`)
}

const copyRecord = (record) => {
  message.info(`复制记录：${record.serviceNo}`)
}

const deleteRecord = (record) => {
  message.warning(`删除记录：${record.serviceNo}`)
}

const addFollowUp = (record) => {
  message.info(`添加跟进：${record.serviceNo}`)
}

const exportSingleRecord = (record) => {
  message.info(`导出记录：${record.serviceNo}`)
}

const exportRecords = () => {
  message.info('正在导出服务记录...')
}

const showStatistics = () => {
  showStatsModal.value = true
}

const refreshData = () => {
  handleSearch()
}

const batchExport = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要导出的记录')
    return
  }
  message.info(`批量导出 ${selectedRowKeys.value.length} 条记录`)
}

const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的记录')
    return
  }
  message.warning(`批量删除 ${selectedRowKeys.value.length} 条记录`)
}

const downloadAttachment = (attachment) => {
  message.info(`下载附件：${attachment.name}`)
}

const previewAttachment = (attachment) => {
  message.info(`预览附件：${attachment.name}`)
}

// 组件挂载
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.service-record {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.title-area h2 {
  margin: 0;
  color: #262626;
}

.title-area p {
  margin: 4px 0 0;
  color: #8c8c8c;
}

.overview-section {
  margin-bottom: 24px;
}

.search-section {
  margin-bottom: 24px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.customer-info {
  line-height: 1.4;
}

.customer-name {
  font-weight: 500;
  color: #262626;
}

.customer-phone {
  font-size: 12px;
  color: #8c8c8c;
}

.record-detail {
  max-height: 600px;
  overflow-y: auto;
}

.service-content {
  margin-top: 24px;
}

.content-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.content-item {
  margin-bottom: 16px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-item strong {
  color: #262626;
}

.content-item p {
  margin: 8px 0 0;
  color: #595959;
  line-height: 1.6;
  white-space: pre-wrap;
}

.attachments-section {
  margin-top: 24px;
}

.follow-up-section {
  margin-top: 24px;
}

.follow-up-content {
  padding-left: 8px;
}

.follow-up-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.follow-up-type {
  font-weight: 500;
  color: #262626;
}

.follow-up-time {
  color: #8c8c8c;
  font-size: 12px;
}

.follow-up-desc {
  margin-bottom: 4px;
  color: #595959;
}

.follow-up-operator {
  font-size: 12px;
  color: #8c8c8c;
}

.stats-content {
  max-height: 700px;
  overflow-y: auto;
}

/* 增强搜索样式 */
.enhanced-search {
  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    > span {
      font-weight: 500;
      font-size: 16px;
    }

    .search-stats {
      display: flex;
      gap: 16px;
      align-items: center;
    }
  }

  .ant-card-extra {
    .ant-space {
      .ant-btn {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>