<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>通话记录</h2>
        <a-space>
          <a-button type="primary" @click="showAddModal = true">
            <plus-outlined />
            添加记录
          </a-button>
          <a-button @click="showQualityModal = true">
            <audit-outlined />
            质检管理
          </a-button>
          <a-button @click="showAnalysisModal = true">
            <bar-chart-outlined />
            数据分析
          </a-button>
          <a-button @click="refreshData">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <!-- 搜索筛选区域 -->
      <a-card class="search-card enhanced-search">
        <template #title>
          <div class="search-header">
            <span>通话记录搜索</span>
            <div class="search-stats">
              <a-statistic
                title="今日通话"
                :value="todayCallCount"
                :value-style="{ color: '#1890ff', fontSize: '16px' }"
              />
            </div>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="toggleAdvanced">
              {{ showAdvancedSearch ? '收起高级搜索' : '展开高级搜索' }}
              <component :is="showAdvancedSearch ? 'UpOutlined' : 'DownOutlined'" />
            </a-button>
            <a-button type="primary" @click="handleQuickSearch">
              <template #icon><SearchOutlined /></template>
              快速搜索
            </a-button>
          </a-space>
        </template>

        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input
                  v-model:value="searchParams.customerName"
                  placeholder="请输入客户姓名"
                  allow-clear
                >
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="手机号码">
                <a-input
                  v-model:value="searchParams.phone"
                  placeholder="请输入手机号码"
                  allow-clear
                >
                  <template #prefix><PhoneOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="通话结果">
                <a-select
                  v-model:value="searchParams.callResult"
                  placeholder="请选择通话结果"
                  allow-clear
                >
                  <a-select-option value="connected">
                    <a-tag color="green">接通</a-tag>
                  </a-select-option>
                  <a-select-option value="no_answer">
                    <a-tag color="orange">无人接听</a-tag>
                  </a-select-option>
                  <a-select-option value="busy">
                    <a-tag color="blue">忙线</a-tag>
                  </a-select-option>
                  <a-select-option value="rejected">
                    <a-tag color="red">拒接</a-tag>
                  </a-select-option>
                  <a-select-option value="invalid">
                    <a-tag color="gray">空号</a-tag>
                  </a-select-option>
                  <a-select-option value="hung_up">
                    <a-tag color="purple">挂断</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="催收员">
                <a-select
                  v-model:value="searchParams.collector"
                  placeholder="请选择催收员"
                  allow-clear
                  show-search
                  :filter-option="filterOption"
                >
                  <a-select-option value="张催收">
                    <div class="collector-option">
                      <a-avatar :size="20">张</a-avatar>
                      <span style="margin-left: 8px;">张催收</span>
                    </div>
                  </a-select-option>
                  <a-select-option value="李催收">
                    <div class="collector-option">
                      <a-avatar :size="20">李</a-avatar>
                      <span style="margin-left: 8px;">李催收</span>
                    </div>
                  </a-select-option>
                  <a-select-option value="王催收">
                    <div class="collector-option">
                      <a-avatar :size="20">王</a-avatar>
                      <span style="margin-left: 8px;">王催收</span>
                    </div>
                  </a-select-option>
                  <a-select-option value="赵催收">
                    <div class="collector-option">
                      <a-avatar :size="20">赵</a-avatar>
                      <span style="margin-left: 8px;">赵催收</span>
                    </div>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" v-if="showAdvancedSearch" style="margin-top: 16px;">
            <a-col :span="6">
              <a-form-item label="通话时间">
                <a-range-picker
                  v-model:value="searchParams.callTimeRange"
                  show-time
                  style="width: 100%"
                  :presets="timePresets"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="通话时长">
                <a-input-group compact>
                  <a-input-number
                    v-model:value="searchParams.durationMin"
                    :min="0"
                    placeholder="最小时长(秒)"
                    style="width: 50%"
                  />
                  <a-input-number
                    v-model:value="searchParams.durationMax"
                    :min="0"
                    placeholder="最大时长(秒)"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="质检状态">
                <a-select
                  v-model:value="searchParams.qualityStatus"
                  placeholder="请选择质检状态"
                  allow-clear
                >
                  <a-select-option value="pending">
                    <a-tag color="orange">待质检</a-tag>
                  </a-select-option>
                  <a-select-option value="passed">
                    <a-tag color="green">质检通过</a-tag>
                  </a-select-option>
                  <a-select-option value="failed">
                    <a-tag color="red">质检不通过</a-tag>
                  </a-select-option>
                  <a-select-option value="reviewing">
                    <a-tag color="blue">质检中</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input
                  v-model:value="searchParams.caseNumber"
                  placeholder="请输入案件编号"
                  allow-clear
                >
                  <template #prefix><FileTextOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="handleReset">
                    <reload-outlined />
                    重置
                  </a-button>
                  <a-button @click="exportData">
                    <download-outlined />
                    导出记录
                  </a-button>
                </a-space>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="总通话次数"
              :value="stats.totalCalls"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <phone-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="接通率"
              :value="stats.connectionRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="平均时长"
              :value="stats.avgDuration"
              suffix="秒"
              :precision="0"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="质检通过率"
              :value="stats.qualityPassRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <audit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 通话记录列表 -->
      <a-card class="call-records-card">
        <template #title>
          <a-space>
            <span>通话记录列表</span>
            <a-tag color="blue">共 {{ callRecords.length }} 条</a-tag>
          </a-space>
        </template>
        <template #extra>
          <a-space>
            <a-dropdown>
              <a-button>
                批量操作
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleBatchOperation">
                  <a-menu-item key="quality">批量质检</a-menu-item>
                  <a-menu-item key="export">批量导出</a-menu-item>
                  <a-menu-item key="delete">批量删除</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>

        <a-table
          :columns="columns"
          :data-source="callRecords"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
          }"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true
          }"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'customerName'">
              <a @click="viewCallDetail(record)">{{ record.customerName }}</a>
            </template>
            <template v-else-if="column.key === 'callResult'">
              <a-tag :color="getCallResultColor(record.callResult)">
                {{ getCallResultText(record.callResult) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'duration'">
              <span>{{ formatDuration(record.duration) }}</span>
            </template>
            <template v-else-if="column.key === 'hasRecording'">
              <a-tag v-if="record.hasRecording" color="green">
                <sound-outlined /> 有录音
              </a-tag>
              <a-tag v-else color="default">
                无录音
              </a-tag>
            </template>
            <template v-else-if="column.key === 'qualityStatus'">
              <a-tag :color="getQualityStatusColor(record.qualityStatus)">
                {{ getQualityStatusText(record.qualityStatus) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'emotion'">
              <a-tag :color="getEmotionColor(record.emotion)" v-if="record.emotion">
                {{ getEmotionText(record.emotion) }}
              </a-tag>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewCallDetail(record)"
                >
                  详情
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="playRecording(record)"
                  v-if="record.hasRecording"
                >
                  播放
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="startQuality(record)"
                  v-if="record.qualityStatus === 'pending'"
                >
                  质检
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMoreAction(key, record)">
                      <a-menu-item key="edit">编辑</a-menu-item>
                      <a-menu-item key="download" v-if="record.hasRecording">下载录音</a-menu-item>
                      <a-menu-item key="summary">生成摘要</a-menu-item>
                      <a-menu-item key="report">举报违规</a-menu-item>
                      <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 添加通话记录弹窗 -->
      <a-modal
        v-model:open="showAddModal"
        title="添加通话记录"
        width="800px"
        @ok="handleAddRecord"
      >
        <a-form :model="addForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-tabs v-model:activeKey="activeAddTab">
            <a-tab-pane key="basic" tab="基本信息">
              <a-form-item label="客户姓名" required>
                <a-select 
                  v-model:value="addForm.customerName" 
                  placeholder="请选择客户"
                  show-search
                  allow-clear
                >
                  <a-select-option value="张三">张三</a-select-option>
                  <a-select-option value="李四">李四</a-select-option>
                  <a-select-option value="王五">王五</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="联系号码" required>
                <a-input v-model:value="addForm.phone" placeholder="请输入联系号码" />
              </a-form-item>
              <a-form-item label="案件编号">
                <a-input v-model:value="addForm.caseNumber" placeholder="请输入案件编号" />
              </a-form-item>
              <a-form-item label="通话时间" required>
                <a-date-picker 
                  v-model:value="addForm.callTime" 
                  show-time
                  style="width: 100%"
                  placeholder="请选择通话时间"
                />
              </a-form-item>
              <a-form-item label="通话时长(秒)" required>
                <a-input-number 
                  v-model:value="addForm.duration" 
                  :min="0"
                  placeholder="请输入通话时长"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="通话结果" required>
                <a-select v-model:value="addForm.callResult" placeholder="请选择通话结果">
                  <a-select-option value="connected">接通</a-select-option>
                  <a-select-option value="no_answer">无人接听</a-select-option>
                  <a-select-option value="busy">忙线</a-select-option>
                  <a-select-option value="rejected">拒接</a-select-option>
                  <a-select-option value="invalid">空号</a-select-option>
                  <a-select-option value="hung_up">挂断</a-select-option>
                </a-select>
              </a-form-item>
            </a-tab-pane>
            
            <a-tab-pane key="content" tab="通话内容">
              <a-form-item label="通话摘要">
                <a-textarea 
                  v-model:value="addForm.summary" 
                  :rows="4"
                  placeholder="请输入通话摘要..."
                />
              </a-form-item>
              <a-form-item label="客户态度">
                <a-select v-model:value="addForm.customerAttitude" placeholder="请选择客户态度">
                  <a-select-option value="cooperative">配合</a-select-option>
                  <a-select-option value="neutral">中性</a-select-option>
                  <a-select-option value="resistant">抗拒</a-select-option>
                  <a-select-option value="aggressive">恶劣</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="还款承诺">
                <a-textarea 
                  v-model:value="addForm.paymentPromise" 
                  :rows="3"
                  placeholder="客户的还款承诺内容..."
                />
              </a-form-item>
              <a-form-item label="关键信息">
                <a-textarea 
                  v-model:value="addForm.keyInfo" 
                  :rows="3"
                  placeholder="通话中获取的关键信息..."
                />
              </a-form-item>
              <a-form-item label="下次联系时间">
                <a-date-picker 
                  v-model:value="addForm.nextContactTime" 
                  show-time
                  style="width: 100%"
                  placeholder="请选择下次联系时间"
                />
              </a-form-item>
            </a-tab-pane>
            
            <a-tab-pane key="recording" tab="录音管理">
              <a-form-item label="录音文件">
                <a-upload
                  v-model:file-list="addForm.recordingFiles"
                  :before-upload="() => false"
                  accept=".mp3,.wav,.m4a"
                >
                  <a-button>
                    <upload-outlined />
                    上传录音
                  </a-button>
                </a-upload>
                <div style="margin-top: 8px; color: #666;">
                  支持mp3、wav、m4a格式，单个文件不超过50MB
                </div>
              </a-form-item>
              <a-form-item label="录音质量">
                <a-radio-group v-model:value="addForm.recordingQuality">
                  <a-radio value="high">高质量</a-radio>
                  <a-radio value="medium">中等</a-radio>
                  <a-radio value="low">低质量</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="备注">
                <a-textarea 
                  v-model:value="addForm.remark" 
                  :rows="3"
                  placeholder="其他备注信息..."
                />
              </a-form-item>
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </a-modal>

      <!-- 通话详情弹窗 -->
      <a-modal
        v-model:open="showDetailModal"
        :title="currentRecord?.customerName + ' - 通话详情'"
        width="1200px"
        :footer="null"
      >
        <div v-if="currentRecord" class="call-detail">
          <!-- 基本信息 -->
          <a-descriptions title="通话基本信息" :column="2" bordered>
            <a-descriptions-item label="客户姓名">{{ currentRecord.customerName }}</a-descriptions-item>
            <a-descriptions-item label="联系号码">{{ currentRecord.phone }}</a-descriptions-item>
            <a-descriptions-item label="案件编号">{{ currentRecord.caseNumber }}</a-descriptions-item>
            <a-descriptions-item label="催收员">{{ currentRecord.collector }}</a-descriptions-item>
            <a-descriptions-item label="通话时间">{{ currentRecord.callTime }}</a-descriptions-item>
            <a-descriptions-item label="通话时长">{{ formatDuration(currentRecord.duration) }}</a-descriptions-item>
            <a-descriptions-item label="通话结果">
              <a-tag :color="getCallResultColor(currentRecord.callResult)">
                {{ getCallResultText(currentRecord.callResult) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="客户态度">
              <a-tag :color="getAttitudeColor(currentRecord.customerAttitude)">
                {{ getAttitudeText(currentRecord.customerAttitude) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 通话内容 -->
          <div class="content-section" style="margin-top: 24px;">
            <h4>通话内容</h4>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="通话摘要">
                {{ currentRecord.summary || '暂无摘要' }}
              </a-descriptions-item>
              <a-descriptions-item label="还款承诺" v-if="currentRecord.paymentPromise">
                {{ currentRecord.paymentPromise }}
              </a-descriptions-item>
              <a-descriptions-item label="关键信息" v-if="currentRecord.keyInfo">
                {{ currentRecord.keyInfo }}
              </a-descriptions-item>
              <a-descriptions-item label="下次联系时间" v-if="currentRecord.nextContactTime">
                {{ currentRecord.nextContactTime }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 录音信息 -->
          <div class="recording-section" style="margin-top: 24px;" v-if="currentRecord.hasRecording">
            <h4>录音信息</h4>
            <a-card size="small">
              <div class="recording-player">
                <audio :src="currentRecord.recordingUrl" controls style="width: 100%"></audio>
                <div class="recording-info" style="margin-top: 12px;">
                  <a-space>
                    <a-tag>时长: {{ formatDuration(currentRecord.duration) }}</a-tag>
                    <a-tag>质量: {{ getRecordingQualityText(currentRecord.recordingQuality) }}</a-tag>
                    <a-tag>大小: {{ currentRecord.recordingSize }}</a-tag>
                    <a-button type="link" size="small" @click="downloadRecording(currentRecord)">
                      <download-outlined />
                      下载
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-card>
          </div>

          <!-- AI分析结果 -->
          <div class="ai-analysis" style="margin-top: 24px;" v-if="currentRecord.aiAnalysis">
            <h4>AI分析结果</h4>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="情绪分析" size="small">
                  <div class="emotion-analysis">
                    <a-tag :color="getEmotionColor(currentRecord.aiAnalysis.emotion)">
                      {{ getEmotionText(currentRecord.aiAnalysis.emotion) }}
                    </a-tag>
                    <a-progress 
                      :percent="currentRecord.aiAnalysis.emotionScore" 
                      size="small"
                      :stroke-color="getEmotionColor(currentRecord.aiAnalysis.emotion)"
                      style="margin-top: 8px"
                    />
                  </div>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card title="关键词提取" size="small">
                  <div class="keywords">
                    <a-tag 
                      v-for="keyword in currentRecord.aiAnalysis.keywords" 
                      :key="keyword"
                      style="margin-bottom: 4px"
                    >
                      {{ keyword }}
                    </a-tag>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card title="合规检查" size="small">
                  <div class="compliance-check">
                    <a-tag :color="currentRecord.aiAnalysis.compliance.status === 'pass' ? 'green' : 'red'">
                      {{ currentRecord.aiAnalysis.compliance.status === 'pass' ? '合规' : '违规' }}
                    </a-tag>
                    <div style="margin-top: 8px; font-size: 12px; color: #666;">
                      {{ currentRecord.aiAnalysis.compliance.details }}
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>

          <!-- 质检信息 -->
          <div class="quality-section" style="margin-top: 24px;" v-if="currentRecord.qualityInfo">
            <h4>质检信息</h4>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="质检状态">
                <a-tag :color="getQualityStatusColor(currentRecord.qualityStatus)">
                  {{ getQualityStatusText(currentRecord.qualityStatus) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="质检员">{{ currentRecord.qualityInfo.inspector }}</a-descriptions-item>
              <a-descriptions-item label="质检时间">{{ currentRecord.qualityInfo.inspectTime }}</a-descriptions-item>
              <a-descriptions-item label="质检分数">
                <span :style="{ color: currentRecord.qualityInfo.score >= 80 ? '#52c41a' : '#f5222d' }">
                  {{ currentRecord.qualityInfo.score }}分
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="质检意见" span="2">
                {{ currentRecord.qualityInfo.comments }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
      </a-modal>

      <!-- 质检管理弹窗 -->
      <a-modal
        v-model:open="showQualityModal"
        title="质检管理"
        width="1000px"
        :footer="null"
      >
        <a-tabs v-model:activeKey="activeQualityTab">
          <a-tab-pane key="pending" tab="待质检">
            <a-table
              :columns="qualityColumns"
              :data-source="pendingQualityRecords"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="startQualityCheck(record)">
                      开始质检
                    </a-button>
                    <a-button type="link" size="small" @click="assignQuality(record)">
                      分配质检员
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="completed" tab="已质检">
            <a-table
              :columns="qualityColumns"
              :data-source="completedQualityRecords"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'qualityResult'">
                  <a-tag :color="record.qualityResult === 'pass' ? 'green' : 'red'">
                    {{ record.qualityResult === 'pass' ? '通过' : '不通过' }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewQualityDetail(record)">
                      查看详情
                    </a-button>
                    <a-button type="link" size="small" @click="reQuality(record)">
                      重新质检
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 数据分析弹窗 -->
      <a-modal
        v-model:open="showAnalysisModal"
        title="通话数据分析"
        width="1200px"
        :footer="null"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="通话趋势分析" size="small">
              <div style="height: 300px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                趋势图表区域
              </div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="通话结果分布" size="small">
              <div style="height: 300px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                饼图区域
              </div>
            </a-card>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="12">
            <a-card title="质检通过率统计" size="small">
              <div style="height: 300px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                柱状图区域
              </div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="催收员效率排行" size="small">
              <div style="height: 300px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                排行榜区域
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import {
  PlusOutlined,
  AuditOutlined,
  BarChartOutlined,
  ReloadOutlined,
  SearchOutlined,
  ClearOutlined,
  DownOutlined,
  UpOutlined,
  PhoneOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SoundOutlined,
  DownloadOutlined,
  UploadOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'

// 状态管理
const loading = ref(false)
const showAdvancedSearch = ref(false)
const showAddModal = ref(false)
const showDetailModal = ref(false)
const showQualityModal = ref(false)
const showAnalysisModal = ref(false)
const currentRecord = ref(null)
const selectedRowKeys = ref([])
const activeAddTab = ref('basic')
const activeQualityTab = ref('pending')

// 搜索参数
const searchParams = reactive({
  customerName: '',
  phone: '',
  callResult: undefined,
  collector: undefined,
  callTimeRange: [],
  durationMin: undefined,
  durationMax: undefined,
  qualityStatus: undefined,
  caseNumber: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 150
})

// 统计数据
const stats = reactive({
  totalCalls: 2856,
  connectionRate: 68.5,
  avgDuration: 185,
  qualityPassRate: 92.3
})

// 今日通话数量
const todayCallCount = ref(156)

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 添加记录表单
const addForm = reactive({
  customerName: '',
  phone: '',
  caseNumber: '',
  callTime: null,
  duration: undefined,
  callResult: undefined,
  summary: '',
  customerAttitude: undefined,
  paymentPromise: '',
  keyInfo: '',
  nextContactTime: null,
  recordingFiles: [],
  recordingQuality: 'medium',
  remark: ''
})

// 通话记录数据
const callRecords = ref([
  {
    id: 1,
    customerName: '张三',
    phone: '138****5678',
    caseNumber: 'CS202401001',
    collector: '李催收',
    callTime: '2024-01-20 14:30:15',
    duration: 285,
    callResult: 'connected',
    hasRecording: true,
    recordingUrl: '/recordings/call_001.mp3',
    recordingQuality: 'high',
    recordingSize: '2.3MB',
    qualityStatus: 'passed',
    summary: '客户表示愿意还款，已约定明天支付5000元',
    customerAttitude: 'cooperative',
    paymentPromise: '明天下午2点前支付5000元',
    keyInfo: '客户目前收入稳定，有还款能力',
    nextContactTime: '2024-01-21 14:00:00',
    emotion: 'positive',
    aiAnalysis: {
      emotion: 'positive',
      emotionScore: 85,
      keywords: ['还款', '支付', '明天', '5000元'],
      compliance: {
        status: 'pass',
        details: '通话内容合规，未发现违规行为'
      }
    },
    qualityInfo: {
      inspector: '王质检',
      inspectTime: '2024-01-20 16:00:00',
      score: 95,
      comments: '话术规范，服务态度良好，获取信息完整'
    }
  },
  {
    id: 2,
    customerName: '李四',
    phone: '139****1234',
    caseNumber: 'CS202401002',
    collector: '张催收',
    callTime: '2024-01-20 15:45:20',
    duration: 120,
    callResult: 'rejected',
    hasRecording: false,
    qualityStatus: 'pending',
    summary: '客户拒接电话，无法沟通',
    customerAttitude: 'resistant',
    emotion: 'negative'
  },
  {
    id: 3,
    customerName: '王五',
    phone: '137****9876',
    caseNumber: 'CS202401003',
    collector: '赵催收',
    callTime: '2024-01-20 16:20:10',
    duration: 95,
    callResult: 'no_answer',
    hasRecording: false,
    qualityStatus: 'pending',
    summary: '无人接听，稍后再试',
    customerAttitude: null
  }
])

// 待质检记录
const pendingQualityRecords = ref([
  {
    id: 2,
    customerName: '李四',
    phone: '139****1234',
    collector: '张催收',
    callTime: '2024-01-20 15:45:20',
    duration: 120,
    callResult: 'rejected'
  },
  {
    id: 3,
    customerName: '王五',
    phone: '137****9876',
    collector: '赵催收',
    callTime: '2024-01-20 16:20:10',
    duration: 95,
    callResult: 'no_answer'
  }
])

// 已质检记录
const completedQualityRecords = ref([
  {
    id: 1,
    customerName: '张三',
    phone: '138****5678',
    collector: '李催收',
    callTime: '2024-01-20 14:30:15',
    duration: 285,
    qualityResult: 'pass',
    inspector: '王质检',
    score: 95
  }
])

// 表格列配置
const columns = [
  { title: '客户姓名', dataIndex: 'customerName', key: 'customerName', width: 100 },
  { title: '联系号码', dataIndex: 'phone', key: 'phone', width: 120 },
  { title: '案件编号', dataIndex: 'caseNumber', key: 'caseNumber', width: 120 },
  { title: '催收员', dataIndex: 'collector', key: 'collector', width: 100 },
  { title: '通话时间', dataIndex: 'callTime', key: 'callTime', width: 150 },
  { title: '通话时长', dataIndex: 'duration', key: 'duration', width: 100 },
  { title: '通话结果', dataIndex: 'callResult', key: 'callResult', width: 100 },
  { title: '录音', dataIndex: 'hasRecording', key: 'hasRecording', width: 80 },
  { title: '质检状态', dataIndex: 'qualityStatus', key: 'qualityStatus', width: 100 },
  { title: '情绪', dataIndex: 'emotion', key: 'emotion', width: 80 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const qualityColumns = [
  { title: '客户姓名', dataIndex: 'customerName', key: 'customerName' },
  { title: '催收员', dataIndex: 'collector', key: 'collector' },
  { title: '通话时间', dataIndex: 'callTime', key: 'callTime' },
  { title: '通话时长', dataIndex: 'duration', key: 'duration' },
  { title: '质检结果', dataIndex: 'qualityResult', key: 'qualityResult' },
  { title: '质检员', dataIndex: 'inspector', key: 'inspector' },
  { title: '分数', dataIndex: 'score', key: 'score' },
  { title: '操作', key: 'action' }
]

// 方法
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const handleSearch = () => {
  console.log('搜索参数:', searchParams)
  message.success('查询成功')
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = undefined
    }
  })
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const viewCallDetail = (record) => {
  currentRecord.value = record
  showDetailModal.value = true
}

const playRecording = (record) => {
  message.info(`播放 ${record.customerName} 的通话录音`)
}

const startQuality = (record) => {
  message.info(`开始质检 ${record.customerName} 的通话记录`)
}

const handleMoreAction = (action, record) => {
  switch (action) {
    case 'edit':
      message.info(`编辑 ${record.customerName} 的通话记录`)
      break
    case 'download':
      downloadRecording(record)
      break
    case 'summary':
      message.info(`生成 ${record.customerName} 的通话摘要`)
      break
    case 'report':
      message.info(`举报 ${record.customerName} 的违规通话`)
      break
    case 'delete':
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除 ${record.customerName} 的通话记录吗？`,
        onOk() {
          message.success('删除成功')
        }
      })
      break
  }
}

const handleBatchOperation = ({ key }) => {
  const count = selectedRowKeys.value.length
  if (count === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  switch (key) {
    case 'quality':
      message.info(`批量质检 ${count} 条记录`)
      break
    case 'export':
      message.info(`批量导出 ${count} 条记录`)
      break
    case 'delete':
      Modal.confirm({
        title: '批量删除',
        content: `确定要删除选中的 ${count} 条通话记录吗？`,
        onOk() {
          message.success('批量删除成功')
        }
      })
      break
  }
}

const handleAddRecord = () => {
  message.success('添加通话记录成功')
  showAddModal.value = false
  // 重置表单
  Object.keys(addForm).forEach(key => {
    if (Array.isArray(addForm[key])) {
      addForm[key] = []
    } else {
      addForm[key] = ''
    }
  })
}

const exportData = () => {
  message.success('正在导出通话记录...')
}

const downloadRecording = (record) => {
  message.success(`下载 ${record.customerName} 的通话录音`)
}

const startQualityCheck = (record) => {
  message.info(`开始质检 ${record.customerName} 的通话`)
}

const assignQuality = (record) => {
  message.info(`分配质检员给 ${record.customerName} 的通话`)
}

const viewQualityDetail = (record) => {
  message.info(`查看 ${record.customerName} 的质检详情`)
}

const reQuality = (record) => {
  message.info(`重新质检 ${record.customerName} 的通话`)
}

// 工具方法
const formatDuration = (seconds) => {
  if (!seconds) return '0秒'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}时${minutes}分${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const getCallResultColor = (result) => {
  const colors = {
    connected: 'green',
    no_answer: 'orange',
    busy: 'orange',
    rejected: 'red',
    invalid: 'red',
    hung_up: 'orange'
  }
  return colors[result] || 'default'
}

const getCallResultText = (result) => {
  const texts = {
    connected: '接通',
    no_answer: '无人接听',
    busy: '忙线',
    rejected: '拒接',
    invalid: '空号',
    hung_up: '挂断'
  }
  return texts[result] || result
}

const getQualityStatusColor = (status) => {
  const colors = {
    pending: 'default',
    passed: 'green',
    failed: 'red',
    reviewing: 'blue'
  }
  return colors[status] || 'default'
}

const getQualityStatusText = (status) => {
  const texts = {
    pending: '待质检',
    passed: '质检通过',
    failed: '质检不通过',
    reviewing: '质检中'
  }
  return texts[status] || status
}

const getEmotionColor = (emotion) => {
  const colors = {
    positive: 'green',
    neutral: 'blue',
    negative: 'red'
  }
  return colors[emotion] || 'default'
}

const getEmotionText = (emotion) => {
  const texts = {
    positive: '积极',
    neutral: '中性',
    negative: '消极'
  }
  return texts[emotion] || emotion
}

const getAttitudeColor = (attitude) => {
  const colors = {
    cooperative: 'green',
    neutral: 'blue',
    resistant: 'orange',
    aggressive: 'red'
  }
  return colors[attitude] || 'default'
}

const getAttitudeText = (attitude) => {
  const texts = {
    cooperative: '配合',
    neutral: '中性',
    resistant: '抗拒',
    aggressive: '恶劣'
  }
  return texts[attitude] || attitude
}

const getRecordingQualityText = (quality) => {
  const texts = {
    high: '高质量',
    medium: '中等',
    low: '低质量'
  }
  return texts[quality] || quality
}

// 新增方法
const toggleAdvanced = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

const handleQuickSearch = () => {
  // 快速搜索逻辑
  handleSearch()
}

const filterOption = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;

  &.enhanced-search {
    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-stats {
        .ant-statistic {
          .ant-statistic-title {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .search-actions {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .collector-option {
      display: flex;
      align-items: center;
    }
  }
}

.stats-cards {
  margin-bottom: 16px;
  
  .ant-card {
    height: 100%;
  }
}

.call-records-card {
  // 通话记录卡片样式
}

.call-detail {
  .content-section,
  .recording-section,
  .ai-analysis,
  .quality-section {
    h4 {
      margin-bottom: 16px;
      font-weight: 500;
    }
  }
  
  .recording-player {
    .recording-info {
      background: #f5f5f5;
      padding: 8px 12px;
      border-radius: 4px;
    }
  }
  
  .emotion-analysis,
  .keywords,
  .compliance-check {
    text-align: center;
  }
}
</style>
