<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>邮件记录</h2>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card">
      <a-form :model="searchForm" @submit="handleSearch">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="案件编号">
              <a-input 
                v-model:value="searchForm.caseNumber" 
                placeholder="请输入案件编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户姓名">
              <a-input 
                v-model:value="searchForm.customerName" 
                placeholder="请输入客户姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="邮件状态">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option value="draft">草稿</a-select-option>
                <a-select-option value="sent">已发送</a-select-option>
                <a-select-option value="delivered">已送达</a-select-option>
                <a-select-option value="opened">已打开</a-select-option>
                <a-select-option value="clicked">已点击</a-select-option>
                <a-select-option value="replied">已回复</a-select-option>
                <a-select-option value="failed">发送失败</a-select-option>
                <a-select-option value="bounced">被退回</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="邮件类型">
              <a-select 
                v-model:value="searchForm.emailType" 
                placeholder="请选择类型"
                allow-clear
              >
                <a-select-option value="reminder">催收提醒</a-select-option>
                <a-select-option value="notice">逾期通知</a-select-option>
                <a-select-option value="legal">法务通知</a-select-option>
                <a-select-option value="payment">还款通知</a-select-option>
                <a-select-option value="agreement">协商邮件</a-select-option>
                <a-select-option value="custom">自定义</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="发送时间">
              <a-range-picker 
                v-model:value="searchForm.sendTimeRange"
                format="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="邮箱地址">
              <a-input 
                v-model:value="searchForm.email" 
                placeholder="请输入邮箱地址"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="模板名称">
              <a-select 
                v-model:value="searchForm.templateId" 
                placeholder="请选择模板"
                allow-clear
              >
                <a-select-option value="template1">催收提醒模板</a-select-option>
                <a-select-option value="template2">逾期通知模板</a-select-option>
                <a-select-option value="template3">法务通知模板</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  <SearchOutlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <ReloadOutlined />
                  重置
                </a-button>
                <a-button 
                  :class="{ 'expand-btn-active': searchExpanded }"
                  @click="searchExpanded = !searchExpanded"
                >
                  {{ searchExpanded ? '收起' : '展开' }}
                  <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
        
        <!-- 展开的搜索条件 -->
        <div v-show="searchExpanded">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="发送人">
                <a-input 
                  v-model:value="searchForm.sender"
                  placeholder="请输入发送人"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="主题关键词">
                <a-input 
                  v-model:value="searchForm.subject"
                  placeholder="请输入主题关键词"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="内容关键词">
                <a-input 
                  v-model:value="searchForm.content"
                  placeholder="请输入内容关键词"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="优先级">
                <a-select 
                  v-model:value="searchForm.priority" 
                  placeholder="请选择优先级"
                  allow-clear
                >
                  <a-select-option value="high">高</a-select-option>
                  <a-select-option value="medium">中</a-select-option>
                  <a-select-option value="low">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic title="今日发送" :value="statistics.todaySent" suffix="封" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="打开率" :value="statistics.openRate" suffix="%" :precision="1" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="回复率" :value="statistics.replyRate" suffix="%" :precision="1" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="转化率" :value="statistics.conversionRate" suffix="%" :precision="1" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索和操作区域 -->
      <a-card class="search-card">
        <div class="search-actions">
          <div class="action-buttons">
            <a-button type="primary" @click="showSendModal = true">
              <PlusOutlined />
              发送邮件
            </a-button>
            <a-button @click="showBatchModal = true">
              <GroupOutlined />
              批量发送
            </a-button>
            <a-button @click="showTemplateModal = true">
              <FileTextOutlined />
              模板管理
            </a-button>
            <a-button @click="showAnalysisModal = true">
              <BarChartOutlined />
              效果分析
            </a-button>
            <a-button @click="handleExport">
              <DownloadOutlined />
              导出记录
            </a-button>
            <a-button @click="handleRefresh">
              <ReloadOutlined />
              刷新
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 邮件记录列表 -->
      <a-card>
        <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag 
              :color="getStatusColor(record.status)"
              class="status-tag"
            >
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'emailType'">
            <a-tag :color="getTypeColor(record.emailType)">
              {{ getTypeText(record.emailType) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'readStatus'">
            <a-space>
              <a-tooltip :title="record.opened ? '已打开' : '未打开'">
                <EyeOutlined 
                  :style="{ color: record.opened ? '#52c41a' : '#d9d9d9' }"
                />
              </a-tooltip>
              <a-tooltip :title="record.clicked ? '已点击' : '未点击'">
                <LinkOutlined 
                  :style="{ color: record.clicked ? '#1890ff' : '#d9d9d9' }"
                />
              </a-tooltip>
              <a-tooltip :title="record.replied ? '已回复' : '未回复'">
                <MessageOutlined 
                  :style="{ color: record.replied ? '#722ed1' : '#d9d9d9' }"
                />
              </a-tooltip>
            </a-space>
          </template>
          
          <template v-if="column.key === 'attachments'">
            <a-button 
              v-if="record.attachmentCount > 0"
              type="link" 
              size="small" 
              @click="handleViewAttachments(record)"
            >
              <PaperClipOutlined />
              {{ record.attachmentCount }}个
            </a-button>
            <span v-else class="no-attachment">无</span>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleResend(record)">
                <RedoOutlined />
                重发
              </a-button>
              <a-button type="link" size="small" @click="handleReply(record)">
                <MessageOutlined />
                回复
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleEdit(record)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="handleForward(record)">
                      <ShareAltOutlined />
                      转发
                    </a-menu-item>
                    <a-menu-item @click="handleAnalysis(record)">
                      <BarChartOutlined />
                      分析
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDelete(record)" style="color: #ff4d4f;">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
      </a-card>

    <!-- 发送邮件模态框 -->
    <a-modal
      v-model:open="showSendModal"
      title="发送邮件"
      width="900px"
      @ok="handleSendSave"
      @cancel="handleSendCancel"
    >
      <a-form
        :model="sendForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-tabs v-model:activeKey="sendActiveKey">
          <a-tab-pane key="recipient" tab="收件人">
            <a-form-item label="案件编号" required>
              <a-input 
                v-model:value="sendForm.caseNumber" 
                placeholder="请输入案件编号"
                @blur="loadCaseInfo"
              />
            </a-form-item>
            
            <a-form-item label="收件人" required>
              <a-select
                v-model:value="sendForm.recipients"
                mode="tags"
                placeholder="请输入邮箱地址"
                :token-separators="[',', ';']"
              >
                <a-select-option 
                  v-for="email in emailSuggestions" 
                  :key="email"
                  :value="email"
                >
                  {{ email }}
                </a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="抄送">
              <a-select
                v-model:value="sendForm.cc"
                mode="tags"
                placeholder="请输入抄送邮箱"
                :token-separators="[',', ';']"
              />
            </a-form-item>
            
            <a-form-item label="密送">
              <a-select
                v-model:value="sendForm.bcc"
                mode="tags"
                placeholder="请输入密送邮箱"
                :token-separators="[',', ';']"
              />
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="content" tab="邮件内容">
            <a-form-item label="邮件模板">
              <a-select 
                v-model:value="sendForm.templateId" 
                placeholder="选择模板（可选）"
                allow-clear
                @change="handleTemplateSelect"
              >
                <a-select-option 
                  v-for="template in templateList" 
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="邮件类型" required>
              <a-select v-model:value="sendForm.emailType" placeholder="请选择类型">
                <a-select-option value="reminder">催收提醒</a-select-option>
                <a-select-option value="notice">逾期通知</a-select-option>
                <a-select-option value="legal">法务通知</a-select-option>
                <a-select-option value="payment">还款通知</a-select-option>
                <a-select-option value="agreement">协商邮件</a-select-option>
                <a-select-option value="custom">自定义</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="优先级">
              <a-select v-model:value="sendForm.priority" placeholder="请选择优先级">
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="邮件主题" required>
              <a-input v-model:value="sendForm.subject" placeholder="请输入邮件主题" />
            </a-form-item>
            
            <a-form-item label="邮件内容" required>
              <div class="editor-container">
                <div class="editor-toolbar">
                  <a-space>
                    <a-button size="small" @click="insertVariable('customerName')">
                      插入客户姓名
                    </a-button>
                    <a-button size="small" @click="insertVariable('caseNumber')">
                      插入案件编号
                    </a-button>
                    <a-button size="small" @click="insertVariable('amount')">
                      插入欠款金额
                    </a-button>
                    <a-button size="small" @click="insertVariable('overdueDate')">
                      插入逾期时间
                    </a-button>
                  </a-space>
                </div>
                <a-textarea
                  id="sendContent"
                  v-model:value="sendForm.content"
                  :rows="8"
                  placeholder="请输入邮件内容，可使用变量如 {{customerName}}, {{caseNumber}} 等"
                />
              </div>
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="schedule" tab="发送设置">
            <a-form-item label="发送方式">
              <a-radio-group v-model:value="sendForm.sendType">
                <a-radio value="immediate">立即发送</a-radio>
                <a-radio value="scheduled">定时发送</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item 
              v-if="sendForm.sendType === 'scheduled'"
              label="发送时间"
            >
              <a-date-picker
                v-model:value="sendForm.scheduledTime"
                show-time
                placeholder="选择发送时间"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item label="跟踪设置">
              <a-checkbox-group v-model:value="sendForm.trackingOptions">
                <a-checkbox value="delivery">送达跟踪</a-checkbox>
                <a-checkbox value="open">打开跟踪</a-checkbox>
                <a-checkbox value="click">点击跟踪</a-checkbox>
                <a-checkbox value="reply">回复跟踪</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="附件">
              <a-upload
                v-model:file-list="sendForm.attachments"
                :before-upload="beforeUpload"
                multiple
              >
                <a-button>
                  <PaperClipOutlined />
                  添加附件
                </a-button>
              </a-upload>
            </a-form-item>
            
            <a-form-item label="备注">
              <a-textarea
                v-model:value="sendForm.remarks"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </a-form-item>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-modal>

    <!-- 邮件详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="邮件详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="案件编号">{{ selectedRecord.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedRecord.customerName }}</a-descriptions-item>
          <a-descriptions-item label="收件人">{{ selectedRecord.recipients.join(', ') }}</a-descriptions-item>
          <a-descriptions-item label="发送人">{{ selectedRecord.sender }}</a-descriptions-item>
          <a-descriptions-item label="邮件类型">
            <a-tag :color="getTypeColor(selectedRecord.emailType)">
              {{ getTypeText(selectedRecord.emailType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="邮件状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送时间">{{ selectedRecord.sendTime }}</a-descriptions-item>
          <a-descriptions-item label="送达时间">{{ selectedRecord.deliveryTime || '未送达' }}</a-descriptions-item>
          <a-descriptions-item label="打开时间">{{ selectedRecord.openTime || '未打开' }}</a-descriptions-item>
          <a-descriptions-item label="点击时间">{{ selectedRecord.clickTime || '未点击' }}</a-descriptions-item>
          <a-descriptions-item label="邮件主题" :span="2">
            <div class="subject-content">{{ selectedRecord.subject }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="邮件内容" :span="2">
            <div class="email-content" v-html="selectedRecord.content"></div>
          </a-descriptions-item>
        </a-descriptions>
        
        <!-- 跟踪信息 -->
        <a-card title="跟踪信息" style="margin-top: 16px;">
          <a-timeline>
            <a-timeline-item 
              v-for="(track, index) in selectedRecord.trackingHistory" 
              :key="index"
              :color="getTrackingColor(track.type)"
            >
              <div class="tracking-content">
                <div class="tracking-header">
                  <span class="tracking-title">{{ getTrackingText(track.type) }}</span>
                  <span class="tracking-time">{{ track.time }}</span>
                </div>
                <div class="tracking-detail">{{ track.detail }}</div>
                <div v-if="track.location" class="tracking-location">
                  <EnvironmentOutlined />
                  {{ track.location }}
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </div>
    </a-modal>

    <!-- 模板管理模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="邮件模板管理"
      width="1200px"
      @ok="handleTemplateSave"
    >
      <a-tabs v-model:activeKey="templateActiveKey">
        <a-tab-pane key="list" tab="模板列表">
          <div class="template-actions">
            <a-input-search
              v-model:value="templateSearch"
              placeholder="搜索模板"
              style="width: 300px; margin-right: 16px;"
            />
            <a-button type="primary" @click="showAddTemplate = true">
              <PlusOutlined />
              新增模板
            </a-button>
          </div>
          
          <a-table
            :columns="templateColumns"
            :data-source="filteredTemplates"
            :pagination="false"
            size="small"
            style="margin-top: 16px;"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleEditTemplate(record)">
                    <EditOutlined />
                    编辑
                  </a-button>
                  <a-button type="link" size="small" @click="handlePreviewTemplate(record)">
                    <EyeOutlined />
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="handleCopyTemplate(record)">
                    <CopyOutlined />
                    复制
                  </a-button>
                  <a-popconfirm title="确定删除？" @confirm="handleDeleteTemplate(record)">
                    <a-button type="link" size="small" danger>
                      <DeleteOutlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="edit" tab="编辑模板">
          <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <a-form-item label="模板名称" required>
              <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
            </a-form-item>
            
            <a-form-item label="模板类型" required>
              <a-select v-model:value="templateForm.type" placeholder="请选择类型">
                <a-select-option value="reminder">催收提醒</a-select-option>
                <a-select-option value="notice">逾期通知</a-select-option>
                <a-select-option value="legal">法务通知</a-select-option>
                <a-select-option value="payment">还款通知</a-select-option>
                <a-select-option value="agreement">协商邮件</a-select-option>
                <a-select-option value="custom">自定义</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="模板描述">
              <a-textarea
                v-model:value="templateForm.description"
                :rows="2"
                placeholder="请输入模板描述"
              />
            </a-form-item>
            
            <a-form-item label="邮件主题" required>
              <a-input v-model:value="templateForm.subject" placeholder="请输入邮件主题" />
            </a-form-item>
            
            <a-form-item label="邮件内容" required>
              <div class="template-editor">
                <div class="variable-panel">
                  <h4>可用变量：</h4>
                  <a-space wrap>
                    <a-tag 
                      v-for="variable in availableVariables" 
                      :key="variable.key"
                      @click="insertTemplateVariable(variable.key)"
                      style="cursor: pointer;"
                    >
                      {{ variable.label }}
                    </a-tag>
                  </a-space>
                </div>
                <a-textarea
                  id="templateContent"
                  v-model:value="templateForm.content"
                  :rows="10"
                  placeholder="请输入邮件内容模板"
                />
              </div>
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 4 }">
              <a-space>
                <a-button type="primary" @click="handleSaveTemplate">
                  保存模板
                </a-button>
                <a-button @click="handlePreviewTemplate(templateForm)">
                  预览模板
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 效果分析模态框 -->
    <a-modal
      v-model:open="showAnalysisModal"
      title="邮件效果分析"
      width="1200px"
      :footer="null"
    >
      <a-tabs v-model:activeKey="analysisActiveKey">
        <a-tab-pane key="overview" tab="总体分析">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="发送统计">
                <div id="sendChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="效果分析">
                <div id="effectChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
          
          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <a-card title="趋势分析">
                <div id="trendChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="template" tab="模板分析">
          <a-table
            :columns="templateAnalysisColumns"
            :data-source="templateAnalysisData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'openRate'">
                <a-progress :percent="record.openRate" size="small" />
              </template>
              <template v-if="column.key === 'clickRate'">
                <a-progress :percent="record.clickRate" size="small" />
              </template>
              <template v-if="column.key === 'replyRate'">
                <a-progress :percent="record.replyRate" size="small" />
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="time" tab="时间分析">
          <div class="time-analysis">
            <a-card title="最佳发送时间">
              <div id="timeChart" style="height: 400px;"></div>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 批量发送模态框 -->
    <a-modal
      v-model:open="showBatchModal"
      title="批量发送邮件"
      width="800px"
      @ok="handleBatchSend"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="选择案件">
          <a-radio-group v-model:value="batchForm.selectionType">
            <a-radio value="current">当前筛选结果</a-radio>
            <a-radio value="upload">上传案件列表</a-radio>
            <a-radio value="manual">手动选择</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item 
          v-if="batchForm.selectionType === 'upload'"
          label="文件上传"
        >
          <a-upload
            :before-upload="beforeBatchUpload"
            accept=".xlsx,.xls,.csv"
          >
            <a-button>
              <UploadOutlined />
              上传案件列表
            </a-button>
          </a-upload>
          <div class="upload-tip">
            支持Excel、CSV格式，需包含案件编号、客户姓名、邮箱地址等字段
          </div>
        </a-form-item>
        
        <a-form-item 
          v-if="batchForm.selectionType === 'manual'"
          label="手动选择"
        >
          <a-select
            v-model:value="batchForm.manualCases"
            mode="multiple"
            placeholder="请选择案件"
            style="width: 100%;"
            :options="manualCaseOptions"
            @change="updateBatchStats"
          />
        </a-form-item>
        
        <a-form-item label="邮件模板" required>
          <a-select v-model:value="batchForm.templateId" placeholder="请选择邮件模板">
            <a-select-option 
              v-for="template in templateList" 
              :key="template.id"
              :value="template.id"
            >
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="发送设置">
          <a-checkbox-group v-model:value="batchForm.options">
            <a-checkbox value="skipInvalid">跳过无效邮箱</a-checkbox>
            <a-checkbox value="throttle">限制发送频率</a-checkbox>
            <a-checkbox value="track">启用跟踪</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item 
          v-if="batchForm.options.includes('throttle')"
          label="发送频率"
        >
          <a-input-number 
            v-model:value="batchForm.throttleRate" 
            :min="1" 
            :max="100"
            style="width: 200px;"
          />
          <span style="margin-left: 8px;">封/分钟</span>
        </a-form-item>
        
        <a-form-item label="发送预览">
          <div class="batch-preview">
            <div class="preview-stats">
              <a-statistic-group>
                <a-statistic title="待发送" :value="batchForm.totalCount" suffix="封" />
                <a-statistic title="有效邮箱" :value="batchForm.validCount" suffix="个" />
                <a-statistic title="无效邮箱" :value="batchForm.invalidCount" suffix="个" />
              </a-statistic-group>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  PlusOutlined,
  GroupOutlined,
  FileTextOutlined,
  BarChartOutlined,
  DownloadOutlined,
  MailOutlined,
  EyeOutlined,
  MessageOutlined,
  TrophyOutlined,
  LinkOutlined,
  PaperClipOutlined,
  RedoOutlined,
  EditOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  CopyOutlined,
  EnvironmentOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)

// 模态框显示状态
const showSendModal = ref(false)
const showDetailModal = ref(false)
const showTemplateModal = ref(false)
const showAnalysisModal = ref(false)
const showBatchModal = ref(false)
const showAddTemplate = ref(false)

// 编辑状态
const selectedRecord = ref(null)
const editingKey = ref(null)

// Tab激活键
const sendActiveKey = ref('recipient')
const templateActiveKey = ref('list')
const analysisActiveKey = ref('overview')

// 表单数据
const searchForm = reactive({
  caseNumber: '',
  customerName: '',
  status: undefined,
  emailType: undefined,
  sendTimeRange: [],
  email: '',
  templateId: undefined,
  sender: '',
  subject: '',
  content: '',
  priority: undefined
})

const sendForm = reactive({
  caseNumber: '',
  recipients: [],
  cc: [],
  bcc: [],
  templateId: '',
  emailType: '',
  priority: 'medium',
  subject: '',
  content: '',
  sendType: 'immediate',
  scheduledTime: null,
  trackingOptions: ['delivery', 'open'],
  attachments: [],
  remarks: ''
})

const templateForm = reactive({
  name: '',
  type: '',
  description: '',
  subject: '',
  content: ''
})

const batchForm = reactive({
  selectionType: 'current',
  templateId: '',
  options: ['skipInvalid', 'track'],
  totalCount: 0,
  validCount: 0,
  invalidCount: 0
})

// 模板搜索
const templateSearch = ref('')
const editingTemplateId = ref(null)

// 统计数据
const statistics = reactive({
  todaySent: 156,
  openRate: 68.5,
  replyRate: 12.3,
  conversionRate: 8.7
})

// 表格列定义
const columns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    sorter: true
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '收件人',
    dataIndex: 'recipients',
    key: 'recipients',
    width: 200,
    ellipsis: true,
    customRender: ({ text }) => text.join(', ')
  },
  {
    title: '邮件类型',
    dataIndex: 'emailType',
    key: 'emailType',
    width: 100
  },
  {
    title: '邮件主题',
    dataIndex: 'subject',
    key: 'subject',
    width: 200,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '草稿', value: 'draft' },
      { text: '已发送', value: 'sent' },
      { text: '已送达', value: 'delivered' },
      { text: '已打开', value: 'opened' },
      { text: '已点击', value: 'clicked' },
      { text: '已回复', value: 'replied' },
      { text: '发送失败', value: 'failed' },
      { text: '被退回', value: 'bounced' }
    ]
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 80
  },
  {
    title: '读取状态',
    key: 'readStatus',
    width: 100
  },
  {
    title: '发送时间',
    dataIndex: 'sendTime',
    key: 'sendTime',
    width: 150,
    sorter: true
  },
  {
    title: '附件',
    key: 'attachments',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模板表格列
const templateColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: 100,
    sorter: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 模板分析表格列
const templateAnalysisColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '发送次数',
    dataIndex: 'sendCount',
    key: 'sendCount',
    sorter: true
  },
  {
    title: '打开率',
    dataIndex: 'openRate',
    key: 'openRate',
    sorter: true
  },
  {
    title: '点击率',
    dataIndex: 'clickRate',
    key: 'clickRate',
    sorter: true
  },
  {
    title: '回复率',
    dataIndex: 'replyRate',
    key: 'replyRate',
    sorter: true
  }
]

// 表格数据
const tableData = ref([
  {
    key: '1',
    caseNumber: 'CS202401001',
    customerName: '张三',
    recipients: ['<EMAIL>'],
    emailType: 'reminder',
    subject: '贷款逾期提醒 - 请及时还款',
    status: 'opened',
    priority: 'high',
    opened: true,
    clicked: true,
    replied: false,
    sendTime: '2024-01-15 09:30:00',
    deliveryTime: '2024-01-15 09:30:05',
    openTime: '2024-01-15 10:15:30',
    clickTime: '2024-01-15 10:16:00',
    attachmentCount: 2,
    sender: '催收专员A',
    content: '尊敬的张三先生，您的贷款已逾期，请及时还款...',
    trackingHistory: [
      {
        type: 'sent',
        time: '2024-01-15 09:30:00',
        detail: '邮件发送成功',
        location: '北京市'
      },
      {
        type: 'delivered',
        time: '2024-01-15 09:30:05',
        detail: '邮件已送达收件箱',
        location: '上海市'
      },
      {
        type: 'opened',
        time: '2024-01-15 10:15:30',
        detail: '收件人已打开邮件',
        location: '上海市'
      },
      {
        type: 'clicked',
        time: '2024-01-15 10:16:00',
        detail: '点击了还款链接',
        location: '上海市'
      }
    ]
  },
  {
    key: '2',
    caseNumber: 'CS202401002',
    customerName: '李四',
    recipients: ['<EMAIL>', '<EMAIL>'],
    emailType: 'notice',
    subject: '逾期30天通知 - 请立即联系我们',
    status: 'delivered',
    priority: 'medium',
    opened: false,
    clicked: false,
    replied: false,
    sendTime: '2024-01-15 14:20:00',
    deliveryTime: '2024-01-15 14:20:08',
    attachmentCount: 1,
    sender: '催收专员B',
    content: '尊敬的李四先生，您的贷款已逾期30天...',
    trackingHistory: [
      {
        type: 'sent',
        time: '2024-01-15 14:20:00',
        detail: '邮件发送成功'
      },
      {
        type: 'delivered',
        time: '2024-01-15 14:20:08',
        detail: '邮件已送达收件箱'
      }
    ]
  },
  {
    key: '3',
    caseNumber: 'CS202401003',
    customerName: '王五',
    recipients: ['<EMAIL>'],
    emailType: 'legal',
    subject: '法务通知 - 即将启动法律程序',
    status: 'replied',
    priority: 'high',
    opened: true,
    clicked: true,
    replied: true,
    sendTime: '2024-01-14 16:45:00',
    deliveryTime: '2024-01-14 16:45:03',
    openTime: '2024-01-14 18:30:00',
    clickTime: '2024-01-14 18:31:00',
    replyTime: '2024-01-14 19:15:00',
    attachmentCount: 3,
    sender: '法务专员',
    content: '尊敬的王五先生，鉴于您的贷款严重逾期...',
    trackingHistory: [
      {
        type: 'sent',
        time: '2024-01-14 16:45:00',
        detail: '邮件发送成功'
      },
      {
        type: 'delivered',
        time: '2024-01-14 16:45:03',
        detail: '邮件已送达收件箱'
      },
      {
        type: 'opened',
        time: '2024-01-14 18:30:00',
        detail: '收件人已打开邮件'
      },
      {
        type: 'clicked',
        time: '2024-01-14 18:31:00',
        detail: '点击了联系方式'
      },
      {
        type: 'replied',
        time: '2024-01-14 19:15:00',
        detail: '客户已回复邮件'
      }
    ]
  }
])

// 模板数据
const templateList = ref([
  {
    id: '1',
    name: '催收提醒模板',
    type: 'reminder',
    description: '标准催收提醒邮件模板',
    subject: '贷款逾期提醒 - 请及时还款',
    content: '尊敬的{{customerName}}，您的贷款（编号：{{caseNumber}}）已逾期{{overdueDate}}天，欠款金额{{amount}}元，请及时还款。',
    usageCount: 156,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: '2',
    name: '逾期通知模板',
    type: 'notice',
    description: '逾期通知邮件模板',
    subject: '逾期{{overdueDate}}天通知 - 请立即联系我们',
    content: '尊敬的{{customerName}}，您的贷款已逾期{{overdueDate}}天，请立即联系我们协商还款事宜。',
    usageCount: 89,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: '3',
    name: '法务通知模板',
    type: 'legal',
    description: '法务程序通知模板',
    subject: '法务通知 - 即将启动法律程序',
    content: '尊敬的{{customerName}}，鉴于您的贷款严重逾期，我方将启动法律程序。',
    usageCount: 23,
    createTime: '2024-01-01 10:00:00'
  }
])

// 模板分析数据
const templateAnalysisData = ref([
  {
    name: '催收提醒模板',
    sendCount: 156,
    openRate: 75.6,
    clickRate: 32.1,
    replyRate: 8.9
  },
  {
    name: '逾期通知模板',
    sendCount: 89,
    openRate: 68.2,
    clickRate: 28.7,
    replyRate: 12.4
  },
  {
    name: '法务通知模板',
    sendCount: 23,
    openRate: 91.3,
    clickRate: 56.5,
    replyRate: 34.8
  }
])

// 邮箱建议
const emailSuggestions = ref([
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
])

// 可用变量
const availableVariables = ref([
  { key: 'customerName', label: '客户姓名' },
  { key: 'caseNumber', label: '案件编号' },
  { key: 'amount', label: '欠款金额' },
  { key: 'overdueDate', label: '逾期天数' },
  { key: 'contactPhone', label: '联系电话' },
  { key: 'collectorName', label: '催收员姓名' },
  { key: 'currentDate', label: '当前日期' }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const filteredTemplates = computed(() => {
  if (!templateSearch.value) {
    return templateList.value
  }
  return templateList.value.filter(
    template => 
      template.name.includes(templateSearch.value) || 
      template.description.includes(templateSearch.value)
  )
})

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    draft: 'default',
    sent: 'blue',
    delivered: 'cyan',
    opened: 'green',
    clicked: 'purple',
    replied: 'gold',
    failed: 'red',
    bounced: 'orange'
  }
  return colorMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    draft: '草稿',
    sent: '已发送',
    delivered: '已送达',
    opened: '已打开',
    clicked: '已点击',
    replied: '已回复',
    failed: '发送失败',
    bounced: '被退回'
  }
  return textMap[status] || status
}

// 类型颜色映射
const getTypeColor = (type) => {
  const colorMap = {
    reminder: 'blue',
    notice: 'orange',
    legal: 'red',
    payment: 'green',
    agreement: 'purple',
    custom: 'default'
  }
  return colorMap[type] || 'default'
}

// 类型文本映射
const getTypeText = (type) => {
  const textMap = {
    reminder: '催收提醒',
    notice: '逾期通知',
    legal: '法务通知',
    payment: '还款通知',
    agreement: '协商邮件',
    custom: '自定义'
  }
  return textMap[type] || type
}

// 优先级颜色映射
const getPriorityColor = (priority) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'default'
  }
  return colorMap[priority] || 'default'
}

// 优先级文本映射
const getPriorityText = (priority) => {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[priority] || priority
}

// 跟踪颜色映射
const getTrackingColor = (type) => {
  const colorMap = {
    sent: 'blue',
    delivered: 'cyan',
    opened: 'green',
    clicked: 'purple',
    replied: 'gold',
    bounced: 'red'
  }
  return colorMap[type] || 'default'
}

// 跟踪文本映射
const getTrackingText = (type) => {
  const textMap = {
    sent: '邮件发送',
    delivered: '邮件送达',
    opened: '邮件打开',
    clicked: '链接点击',
    replied: '邮件回复',
    bounced: '邮件退回'
  }
  return textMap[type] || type
}

// 事件处理函数
const handleSearch = () => {
  loading.value = true
  console.log('搜索条件:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  message.success('已重置搜索条件')
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 800)
}

const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变化:', pag, filters, sorter)
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleView = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleResend = (record) => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    // 更新记录状态
    const index = tableData.value.findIndex(item => item.key === record.key)
    if (index !== -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        status: 'sent',
        sendTime: new Date().toLocaleString(),
        opened: false,
        clicked: false,
        replied: false
      }
    }
    message.success('邮件已重新发送')
  }, 1000)
}

const handleReply = (record) => {
  // 填充回复表单
  Object.assign(sendForm, {
    caseNumber: record.caseNumber,
    recipients: [record.recipients[0]], // 默认回复给发件人
    subject: `Re: ${record.subject}`,
    content: `\n\n------- 原始邮件 -------\n发件人: ${record.sender}\n时间: ${record.sendTime}\n主题: ${record.subject}\n\n${record.content}`,
    emailType: 'custom'
  })
  showSendModal.value = true
  sendActiveKey.value = 'content'
}

const handleEdit = (record) => {
  if (record.status !== 'draft') {
    message.warning('只能编辑草稿邮件')
    return
  }
  
  // 填充编辑表单
  Object.assign(sendForm, {
    caseNumber: record.caseNumber,
    recipients: record.recipients,
    subject: record.subject,
    content: record.content,
    emailType: record.emailType,
    priority: record.priority
  })
  editingKey.value = record.key
  showSendModal.value = true
}

const handleForward = (record) => {
  // 填充转发表单
  Object.assign(sendForm, {
    subject: `Fwd: ${record.subject}`,
    content: `\n\n------- 转发邮件 -------\n发件人: ${record.sender}\n时间: ${record.sendTime}\n主题: ${record.subject}\n收件人: ${record.recipients.join(', ')}\n\n${record.content}`,
    emailType: record.emailType,
    priority: record.priority
  })
  showSendModal.value = true
  sendActiveKey.value = 'recipient'
}

const handleAnalysis = (record) => {
  selectedRecord.value = record
  showAnalysisModal.value = true
  
  // 更新分析数据
  if (record) {
    // 更新特定邮件的分析数据
    message.info(`正在分析"${record.subject}"的效果数据...`)
  }
}

const handleDelete = (record) => {
  const index = tableData.value.findIndex(item => item.key === record.key)
  if (index !== -1) {
    tableData.value.splice(index, 1)
    pagination.total--
    message.success('邮件已删除')
  }
}

const handleSendSave = () => {
  // 验证必填字段
  if (!sendForm.recipients.length) {
    message.error('请输入收件人')
    return
  }
  if (!sendForm.subject) {
    message.error('请输入邮件主题')
    return
  }
  if (!sendForm.content) {
    message.error('请输入邮件内容')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    // 如果是编辑模式
    if (editingKey.value) {
      const index = tableData.value.findIndex(item => item.key === editingKey.value)
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...sendForm,
          status: sendForm.sendType === 'immediate' ? 'sent' : 'draft',
          sendTime: sendForm.sendType === 'immediate' ? new Date().toLocaleString() : tableData.value[index].sendTime
        }
      }
      editingKey.value = null
    } else {
      // 新增邮件记录
      const newRecord = {
        key: Date.now().toString(),
        caseNumber: sendForm.caseNumber || 'CS' + Date.now(),
        customerName: '客户' + Math.floor(Math.random() * 100),
        recipients: sendForm.recipients,
        emailType: sendForm.emailType,
        subject: sendForm.subject,
        status: sendForm.sendType === 'immediate' ? 'sent' : 'draft',
        priority: sendForm.priority,
        opened: false,
        clicked: false,
        replied: false,
        sendTime: sendForm.sendType === 'immediate' ? new Date().toLocaleString() : '-',
        attachmentCount: sendForm.attachments.length,
        sender: '当前用户',
        content: sendForm.content,
        trackingHistory: sendForm.sendType === 'immediate' ? [
          {
            type: 'sent',
            time: new Date().toLocaleString(),
            detail: '邮件发送成功'
          }
        ] : []
      }
      
      tableData.value.unshift(newRecord)
      pagination.total++
    }
    
    message.success(sendForm.sendType === 'immediate' ? '邮件发送成功' : '草稿保存成功')
    showSendModal.value = false
    handleSendCancel()
  }, 1500)
}

const handleSendCancel = () => {
  Object.assign(sendForm, {
    caseNumber: '',
    recipients: [],
    cc: [],
    bcc: [],
    templateId: '',
    emailType: '',
    priority: 'medium',
    subject: '',
    content: '',
    sendType: 'immediate',
    scheduledTime: null,
    trackingOptions: ['delivery', 'open'],
    attachments: [],
    remarks: ''
  })
  sendActiveKey.value = 'recipient'
}

const loadCaseInfo = () => {
  if (sendForm.caseNumber) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      
      // 模拟加载案件信息
      const mockCaseInfo = {
        'CS202401001': {
          customerName: '张三',
          emails: ['<EMAIL>', '<EMAIL>'],
          amount: '15000',
          overdueDate: '30'
        },
        'CS202401002': {
          customerName: '李四',
          emails: ['<EMAIL>', '<EMAIL>'],
          amount: '25000',
          overdueDate: '45'
        },
        'CS202401003': {
          customerName: '王五',
          emails: ['<EMAIL>'],
          amount: '8000',
          overdueDate: '60'
        }
      }
      
      const caseInfo = mockCaseInfo[sendForm.caseNumber]
      if (caseInfo) {
        emailSuggestions.value = caseInfo.emails
        // 替换模板变量
        if (sendForm.content) {
          sendForm.content = sendForm.content
            .replace(/{{customerName}}/g, caseInfo.customerName)
            .replace(/{{caseNumber}}/g, sendForm.caseNumber)
            .replace(/{{amount}}/g, caseInfo.amount)
            .replace(/{{overdueDate}}/g, caseInfo.overdueDate)
        }
        message.success('已加载案件信息')
      } else {
        message.warning('未找到案件信息')
      }
    }, 500)
  }
}

const handleTemplateSelect = (value) => {
  const template = templateList.value.find(t => t.id === value)
  if (template) {
    sendForm.subject = template.subject
    sendForm.content = template.content
    sendForm.emailType = template.type
    
    // 自动替换部分变量
    const currentDate = new Date().toLocaleDateString()
    const collectorName = '催收专员'
    
    sendForm.content = sendForm.content
      .replace(/{{currentDate}}/g, currentDate)
      .replace(/{{collectorName}}/g, collectorName)
    
    // 如果已填写案件编号，自动加载案件信息
    if (sendForm.caseNumber) {
      loadCaseInfo()
    }
  }
}

const insertVariable = (variable) => {
  // 在光标位置插入变量
  const textarea = document.querySelector('textarea[id="sendContent"]')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = sendForm.content
    sendForm.content = text.substring(0, start) + `{{${variable}}}` + text.substring(end)
    
    // 恢复光标位置
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4)
    }, 0)
  } else {
    sendForm.content += `{{${variable}}}`
  }
}

const insertTemplateVariable = (variable) => {
  // 在光标位置插入变量
  const textarea = document.querySelector('textarea[id="templateContent"]')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = templateForm.content
    templateForm.content = text.substring(0, start) + `{{${variable}}}` + text.substring(end)
    
    // 恢复光标位置
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4)
    }, 0)
  } else {
    templateForm.content += `{{${variable}}}`
  }
}

const beforeUpload = (file) => {
  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('附件大小不能超过10MB!')
    return false
  }
  
  // 检查文件类型
  const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'zip', 'rar']
  const fileType = file.name.split('.').pop().toLowerCase()
  if (!allowedTypes.includes(fileType)) {
    message.error(`不支持${fileType}格式的文件!`)
    return false
  }
  
  sendForm.attachments.push(file)
  return false
}

const beforeBatchUpload = (file) => {
  // 检查文件类型
  const fileType = file.name.split('.').pop().toLowerCase()
  if (!['xlsx', 'xls', 'csv'].includes(fileType)) {
    message.error('只支持Excel和CSV格式的文件!')
    return false
  }
  
  // 模拟解析文件
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    // 模拟解析结果
    const totalCount = Math.floor(Math.random() * 200) + 50
    const invalidCount = Math.floor(totalCount * 0.05)
    
    batchForm.totalCount = totalCount
    batchForm.validCount = totalCount - invalidCount
    batchForm.invalidCount = invalidCount
    
    message.success(`文件解析成功！共${totalCount}条记录，有效${batchForm.validCount}条，无效${invalidCount}条`)
  }, 1500)
  
  return false
}

const handleViewAttachments = (record) => {
  Modal.info({
    title: '附件列表',
    content: h('div', [
      h('p', `共${record.attachmentCount}个附件：`),
      h('ul', { style: 'list-style: none; padding: 0;' }, [
        h('li', { style: 'padding: 8px 0; border-bottom: 1px solid #f0f0f0;' }, [
          h('span', '📄 逾期通知书.pdf'),
          h('span', { style: 'float: right; color: #999;' }, '256KB')
        ]),
        h('li', { style: 'padding: 8px 0;' }, [
          h('span', '📄 还款计划.docx'),
          h('span', { style: 'float: right; color: #999;' }, '128KB')
        ])
      ])
    ]),
    width: 400
  })
}

// 模板管理
const handleTemplateSave = () => {
  message.success('模板设置已保存')
  showTemplateModal.value = false
}

const handleEditTemplate = (template) => {
  Object.assign(templateForm, template)
  templateActiveKey.value = 'edit'
  editingTemplateId.value = template.id
}

const handlePreviewTemplate = (template) => {
  // 显示预览模态框
  const previewContent = template.content
    .replace(/{{customerName}}/g, '张三')
    .replace(/{{caseNumber}}/g, 'CS202401001')
    .replace(/{{amount}}/g, '10000')
    .replace(/{{overdueDate}}/g, '30')
    .replace(/{{contactPhone}}/g, '138****8888')
    .replace(/{{collectorName}}/g, '催收专员')
    .replace(/{{currentDate}}/g, new Date().toLocaleDateString())
  
  Modal.info({
    title: `预览: ${template.name}`,
    content: h('div', [
      h('div', { style: 'margin-bottom: 16px;' }, [
        h('strong', '主题: '),
        template.subject.replace(/{{(\w+)}}/g, (match, p1) => {
          const replacements = {
            overdueDate: '30',
            customerName: '张三'
          }
          return replacements[p1] || match
        })
      ]),
      h('div', { style: 'white-space: pre-wrap; background: #f5f5f5; padding: 16px; border-radius: 4px;' }, previewContent)
    ]),
    width: 600,
    maskClosable: true
  })
}

const handleCopyTemplate = (template) => {
  const newTemplate = {
    ...template,
    id: Date.now().toString(),
    name: template.name + ' - 副本',
    createTime: new Date().toLocaleString(),
    usageCount: 0
  }
  templateList.value.push(newTemplate)
  message.success('模板已复制')
}

const handleDeleteTemplate = (template) => {
  const index = templateList.value.findIndex(t => t.id === template.id)
  if (index !== -1) {
    templateList.value.splice(index, 1)
    message.success('模板已删除')
  }
}

const handleSaveTemplate = () => {
  // 验证必填字段
  if (!templateForm.name) {
    message.error('请输入模板名称')
    return
  }
  if (!templateForm.type) {
    message.error('请选择模板类型')
    return
  }
  if (!templateForm.subject) {
    message.error('请输入邮件主题')
    return
  }
  if (!templateForm.content) {
    message.error('请输入邮件内容')
    return
  }
  
  if (editingTemplateId.value) {
    // 编辑模式
    const index = templateList.value.findIndex(t => t.id === editingTemplateId.value)
    if (index !== -1) {
      templateList.value[index] = {
        ...templateList.value[index],
        ...templateForm
      }
    }
    editingTemplateId.value = null
  } else {
    // 新增模式
    const newTemplate = {
      ...templateForm,
      id: Date.now().toString(),
      createTime: new Date().toLocaleString(),
      usageCount: 0
    }
    templateList.value.unshift(newTemplate)
  }
  
  message.success('模板保存成功')
  
  // 清空表单
  Object.assign(templateForm, {
    name: '',
    type: '',
    description: '',
    subject: '',
    content: ''
  })
  
  // 切换到列表tab
  templateActiveKey.value = 'list'
}

// 批量发送
const handleBatchSend = () => {
  // 验证必填字段
  if (!batchForm.templateId) {
    message.error('请选择邮件模板')
    return
  }
  
  if (batchForm.totalCount === 0) {
    message.error('没有可发送的邮件')
    return
  }
  
  loading.value = true
  const progress = message.loading(`正在发送邮件 0/${batchForm.totalCount}`, 0)
  
  let sent = 0
  const interval = setInterval(() => {
    sent += Math.floor(Math.random() * 10) + 5
    if (sent >= batchForm.totalCount) {
      sent = batchForm.totalCount
      clearInterval(interval)
      loading.value = false
      progress()
      message.success(`批量发送完成！成功发送${batchForm.validCount}封，失败${batchForm.totalCount - batchForm.validCount}封`)
      showBatchModal.value = false
      
      // 重置表单
      Object.assign(batchForm, {
        selectionType: 'current',
        templateId: '',
        options: ['skipInvalid', 'track'],
        totalCount: 0,
        validCount: 0,
        invalidCount: 0
      })
      
      // 刷新列表
      handleRefresh()
    } else {
      progress()
      message.loading(`正在发送邮件 ${sent}/${batchForm.totalCount}`, 0)
    }
  }, 500)
}

const handleExport = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    // 模拟下载文件
    const link = document.createElement('a')
    link.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent('案件编号,客户姓名,收件人,邮件类型,主题,状态,发送时间\n')
    link.download = `邮件记录_${new Date().toLocaleDateString().replace(/\//g, '-')}.csv`
    link.click()
    
    message.success('导出成功')
  }, 1000)
}

// 初始化图表
const initCharts = () => {
  // 等待DOM更新后再初始化图表
  setTimeout(() => {
    if (showAnalysisModal.value) {
      initSendChart()
      initEffectChart()
      initTrendChart()
      initTimeChart()
    }
  }, 100)
}

// 发送统计图表
const initSendChart = () => {
  const chartDom = document.getElementById('sendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['发送量', '送达量', '失败量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '发送量',
        type: 'bar',
        data: [120, 132, 101, 134, 90, 230, 210]
      },
      {
        name: '送达量',
        type: 'bar',
        data: [110, 125, 95, 128, 85, 220, 195]
      },
      {
        name: '失败量',
        type: 'bar',
        data: [10, 7, 6, 6, 5, 10, 15]
      }
    ]
  }
  myChart.setOption(option)
}

// 效果分析图表
const initEffectChart = () => {
  const chartDom = document.getElementById('effectChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '邮件效果',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '已打开' },
          { value: 735, name: '已点击' },
          { value: 580, name: '已回复' },
          { value: 484, name: '未打开' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}

// 趋势分析图表
const initTrendChart = () => {
  const chartDom = document.getElementById('trendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['发送量', '打开率', '点击率', '回复率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        max: 2500,
        interval: 500
      },
      {
        type: 'value',
        name: '比率',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '发送量',
        type: 'line',
        data: [820, 932, 901, 934, 1290, 1330, 1320]
      },
      {
        name: '打开率',
        type: 'line',
        yAxisIndex: 1,
        data: [65, 68, 72, 70, 75, 78, 80]
      },
      {
        name: '点击率',
        type: 'line',
        yAxisIndex: 1,
        data: [25, 28, 30, 32, 35, 38, 40]
      },
      {
        name: '回复率',
        type: 'line',
        yAxisIndex: 1,
        data: [8, 10, 12, 13, 14, 15, 16]
      }
    ]
  }
  myChart.setOption(option)
}

// 时间分析图表
const initTimeChart = () => {
  const chartDom = document.getElementById('timeChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const hours = ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00',
    '07:00', '08:00', '09:00', '10:00', '11:00',
    '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
    '18:00', '19:00', '20:00', '21:00', '22:00', '23:00']
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  const data = []
  for (let i = 0; i < days.length; i++) {
    for (let j = 0; j < hours.length; j++) {
      data.push([j, i, Math.floor(Math.random() * 100)])
    }
  }
  
  const option = {
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [
      {
        name: '打开率',
        type: 'heatmap',
        data: data,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}

// AI 分析邮件效果
const analyzeEmailEffect = () => {
  const suggestions = [
    '建议在工作日上午9-11点发送邮件，打开率最高',
    '法务通知类邮件的回复率最高，建议优化其他类型模板',
    '邮件主题中包含客户姓名可提高25%的打开率',
    '短邮件（少于200字）的阅读完成率高于长邮件',
    '添加明确的行动号召按钮可提高点击率'
  ]
  
  return suggestions[Math.floor(Math.random() * suggestions.length)]
}

// 生成邮件内容
const generateEmailContent = (type) => {
  const templates = {
    reminder: `尊敬的{{customerName}}先生/女士：

您好！

我们注意到您的贷款（编号：{{caseNumber}}）已逾期{{overdueDate}}天，当前欠款金额为{{amount}}元。

为避免影响您的信用记录和产生额外费用，请您尽快还款。您可以通过以下方式进行还款：
1. 登录我们的在线平台进行还款
2. 拨打客服电话：400-xxx-xxxx
3. 前往就近的服务网点

如有任何疑问或需要协商还款计划，请及时与我们联系。

祝好！
{{collectorName}}
{{currentDate}}`,
    
    notice: `尊敬的{{customerName}}先生/女士：

重要通知！

您的贷款已严重逾期{{overdueDate}}天，累计欠款{{amount}}元。根据相关合同条款，您需要立即偿还全部欠款。

请在收到本通知后3个工作日内完成还款，否则我们将采取进一步的法律措施。

联系电话：400-xxx-xxxx
工作时间：周一至周五 9:00-18:00

{{collectorName}}
{{currentDate}}`,
    
    legal: `尊敬的{{customerName}}先生/女士：

法务通知

鉴于您的贷款（编号：{{caseNumber}}）已严重逾期，经多次催收无果，我公司决定启动法律程序。

根据《中华人民共和国合同法》等相关法律法规，您需承担以下责任：
1. 偿还本金{{amount}}元
2. 支付逾期利息及违约金
3. 承担诉讼费用

请您在收到本通知后立即与我们联系，否则我们将向法院提起诉讼。

法务部：{{collectorName}}
{{currentDate}}`
  }
  
  return templates[type] || ''
}

// 监听分析模态框变化
watch(showAnalysisModal, (newVal) => {
  if (newVal) {
    initCharts()
  }
})

// 导入必要模块
import * as echarts from 'echarts'
import { watch, h } from 'vue'
import { Modal } from 'ant-design-vue'

// 组件挂载后初始化
onMounted(() => {
  handleRefresh()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}


.status-tag {
  margin: 0;
}

.no-attachment {
  color: #999;
  font-size: 12px;
}

/* 编辑器样式 */
.editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.editor-toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.editor-toolbar .ant-btn {
  margin-right: 8px;
}

/* 邮件内容样式 */
.subject-content {
  font-weight: 500;
  color: #262626;
}

.email-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 跟踪信息样式 */
.tracking-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
}

.tracking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.tracking-title {
  font-weight: 500;
  color: #262626;
}

.tracking-time {
  font-size: 12px;
  color: #999;
}

.tracking-detail {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.tracking-location {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 模板编辑器样式 */
.template-editor {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.variable-panel {
  padding: 12px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.variable-panel h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #262626;
}

.variable-panel .ant-tag {
  margin-bottom: 4px;
}

/* 批量发送样式 */
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.batch-preview {
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.preview-stats {
  display: flex;
  gap: 24px;
}

/* 时间分析样式 */
.time-analysis {
  padding: 0;
}

/* 模板操作样式 */
.template-actions {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .statistics-section .ant-col {
    margin-bottom: 16px;
  }
  
  .action-section .ant-space {
    flex-wrap: wrap;
  }
  
  .action-section .ant-btn {
    margin-bottom: 8px;
  }
  
  .tracking-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .preview-stats {
    flex-direction: column;
    gap: 12px;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 48px;
  color: #bbb;
  margin-bottom: 16px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}
</style>