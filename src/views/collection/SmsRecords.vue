<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>短信记录</h1>
      <p>管理和查看所有催收短信记录，支持短信发送、模板管理和效果分析</p>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card">
      <a-form :model="searchForm" @submit="handleSearch">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="案件编号">
              <a-input 
                v-model:value="searchForm.caseNumber" 
                placeholder="请输入案件编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户姓名">
              <a-input 
                v-model:value="searchForm.customerName" 
                placeholder="请输入客户姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="手机号码">
              <a-input 
                v-model:value="searchForm.phoneNumber" 
                placeholder="请输入手机号码"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="短信状态">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option value="success">发送成功</a-select-option>
                <a-select-option value="failed">发送失败</a-select-option>
                <a-select-option value="pending">待发送</a-select-option>
                <a-select-option value="delivered">已送达</a-select-option>
                <a-select-option value="read">已读</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="短信类型">
              <a-select 
                v-model:value="searchForm.type" 
                placeholder="请选择类型"
                allow-clear
              >
                <a-select-option value="reminder">催收提醒</a-select-option>
                <a-select-option value="notice">缴费通知</a-select-option>
                <a-select-option value="warning">逾期警告</a-select-option>
                <a-select-option value="promotion">优惠活动</a-select-option>
                <a-select-option value="custom">自定义</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="发送人">
              <a-select 
                v-model:value="searchForm.sender" 
                placeholder="请选择发送人"
                allow-clear
              >
                <a-select-option value="collector1">张三</a-select-option>
                <a-select-option value="collector2">李四</a-select-option>
                <a-select-option value="system">系统自动</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="发送时间">
              <a-range-picker 
                v-model:value="searchForm.dateRange"
                format="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" html-type="submit">
                  <search-outlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <reload-outlined />
                  重置
                </a-button>
                <a-button 
                  :class="{ 'expand-btn-active': searchExpanded }"
                  @click="searchExpanded = !searchExpanded"
                >
                  {{ searchExpanded ? '收起' : '展开' }}
                  <down-outlined :class="{ 'expand-icon-active': searchExpanded }" />
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
        
        <!-- 展开的搜索条件 -->
        <div v-show="searchExpanded">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="模板名称">
                <a-select 
                  v-model:value="searchForm.template" 
                  placeholder="请选择模板"
                  allow-clear
                >
                  <a-select-option value="template1">标准催收模板</a-select-option>
                  <a-select-option value="template2">温馨提醒模板</a-select-option>
                  <a-select-option value="template3">最终警告模板</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="回复状态">
                <a-select 
                  v-model:value="searchForm.replyStatus" 
                  placeholder="请选择回复状态"
                  allow-clear
                >
                  <a-select-option value="replied">已回复</a-select-option>
                  <a-select-option value="no-reply">未回复</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="逾期天数">
                <a-input-number 
                  v-model:value="searchForm.overdueDays"
                  placeholder="逾期天数"
                  :min="0"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="金额范围">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.amountMin"
                    placeholder="最小金额"
                    :min="0"
                    style="width: 50%"
                  />
                  <a-input-number 
                    v-model:value="searchForm.amountMax"
                    placeholder="最大金额"
                    :min="0"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日发送"
              :value="statistics.todaySent"
              suffix="条"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <MessageOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="发送成功率"
              :value="statistics.successRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="回复率"
              :value="statistics.replyRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CommentOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总短信数"
              :value="statistics.totalSms"
              suffix="条"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <InboxOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <a-space>
        <a-button type="primary" @click="showSendModal = true">
          <PlusOutlined />
          发送短信
        </a-button>
        <a-button @click="showTemplateModal = true">
          <FileTextOutlined />
          模板管理
        </a-button>
        <a-button @click="handleBatchAction">
          <SendOutlined />
          批量发送
        </a-button>
        <a-button @click="handleExport">
          <DownloadOutlined />
          导出记录
        </a-button>
        <a-button @click="handleRefresh">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag 
              :color="getStatusColor(record.status)"
              class="status-tag"
            >
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'content'">
            <div class="sms-content">
              <a-tooltip :title="record.content">
                {{ record.content.length > 30 ? record.content.substring(0, 30) + '...' : record.content }}
              </a-tooltip>
            </div>
          </template>
          
          <template v-if="column.key === 'reply'">
            <div v-if="record.reply">
              <a-tooltip :title="record.reply">
                <a-tag color="blue">已回复</a-tag>
              </a-tooltip>
            </div>
            <a-tag v-else color="gray">未回复</a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleResend(record)">
                <RedoOutlined />
                重发
              </a-button>
              <a-button type="link" size="small" @click="handleAnalyze(record)">
                <BarChartOutlined />
                分析
              </a-button>
              <a-popconfirm
                title="确定要删除这条记录吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 发送短信模态框 -->
    <a-modal
      v-model:open="showSendModal"
      title="发送短信"
      width="800px"
      @ok="handleSendSms"
      @cancel="handleSendCancel"
    >
      <a-form
        :model="sendForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="接收对象" required>
          <a-radio-group v-model:value="sendForm.targetType">
            <a-radio value="single">单个客户</a-radio>
            <a-radio value="batch">批量客户</a-radio>
            <a-radio value="case">按案件</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item v-if="sendForm.targetType === 'single'" label="手机号码" required>
          <a-input v-model:value="sendForm.phoneNumber" placeholder="请输入手机号码" />
        </a-form-item>
        
        <a-form-item v-if="sendForm.targetType === 'batch'" label="客户列表" required>
          <a-select
            v-model:value="sendForm.customerList"
            mode="multiple"
            placeholder="请选择客户"
            style="width: 100%"
          >
            <a-select-option value="cust1">张三 (138****1234)</a-select-option>
            <a-select-option value="cust2">李四 (139****5678)</a-select-option>
            <a-select-option value="cust3">王五 (137****9012)</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item v-if="sendForm.targetType === 'case'" label="案件编号" required>
          <a-input v-model:value="sendForm.caseNumber" placeholder="请输入案件编号" />
        </a-form-item>
        
        <a-form-item label="短信类型" required>
          <a-select v-model:value="sendForm.type" placeholder="请选择短信类型">
            <a-select-option value="reminder">催收提醒</a-select-option>
            <a-select-option value="notice">缴费通知</a-select-option>
            <a-select-option value="warning">逾期警告</a-select-option>
            <a-select-option value="promotion">优惠活动</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="短信模板">
          <a-select 
            v-model:value="sendForm.template" 
            placeholder="请选择模板或自定义内容"
            @change="handleTemplateChange"
          >
            <a-select-option value="template1">标准催收模板</a-select-option>
            <a-select-option value="template2">温馨提醒模板</a-select-option>
            <a-select-option value="template3">最终警告模板</a-select-option>
            <a-select-option value="custom">自定义内容</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="短信内容" required>
          <a-textarea
            v-model:value="sendForm.content"
            :rows="4"
            placeholder="请输入短信内容"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item label="发送时间">
          <a-radio-group v-model:value="sendForm.sendTime">
            <a-radio value="immediate">立即发送</a-radio>
            <a-radio value="scheduled">定时发送</a-radio>
          </a-radio-group>
          <a-date-picker
            v-if="sendForm.sendTime === 'scheduled'"
            v-model:value="sendForm.scheduledTime"
            show-time
            placeholder="选择发送时间"
            style="margin-top: 8px; width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="发送选项">
          <a-checkbox-group v-model:value="sendForm.options">
            <a-checkbox value="receipt">需要回执</a-checkbox>
            <a-checkbox value="priority">优先发送</a-checkbox>
            <a-checkbox value="track">跟踪效果</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 短信详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="短信详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="案件编号">{{ selectedRecord.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedRecord.customerName }}</a-descriptions-item>
          <a-descriptions-item label="手机号码">{{ selectedRecord.phoneNumber }}</a-descriptions-item>
          <a-descriptions-item label="短信状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="短信类型">
            <a-tag :color="getTypeColor(selectedRecord.type)">
              {{ getTypeText(selectedRecord.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送人">{{ selectedRecord.sender }}</a-descriptions-item>
          <a-descriptions-item label="发送时间">{{ selectedRecord.sendTime }}</a-descriptions-item>
          <a-descriptions-item label="送达时间">{{ selectedRecord.deliverTime || '未送达' }}</a-descriptions-item>
          <a-descriptions-item label="短信内容" :span="2">
            <div class="sms-content-detail">{{ selectedRecord.content }}</div>
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.reply" label="客户回复" :span="2">
            <div class="reply-content">{{ selectedRecord.reply }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="费用">{{ selectedRecord.cost }}元</a-descriptions-item>
          <a-descriptions-item label="字数">{{ selectedRecord.length }}字</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 短信模板管理模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="短信模板管理"
      width="1000px"
      @ok="handleTemplateSave"
    >
      <a-tabs v-model:activeKey="templateActiveKey">
        <a-tab-pane key="list" tab="模板列表">
          <div class="template-actions">
            <a-button type="primary" @click="showAddTemplate = true">
              <PlusOutlined />
              新增模板
            </a-button>
          </div>
          <a-table
            :columns="templateColumns"
            :data-source="templateData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleEditTemplate(record)">编辑</a-button>
                  <a-button type="link" size="small" @click="handlePreviewTemplate(record)">预览</a-button>
                  <a-popconfirm title="确定删除？" @confirm="handleDeleteTemplate(record)">
                    <a-button type="link" size="small" danger>删除</a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="statistics" tab="使用统计">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="模板使用排行">
                <div id="templateChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="发送效果对比">
                <div id="effectChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 效果分析模态框 -->
    <a-modal
      v-model:open="showAnalysisModal"
      title="短信效果分析"
      width="1200px"
      :footer="null"
    >
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card title="发送统计">
            <a-statistic title="发送时间" :value="analysisData.sendTime" />
            <a-statistic title="送达时间" :value="analysisData.deliverTime" />
            <a-statistic title="阅读时间" :value="analysisData.readTime" />
            <a-statistic title="回复时间" :value="analysisData.replyTime" />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="效果指标">
            <a-statistic title="送达率" :value="analysisData.deliverRate" suffix="%" />
            <a-statistic title="阅读率" :value="analysisData.readRate" suffix="%" />
            <a-statistic title="回复率" :value="analysisData.replyRate" suffix="%" />
            <a-statistic title="转化率" :value="analysisData.conversionRate" suffix="%" />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="成本分析">
            <a-statistic title="发送成本" :value="analysisData.cost" suffix="元" />
            <a-statistic title="获客成本" :value="analysisData.acquisitionCost" suffix="元" />
            <a-statistic title="ROI" :value="analysisData.roi" suffix="%" />
          </a-card>
        </a-col>
      </a-row>
      
      <a-card title="时间线分析" style="margin-top: 16px;">
        <div id="timelineChart" style="height: 300px;"></div>
      </a-card>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  PlusOutlined,
  FileTextOutlined,
  SendOutlined,
  DownloadOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  CommentOutlined,
  InboxOutlined,
  EyeOutlined,
  RedoOutlined,
  BarChartOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)

// 模态框显示状态
const showSendModal = ref(false)
const showDetailModal = ref(false)
const showTemplateModal = ref(false)
const showAnalysisModal = ref(false)
const showAddTemplate = ref(false)

// 表单数据
const searchForm = reactive({
  caseNumber: '',
  customerName: '',
  phoneNumber: '',
  status: undefined,
  type: undefined,
  sender: undefined,
  dateRange: [],
  template: undefined,
  replyStatus: undefined,
  overdueDays: undefined,
  amountMin: undefined,
  amountMax: undefined
})

const sendForm = reactive({
  targetType: 'single',
  phoneNumber: '',
  customerList: [],
  caseNumber: '',
  type: 'reminder',
  template: '',
  content: '',
  sendTime: 'immediate',
  scheduledTime: null,
  options: []
})

// 统计数据
const statistics = reactive({
  todaySent: 142,
  successRate: 94.5,
  replyRate: 23.7,
  totalSms: 8567
})

// 选中记录
const selectedRecord = ref(null)
const templateActiveKey = ref('list')

// 分析数据
const analysisData = reactive({
  sendTime: '2024-01-20 14:30:00',
  deliverTime: '2024-01-20 14:30:15',
  readTime: '2024-01-20 14:35:22',
  replyTime: '2024-01-20 15:20:30',
  deliverRate: 98.5,
  readRate: 76.3,
  replyRate: 23.7,
  conversionRate: 15.2,
  cost: 0.05,
  acquisitionCost: 2.50,
  roi: 285.6
})

// 表格列定义
const columns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    sorter: true
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '手机号码',
    dataIndex: 'phoneNumber',
    key: 'phoneNumber',
    width: 120
  },
  {
    title: '短信内容',
    dataIndex: 'content',
    key: 'content',
    width: 200,
    ellipsis: true
  },
  {
    title: '短信类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '发送状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '发送成功', value: 'success' },
      { text: '发送失败', value: 'failed' },
      { text: '待发送', value: 'pending' },
      { text: '已送达', value: 'delivered' },
      { text: '已读', value: 'read' }
    ]
  },
  {
    title: '客户回复',
    dataIndex: 'reply',
    key: 'reply',
    width: 100
  },
  {
    title: '发送人',
    dataIndex: 'sender',
    key: 'sender',
    width: 80
  },
  {
    title: '发送时间',
    dataIndex: 'sendTime',
    key: 'sendTime',
    width: 140,
    sorter: true
  },
  {
    title: '费用',
    dataIndex: 'cost',
    key: 'cost',
    width: 80,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模板表格列
const templateColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '模板类型',
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount'
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 表格数据
const tableData = ref([
  {
    key: '1',
    caseNumber: 'CS202401001',
    customerName: '张三',
    phoneNumber: '138****1234',
    content: '尊敬的张三先生，您的借款已逾期3天，请及时还款，避免影响个人信用记录。如有疑问请联系我们。',
    type: 'reminder',
    status: 'delivered',
    reply: '已收到，明天处理',
    sender: '李收款',
    sendTime: '2024-01-20 14:30:00',
    deliverTime: '2024-01-20 14:30:15',
    cost: 0.05,
    length: 45
  },
  {
    key: '2',
    caseNumber: 'CS202401002',
    customerName: '李四',
    phoneNumber: '139****5678',
    content: '温馨提醒：您的还款日期为今日，请确保账户余额充足，系统将自动扣款。',
    type: 'notice',
    status: 'read',
    reply: null,
    sender: '王催收',
    sendTime: '2024-01-20 09:00:00',
    deliverTime: '2024-01-20 09:00:12',
    cost: 0.05,
    length: 32
  },
  {
    key: '3',
    caseNumber: 'CS202401003',
    customerName: '王五',
    phoneNumber: '137****9012',
    content: '【最终警告】您的欠款已严重逾期，如不及时处理将采取法律手段追讨。',
    type: 'warning',
    status: 'success',
    reply: '马上联系处理',
    sender: '赵专员',
    sendTime: '2024-01-19 16:45:00',
    deliverTime: '2024-01-19 16:45:08',
    cost: 0.05,
    length: 28
  }
])

// 模板数据
const templateData = ref([
  {
    key: '1',
    name: '标准催收模板',
    type: '催收提醒',
    usageCount: 1250,
    successRate: '94.5%',
    createTime: '2024-01-15'
  },
  {
    key: '2',
    name: '温馨提醒模板',
    type: '缴费通知',
    usageCount: 856,
    successRate: '89.2%',
    createTime: '2024-01-10'
  },
  {
    key: '3',
    name: '最终警告模板',
    type: '逾期警告',
    usageCount: 423,
    successRate: '76.8%',
    createTime: '2024-01-08'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
  }
}

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    success: 'green',
    failed: 'red',
    pending: 'orange',
    delivered: 'blue',
    read: 'purple'
  }
  return colorMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    success: '发送成功',
    failed: '发送失败',
    pending: '待发送',
    delivered: '已送达',
    read: '已读'
  }
  return textMap[status] || status
}

// 类型颜色映射
const getTypeColor = (type) => {
  const colorMap = {
    reminder: 'blue',
    notice: 'green',
    warning: 'red',
    promotion: 'purple',
    custom: 'orange'
  }
  return colorMap[type] || 'default'
}

// 类型文本映射
const getTypeText = (type) => {
  const textMap = {
    reminder: '催收提醒',
    notice: '缴费通知',
    warning: '逾期警告',
    promotion: '优惠活动',
    custom: '自定义'
  }
  return textMap[type] || type
}

// 事件处理函数
const handleSearch = () => {
  loading.value = true
  console.log('搜索条件:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  message.success('已重置搜索条件')
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 800)
}

const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变化:', pag, filters, sorter)
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleView = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleResend = (record) => {
  console.log('重发短信:', record)
  message.success('短信已重新发送')
}

const handleAnalyze = (record) => {
  selectedRecord.value = record
  showAnalysisModal.value = true
}

const handleDelete = (record) => {
  console.log('删除记录:', record)
  message.success('记录已删除')
}

const handleSendSms = () => {
  console.log('发送短信:', sendForm)
  message.success('短信发送成功')
  showSendModal.value = false
}

const handleSendCancel = () => {
  // 重置发送表单
  Object.assign(sendForm, {
    targetType: 'single',
    phoneNumber: '',
    customerList: [],
    caseNumber: '',
    type: 'reminder',
    template: '',
    content: '',
    sendTime: 'immediate',
    scheduledTime: null,
    options: []
  })
}

const handleTemplateChange = (value) => {
  // 根据选择的模板填充内容
  const templateMap = {
    template1: '尊敬的${customerName}先生/女士，您的借款已逾期${overdueDays}天，请及时还款，避免影响个人信用记录。',
    template2: '温馨提醒：您的还款日期为今日，请确保账户余额充足，系统将自动扣款。',
    template3: '【最终警告】您的欠款已严重逾期，如不及时处理将采取法律手段追讨。'
  }
  if (value && value !== 'custom') {
    sendForm.content = templateMap[value] || ''
  }
}

const handleBatchAction = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  console.log('批量发送:', selectedRowKeys.value)
  message.success(`已选择 ${selectedRowKeys.value.length} 条记录进行批量发送`)
}

const handleExport = () => {
  console.log('导出记录')
  message.success('导出成功')
}

const handleTemplateSave = () => {
  console.log('保存模板设置')
  message.success('模板设置已保存')
  showTemplateModal.value = false
}

const handleEditTemplate = (record) => {
  console.log('编辑模板:', record)
  message.info('编辑模板功能')
}

const handlePreviewTemplate = (record) => {
  console.log('预览模板:', record)
  message.info('预览模板功能')
}

const handleDeleteTemplate = (record) => {
  console.log('删除模板:', record)
  message.success('模板已删除')
}

// 组件挂载后初始化
onMounted(() => {
  handleRefresh()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-section {
  margin-bottom: 16px;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.statistics-section {
  margin-bottom: 16px;
}

.action-section {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.table-section {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.status-tag {
  font-weight: 500;
}

.sms-content {
  max-width: 200px;
  word-break: break-word;
}

.sms-content-detail {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  word-break: break-word;
  line-height: 1.6;
}

.reply-content {
  background-color: #e6f7ff;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  word-break: break-word;
  line-height: 1.6;
}

.template-actions {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .search-section :deep(.ant-col) {
    margin-bottom: 16px;
  }
  
  .statistics-section :deep(.ant-col) {
    margin-bottom: 16px;
  }
  
  .action-section {
    padding: 12px;
  }
  
  .action-section :deep(.ant-space) {
    flex-wrap: wrap;
  }
}

/* 表格优化 */
.table-section :deep(.ant-table) {
  font-size: 13px;
}

.table-section :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  color: #262626;
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8;
}

.table-section :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f0f0f0;
}

.table-section :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 模态框优化 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

:deep(.ant-modal-title) {
  color: #262626;
  font-weight: 600;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #595959;
}

/* 统计卡片优化 */
.statistics-section :deep(.ant-card) {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.statistics-section :deep(.ant-statistic-title) {
  color: #8c8c8c;
  font-size: 13px;
  margin-bottom: 4px;
}

.statistics-section :deep(.ant-statistic-content) {
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

/* 表单优化 */
:deep(.ant-form-item-label > label) {
  color: #262626;
  font-weight: 500;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 4px;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮优化 */
:deep(.ant-btn) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 标签优化 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}
</style>
