<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>外访记录</h1>
      <p>管理和跟踪催收外访活动，包括上门拜访、现场调查、资产查看等记录</p>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card">
      <a-form :model="searchForm" @submit="handleSearch">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="案件编号">
              <a-input 
                v-model:value="searchForm.caseNumber" 
                placeholder="请输入案件编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户姓名">
              <a-input 
                v-model:value="searchForm.customerName" 
                placeholder="请输入客户姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="外访类型">
              <a-select 
                v-model:value="searchForm.visitType" 
                placeholder="请选择外访类型"
                allow-clear
              >
                <a-select-option value="home_visit">上门拜访</a-select-option>
                <a-select-option value="workplace_visit">工作地拜访</a-select-option>
                <a-select-option value="asset_check">资产查看</a-select-option>
                <a-select-option value="investigation">现场调查</a-select-option>
                <a-select-option value="mediation">现场调解</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="外访状态">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option value="planned">计划中</a-select-option>
                <a-select-option value="in_progress">进行中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="cancelled">已取消</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="外访人员">
              <a-select 
                v-model:value="searchForm.visitor" 
                placeholder="请选择外访人员"
                allow-clear
              >
                <a-select-option value="visitor1">张催收</a-select-option>
                <a-select-option value="visitor2">李外访</a-select-option>
                <a-select-option value="visitor3">王调查</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="外访结果">
              <a-select 
                v-model:value="searchForm.result" 
                placeholder="请选择外访结果"
                allow-clear
              >
                <a-select-option value="success">成功</a-select-option>
                <a-select-option value="partial_success">部分成功</a-select-option>
                <a-select-option value="no_contact">未联系到</a-select-option>
                <a-select-option value="refused">拒绝见面</a-select-option>
                <a-select-option value="no_one_home">无人在家</a-select-option>
                <a-select-option value="address_error">地址错误</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="外访时间">
              <a-range-picker 
                v-model:value="searchForm.dateRange"
                format="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" html-type="submit">
                  <search-outlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <reload-outlined />
                  重置
                </a-button>
                <a-button 
                  :class="{ 'expand-btn-active': searchExpanded }"
                  @click="searchExpanded = !searchExpanded"
                >
                  {{ searchExpanded ? '收起' : '展开' }}
                  <down-outlined :class="{ 'expand-icon-active': searchExpanded }" />
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
        
        <!-- 展开的搜索条件 -->
        <div v-show="searchExpanded">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="地区">
                <a-cascader
                  v-model:value="searchForm.region"
                  :options="regionOptions"
                  placeholder="请选择地区"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="逾期天数">
                <a-input-number 
                  v-model:value="searchForm.overdueDays"
                  placeholder="逾期天数"
                  :min="0"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="金额范围">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.amountMin"
                    placeholder="最小金额"
                    :min="0"
                    style="width: 50%"
                  />
                  <a-input-number 
                    v-model:value="searchForm.amountMax"
                    placeholder="最大金额"
                    :min="0"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="是否拍照">
                <a-select 
                  v-model:value="searchForm.hasPhotos" 
                  placeholder="是否有拍照"
                  allow-clear
                >
                  <a-select-option value="true">有拍照</a-select-option>
                  <a-select-option value="false">无拍照</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日外访"
              :value="statistics.todayVisits"
              suffix="次"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <CarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="statistics.successRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均耗时"
              :value="statistics.avgDuration"
              suffix="小时"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总外访次数"
              :value="statistics.totalVisits"
              suffix="次"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <TeamOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <a-space>
        <a-button type="primary" @click="showPlanModal = true">
          <PlusOutlined />
          新增外访
        </a-button>
        <a-button @click="showMapModal = true">
          <EnvironmentOutlined />
          地图模式
        </a-button>
        <a-button @click="handleBatchAction">
          <CalendarOutlined />
          批量安排
        </a-button>
        <a-button @click="handleExport">
          <DownloadOutlined />
          导出记录
        </a-button>
        <a-button @click="handleRefresh">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag 
              :color="getStatusColor(record.status)"
              class="status-tag"
            >
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'visitType'">
            <a-tag :color="getTypeColor(record.visitType)">
              {{ getTypeText(record.visitType) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'result'">
            <a-tag :color="getResultColor(record.result)">
              {{ getResultText(record.result) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'address'">
            <div class="address-cell">
              <a-tooltip :title="record.address">
                {{ record.address.length > 20 ? record.address.substring(0, 20) + '...' : record.address }}
              </a-tooltip>
              <a-button 
                type="link" 
                size="small" 
                @click="handleViewMap(record)"
                style="padding: 0; margin-left: 4px;"
              >
                <EnvironmentOutlined />
              </a-button>
            </div>
          </template>
          
          <template v-if="column.key === 'photos'">
            <div class="photos-cell">
              <span v-if="record.photos && record.photos.length > 0">
                <a-button type="link" size="small" @click="handleViewPhotos(record)">
                  <PictureOutlined />
                  {{ record.photos.length }}张
                </a-button>
              </span>
              <span v-else class="no-photos">无照片</span>
            </div>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button 
                v-if="record.status === 'planned'"
                type="link" 
                size="small" 
                @click="handleStart(record)"
              >
                <PlayCircleOutlined />
                开始
              </a-button>
              <a-button 
                v-if="record.status === 'in_progress'"
                type="link" 
                size="small" 
                @click="handleComplete(record)"
              >
                <CheckOutlined />
                完成
              </a-button>
              <a-popconfirm
                title="确定要删除这条记录吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑外访计划模态框 -->
    <a-modal
      v-model:open="showPlanModal"
      :title="editingRecord ? '编辑外访计划' : '新增外访计划'"
      width="800px"
      @ok="handlePlanSave"
      @cancel="handlePlanCancel"
    >
      <a-form
        :model="planForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="案件编号" required>
          <a-input v-model:value="planForm.caseNumber" placeholder="请输入案件编号" />
        </a-form-item>
        
        <a-form-item label="客户姓名" required>
          <a-input v-model:value="planForm.customerName" placeholder="请输入客户姓名" />
        </a-form-item>
        
        <a-form-item label="外访类型" required>
          <a-select v-model:value="planForm.visitType" placeholder="请选择外访类型">
            <a-select-option value="home_visit">上门拜访</a-select-option>
            <a-select-option value="workplace_visit">工作地拜访</a-select-option>
            <a-select-option value="asset_check">资产查看</a-select-option>
            <a-select-option value="investigation">现场调查</a-select-option>
            <a-select-option value="mediation">现场调解</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="外访地址" required>
          <a-input v-model:value="planForm.address" placeholder="请输入外访地址" />
        </a-form-item>
        
        <a-form-item label="预约时间" required>
          <a-date-picker
            v-model:value="planForm.scheduledTime"
            show-time
            placeholder="选择预约时间"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="外访人员" required>
          <a-select 
            v-model:value="planForm.visitors" 
            mode="multiple"
            placeholder="请选择外访人员"
          >
            <a-select-option value="visitor1">张催收</a-select-option>
            <a-select-option value="visitor2">李外访</a-select-option>
            <a-select-option value="visitor3">王调查</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="联系电话">
          <a-input v-model:value="planForm.contactPhone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="外访目的" required>
          <a-textarea
            v-model:value="planForm.purpose"
            :rows="3"
            placeholder="请描述外访目的"
          />
        </a-form-item>
        
        <a-form-item label="注意事项">
          <a-textarea
            v-model:value="planForm.notes"
            :rows="2"
            placeholder="请输入注意事项"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 外访详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="外访详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="案件编号">{{ selectedRecord.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedRecord.customerName }}</a-descriptions-item>
          <a-descriptions-item label="外访类型">
            <a-tag :color="getTypeColor(selectedRecord.visitType)">
              {{ getTypeText(selectedRecord.visitType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="外访状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="外访结果">
            <a-tag :color="getResultColor(selectedRecord.result)">
              {{ getResultText(selectedRecord.result) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="外访人员">{{ selectedRecord.visitors?.join(', ') }}</a-descriptions-item>
          <a-descriptions-item label="外访地址" :span="3">{{ selectedRecord.address }}</a-descriptions-item>
          <a-descriptions-item label="预约时间">{{ selectedRecord.scheduledTime }}</a-descriptions-item>
          <a-descriptions-item label="实际时间">{{ selectedRecord.actualTime || '未开始' }}</a-descriptions-item>
          <a-descriptions-item label="耗时">{{ selectedRecord.duration || '-' }}</a-descriptions-item>
          <a-descriptions-item label="外访目的" :span="3">{{ selectedRecord.purpose }}</a-descriptions-item>
          <a-descriptions-item label="外访过程" :span="3">
            <div class="visit-process">{{ selectedRecord.process || '无记录' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="外访结果说明" :span="3">
            <div class="visit-result">{{ selectedRecord.resultDesc || '无说明' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="费用">{{ selectedRecord.cost || 0 }}元</a-descriptions-item>
          <a-descriptions-item label="里程">{{ selectedRecord.mileage || 0 }}公里</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ selectedRecord.createTime }}</a-descriptions-item>
        </a-descriptions>
        
        <!-- 外访照片 -->
        <div v-if="selectedRecord.photos && selectedRecord.photos.length > 0" class="photos-section">
          <h4>外访照片</h4>
          <div class="photo-grid">
            <div 
              v-for="(photo, index) in selectedRecord.photos" 
              :key="index"
              class="photo-item"
              @click="handlePreviewPhoto(photo, index)"
            >
              <img :src="photo.url" :alt="photo.desc" />
              <div class="photo-desc">{{ photo.desc }}</div>
            </div>
          </div>
        </div>
        
        <!-- 位置信息 -->
        <div v-if="selectedRecord.location" class="location-section">
          <h4>位置信息</h4>
          <div class="location-info">
            <p><strong>经度:</strong> {{ selectedRecord.location.lng }}</p>
            <p><strong>纬度:</strong> {{ selectedRecord.location.lat }}</p>
            <p><strong>定位精度:</strong> {{ selectedRecord.location.accuracy }}米</p>
            <p><strong>定位时间:</strong> {{ selectedRecord.location.time }}</p>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 外访执行模态框 -->
    <a-modal
      v-model:open="showExecuteModal"
      title="外访执行"
      width="800px"
      @ok="handleExecuteSave"
    >
      <a-form
        :model="executeForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="实际到达时间">
          <a-date-picker
            v-model:value="executeForm.actualTime"
            show-time
            placeholder="选择实际到达时间"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="外访结果" required>
          <a-select v-model:value="executeForm.result" placeholder="请选择外访结果">
            <a-select-option value="success">成功</a-select-option>
            <a-select-option value="partial_success">部分成功</a-select-option>
            <a-select-option value="no_contact">未联系到</a-select-option>
            <a-select-option value="refused">拒绝见面</a-select-option>
            <a-select-option value="no_one_home">无人在家</a-select-option>
            <a-select-option value="address_error">地址错误</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="外访过程" required>
          <a-textarea
            v-model:value="executeForm.process"
            :rows="4"
            placeholder="请详细描述外访过程"
          />
        </a-form-item>
        
        <a-form-item label="结果说明">
          <a-textarea
            v-model:value="executeForm.resultDesc"
            :rows="3"
            placeholder="请说明外访结果"
          />
        </a-form-item>
        
        <a-form-item label="费用">
          <a-input-number
            v-model:value="executeForm.cost"
            placeholder="外访费用"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="里程">
          <a-input-number
            v-model:value="executeForm.mileage"
            placeholder="外访里程"
            :min="0"
            :precision="1"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="现场照片">
          <a-upload
            v-model:file-list="executeForm.photos"
            list-type="picture-card"
            :before-upload="beforeUpload"
            @preview="handlePhotoPreview"
          >
            <div>
              <PlusOutlined />
              <div style="margin-top: 8px">上传照片</div>
            </div>
          </a-upload>
        </a-form-item>
        
        <a-form-item label="获取位置">
          <a-button @click="handleGetLocation" :loading="locationLoading">
            <EnvironmentOutlined />
            获取当前位置
          </a-button>
          <span v-if="executeForm.location" style="margin-left: 12px; color: #52c41a;">
            <CheckOutlined /> 位置已获取
          </span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 地图模式模态框 -->
    <a-modal
      v-model:open="showMapModal"
      title="外访地图"
      width="1200px"
      :footer="null"
    >
      <div class="map-container">
        <div class="map-sidebar">
          <h4>外访列表</h4>
          <div class="visit-list">
            <div 
              v-for="record in tableData" 
              :key="record.key"
              class="visit-item"
              :class="{ active: selectedMapRecord?.key === record.key }"
              @click="handleSelectMapRecord(record)"
            >
              <div class="visit-header">
                <span class="case-number">{{ record.caseNumber }}</span>
                <a-tag :color="getStatusColor(record.status)" size="small">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </div>
              <div class="visit-info">
                <p>{{ record.customerName }} - {{ getTypeText(record.visitType) }}</p>
                <p class="address">{{ record.address }}</p>
                <p class="time">{{ record.scheduledTime }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="map-content">
          <div id="visitMap" style="height: 500px; background: #f0f0f0;">
            <div style="padding: 200px 0; text-align: center; color: #999;">
              地图组件区域<br/>
              (实际项目中可集成百度地图、高德地图等)
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 照片预览模态框 -->
    <a-modal
      v-model:open="showPhotoModal"
      title="照片预览"
      width="800px"
      :footer="null"
    >
      <div v-if="previewPhoto" class="photo-preview">
        <img :src="previewPhoto.url" :alt="previewPhoto.desc" style="width: 100%; max-height: 500px; object-fit: contain;" />
        <div style="margin-top: 16px;">
          <p><strong>描述:</strong> {{ previewPhoto.desc }}</p>
          <p><strong>拍摄时间:</strong> {{ previewPhoto.time }}</p>
          <p><strong>位置:</strong> {{ previewPhoto.location }}</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  PlusOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  DownloadOutlined,
  CarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  EyeOutlined,
  EditOutlined,
  PlayCircleOutlined,
  CheckOutlined,
  DeleteOutlined,
  PictureOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const locationLoading = ref(false)

// 模态框显示状态
const showPlanModal = ref(false)
const showDetailModal = ref(false)
const showExecuteModal = ref(false)
const showMapModal = ref(false)
const showPhotoModal = ref(false)

// 编辑状态
const editingRecord = ref(null)
const selectedRecord = ref(null)
const selectedMapRecord = ref(null)
const previewPhoto = ref(null)

// 表单数据
const searchForm = reactive({
  caseNumber: '',
  customerName: '',
  visitType: undefined,
  status: undefined,
  visitor: undefined,
  result: undefined,
  dateRange: [],
  region: [],
  overdueDays: undefined,
  amountMin: undefined,
  amountMax: undefined,
  hasPhotos: undefined
})

const planForm = reactive({
  caseNumber: '',
  customerName: '',
  visitType: '',
  address: '',
  scheduledTime: null,
  visitors: [],
  contactPhone: '',
  purpose: '',
  notes: ''
})

const executeForm = reactive({
  actualTime: null,
  result: '',
  process: '',
  resultDesc: '',
  cost: 0,
  mileage: 0,
  photos: [],
  location: null
})

// 统计数据
const statistics = reactive({
  todayVisits: 12,
  successRate: 78.5,
  avgDuration: 2.3,
  totalVisits: 458
})

// 地区选项
const regionOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'haidian', label: '海淀区' },
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      { value: 'pudong', label: '浦东新区' },
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' }
    ]
  }
]

// 表格列定义
const columns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    sorter: true
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '外访类型',
    dataIndex: 'visitType',
    key: 'visitType',
    width: 120
  },
  {
    title: '外访地址',
    dataIndex: 'address',
    key: 'address',
    width: 200,
    ellipsis: true
  },
  {
    title: '外访状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '计划中', value: 'planned' },
      { text: '进行中', value: 'in_progress' },
      { text: '已完成', value: 'completed' },
      { text: '已取消', value: 'cancelled' },
      { text: '失败', value: 'failed' }
    ]
  },
  {
    title: '外访结果',
    dataIndex: 'result',
    key: 'result',
    width: 120
  },
  {
    title: '外访人员',
    dataIndex: 'visitors',
    key: 'visitors',
    width: 120,
    customRender: ({ record }) => record.visitors?.join(', ')
  },
  {
    title: '预约时间',
    dataIndex: 'scheduledTime',
    key: 'scheduledTime',
    width: 140,
    sorter: true
  },
  {
    title: '实际时间',
    dataIndex: 'actualTime',
    key: 'actualTime',
    width: 140
  },
  {
    title: '照片',
    key: 'photos',
    width: 80
  },
  {
    title: '费用',
    dataIndex: 'cost',
    key: 'cost',
    width: 80,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 220,
    fixed: 'right'
  }
]

// 表格数据
const tableData = ref([
  {
    key: '1',
    caseNumber: 'CS202401001',
    customerName: '张三',
    visitType: 'home_visit',
    address: '北京市海淀区中关村大街1号',
    status: 'completed',
    result: 'success',
    visitors: ['张催收', '李外访'],
    scheduledTime: '2024-01-20 14:00:00',
    actualTime: '2024-01-20 14:15:00',
    duration: '1.5小时',
    purpose: '上门催收逾期款项，了解还款意愿',
    process: '与客户面谈，了解其经济状况，协商还款计划',
    resultDesc: '客户同意在本月底前还清全部欠款',
    cost: 50,
    mileage: 25,
    createTime: '2024-01-20 10:00:00',
    photos: [
      {
        url: '/api/photos/visit1_1.jpg',
        desc: '客户住所外观',
        time: '2024-01-20 14:20:00',
        location: '北京市海淀区中关村大街1号'
      },
      {
        url: '/api/photos/visit1_2.jpg',
        desc: '与客户沟通现场',
        time: '2024-01-20 14:45:00',
        location: '北京市海淀区中关村大街1号'
      }
    ],
    location: {
      lng: 116.3974,
      lat: 39.9093,
      accuracy: 10,
      time: '2024-01-20 14:15:00'
    }
  },
  {
    key: '2',
    caseNumber: 'CS202401002',
    customerName: '李四',
    visitType: 'workplace_visit',
    address: '北京市朝阳区建国门外大街8号',
    status: 'planned',
    result: '',
    visitors: ['王调查'],
    scheduledTime: '2024-01-22 10:00:00',
    actualTime: '',
    purpose: '到工作地了解客户收入情况',
    cost: 0,
    mileage: 0,
    createTime: '2024-01-21 09:00:00',
    photos: [],
    location: null
  },
  {
    key: '3',
    caseNumber: 'CS202401003',
    customerName: '王五',
    visitType: 'asset_check',
    address: '上海市浦东新区陆家嘴环路1000号',
    status: 'in_progress',
    result: '',
    visitors: ['赵专员'],
    scheduledTime: '2024-01-21 15:00:00',
    actualTime: '2024-01-21 15:10:00',
    purpose: '查看客户资产情况，评估还款能力',
    cost: 0,
    mileage: 0,
    createTime: '2024-01-21 12:00:00',
    photos: [],
    location: {
      lng: 121.4737,
      lat: 31.2304,
      accuracy: 15,
      time: '2024-01-21 15:10:00'
    }
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    planned: 'blue',
    in_progress: 'orange',
    completed: 'green',
    cancelled: 'gray',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    planned: '计划中',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    failed: '失败'
  }
  return textMap[status] || status
}

// 类型颜色映射
const getTypeColor = (type) => {
  const colorMap = {
    home_visit: 'blue',
    workplace_visit: 'green',
    asset_check: 'purple',
    investigation: 'orange',
    mediation: 'red',
    other: 'gray'
  }
  return colorMap[type] || 'default'
}

// 类型文本映射
const getTypeText = (type) => {
  const textMap = {
    home_visit: '上门拜访',
    workplace_visit: '工作地拜访',
    asset_check: '资产查看',
    investigation: '现场调查',
    mediation: '现场调解',
    other: '其他'
  }
  return textMap[type] || type
}

// 结果颜色映射
const getResultColor = (result) => {
  const colorMap = {
    success: 'green',
    partial_success: 'blue',
    no_contact: 'orange',
    refused: 'red',
    no_one_home: 'gray',
    address_error: 'purple'
  }
  return colorMap[result] || 'default'
}

// 结果文本映射
const getResultText = (result) => {
  const textMap = {
    success: '成功',
    partial_success: '部分成功',
    no_contact: '未联系到',
    refused: '拒绝见面',
    no_one_home: '无人在家',
    address_error: '地址错误'
  }
  return textMap[result] || result
}

// 事件处理函数
const handleSearch = () => {
  loading.value = true
  console.log('搜索条件:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  message.success('已重置搜索条件')
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 800)
}

const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变化:', pag, filters, sorter)
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleView = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleEdit = (record) => {
  editingRecord.value = record
  Object.assign(planForm, {
    caseNumber: record.caseNumber,
    customerName: record.customerName,
    visitType: record.visitType,
    address: record.address,
    scheduledTime: record.scheduledTime,
    visitors: record.visitors || [],
    contactPhone: record.contactPhone || '',
    purpose: record.purpose || '',
    notes: record.notes || ''
  })
  showPlanModal.value = true
}

const handleStart = (record) => {
  editingRecord.value = record
  Object.assign(executeForm, {
    actualTime: new Date(),
    result: '',
    process: '',
    resultDesc: '',
    cost: 0,
    mileage: 0,
    photos: [],
    location: null
  })
  showExecuteModal.value = true
}

const handleComplete = (record) => {
  editingRecord.value = record
  Object.assign(executeForm, {
    actualTime: record.actualTime || new Date(),
    result: record.result || '',
    process: record.process || '',
    resultDesc: record.resultDesc || '',
    cost: record.cost || 0,
    mileage: record.mileage || 0,
    photos: record.photos || [],
    location: record.location || null
  })
  showExecuteModal.value = true
}

const handleDelete = (record) => {
  console.log('删除记录:', record)
  message.success('记录已删除')
}

const handlePlanSave = () => {
  console.log('保存外访计划:', planForm)
  message.success('外访计划已保存')
  showPlanModal.value = false
  handlePlanCancel()
}

const handlePlanCancel = () => {
  editingRecord.value = null
  Object.assign(planForm, {
    caseNumber: '',
    customerName: '',
    visitType: '',
    address: '',
    scheduledTime: null,
    visitors: [],
    contactPhone: '',
    purpose: '',
    notes: ''
  })
}

const handleExecuteSave = () => {
  console.log('保存外访执行:', executeForm)
  message.success('外访执行记录已保存')
  showExecuteModal.value = false
  Object.assign(executeForm, {
    actualTime: null,
    result: '',
    process: '',
    resultDesc: '',
    cost: 0,
    mileage: 0,
    photos: [],
    location: null
  })
}

const handleViewMap = (record) => {
  selectedMapRecord.value = record
  showMapModal.value = true
}

const handleSelectMapRecord = (record) => {
  selectedMapRecord.value = record
}

const handleViewPhotos = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handlePreviewPhoto = (photo, index) => {
  previewPhoto.value = photo
  showPhotoModal.value = true
}

const handlePhotoPreview = (file) => {
  console.log('预览照片:', file)
}

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return false // 阻止自动上传
}

const handleGetLocation = () => {
  locationLoading.value = true
  // 模拟获取位置
  setTimeout(() => {
    executeForm.location = {
      lng: 116.3974 + Math.random() * 0.01,
      lat: 39.9093 + Math.random() * 0.01,
      accuracy: Math.floor(Math.random() * 20) + 5,
      time: new Date().toISOString()
    }
    locationLoading.value = false
    message.success('位置获取成功')
  }, 1500)
}

const handleBatchAction = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  console.log('批量安排:', selectedRowKeys.value)
  message.success(`已选择 ${selectedRowKeys.value.length} 条记录进行批量安排`)
}

const handleExport = () => {
  console.log('导出记录')
  message.success('导出成功')
}

// 组件挂载后初始化
onMounted(() => {
  handleRefresh()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-section {
  margin-bottom: 16px;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.statistics-section {
  margin-bottom: 16px;
}

.action-section {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.table-section {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.status-tag {
  font-weight: 500;
}

.address-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.photos-cell .no-photos {
  color: #8c8c8c;
  font-size: 12px;
}

.visit-process,
.visit-result {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  word-break: break-word;
  line-height: 1.6;
}

.photos-section {
  margin-top: 24px;
}

.photos-section h4 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 600;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.photo-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.photo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.photo-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
}

.photo-desc {
  padding: 6px 8px;
  font-size: 12px;
  color: #666;
  background-color: #fafafa;
  text-align: center;
}

.location-section {
  margin-top: 24px;
}

.location-section h4 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 600;
}

.location-info {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.location-info p {
  margin: 8px 0;
  color: #595959;
}

.map-container {
  display: flex;
  height: 500px;
}

.map-sidebar {
  width: 300px;
  background-color: #fafafa;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.map-sidebar h4 {
  padding: 16px;
  margin: 0;
  color: #262626;
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8;
}

.visit-list {
  padding: 12px;
}

.visit-item {
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s;
}

.visit-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.visit-item.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.visit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.case-number {
  font-weight: 600;
  color: #262626;
}

.visit-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #595959;
}

.visit-info .address {
  color: #8c8c8c;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.visit-info .time {
  color: #1890ff;
}

.map-content {
  flex: 1;
}

.photo-preview {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .search-section :deep(.ant-col) {
    margin-bottom: 16px;
  }
  
  .statistics-section :deep(.ant-col) {
    margin-bottom: 16px;
  }
  
  .action-section {
    padding: 12px;
  }
  
  .action-section :deep(.ant-space) {
    flex-wrap: wrap;
  }

  .map-container {
    flex-direction: column;
    height: auto;
  }

  .map-sidebar {
    width: 100%;
    height: 200px;
  }

  .map-content {
    height: 300px;
  }

  .photo-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

/* 表格优化 */
.table-section :deep(.ant-table) {
  font-size: 13px;
}

.table-section :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  color: #262626;
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8;
}

.table-section :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f0f0f0;
}

.table-section :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 模态框优化 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

:deep(.ant-modal-title) {
  color: #262626;
  font-weight: 600;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #595959;
}

/* 统计卡片优化 */
.statistics-section :deep(.ant-card) {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.statistics-section :deep(.ant-statistic-title) {
  color: #8c8c8c;
  font-size: 13px;
  margin-bottom: 4px;
}

.statistics-section :deep(.ant-statistic-content) {
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

/* 表单优化 */
:deep(.ant-form-item-label > label) {
  color: #262626;
  font-weight: 500;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker),
:deep(.ant-cascader-picker) {
  border-radius: 4px;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused),
:deep(.ant-cascader-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮优化 */
:deep(.ant-btn) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 标签优化 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 上传组件优化 */
:deep(.ant-upload-select-picture-card) {
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

:deep(.ant-upload-select-picture-card:hover) {
  border-color: #1890ff;
}
</style>
