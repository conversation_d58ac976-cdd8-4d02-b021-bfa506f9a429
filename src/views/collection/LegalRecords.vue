<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>法务记录</h1>
      <p>管理法律诉讼、仲裁、执行等法务活动记录，跟踪法务进展和结果</p>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card">
      <a-form :model="searchForm" @submit="handleSearch">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="案件编号">
              <a-input 
                v-model:value="searchForm.caseNumber" 
                placeholder="请输入案件编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户姓名">
              <a-input 
                v-model:value="searchForm.customerName" 
                placeholder="请输入客户姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="法务类型">
              <a-select 
                v-model:value="searchForm.legalType" 
                placeholder="请选择法务类型"
                allow-clear
              >
                <a-select-option value="litigation">民事诉讼</a-select-option>
                <a-select-option value="arbitration">仲裁</a-select-option>
                <a-select-option value="execution">强制执行</a-select-option>
                <a-select-option value="bankruptcy">破产申请</a-select-option>
                <a-select-option value="negotiation">法务谈判</a-select-option>
                <a-select-option value="property_preservation">财产保全</a-select-option>
                <a-select-option value="criminal">刑事报案</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="处理状态">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option value="preparing">准备中</a-select-option>
                <a-select-option value="submitted">已提交</a-select-option>
                <a-select-option value="accepted">已受理</a-select-option>
                <a-select-option value="in_progress">进行中</a-select-option>
                <a-select-option value="judgment">已判决</a-select-option>
                <a-select-option value="executing">执行中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="suspended">已中止</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="法院/机构">
              <a-input 
                v-model:value="searchForm.court" 
                placeholder="请输入法院或仲裁机构"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="律师事务所">
              <a-select 
                v-model:value="searchForm.lawFirm" 
                placeholder="请选择律师事务所"
                allow-clear
              >
                <a-select-option value="firm1">北京XX律师事务所</a-select-option>
                <a-select-option value="firm2">上海XX律师事务所</a-select-option>
                <a-select-option value="firm3">广州XX律师事务所</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="立案时间">
              <a-range-picker 
                v-model:value="searchForm.filingDateRange"
                format="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" html-type="submit">
                  <search-outlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <reload-outlined />
                  重置
                </a-button>
                <a-button 
                  :class="{ 'expand-btn-active': searchExpanded }"
                  @click="searchExpanded = !searchExpanded"
                >
                  {{ searchExpanded ? '收起' : '展开' }}
                  <down-outlined :class="{ 'expand-icon-active': searchExpanded }" />
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
        
        <!-- 展开的搜索条件 -->
        <div v-show="searchExpanded">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="案号">
                <a-input 
                  v-model:value="searchForm.caseCode"
                  placeholder="请输入案号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="诉讼标的">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.amountMin"
                    placeholder="最小金额"
                    :min="0"
                    style="width: 50%"
                  />
                  <a-input-number 
                    v-model:value="searchForm.amountMax"
                    placeholder="最大金额"
                    :min="0"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="承办律师">
                <a-input 
                  v-model:value="searchForm.lawyer"
                  placeholder="请输入承办律师"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="判决结果">
                <a-select 
                  v-model:value="searchForm.result" 
                  placeholder="请选择判决结果"
                  allow-clear
                >
                  <a-select-option value="win">胜诉</a-select-option>
                  <a-select-option value="partial_win">部分胜诉</a-select-option>
                  <a-select-option value="lose">败诉</a-select-option>
                  <a-select-option value="settlement">和解</a-select-option>
                  <a-select-option value="withdrawal">撤诉</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="进行中案件"
              :value="statistics.inProgress"
              suffix="件"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileProtectOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="胜诉率"
              :value="statistics.winRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="法务费用"
              :value="statistics.totalCost"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <AccountBookOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="执行到位金额"
              :value="statistics.recoveredAmount"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <MoneyCollectOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <a-space>
        <a-button type="primary" @click="showCreateModal = true">
          <PlusOutlined />
          新增法务
        </a-button>
        <a-button @click="showBatchModal = true">
          <GroupOutlined />
          批量立案
        </a-button>
        <a-button @click="showTemplateModal = true">
          <FileTextOutlined />
          文书模板
        </a-button>
        <a-button @click="handleExport">
          <DownloadOutlined />
          导出记录
        </a-button>
        <a-button @click="handleRefresh">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag 
              :color="getStatusColor(record.status)"
              class="status-tag"
            >
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'legalType'">
            <a-tag :color="getTypeColor(record.legalType)">
              {{ getTypeText(record.legalType) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'result'">
            <a-tag v-if="record.result" :color="getResultColor(record.result)">
              {{ getResultText(record.result) }}
            </a-tag>
            <span v-else class="pending-text">待判决</span>
          </template>
          
          <template v-if="column.key === 'progress'">
            <a-progress 
              :percent="record.progress" 
              :size="'small'"
              :status="record.progress === 100 ? 'success' : 'active'"
            />
          </template>
          
          <template v-if="column.key === 'documents'">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="handleViewDocuments(record)"
              >
                <FileSearchOutlined />
                {{ record.documentCount || 0 }}份
              </a-button>
            </a-space>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                <EyeOutlined />
                详情
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleProgress(record)">
                <HistoryOutlined />
                进展
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleAddDocument(record)">
                      <UploadOutlined />
                      上传文书
                    </a-menu-item>
                    <a-menu-item @click="handleAddProgress(record)">
                      <PlusCircleOutlined />
                      添加进展
                    </a-menu-item>
                    <a-menu-item @click="handleCostManage(record)">
                      <DollarOutlined />
                      费用管理
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDelete(record)" style="color: #ff4d4f;">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑法务记录模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingRecord ? '编辑法务记录' : '新增法务记录'"
      width="900px"
      @ok="handleCreateSave"
      @cancel="handleCreateCancel"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-tabs v-model:activeKey="formActiveKey">
          <a-tab-pane key="basic" tab="基本信息">
            <a-form-item label="案件编号" required>
              <a-input v-model:value="createForm.caseNumber" placeholder="请输入案件编号" />
            </a-form-item>
            
            <a-form-item label="客户姓名" required>
              <a-input v-model:value="createForm.customerName" placeholder="请输入客户姓名" />
            </a-form-item>
            
            <a-form-item label="法务类型" required>
              <a-select v-model:value="createForm.legalType" placeholder="请选择法务类型">
                <a-select-option value="litigation">民事诉讼</a-select-option>
                <a-select-option value="arbitration">仲裁</a-select-option>
                <a-select-option value="execution">强制执行</a-select-option>
                <a-select-option value="bankruptcy">破产申请</a-select-option>
                <a-select-option value="negotiation">法务谈判</a-select-option>
                <a-select-option value="property_preservation">财产保全</a-select-option>
                <a-select-option value="criminal">刑事报案</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="诉讼标的" required>
              <a-input-number
                v-model:value="createForm.amount"
                placeholder="请输入诉讼标的金额"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="诉讼请求">
              <a-textarea
                v-model:value="createForm.claims"
                :rows="3"
                placeholder="请输入诉讼请求"
              />
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="legal" tab="法务信息">
            <a-form-item label="法院/机构" required>
              <a-input v-model:value="createForm.court" placeholder="请输入法院或仲裁机构名称" />
            </a-form-item>
            
            <a-form-item label="案号">
              <a-input v-model:value="createForm.caseCode" placeholder="请输入案号" />
            </a-form-item>
            
            <a-form-item label="律师事务所">
              <a-select v-model:value="createForm.lawFirm" placeholder="请选择律师事务所">
                <a-select-option value="firm1">北京XX律师事务所</a-select-option>
                <a-select-option value="firm2">上海XX律师事务所</a-select-option>
                <a-select-option value="firm3">广州XX律师事务所</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="承办律师">
              <a-input v-model:value="createForm.lawyer" placeholder="请输入承办律师姓名" />
            </a-form-item>
            
            <a-form-item label="立案时间">
              <a-date-picker
                v-model:value="createForm.filingDate"
                placeholder="选择立案时间"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item label="开庭时间">
              <a-date-picker
                v-model:value="createForm.trialDate"
                show-time
                placeholder="选择开庭时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="evidence" tab="证据材料">
            <a-form-item label="证据清单">
              <div class="evidence-list">
                <div 
                  v-for="(evidence, index) in createForm.evidences" 
                  :key="index"
                  class="evidence-item"
                >
                  <a-input 
                    v-model:value="evidence.name" 
                    placeholder="证据名称"
                    style="width: 60%; margin-right: 8px;"
                  />
                  <a-select 
                    v-model:value="evidence.type" 
                    placeholder="证据类型"
                    style="width: 30%; margin-right: 8px;"
                  >
                    <a-select-option value="contract">合同</a-select-option>
                    <a-select-option value="iou">借条</a-select-option>
                    <a-select-option value="transfer">转账记录</a-select-option>
                    <a-select-option value="communication">通信记录</a-select-option>
                    <a-select-option value="other">其他</a-select-option>
                  </a-select>
                  <a-button 
                    type="link" 
                    danger 
                    @click="removeEvidence(index)"
                  >
                    删除
                  </a-button>
                </div>
                <a-button 
                  type="dashed" 
                  @click="addEvidence"
                  style="width: 100%; margin-top: 8px;"
                >
                  <PlusOutlined />
                  添加证据
                </a-button>
              </div>
            </a-form-item>
            
            <a-form-item label="文件上传">
              <a-upload
                v-model:file-list="createForm.files"
                :before-upload="beforeUpload"
                multiple
              >
                <a-button>
                  <UploadOutlined />
                  上传证据文件
                </a-button>
              </a-upload>
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="cost" tab="费用信息">
            <a-form-item label="诉讼费">
              <a-input-number
                v-model:value="createForm.litigationFee"
                placeholder="请输入诉讼费"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="律师费">
              <a-input-number
                v-model:value="createForm.lawyerFee"
                placeholder="请输入律师费"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="其他费用">
              <a-input-number
                v-model:value="createForm.otherFee"
                placeholder="请输入其他费用"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="费用说明">
              <a-textarea
                v-model:value="createForm.costRemarks"
                :rows="3"
                placeholder="请输入费用说明"
              />
            </a-form-item>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-modal>

    <!-- 法务详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="法务详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="案件编号">{{ selectedRecord.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedRecord.customerName }}</a-descriptions-item>
          <a-descriptions-item label="法务类型">
            <a-tag :color="getTypeColor(selectedRecord.legalType)">
              {{ getTypeText(selectedRecord.legalType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="处理状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="诉讼标的">¥{{ selectedRecord.amount }}</a-descriptions-item>
          <a-descriptions-item label="进度">
            <a-progress :percent="selectedRecord.progress" />
          </a-descriptions-item>
          <a-descriptions-item label="法院/机构" :span="2">{{ selectedRecord.court }}</a-descriptions-item>
          <a-descriptions-item label="案号">{{ selectedRecord.caseCode || '待分配' }}</a-descriptions-item>
          <a-descriptions-item label="律师事务所">{{ selectedRecord.lawFirmName }}</a-descriptions-item>
          <a-descriptions-item label="承办律师">{{ selectedRecord.lawyer }}</a-descriptions-item>
          <a-descriptions-item label="立案时间">{{ selectedRecord.filingDate }}</a-descriptions-item>
          <a-descriptions-item label="开庭时间">{{ selectedRecord.trialDate || '待定' }}</a-descriptions-item>
          <a-descriptions-item label="判决时间">{{ selectedRecord.judgmentDate || '待判决' }}</a-descriptions-item>
          <a-descriptions-item label="判决结果">
            <a-tag v-if="selectedRecord.result" :color="getResultColor(selectedRecord.result)">
              {{ getResultText(selectedRecord.result) }}
            </a-tag>
            <span v-else>待判决</span>
          </a-descriptions-item>
          <a-descriptions-item label="诉讼请求" :span="3">
            <div class="claims-content">{{ selectedRecord.claims }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="判决内容" :span="3">
            <div class="judgment-content">{{ selectedRecord.judgmentContent || '暂无' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="费用合计">¥{{ selectedRecord.totalCost }}</a-descriptions-item>
          <a-descriptions-item label="执行到位">¥{{ selectedRecord.recoveredAmount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ selectedRecord.createTime }}</a-descriptions-item>
        </a-descriptions>
        
        <!-- 进展时间线 -->
        <a-card title="法务进展" style="margin-top: 16px;">
          <a-timeline>
            <a-timeline-item 
              v-for="(progress, index) in selectedRecord.progressList" 
              :key="index"
              :color="progress.important ? 'red' : 'blue'"
            >
              <template #dot v-if="progress.important">
                <ClockCircleOutlined style="font-size: 16px;" />
              </template>
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="timeline-title">{{ progress.title }}</span>
                  <span class="timeline-time">{{ progress.time }}</span>
                </div>
                <div class="timeline-body">{{ progress.content }}</div>
                <div v-if="progress.attachments" class="timeline-attachments">
                  <a-tag 
                    v-for="(file, idx) in progress.attachments" 
                    :key="idx"
                    color="blue"
                    style="cursor: pointer;"
                  >
                    <PaperClipOutlined />
                    {{ file.name }}
                  </a-tag>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </div>
    </a-modal>

    <!-- 法务进展模态框 -->
    <a-modal
      v-model:open="showProgressModal"
      title="法务进展管理"
      width="1000px"
      :footer="null"
    >
      <div class="progress-header">
        <h3>案件编号：{{ selectedRecord?.caseNumber }}</h3>
        <a-button type="primary" @click="showAddProgressModal = true">
          <PlusOutlined />
          添加进展
        </a-button>
      </div>
      
      <a-table
        :columns="progressColumns"
        :data-source="progressData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'important'">
            <a-tag v-if="record.important" color="red">重要</a-tag>
            <a-tag v-else color="default">普通</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEditProgress(record)">编辑</a-button>
              <a-popconfirm title="确定删除？" @confirm="handleDeleteProgress(record)">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 文书模板管理模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="法律文书模板"
      width="1200px"
      @ok="handleTemplateSave"
    >
      <a-tabs v-model:activeKey="templateActiveKey">
        <a-tab-pane key="list" tab="模板列表">
          <div class="template-actions">
            <a-input-search
              v-model:value="templateSearch"
              placeholder="搜索模板"
              style="width: 300px; margin-right: 16px;"
            />
            <a-button type="primary" @click="showAddTemplate = true">
              <PlusOutlined />
              新增模板
            </a-button>
          </div>
          
          <a-row :gutter="[16, 16]" style="margin-top: 16px;">
            <a-col 
              v-for="template in filteredTemplates" 
              :key="template.id"
              :span="6"
            >
              <a-card 
                hoverable
                class="template-card"
                @click="handleSelectTemplate(template)"
              >
                <template #cover>
                  <div class="template-icon">
                    <FileTextOutlined style="font-size: 48px; color: #1890ff;" />
                  </div>
                </template>
                <a-card-meta
                  :title="template.name"
                  :description="template.description"
                />
                <template #actions>
                  <EditOutlined key="edit" @click.stop="handleEditTemplate(template)" />
                  <DownloadOutlined key="download" @click.stop="handleDownloadTemplate(template)" />
                  <DeleteOutlined key="delete" @click.stop="handleDeleteTemplate(template)" />
                </template>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="generate" tab="文书生成">
          <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <a-form-item label="选择模板">
              <a-select 
                v-model:value="generateForm.templateId" 
                placeholder="请选择文书模板"
                @change="handleTemplateSelect"
              >
                <a-select-option 
                  v-for="template in templateList" 
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="案件信息">
              <a-input 
                v-model:value="generateForm.caseNumber" 
                placeholder="请输入案件编号"
                @blur="loadCaseInfo"
              />
            </a-form-item>
            
            <a-form-item label="填充字段">
              <div class="field-list">
                <div 
                  v-for="field in templateFields" 
                  :key="field.key"
                  class="field-item"
                >
                  <span class="field-label">{{ field.label }}:</span>
                  <a-input 
                    v-model:value="generateForm.fields[field.key]"
                    :placeholder="field.placeholder"
                  />
                </div>
              </div>
            </a-form-item>
            
            <a-form-item label="预览" v-if="documentPreview">
              <div class="document-preview">
                <div v-html="documentPreview"></div>
              </div>
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 4 }">
              <a-space>
                <a-button type="primary" @click="handleGenerateDocument">
                  生成文书
                </a-button>
                <a-button @click="handlePreviewDocument">
                  预览
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 文档管理模态框 -->
    <a-modal
      v-model:open="showDocumentModal"
      title="文档管理"
      width="1000px"
      :footer="null"
    >
      <div class="document-header">
        <h3>案件编号：{{ selectedRecord?.caseNumber }}</h3>
        <a-upload
          :before-upload="beforeDocumentUpload"
          :showUploadList="false"
        >
          <a-button type="primary">
            <UploadOutlined />
            上传文档
          </a-button>
        </a-upload>
      </div>
      
      <a-table
        :columns="documentColumns"
        :data-source="documentData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getDocumentTypeColor(record.type)">
              {{ getDocumentTypeText(record.type) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'size'">
            {{ formatFileSize(record.size) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handlePreviewDocument(record)">
                <EyeOutlined />
                预览
              </a-button>
              <a-button type="link" size="small" @click="handleDownloadDocument(record)">
                <DownloadOutlined />
                下载
              </a-button>
              <a-popconfirm title="确定删除？" @confirm="handleDeleteDocument(record)">
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 费用管理模态框 -->
    <a-modal
      v-model:open="showCostModal"
      title="费用管理"
      width="800px"
      @ok="handleCostSave"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="费用明细">
          <a-table
            :columns="costColumns"
            :data-source="costData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'type'">
                <a-select 
                  v-model:value="record.type" 
                  style="width: 100%"
                >
                  <a-select-option value="litigation">诉讼费</a-select-option>
                  <a-select-option value="lawyer">律师费</a-select-option>
                  <a-select-option value="preservation">保全费</a-select-option>
                  <a-select-option value="appraisal">鉴定费</a-select-option>
                  <a-select-option value="execution">执行费</a-select-option>
                  <a-select-option value="other">其他费用</a-select-option>
                </a-select>
              </template>
              <template v-if="column.key === 'amount'">
                <a-input-number
                  v-model:value="record.amount"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </template>
              <template v-if="column.key === 'payTime'">
                <a-date-picker
                  v-model:value="record.payTime"
                  style="width: 100%"
                />
              </template>
              <template v-if="column.key === 'remarks'">
                <a-input v-model:value="record.remarks" />
              </template>
              <template v-if="column.key === 'action'">
                <a-button 
                  type="link" 
                  danger 
                  size="small"
                  @click="removeCostItem(index)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 4 }">
          <a-button type="dashed" @click="addCostItem">
            <PlusOutlined />
            添加费用项
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  PlusOutlined,
  GroupOutlined,
  FileTextOutlined,
  DownloadOutlined,
  FileProtectOutlined,
  TrophyOutlined,
  AccountBookOutlined,
  MoneyCollectOutlined,
  EyeOutlined,
  EditOutlined,
  HistoryOutlined,
  UploadOutlined,
  PlusCircleOutlined,
  DollarOutlined,
  DeleteOutlined,
  FileSearchOutlined,
  ClockCircleOutlined,
  PaperClipOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)

// 模态框显示状态
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showProgressModal = ref(false)
const showTemplateModal = ref(false)
const showDocumentModal = ref(false)
const showCostModal = ref(false)
const showBatchModal = ref(false)
const showAddProgressModal = ref(false)
const showAddTemplate = ref(false)

// 编辑状态
const editingRecord = ref(null)
const selectedRecord = ref(null)

// Tab激活键
const formActiveKey = ref('basic')
const templateActiveKey = ref('list')

// 表单数据
const searchForm = reactive({
  caseNumber: '',
  customerName: '',
  legalType: undefined,
  status: undefined,
  court: '',
  lawFirm: undefined,
  filingDateRange: [],
  caseCode: '',
  amountMin: undefined,
  amountMax: undefined,
  lawyer: '',
  result: undefined
})

const createForm = reactive({
  caseNumber: '',
  customerName: '',
  legalType: '',
  amount: 0,
  claims: '',
  court: '',
  caseCode: '',
  lawFirm: '',
  lawyer: '',
  filingDate: null,
  trialDate: null,
  evidences: [],
  files: [],
  litigationFee: 0,
  lawyerFee: 0,
  otherFee: 0,
  costRemarks: ''
})

// 模板管理
const templateSearch = ref('')
const generateForm = reactive({
  templateId: '',
  caseNumber: '',
  fields: {}
})
const documentPreview = ref('')
const templateFields = ref([])

// 统计数据
const statistics = reactive({
  inProgress: 28,
  winRate: 78.5,
  totalCost: 285600,
  recoveredAmount: 1586900
})

// 表格列定义
const columns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    sorter: true
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '法务类型',
    dataIndex: 'legalType',
    key: 'legalType',
    width: 100
  },
  {
    title: '诉讼标的',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    sorter: true,
    customRender: ({ text }) => `¥${text.toLocaleString()}`
  },
  {
    title: '处理状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '准备中', value: 'preparing' },
      { text: '已提交', value: 'submitted' },
      { text: '已受理', value: 'accepted' },
      { text: '进行中', value: 'in_progress' },
      { text: '已判决', value: 'judgment' },
      { text: '执行中', value: 'executing' },
      { text: '已完成', value: 'completed' },
      { text: '已中止', value: 'suspended' }
    ]
  },
  {
    title: '进度',
    key: 'progress',
    width: 120
  },
  {
    title: '法院/机构',
    dataIndex: 'court',
    key: 'court',
    width: 150,
    ellipsis: true
  },
  {
    title: '承办律师',
    dataIndex: 'lawyer',
    key: 'lawyer',
    width: 100
  },
  {
    title: '立案时间',
    dataIndex: 'filingDate',
    key: 'filingDate',
    width: 110,
    sorter: true
  },
  {
    title: '判决结果',
    dataIndex: 'result',
    key: 'result',
    width: 100
  },
  {
    title: '文档',
    key: 'documents',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 220,
    fixed: 'right'
  }
]

// 进展表格列
const progressColumns = [
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
    width: 150
  },
  {
    title: '进展标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '进展内容',
    dataIndex: 'content',
    key: 'content'
  },
  {
    title: '重要性',
    dataIndex: 'important',
    key: 'important',
    width: 80
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
]

// 文档表格列
const documentColumns = [
  {
    title: '文档名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '文档类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    key: 'size',
    width: 100
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    key: 'uploadTime',
    width: 150
  },
  {
    title: '上传人',
    dataIndex: 'uploader',
    key: 'uploader',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 费用表格列
const costColumns = [
  {
    title: '费用类型',
    dataIndex: 'type',
    key: 'type',
    width: 150
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 150
  },
  {
    title: '支付时间',
    dataIndex: 'payTime',
    key: 'payTime',
    width: 150
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    key: 'remarks'
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 表格数据
const tableData = ref([
  {
    key: '1',
    caseNumber: 'CS202401001',
    customerName: '张三',
    legalType: 'litigation',
    amount: 50000,
    status: 'in_progress',
    progress: 60,
    court: '北京市朝阳区人民法院',
    caseCode: '(2024)京0105民初123号',
    lawyer: '李律师',
    lawFirmName: '北京XX律师事务所',
    filingDate: '2024-01-10',
    trialDate: '2024-02-15 09:30:00',
    result: null,
    documentCount: 12,
    totalCost: 8500,
    recoveredAmount: 0,
    claims: '1.要求被告偿还借款本金50000元；2.支付逾期利息5000元；3.承担诉讼费用。',
    createTime: '2024-01-08 10:30:00',
    progressList: [
      {
        title: '案件立案',
        time: '2024-01-10 14:30:00',
        content: '案件已在北京市朝阳区人民法院成功立案，案号：(2024)京0105民初123号',
        important: true,
        attachments: [
          { name: '立案通知书.pdf' },
          { name: '缴费凭证.pdf' }
        ]
      },
      {
        title: '提交证据',
        time: '2024-01-15 10:00:00',
        content: '已向法院提交借款合同、转账记录等证据材料',
        important: false
      },
      {
        title: '开庭通知',
        time: '2024-01-25 16:00:00',
        content: '收到法院开庭通知，开庭时间：2024年2月15日上午9:30',
        important: true
      }
    ]
  },
  {
    key: '2',
    caseNumber: 'CS202401002',
    customerName: '李四',
    legalType: 'execution',
    amount: 120000,
    status: 'executing',
    progress: 80,
    court: '上海市浦东新区人民法院',
    caseCode: '(2024)沪0115执123号',
    lawyer: '王律师',
    lawFirmName: '上海XX律师事务所',
    filingDate: '2024-01-05',
    result: 'win',
    judgmentDate: '2023-12-20',
    judgmentContent: '判决被告于判决生效之日起十日内偿还原告借款本金120000元及利息。',
    documentCount: 8,
    totalCost: 15000,
    recoveredAmount: 45000,
    claims: '申请强制执行判决书确定的债权120000元及利息。',
    createTime: '2024-01-03 09:00:00'
  },
  {
    key: '3',
    caseNumber: 'CS202401003',
    customerName: '王五',
    legalType: 'arbitration',
    amount: 80000,
    status: 'completed',
    progress: 100,
    court: '北京仲裁委员会',
    caseCode: '京仲案字[2024]第0123号',
    lawyer: '赵律师',
    lawFirmName: '北京XX律师事务所',
    filingDate: '2023-11-15',
    result: 'win',
    judgmentDate: '2024-01-10',
    documentCount: 15,
    totalCost: 12000,
    recoveredAmount: 80000,
    claims: '1.裁决被申请人支付申请人借款本金80000元；2.支付逾期利息8000元。',
    createTime: '2023-11-10 15:20:00'
  }
])

// 进展数据
const progressData = ref([
  {
    key: '1',
    time: '2024-01-25 16:00:00',
    title: '开庭通知',
    content: '收到法院开庭通知，开庭时间：2024年2月15日上午9:30',
    important: true,
    operator: '系统'
  },
  {
    key: '2',
    time: '2024-01-15 10:00:00',
    title: '提交证据',
    content: '已向法院提交借款合同、转账记录等证据材料',
    important: false,
    operator: '李律师'
  }
])

// 文档数据
const documentData = ref([
  {
    key: '1',
    name: '民事起诉状.docx',
    type: 'litigation',
    size: 45678,
    uploadTime: '2024-01-08 14:30:00',
    uploader: '李律师'
  },
  {
    key: '2',
    name: '立案通知书.pdf',
    type: 'court',
    size: 123456,
    uploadTime: '2024-01-10 16:00:00',
    uploader: '系统'
  },
  {
    key: '3',
    name: '证据清单.xlsx',
    type: 'evidence',
    size: 34567,
    uploadTime: '2024-01-12 10:00:00',
    uploader: '李律师'
  }
])

// 费用数据
const costData = ref([
  {
    key: '1',
    type: 'litigation',
    amount: 1050,
    payTime: '2024-01-10',
    remarks: '案件受理费'
  },
  {
    key: '2',
    type: 'lawyer',
    amount: 5000,
    payTime: '2024-01-08',
    remarks: '律师费首期款'
  },
  {
    key: '3',
    type: 'preservation',
    amount: 2450,
    payTime: '2024-01-12',
    remarks: '财产保全费'
  }
])

// 模板数据
const templateList = ref([
  {
    id: '1',
    name: '民事起诉状',
    description: '标准民事诉讼起诉状模板',
    type: 'litigation'
  },
  {
    id: '2',
    name: '强制执行申请书',
    description: '申请强制执行的标准模板',
    type: 'execution'
  },
  {
    id: '3',
    name: '仲裁申请书',
    description: '仲裁申请的标准模板',
    type: 'arbitration'
  },
  {
    id: '4',
    name: '财产保全申请书',
    description: '申请财产保全的标准模板',
    type: 'preservation'
  },
  {
    id: '5',
    name: '律师函',
    description: '催收律师函模板',
    type: 'lawyer_letter'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const filteredTemplates = computed(() => {
  if (!templateSearch.value) {
    return templateList.value
  }
  return templateList.value.filter(
    template => 
      template.name.includes(templateSearch.value) || 
      template.description.includes(templateSearch.value)
  )
})

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    preparing: 'blue',
    submitted: 'cyan',
    accepted: 'purple',
    in_progress: 'orange',
    judgment: 'green',
    executing: 'processing',
    completed: 'success',
    suspended: 'default'
  }
  return colorMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    preparing: '准备中',
    submitted: '已提交',
    accepted: '已受理',
    in_progress: '进行中',
    judgment: '已判决',
    executing: '执行中',
    completed: '已完成',
    suspended: '已中止'
  }
  return textMap[status] || status
}

// 类型颜色映射
const getTypeColor = (type) => {
  const colorMap = {
    litigation: 'blue',
    arbitration: 'green',
    execution: 'orange',
    bankruptcy: 'red',
    negotiation: 'purple',
    property_preservation: 'cyan',
    criminal: 'magenta'
  }
  return colorMap[type] || 'default'
}

// 类型文本映射
const getTypeText = (type) => {
  const textMap = {
    litigation: '民事诉讼',
    arbitration: '仲裁',
    execution: '强制执行',
    bankruptcy: '破产申请',
    negotiation: '法务谈判',
    property_preservation: '财产保全',
    criminal: '刑事报案'
  }
  return textMap[type] || type
}

// 结果颜色映射
const getResultColor = (result) => {
  const colorMap = {
    win: 'green',
    partial_win: 'blue',
    lose: 'red',
    settlement: 'orange',
    withdrawal: 'gray'
  }
  return colorMap[result] || 'default'
}

// 结果文本映射
const getResultText = (result) => {
  const textMap = {
    win: '胜诉',
    partial_win: '部分胜诉',
    lose: '败诉',
    settlement: '和解',
    withdrawal: '撤诉'
  }
  return textMap[result] || result
}

// 文档类型颜色映射
const getDocumentTypeColor = (type) => {
  const colorMap = {
    litigation: 'blue',
    court: 'green',
    evidence: 'orange',
    judgment: 'red',
    execution: 'purple'
  }
  return colorMap[type] || 'default'
}

// 文档类型文本映射
const getDocumentTypeText = (type) => {
  const textMap = {
    litigation: '诉讼文书',
    court: '法院文书',
    evidence: '证据材料',
    judgment: '判决文书',
    execution: '执行文书'
  }
  return textMap[type] || type
}

// 文件大小格式化
const formatFileSize = (size) => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + 'KB'
  return (size / 1024 / 1024).toFixed(2) + 'MB'
}

// 事件处理函数
const handleSearch = () => {
  loading.value = true
  console.log('搜索条件:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  message.success('已重置搜索条件')
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 800)
}

const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变化:', pag, filters, sorter)
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleView = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleEdit = (record) => {
  editingRecord.value = record
  Object.assign(createForm, {
    caseNumber: record.caseNumber,
    customerName: record.customerName,
    legalType: record.legalType,
    amount: record.amount,
    claims: record.claims,
    court: record.court,
    caseCode: record.caseCode,
    lawFirm: record.lawFirm,
    lawyer: record.lawyer,
    filingDate: record.filingDate,
    trialDate: record.trialDate,
    evidences: [],
    files: [],
    litigationFee: record.litigationFee || 0,
    lawyerFee: record.lawyerFee || 0,
    otherFee: record.otherFee || 0,
    costRemarks: record.costRemarks || ''
  })
  showCreateModal.value = true
}

const handleProgress = (record) => {
  selectedRecord.value = record
  showProgressModal.value = true
}

const handleAddDocument = (record) => {
  selectedRecord.value = record
  showDocumentModal.value = true
}

const handleAddProgress = (record) => {
  selectedRecord.value = record
  showProgressModal.value = true
}

const handleCostManage = (record) => {
  selectedRecord.value = record
  showCostModal.value = true
}

const handleDelete = (record) => {
  console.log('删除记录:', record)
  message.success('记录已删除')
}

const handleCreateSave = () => {
  console.log('保存法务记录:', createForm)
  message.success('法务记录已保存')
  showCreateModal.value = false
  handleCreateCancel()
}

const handleCreateCancel = () => {
  editingRecord.value = null
  Object.assign(createForm, {
    caseNumber: '',
    customerName: '',
    legalType: '',
    amount: 0,
    claims: '',
    court: '',
    caseCode: '',
    lawFirm: '',
    lawyer: '',
    filingDate: null,
    trialDate: null,
    evidences: [],
    files: [],
    litigationFee: 0,
    lawyerFee: 0,
    otherFee: 0,
    costRemarks: ''
  })
  formActiveKey.value = 'basic'
}

const handleViewDocuments = (record) => {
  selectedRecord.value = record
  showDocumentModal.value = true
}

// 证据管理
const addEvidence = () => {
  createForm.evidences.push({
    name: '',
    type: ''
  })
}

const removeEvidence = (index) => {
  createForm.evidences.splice(index, 1)
}

// 文件上传
const beforeUpload = (file) => {
  createForm.files.push(file)
  return false
}

const beforeDocumentUpload = (file) => {
  console.log('上传文档:', file)
  message.success('文档上传成功')
  return false
}

// 费用管理
const addCostItem = () => {
  costData.value.push({
    key: Date.now().toString(),
    type: '',
    amount: 0,
    payTime: null,
    remarks: ''
  })
}

const removeCostItem = (index) => {
  costData.value.splice(index, 1)
}

const totalCost = computed(() => {
  return costData.value.reduce((sum, item) => sum + (item.amount || 0), 0)
})

const handleCostSave = () => {
  console.log('保存费用信息:', costData.value)
  message.success('费用信息已保存')
  showCostModal.value = false
}

// 模板管理
const handleTemplateSave = () => {
  message.success('模板设置已保存')
  showTemplateModal.value = false
}

const handleSelectTemplate = (template) => {
  generateForm.templateId = template.id
  message.info(`已选择模板：${template.name}`)
}

const handleEditTemplate = (template) => {
  console.log('编辑模板:', template)
  message.info('编辑模板功能')
}

const handleDownloadTemplate = (template) => {
  console.log('下载模板:', template)
  message.success('模板下载成功')
}

const handleDeleteTemplate = (template) => {
  console.log('删除模板:', template)
  message.success('模板已删除')
}

const handleTemplateSelect = (value) => {
  // 根据模板加载字段
  templateFields.value = [
    { key: 'plaintiff', label: '原告', placeholder: '请输入原告名称' },
    { key: 'defendant', label: '被告', placeholder: '请输入被告名称' },
    { key: 'amount', label: '标的金额', placeholder: '请输入标的金额' },
    { key: 'court', label: '法院', placeholder: '请输入法院名称' }
  ]
}

const loadCaseInfo = () => {
  // 加载案件信息自动填充
  console.log('加载案件信息:', generateForm.caseNumber)
}

const handleGenerateDocument = () => {
  console.log('生成文书:', generateForm)
  message.success('文书生成成功')
}

const handlePreviewDocument = () => {
  documentPreview.value = `
    <h3>民事起诉状</h3>
    <p><strong>原告：</strong>${generateForm.fields.plaintiff || '[原告]'}</p>
    <p><strong>被告：</strong>${generateForm.fields.defendant || '[被告]'}</p>
    <p><strong>诉讼请求：</strong></p>
    <p>1. 判令被告偿还原告借款本金${generateForm.fields.amount || '[金额]'}元；</p>
    <p>2. 判令被告支付逾期利息；</p>
    <p>3. 本案诉讼费用由被告承担。</p>
  `
}

const handleDownloadDocument = (record) => {
  console.log('下载文档:', record)
  message.success('文档下载成功')
}

const handleDeleteDocument = (record) => {
  console.log('删除文档:', record)
  message.success('文档已删除')
}

// 进展管理
const handleEditProgress = (record) => {
  console.log('编辑进展:', record)
  message.info('编辑进展功能')
}

const handleDeleteProgress = (record) => {
  console.log('删除进展:', record)
  message.success('进展已删除')
}

const handleExport = () => {
  console.log('导出记录')
  message.success('导出成功')
}

// 组件挂载后初始化
onMounted(() => {
  handleRefresh()
})
</script>

<style scoped>
.legal-records {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

/* 搜索区域样式 */
.search-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.search-form {
  margin-bottom: 16px;
}

.btn-expand {
  margin-top: 16px;
}

/* 按钮组样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}

.stat-icon.blue { background: #e6f7ff; color: #1890ff; }
.stat-icon.green { background: #f6ffed; color: #52c41a; }
.stat-icon.orange { background: #fff7e6; color: #fa8c16; }
.stat-icon.red { background: #fff1f0; color: #f5222d; }
.stat-icon.purple { background: #f9f0ff; color: #722ed1; }
.stat-icon.cyan { background: #e6fffb; color: #13c2c2; }

.stat-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.stat-unit {
  font-size: 14px;
  color: #666;
  font-weight: normal;
  margin-left: 4px;
}

.stat-comparison {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
}

.stat-comparison.up { color: #52c41a; }
.stat-comparison.down { color: #f5222d; }

/* 数据表格区域样式 */
.data-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 标签样式 */
.tabs-container {
  margin-bottom: 16px;
}

/* 立案弹窗样式 */
.create-form {
  max-height: 600px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

/* 文件上传区域样式 */
.upload-area {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 8px;
}

.upload-text {
  color: #666;
  font-size: 14px;
}

.upload-hint {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

/* 表单项样式 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 费用汇总样式 */
.cost-summary {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-top: 16px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cost-label {
  color: #666;
  font-size: 14px;
}

.cost-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

/* 进度时间线样式 */
.progress-timeline {
  padding: 0 24px;
}

.timeline-item {
  position: relative;
  padding-left: 32px;
  padding-bottom: 24px;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: 7px;
  top: 24px;
  bottom: 0;
  width: 2px;
  background: #f0f0f0;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #1890ff;
  background: white;
}

.timeline-content {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #262626;
}

.timeline-date {
  font-size: 12px;
  color: #999;
}

.timeline-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.timeline-files {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.file-tag {
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

/* 文档列表样式 */
.document-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.document-card {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.document-card:hover {
  background: #e6f7ff;
  transform: translateY(-2px);
}

.document-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 8px;
}

.document-name {
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.document-info {
  font-size: 12px;
  color: #999;
}

/* 成本明细样式 */
.cost-detail-card {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 12px;
}

.cost-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cost-type {
  font-weight: 600;
  color: #262626;
}

.cost-amount {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.cost-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.cost-desc {
  color: #666;
  font-size: 14px;
  margin-top: 8px;
}

/* 状态标签样式 */
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-tag.filing { background: #e6f7ff; color: #1890ff; }
.status-tag.accepted { background: #f6ffed; color: #52c41a; }
.status-tag.trial { background: #fff7e6; color: #fa8c16; }
.status-tag.judgment { background: #f9f0ff; color: #722ed1; }
.status-tag.execution { background: #e6fffb; color: #13c2c2; }
.status-tag.closed { background: #f5f5f5; color: #666; }

/* 类型标签样式 */
.type-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.type-tag.civil { background: #e6f7ff; color: #1890ff; }
.type-tag.criminal { background: #fff1f0; color: #f5222d; }
.type-tag.administrative { background: #fff7e6; color: #fa8c16; }
.type-tag.arbitration { background: #f9f0ff; color: #722ed1; }

/* 模板卡片样式 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.template-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: #1890ff;
  background: #e6f7ff;
}

.template-icon {
  width: 48px;
  height: 48px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #1890ff;
  margin-bottom: 12px;
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons > * {
    width: 100%;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .document-list {
    grid-template-columns: 1fr;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 64px;
  color: #bbb;
  margin-bottom: 16px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}
</style>
