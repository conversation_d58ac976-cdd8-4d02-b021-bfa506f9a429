<template>
  <div class="mobile-workspace">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="workspace-info">
          <h2>移动工作台</h2>
          <p class="workspace-desc">随时随地高效办公，移动催收管理助手</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshWorkspace">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="syncData">
            <template #icon><SyncOutlined /></template>
            同步数据
          </a-button>
          <a-button type="primary" @click="showQuickActionModal = true">
            <template #icon><PlusOutlined /></template>
            快速操作
          </a-button>
        </div>
      </div>
    </div>

    <!-- 工作台统计 -->
    <div class="workspace-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日任务" 
              :value="workspaceStats.todayTasks" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><CalendarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="待处理" 
              :value="workspaceStats.pendingTasks" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="已完成" 
              :value="workspaceStats.completedTasks" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="消息通知" 
              :value="workspaceStats.notifications" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><BellOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 快速操作 -->
        <a-card title="快速操作" class="workspace-card">
          <div class="quick-actions">
            <a-row :gutter="16">
              <a-col :span="6" v-for="action in quickActions" :key="action.id">
                <div class="action-item" @click="handleQuickAction(action)">
                  <div class="action-icon" :style="{ backgroundColor: action.color }">
                    <component :is="action.icon" />
                  </div>
                  <div class="action-text">{{ action.name }}</div>
                  <div class="action-desc">{{ action.description }}</div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 任务列表 -->
        <a-card title="我的任务" class="workspace-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedTaskFilter" style="width: 120px" @change="filterTasks">
                <a-select-option value="all">全部任务</a-select-option>
                <a-select-option value="today">今日任务</a-select-option>
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="overdue">已逾期</a-select-option>
              </a-select>
              <a-button size="small" @click="addTask">
                <template #icon><PlusOutlined /></template>
                新建任务
              </a-button>
            </a-space>
          </template>
          
          <div class="task-list">
            <div v-for="task in filteredTasks" :key="task.id" class="task-item">
              <div class="task-content">
                <div class="task-header">
                  <a-checkbox 
                    v-model:checked="task.completed" 
                    @change="updateTaskStatus(task)"
                  />
                  <span class="task-title" :class="{ 'completed': task.completed }">
                    {{ task.title }}
                  </span>
                  <a-tag :color="getPriorityColor(task.priority)">
                    {{ getPriorityText(task.priority) }}
                  </a-tag>
                </div>
                <div class="task-description">{{ task.description }}</div>
                <div class="task-meta">
                  <span class="task-type">
                    <component :is="getTaskIcon(task.type)" style="margin-right: 4px;" />
                    {{ task.type }}
                  </span>
                  <span class="task-time">{{ task.dueTime }}</span>
                  <span class="task-customer">{{ task.customerName }}</span>
                </div>
              </div>
              <div class="task-actions">
                <a-button type="link" size="small" @click="executeTask(task)">
                  执行
                </a-button>
                <a-button type="link" size="small" @click="editTask(task)">
                  编辑
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="postponeTask(task)">延期</a-menu-item>
                      <a-menu-item @click="assignTask(task)">分配</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="deleteTask(task)" danger>删除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
            <a-empty v-if="filteredTasks.length === 0" description="暂无任务" />
          </div>
        </a-card>

        <!-- 数据面板 -->
        <a-card title="数据面板" class="workspace-card">
          <div class="data-dashboard">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="taskTrendChart" class="chart-container-small"></div>
              </a-col>
              <a-col :span="12">
                <div ref="workloadChart" class="chart-container-small"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 今日日程 -->
        <a-card title="今日日程" class="workspace-card">
          <template #extra>
            <a-button size="small" @click="showScheduleModal = true">
              <template #icon><PlusOutlined /></template>
              添加
            </a-button>
          </template>
          <div class="schedule-list">
            <div v-for="schedule in todaySchedules" :key="schedule.id" class="schedule-item">
              <div class="schedule-time">{{ schedule.time }}</div>
              <div class="schedule-content">
                <div class="schedule-title">{{ schedule.title }}</div>
                <div class="schedule-desc">{{ schedule.description }}</div>
                <div class="schedule-location" v-if="schedule.location">
                  <EnvironmentOutlined style="margin-right: 4px;" />
                  {{ schedule.location }}
                </div>
              </div>
              <div class="schedule-status">
                <a-tag :color="getScheduleStatusColor(schedule.status)">
                  {{ getScheduleStatusText(schedule.status) }}
                </a-tag>
              </div>
            </div>
            <a-empty v-if="todaySchedules.length === 0" description="今日无日程" size="small" />
          </div>
        </a-card>

        <!-- 消息通知 -->
        <a-card title="消息通知" class="workspace-card">
          <template #extra>
            <a-badge :count="notifications.filter(n => !n.read).length" />
          </template>
          <div class="notification-list">
            <div v-for="notification in notifications" :key="notification.id" class="notification-item" :class="{ 'unread': !notification.read }">
              <div class="notification-header">
                <div class="notification-type" :class="getNotificationClass(notification.type)">
                  {{ getNotificationTypeText(notification.type) }}
                </div>
                <div class="notification-time">{{ notification.time }}</div>
              </div>
              <div class="notification-content">{{ notification.content }}</div>
              <div class="notification-actions">
                <a-button v-if="!notification.read" type="link" size="small" @click="markAsRead(notification)">
                  标记已读
                </a-button>
                <a-button type="link" size="small" @click="viewNotification(notification)">
                  查看详情
                </a-button>
              </div>
            </div>
            <a-empty v-if="notifications.length === 0" description="暂无通知" size="small" />
          </div>
        </a-card>

        <!-- 设备状态 -->
        <a-card title="设备状态" class="workspace-card">
          <div class="device-status">
            <div class="status-item">
              <div class="status-icon online">
                <WifiOutlined />
              </div>
              <div class="status-content">
                <div class="status-label">网络状态</div>
                <div class="status-value">已连接 (WiFi)</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon">
                <ThunderboltOutlined style="color:#52c41a" />
              </div>
              <div class="status-content">
                <div class="status-label">电池电量</div>
                <div class="status-value">85%</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon">
                <EnvironmentOutlined style="color:#1890ff" />
              </div>
              <div class="status-content">
                <div class="status-label">GPS定位</div>
                <div class="status-value">已开启</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon">
                <CloudSyncOutlined />
              </div>
              <div class="status-content">
                <div class="status-label">数据同步</div>
                <div class="status-value">{{ syncStatus.lastSync }}</div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 快捷工具 -->
        <a-card title="快捷工具" class="workspace-card">
          <div class="quick-tools">
            <div class="tool-grid">
              <div v-for="tool in quickTools" :key="tool.id" class="tool-item" @click="useTool(tool)">
                <div class="tool-icon">
                  <component :is="tool.icon" />
                </div>
                <div class="tool-name">{{ tool.name }}</div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 快速操作模态框 -->
    <a-modal
      v-model:open="showQuickActionModal"
      title="快速操作"
      width="600px"
      @ok="performQuickAction"
    >
      <a-form layout="vertical">
        <a-form-item label="操作类型" required>
          <a-select v-model:value="quickActionForm.type" placeholder="选择操作类型">
            <a-select-option value="call">外呼客户</a-select-option>
            <a-select-option value="visit">安排外访</a-select-option>
            <a-select-option value="record">记录催收</a-select-option>
            <a-select-option value="payment">登记还款</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="客户选择" required>
          <a-select v-model:value="quickActionForm.customerId" placeholder="选择客户" show-search>
            <a-select-option v-for="customer in customerList" :key="customer.id" :value="customer.id">
              {{ customer.name }} - {{ customer.phone }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注说明">
          <a-textarea v-model:value="quickActionForm.notes" placeholder="操作备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加日程模态框 -->
    <a-modal
      v-model:open="showScheduleModal"
      title="添加日程"
      @ok="addSchedule"
    >
      <a-form layout="vertical">
        <a-form-item label="日程标题" required>
          <a-input v-model:value="scheduleForm.title" placeholder="请输入日程标题" />
        </a-form-item>
        <a-form-item label="时间安排" required>
          <a-date-picker 
            v-model:value="scheduleForm.datetime" 
            show-time 
            format="YYYY-MM-DD HH:mm"
            placeholder="选择日期时间"
          />
        </a-form-item>
        <a-form-item label="地点">
          <a-input v-model:value="scheduleForm.location" placeholder="请输入地点" />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:value="scheduleForm.description" placeholder="日程描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SyncOutlined,
  PlusOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  BellOutlined,
  DownOutlined,
  EnvironmentOutlined,
  WifiOutlined,
  ThunderboltOutlined,
  CloudSyncOutlined,
  PhoneOutlined,
  CarOutlined,
  EditOutlined,
  MoneyCollectOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  CameraOutlined,
  CompassOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showQuickActionModal = ref(false)
const showScheduleModal = ref(false)
const selectedTaskFilter = ref('all')

// 图表引用
const taskTrendChart = ref(null)
const workloadChart = ref(null)

// 工作台统计
const workspaceStats = reactive({
  todayTasks: 12,
  pendingTasks: 8,
  completedTasks: 15,
  notifications: 6
})

// 快速操作
const quickActions = ref([
  {
    id: 1,
    name: '外呼客户',
    description: '快速发起客户通话',
    icon: 'PhoneOutlined',
    color: '#1890ff'
  },
  {
    id: 2,
    name: '安排外访',
    description: '创建外访计划',
    icon: 'CarOutlined',
    color: '#52c41a'
  },
  {
    id: 3,
    name: '记录催收',
    description: '快速记录催收结果',
    icon: 'EditOutlined',
    color: '#faad14'
  },
  {
    id: 4,
    name: '还款登记',
    description: '登记客户还款',
    icon: 'MoneyCollectOutlined',
    color: '#722ed1'
  }
])

// 任务列表
const taskList = ref([
  {
    id: 1,
    title: '联系张三进行还款提醒',
    description: '客户逾期3天，需要电话沟通还款事宜',
    type: '电话催收',
    priority: 'high',
    dueTime: '09:00',
    customerName: '张三',
    completed: false,
    category: 'today'
  },
  {
    id: 2,
    title: '外访李四确认还款计划',
    description: '客户要求面谈制定还款计划',
    type: '上门催收',
    priority: 'medium',
    dueTime: '14:00',
    customerName: '李四',
    completed: false,
    category: 'today'
  },
  {
    id: 3,
    title: '处理王五投诉反馈',
    description: '客户对催收方式提出异议',
    type: '客户服务',
    priority: 'high',
    dueTime: '昨天',
    customerName: '王五',
    completed: false,
    category: 'overdue'
  },
  {
    id: 4,
    title: '更新赵六还款记录',
    description: '客户已完成部分还款，需要更新系统',
    type: '记录更新',
    priority: 'low',
    dueTime: '16:00',
    customerName: '赵六',
    completed: true,
    category: 'today'
  }
])

// 今日日程
const todaySchedules = ref([
  {
    id: 1,
    time: '09:00',
    title: '团队晨会',
    description: '讨论今日工作安排',
    location: '会议室A',
    status: 'pending'
  },
  {
    id: 2,
    time: '10:30',
    title: '客户外访',
    description: '拜访李四，商讨还款计划',
    location: '客户公司',
    status: 'pending'
  },
  {
    id: 3,
    time: '14:00',
    title: '催收培训',
    description: '新员工催收技巧培训',
    location: '培训室',
    status: 'completed'
  }
])

// 消息通知
const notifications = ref([
  {
    id: 1,
    type: 'urgent',
    time: '5分钟前',
    content: '客户张三承诺今日下午3点还款，请及时跟进',
    read: false
  },
  {
    id: 2,
    type: 'info',
    time: '30分钟前',
    content: '系统已自动生成本周催收报表',
    read: false
  },
  {
    id: 3,
    type: 'warning',
    time: '1小时前',
    content: '李四外访任务即将到期，请确认执行状态',
    read: true
  },
  {
    id: 4,
    type: 'success',
    time: '2小时前',
    content: '恭喜！本月回收率已达成目标的95%',
    read: true
  }
])

// 同步状态
const syncStatus = reactive({
  lastSync: '5分钟前',
  isOnline: true,
  pendingSync: 3
})

// 快捷工具
const quickTools = ref([
  { id: 1, name: '计算器', icon: 'CalculatorOutlined' },
  { id: 2, name: '拍照', icon: 'CameraOutlined' },
  { id: 3, name: '定位', icon: 'CompassOutlined' },
  { id: 4, name: '笔记', icon: 'FileTextOutlined' }
])

// 客户列表
const customerList = ref([
  { id: 1, name: '张三', phone: '138****1234' },
  { id: 2, name: '李四', phone: '139****5678' },
  { id: 3, name: '王五', phone: '137****9012' },
  { id: 4, name: '赵六', phone: '136****3456' }
])

// 表单数据
const quickActionForm = reactive({
  type: '',
  customerId: '',
  notes: ''
})

const scheduleForm = reactive({
  title: '',
  datetime: null,
  location: '',
  description: ''
})

// 计算属性
const filteredTasks = computed(() => {
  if (selectedTaskFilter.value === 'all') return taskList.value
  if (selectedTaskFilter.value === 'today') return taskList.value.filter(task => task.category === 'today')
  if (selectedTaskFilter.value === 'pending') return taskList.value.filter(task => !task.completed)
  if (selectedTaskFilter.value === 'overdue') return taskList.value.filter(task => task.category === 'overdue')
  return taskList.value
})

// 方法定义
const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '紧急',
    medium: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const getTaskIcon = (type) => {
  const icons = {
    '电话催收': 'PhoneOutlined',
    '上门催收': 'CarOutlined',
    '客户服务': 'UserOutlined',
    '记录更新': 'EditOutlined'
  }
  return icons[type] || 'FileTextOutlined'
}

const getScheduleStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    completed: 'green',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const getScheduleStatusText = (status) => {
  const texts = {
    pending: '待执行',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getNotificationClass = (type) => {
  return `notification-${type}`
}

const getNotificationTypeText = (type) => {
  const texts = {
    urgent: '紧急',
    warning: '警告',
    info: '信息',
    success: '成功'
  }
  return texts[type] || type
}

const refreshWorkspace = () => {
  console.log('刷新工作台')
  initCharts()
}

const syncData = () => {
  console.log('同步数据')
  syncStatus.lastSync = '刚刚'
}

const handleQuickAction = (action) => {
  quickActionForm.type = action.name === '外呼客户' ? 'call' : 
                        action.name === '安排外访' ? 'visit' :
                        action.name === '记录催收' ? 'record' : 'payment'
  showQuickActionModal.value = true
}

const filterTasks = () => {
  console.log('筛选任务:', selectedTaskFilter.value)
}

const updateTaskStatus = (task) => {
  console.log('更新任务状态:', task)
}

const executeTask = (task) => {
  console.log('执行任务:', task)
}

const editTask = (task) => {
  console.log('编辑任务:', task)
}

const postponeTask = (task) => {
  console.log('延期任务:', task)
}

const assignTask = (task) => {
  console.log('分配任务:', task)
}

const deleteTask = (task) => {
  const index = taskList.value.findIndex(t => t.id === task.id)
  if (index > -1) {
    taskList.value.splice(index, 1)
  }
}

const addTask = () => {
  console.log('新建任务')
}

const markAsRead = (notification) => {
  notification.read = true
}

const viewNotification = (notification) => {
  console.log('查看通知详情:', notification)
}

const useTool = (tool) => {
  console.log('使用工具:', tool)
}

const performQuickAction = () => {
  console.log('执行快速操作:', quickActionForm)
  showQuickActionModal.value = false
}

const addSchedule = () => {
  console.log('添加日程:', scheduleForm)
  showScheduleModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 任务趋势图
    if (taskTrendChart.value) {
      const chart1 = echarts.init(taskTrendChart.value)
      chart1.setOption({
        title: { text: '任务完成趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '完成任务',
          type: 'line',
          data: [12, 15, 18, 14, 16, 20, 15],
          smooth: true,
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 }
        }]
      })
    }

    // 工作量分布图
    if (workloadChart.value) {
      const chart2 = echarts.init(workloadChart.value)
      chart2.setOption({
        title: { text: '工作量分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 35, name: '电话催收' },
            { value: 25, name: '上门催收' },
            { value: 20, name: '客户服务' },
            { value: 20, name: '记录更新' }
          ]
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshWorkspace()
})
</script>

<style scoped>
.mobile-workspace {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workspace-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.workspace-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.workspace-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.workspace-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quick-actions {
  padding: 8px 0;
}

.action-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.action-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.2);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  color: white;
  font-size: 20px;
}

.action-text {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #999;
}

.task-list {
  max-height: 400px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.task-content {
  flex: 1;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.task-title {
  font-weight: 500;
  color: #262626;
}

.task-title.completed {
  text-decoration: line-through;
  color: #999;
}

.task-description {
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.task-type {
  display: flex;
  align-items: center;
}

.task-actions {
  display: flex;
  gap: 4px;
}

.chart-container-small {
  height: 250px;
  width: 100%;
}

.schedule-list {
  max-height: 300px;
  overflow-y: auto;
}

.schedule-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-time {
  font-weight: 500;
  color: #1890ff;
  min-width: 60px;
}

.schedule-content {
  flex: 1;
}

.schedule-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.schedule-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.schedule-location {
  font-size: 11px;
  color: #999;
  display: flex;
  align-items: center;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px;
  border-left: 3px solid transparent;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item.unread {
  background: #f6f8ff;
  border-left-color: #1890ff;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.notification-type {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.notification-urgent {
  background: #fff2f0;
  color: #ff4d4f;
}

.notification-warning {
  background: #fff7e6;
  color: #faad14;
}

.notification-info {
  background: #f6f8ff;
  color: #1890ff;
}

.notification-success {
  background: #f6ffed;
  color: #52c41a;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.notification-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.device-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.online {
  color: #52c41a;
}

.status-content {
  flex: 1;
}

.status-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.status-value {
  font-size: 13px;
  color: #262626;
  font-weight: 500;
}

.quick-tools {
  padding: 8px 0;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #1890ff;
  background: #f6f8ff;
}

.tool-icon {
  font-size: 20px;
  color: #1890ff;
  margin-bottom: 6px;
}

.tool-name {
  font-size: 12px;
  color: #666;
}
</style>