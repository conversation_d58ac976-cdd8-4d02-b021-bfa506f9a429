<template>
  <div class="field-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="field-info">
          <h2>外访管理</h2>
          <p class="field-desc">智能化外访计划管理，提升现场催收效率</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshField">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="getCurrentLocation">
            <template #icon><EnvironmentOutlined /></template>
            定位
          </a-button>
          <a-button type="primary" @click="showVisitModal = true">
            <template #icon><PlusOutlined /></template>
            新建外访
          </a-button>
        </div>
      </div>
    </div>

    <!-- 外访统计 -->
    <div class="field-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日计划" 
              :value="fieldStats.todayPlans" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><CalendarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="进行中" 
              :value="fieldStats.inProgress" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><CarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="已完成" 
              :value="fieldStats.completed" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="总里程" 
              :value="fieldStats.totalDistance" 
              :value-style="{ color: '#722ed1' }"
              suffix="km"
            >
              <template #prefix><DashboardOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 外访计划列表 -->
        <a-card title="外访计划" class="field-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedFilter" style="width: 120px" @change="filterVisits">
                <a-select-option value="all">全部计划</a-select-option>
                <a-select-option value="today">今日计划</a-select-option>
                <a-select-option value="pending">待执行</a-select-option>
                <a-select-option value="inProgress">进行中</a-select-option>
              </a-select>
              <a-button @click="optimizeRoute">
                <template #icon><NodeIndexOutlined /></template>
                路线优化
              </a-button>
            </a-space>
          </template>
          
          <div class="visit-list">
            <div v-for="visit in filteredVisits" :key="visit.id" class="visit-item">
              <div class="visit-content">
                <div class="visit-header">
                  <div class="visit-customer">
                    <h4>{{ visit.customerName }}</h4>
                    <a-tag :color="getStatusColor(visit.status)">
                      {{ getStatusText(visit.status) }}
                    </a-tag>
                  </div>
                  <div class="visit-time">{{ visit.plannedTime }}</div>
                </div>
                <div class="visit-details">
                  <div class="detail-item">
                    <EnvironmentOutlined style="margin-right: 4px;" />
                    <span>{{ visit.address }}</span>
                  </div>
                  <div class="detail-item">
                    <PhoneOutlined style="margin-right: 4px;" />
                    <span>{{ visit.phone }}</span>
                  </div>
                  <div class="detail-item">
                    <DollarOutlined style="margin-right: 4px;" />
                    <span>逾期金额：¥{{ visit.overdueAmount.toLocaleString() }}</span>
                  </div>
                </div>
                <div class="visit-purpose">
                  <span class="purpose-label">拜访目的：</span>
                  <span>{{ visit.purpose }}</span>
                </div>
              </div>
              <div class="visit-actions">
                <a-space>
                  <a-button 
                    v-if="visit.status === 'pending'" 
                    type="primary" 
                    size="small" 
                    @click="startVisit(visit)"
                  >
                    开始
                  </a-button>
                  <a-button 
                    v-if="visit.status === 'inProgress'" 
                    type="primary" 
                    size="small" 
                    @click="completeVisit(visit)"
                  >
                    完成
                  </a-button>
                  <a-button size="small" @click="navigate(visit)">
                    <template #icon><CompassOutlined /></template>
                    导航
                  </a-button>
                  <a-dropdown>
                    <a-button size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="editVisit(visit)">编辑</a-menu-item>
                        <a-menu-item @click="postponeVisit(visit)">延期</a-menu-item>
                        <a-menu-item @click="viewRecord(visit)">查看记录</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="cancelVisit(visit)" danger>取消</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </div>
            </div>
            <a-empty v-if="filteredVisits.length === 0" description="暂无外访计划" />
          </div>
        </a-card>

        <!-- 地图视图 -->
        <a-card title="地图视图" class="field-card">
          <template #extra>
            <a-space>
              <a-button @click="centerMap">
                <template #icon><AimOutlined /></template>
                回到中心
              </a-button>
              <a-button @click="showTraffic = !showTraffic" :type="showTraffic ? 'primary' : 'default'">
                <template #icon><CarOutlined /></template>
                路况
              </a-button>
            </a-space>
          </template>
          
          <div class="map-container">
            <div class="map-placeholder">
              <div class="map-content">
                <EnvironmentOutlined style="color:#1890ff; font-size: 48px; margin-bottom: 16px;" />
                <div class="map-text">地图加载中...</div>
                <div class="map-info">
                  <div class="location-info">
                    <span class="info-label">当前位置：</span>
                    <span>{{ currentLocation.address }}</span>
                  </div>
                  <div class="location-coords">
                    <span class="info-label">坐标：</span>
                    <span>{{ currentLocation.lat }}, {{ currentLocation.lng }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 路线分析 -->
        <a-card title="路线分析" class="field-card">
          <div class="route-analysis">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="routeChart" class="chart-container-small"></div>
              </a-col>
              <a-col :span="12">
                <div class="route-summary">
                  <div class="summary-item">
                    <div class="summary-label">总距离</div>
                    <div class="summary-value">{{ routeData.totalDistance }}km</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">预计时间</div>
                    <div class="summary-value">{{ routeData.estimatedTime }}</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">油费预估</div>
                    <div class="summary-value">¥{{ routeData.fuelCost }}</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">访问点数</div>
                    <div class="summary-value">{{ routeData.visitPoints }}个</div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 当前状态 -->
        <a-card title="当前状态" class="field-card">
          <div class="current-status">
            <div class="status-info">
              <div class="status-item">
                <div class="status-label">当前时间</div>
                <div class="status-value">{{ currentTime }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">工作状态</div>
                <div class="status-value">
                  <a-tag :color="workStatus.color">{{ workStatus.text }}</a-tag>
                </div>
              </div>
              <div class="status-item">
                <div class="status-label">当前位置</div>
                <div class="status-value">{{ currentLocation.address }}</div>
              </div>
              <div class="status-item">
                <div class="status-label">移动速度</div>
                <div class="status-value">{{ currentSpeed }} km/h</div>
              </div>
            </div>
            <div class="status-actions">
              <a-button 
                :type="workStatus.value === 'working' ? 'danger' : 'primary'" 
                block 
                @click="toggleWorkStatus"
              >
                {{ workStatus.value === 'working' ? '结束工作' : '开始工作' }}
              </a-button>
            </div>
          </div>
        </a-card>

        <!-- 外访记录 -->
        <a-card title="外访记录" class="field-card">
          <template #extra>
            <a-button size="small" @click="showRecordModal = true">
              <template #icon><PlusOutlined /></template>
              添加记录
            </a-button>
          </template>
          <div class="visit-records">
            <div v-for="record in visitRecords" :key="record.id" class="record-item">
              <div class="record-header">
                <span class="record-customer">{{ record.customerName }}</span>
                <span class="record-time">{{ record.visitTime }}</span>
              </div>
              <div class="record-result">
                <a-tag :color="getResultColor(record.result)">
                  {{ record.result }}
                </a-tag>
              </div>
              <div class="record-notes">{{ record.notes }}</div>
              <div class="record-photos" v-if="record.photos.length > 0">
                <div class="photo-count">
                  <CameraOutlined style="margin-right: 4px;" />
                  {{ record.photos.length }}张照片
                </div>
              </div>
            </div>
            <a-empty v-if="visitRecords.length === 0" description="暂无外访记录" size="small" />
          </div>
        </a-card>

        <!-- 费用统计 -->
        <a-card title="费用统计" class="field-card">
          <div class="expense-stats">
            <div class="expense-item">
              <div class="expense-icon">
                <CarOutlined style="color: #1890ff;" />
              </div>
              <div class="expense-content">
                <div class="expense-label">交通费</div>
                <div class="expense-amount">¥{{ expenses.transport }}</div>
              </div>
            </div>
            <div class="expense-item">
              <div class="expense-icon">
                <ShopOutlined style="color: #52c41a;" />
              </div>
              <div class="expense-content">
                <div class="expense-label">餐饮费</div>
                <div class="expense-amount">¥{{ expenses.meal }}</div>
              </div>
            </div>
            <div class="expense-item">
              <div class="expense-icon">
                <PhoneOutlined style="color: #faad14;" />
              </div>
              <div class="expense-content">
                <div class="expense-label">通讯费</div>
                <div class="expense-amount">¥{{ expenses.communication }}</div>
              </div>
            </div>
            <div class="expense-item">
              <div class="expense-icon">
                <EllipsisOutlined style="color: #722ed1;" />
              </div>
              <div class="expense-content">
                <div class="expense-label">其他费用</div>
                <div class="expense-amount">¥{{ expenses.other }}</div>
              </div>
            </div>
            <div class="expense-total">
              <div class="total-label">今日总计</div>
              <div class="total-amount">¥{{ totalExpense }}</div>
            </div>
          </div>
        </a-card>

        <!-- 天气信息 -->
        <a-card title="天气信息" class="field-card">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <CloudOutlined style="font-size: 32px; color: #1890ff;" />
              </div>
              <div class="weather-temp">{{ weather.temperature }}°C</div>
              <div class="weather-desc">{{ weather.description }}</div>
            </div>
            <div class="weather-details">
              <div class="weather-item">
                <span class="weather-label">湿度：</span>
                <span>{{ weather.humidity }}%</span>
              </div>
              <div class="weather-item">
                <span class="weather-label">风速：</span>
                <span>{{ weather.windSpeed }} km/h</span>
              </div>
              <div class="weather-item">
                <span class="weather-label">能见度：</span>
                <span>{{ weather.visibility }} km</span>
              </div>
              <div class="weather-item">
                <span class="weather-label">紫外线：</span>
                <span>{{ weather.uvIndex }}</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 新建外访模态框 -->
    <a-modal
      v-model:open="showVisitModal"
      title="新建外访计划"
      width="600px"
      @ok="createVisit"
    >
      <a-form layout="vertical">
        <a-form-item label="客户选择" required>
          <a-select v-model:value="visitForm.customerId" placeholder="选择客户" show-search>
            <a-select-option v-for="customer in customerList" :key="customer.id" :value="customer.id">
              {{ customer.name }} - {{ customer.phone }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="计划时间" required>
          <a-date-picker 
            v-model:value="visitForm.plannedTime" 
            show-time 
            format="YYYY-MM-DD HH:mm"
            placeholder="选择拜访时间"
          />
        </a-form-item>
        <a-form-item label="拜访地址" required>
          <a-input v-model:value="visitForm.address" placeholder="请输入详细地址" />
        </a-form-item>
        <a-form-item label="拜访目的">
          <a-select v-model:value="visitForm.purpose" placeholder="选择拜访目的">
            <a-select-option value="还款协商">还款协商</a-select-option>
            <a-select-option value="财产调查">财产调查</a-select-option>
            <a-select-option value="送达文书">送达文书</a-select-option>
            <a-select-option value="签订协议">签订协议</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注说明">
          <a-textarea v-model:value="visitForm.notes" placeholder="拜访备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 外访记录模态框 -->
    <a-modal
      v-model:open="showRecordModal"
      title="添加外访记录"
      @ok="addRecord"
    >
      <a-form layout="vertical">
        <a-form-item label="客户名称" required>
          <a-input v-model:value="recordForm.customerName" placeholder="请输入客户名称" />
        </a-form-item>
        <a-form-item label="拜访结果" required>
          <a-select v-model:value="recordForm.result" placeholder="选择拜访结果">
            <a-select-option value="成功接触">成功接触</a-select-option>
            <a-select-option value="未能接触">未能接触</a-select-option>
            <a-select-option value="达成协议">达成协议</a-select-option>
            <a-select-option value="拒绝配合">拒绝配合</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="详细记录">
          <a-textarea v-model:value="recordForm.notes" placeholder="详细描述拜访过程和结果" :rows="4" />
        </a-form-item>
        <a-form-item label="现场照片">
          <a-upload
            v-model:file-list="recordForm.photos"
            list-type="picture-card"
            :before-upload="() => false"
          >
            <div>
              <PlusOutlined />
              <div style="margin-top: 8px">上传照片</div>
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  EnvironmentOutlined,
  PlusOutlined,
  CalendarOutlined,
  CarOutlined,
  CheckCircleOutlined,
  DashboardOutlined,
  NodeIndexOutlined,
  PhoneOutlined,
  DollarOutlined,
  CompassOutlined,
  DownOutlined,
  AimOutlined,
  CameraOutlined,
  ShopOutlined,
  EllipsisOutlined,
  CloudOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showVisitModal = ref(false)
const showRecordModal = ref(false)
const selectedFilter = ref('all')
const showTraffic = ref(false)
const currentTime = ref('')
const currentSpeed = ref(0)

// 图表引用
const routeChart = ref(null)

// 外访统计
const fieldStats = reactive({
  todayPlans: 8,
  inProgress: 2,
  completed: 5,
  totalDistance: 125.6
})

// 外访计划列表
const visitPlans = ref([
  {
    id: 1,
    customerName: '张三',
    phone: '138****1234',
    address: '北京市朝阳区建国路88号',
    plannedTime: '09:00',
    overdueAmount: 50000,
    purpose: '还款协商',
    status: 'pending'
  },
  {
    id: 2,
    customerName: '李四',
    phone: '139****5678',
    address: '北京市海淀区中关村大街1号',
    plannedTime: '11:00',
    overdueAmount: 120000,
    purpose: '财产调查',
    status: 'inProgress'
  },
  {
    id: 3,
    customerName: '王五',
    phone: '137****9012',
    address: '北京市东城区王府井大街168号',
    plannedTime: '14:00',
    overdueAmount: 80000,
    purpose: '送达文书',
    status: 'pending'
  },
  {
    id: 4,
    customerName: '赵六',
    phone: '136****3456',
    address: '北京市西城区金融街35号',
    plannedTime: '16:00',
    overdueAmount: 200000,
    purpose: '签订协议',
    status: 'completed'
  }
])

// 当前位置
const currentLocation = reactive({
  lat: '39.9042',
  lng: '116.4074',
  address: '北京市朝阳区建国门外大街2号'
})

// 工作状态
const workStatus = reactive({
  value: 'working',
  text: '工作中',
  color: 'green'
})

// 路线数据
const routeData = reactive({
  totalDistance: 45.2,
  estimatedTime: '3小时15分',
  fuelCost: 28,
  visitPoints: 4
})

// 外访记录
const visitRecords = ref([
  {
    id: 1,
    customerName: '张三',
    visitTime: '上午 9:30',
    result: '成功接触',
    notes: '客户同意制定还款计划，预计下周签署协议',
    photos: ['photo1.jpg', 'photo2.jpg']
  },
  {
    id: 2,
    customerName: '李四',
    visitTime: '上午 11:45',
    result: '未能接触',
    notes: '客户不在家，邻居告知可能已搬家',
    photos: []
  },
  {
    id: 3,
    customerName: '王五',
    visitTime: '下午 2:15',
    result: '达成协议',
    notes: '成功签署分期还款协议，首期款项已收取',
    photos: ['agreement.jpg']
  }
])

// 费用统计
const expenses = reactive({
  transport: 85,
  meal: 45,
  communication: 15,
  other: 20
})

// 天气信息
const weather = reactive({
  temperature: 22,
  description: '多云',
  humidity: 65,
  windSpeed: 12,
  visibility: 10,
  uvIndex: 3
})

// 客户列表
const customerList = ref([
  { id: 1, name: '张三', phone: '138****1234' },
  { id: 2, name: '李四', phone: '139****5678' },
  { id: 3, name: '王五', phone: '137****9012' },
  { id: 4, name: '赵六', phone: '136****3456' }
])

// 表单数据
const visitForm = reactive({
  customerId: '',
  plannedTime: null,
  address: '',
  purpose: '',
  notes: ''
})

const recordForm = reactive({
  customerName: '',
  result: '',
  notes: '',
  photos: []
})

// 计算属性
const filteredVisits = computed(() => {
  if (selectedFilter.value === 'all') return visitPlans.value
  if (selectedFilter.value === 'today') return visitPlans.value
  if (selectedFilter.value === 'pending') return visitPlans.value.filter(visit => visit.status === 'pending')
  if (selectedFilter.value === 'inProgress') return visitPlans.value.filter(visit => visit.status === 'inProgress')
  return visitPlans.value
})

const totalExpense = computed(() => {
  return expenses.transport + expenses.meal + expenses.communication + expenses.other
})

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    inProgress: 'blue',
    completed: 'green',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待执行',
    inProgress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getResultColor = (result) => {
  const colors = {
    '成功接触': 'green',
    '未能接触': 'orange',
    '达成协议': 'green',
    '拒绝配合': 'red'
  }
  return colors[result] || 'default'
}

const refreshField = () => {
  console.log('刷新外访管理')
  initCharts()
}

const getCurrentLocation = () => {
  console.log('获取当前位置')
  // 模拟获取位置
  currentLocation.address = '位置更新中...'
  setTimeout(() => {
    currentLocation.address = '北京市朝阳区建国门外大街2号'
  }, 2000)
}

const filterVisits = () => {
  console.log('筛选外访计划:', selectedFilter.value)
}

const optimizeRoute = () => {
  console.log('优化路线')
}

const startVisit = (visit) => {
  visit.status = 'inProgress'
  console.log('开始外访:', visit)
}

const completeVisit = (visit) => {
  visit.status = 'completed'
  console.log('完成外访:', visit)
}

const navigate = (visit) => {
  console.log('导航到:', visit.address)
}

const editVisit = (visit) => {
  console.log('编辑外访:', visit)
}

const postponeVisit = (visit) => {
  console.log('延期外访:', visit)
}

const viewRecord = (visit) => {
  console.log('查看记录:', visit)
}

const cancelVisit = (visit) => {
  visit.status = 'cancelled'
  console.log('取消外访:', visit)
}

const centerMap = () => {
  console.log('回到地图中心')
}

const toggleWorkStatus = () => {
  if (workStatus.value === 'working') {
    workStatus.value = 'resting'
    workStatus.text = '休息中'
    workStatus.color = 'orange'
  } else {
    workStatus.value = 'working'
    workStatus.text = '工作中'
    workStatus.color = 'green'
  }
}

const createVisit = () => {
  console.log('创建外访计划:', visitForm)
  showVisitModal.value = false
}

const addRecord = () => {
  console.log('添加外访记录:', recordForm)
  showRecordModal.value = false
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN')
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 路线分析图
    if (routeChart.value) {
      const chart = echarts.init(routeChart.value)
      chart.setOption({
        title: { text: '路线效率分析', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['计划时间', '实际时间'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['客户A', '客户B', '客户C', '客户D']
        },
        yAxis: { type: 'value', name: '时间(分钟)' },
        series: [
          {
            name: '计划时间',
            type: 'bar',
            data: [30, 45, 60, 40],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '实际时间',
            type: 'bar',
            data: [25, 50, 55, 45],
            itemStyle: { color: '#52c41a' }
          }
        ]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshField()
  updateCurrentTime()
  // 每秒更新时间
  setInterval(updateCurrentTime, 1000)
  // 模拟速度变化
  setInterval(() => {
    currentSpeed.value = Math.floor(Math.random() * 60)
  }, 3000)
})
</script>

<style scoped>
.field-management {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.field-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.field-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.field-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.visit-list {
  max-height: 500px;
  overflow-y: auto;
}

.visit-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  background: white;
}

.visit-content {
  flex: 1;
}

.visit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.visit-customer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.visit-customer h4 {
  margin: 0;
  color: #262626;
}

.visit-time {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.visit-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.visit-purpose {
  font-size: 13px;
}

.purpose-label {
  color: #999;
}

.visit-actions {
  margin-left: 16px;
}

.map-container {
  height: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.map-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.map-content {
  text-align: center;
}

.map-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
}

.map-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
  color: #999;
}

.info-label {
  font-weight: 500;
}

.route-analysis {
  padding: 8px 0;
}

.chart-container-small {
  height: 250px;
  width: 100%;
}

.route-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  color: #666;
  font-size: 13px;
}

.summary-value {
  font-weight: 500;
  color: #262626;
  font-size: 15px;
}

.current-status {
  padding: 8px 0;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: #666;
  font-size: 13px;
}

.status-value {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.visit-records {
  max-height: 300px;
  overflow-y: auto;
}

.record-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.record-customer {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.record-time {
  font-size: 11px;
  color: #999;
}

.record-result {
  margin-bottom: 6px;
}

.record-notes {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 6px;
}

.record-photos {
  font-size: 11px;
  color: #999;
}

.photo-count {
  display: flex;
  align-items: center;
}

.expense-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.expense-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.expense-icon {
  font-size: 16px;
}

.expense-content {
  flex: 1;
}

.expense-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.expense-amount {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.expense-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 2px solid #1890ff;
  border-radius: 6px;
  background: #f6f8ff;
}

.total-label {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.total-amount {
  font-size: 16px;
  color: #1890ff;
  font-weight: bold;
}

.weather-info {
  text-align: center;
}

.weather-current {
  margin-bottom: 16px;
}

.weather-icon {
  margin-bottom: 8px;
}

.weather-temp {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.weather-desc {
  font-size: 14px;
  color: #666;
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: left;
}

.weather-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.weather-label {
  font-weight: 500;
}
</style>