<template>
  <div class="location-service">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="location-info">
          <h2>位置服务</h2>
          <p class="location-desc">精准定位、智能导航、轨迹管理一体化解决方案</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshLocation">
            <template #icon><ReloadOutlined /></template>
            刷新位置
          </a-button>
          <a-button @click="showLocationSettings = true">
            <template #icon><SettingOutlined /></template>
            定位设置
          </a-button>
          <a-button type="primary" @click="getCurrentLocation" :loading="locating">
            <template #icon><EnvironmentOutlined /></template>
            {{ locating ? '定位中' : '立即定位' }}
          </a-button>
        </div>
      </div>
    </div>

    <!-- 位置状态 -->
    <div class="location-status">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="GPS状态" 
              :value="locationStats.gpsStatus" 
              :value-style="{ color: getGpsStatusColor() }"
            >
              <template #prefix><CompassOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="定位精度" 
              :value="locationStats.accuracy" 
              suffix="米"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><AimOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日轨迹" 
              :value="locationStats.todayDistance" 
              suffix="km"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><CarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="位置记录" 
              :value="locationStats.recordCount" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><AimOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 地图导航 -->
        <a-card title="地图导航" class="location-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedMapType" style="width: 100px" @change="changeMapType">
                <a-select-option value="street">街道</a-select-option>
                <a-select-option value="satellite">卫星</a-select-option>
                <a-select-option value="hybrid">混合</a-select-option>
              </a-select>
              <a-button @click="centerToCurrentLocation">
                <template #icon><EnvironmentOutlined /></template>
                居中
              </a-button>
              <a-button @click="showNavigationModal = true">
                <template #icon><ArrowRightOutlined /></template>
                导航
              </a-button>
            </a-space>
          </template>
          
          <div class="map-container">
            <div class="map-placeholder">
              <div class="map-info">
                <h3>地图加载中...</h3>
                <p>当前位置: {{ currentLocation.address }}</p>
                <p>坐标: {{ currentLocation.latitude }}, {{ currentLocation.longitude }}</p>
                <p>海拔: {{ currentLocation.altitude }}米</p>
                <p>更新时间: {{ currentLocation.timestamp }}</p>
              </div>
              <div class="map-controls">
                <a-button-group>
                  <a-button @click="zoomIn">放大</a-button>
                  <a-button @click="zoomOut">缩小</a-button>
                </a-button-group>
                <a-button-group style="margin-left: 8px;">
                  <a-button @click="addLocationMarker">
                    <template #icon><AimOutlined /></template>
                    标记
                  </a-button>
                  <a-button @click="measureDistance">
                    <template #icon><LineOutlined /></template>
                    测距
                  </a-button>
                </a-button-group>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 位置记录 -->
        <a-card title="位置记录" class="location-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedRecordFilter" style="width: 120px" @change="filterLocationRecords">
                <a-select-option value="all">全部记录</a-select-option>
                <a-select-option value="today">今日记录</a-select-option>
                <a-select-option value="work">工作地点</a-select-option>
                <a-select-option value="visit">外访地点</a-select-option>
              </a-select>
              <a-button size="small" @click="exportLocationData">
                <template #icon><DownloadOutlined /></template>
                导出数据
              </a-button>
            </a-space>
          </template>
          
          <div class="location-records">
            <div v-for="record in filteredLocationRecords" :key="record.id" class="record-item">
              <div class="record-content">
                <div class="record-header">
                  <div class="record-type" :class="getRecordTypeClass(record.type)">
                    <component :is="getRecordTypeIcon(record.type)" />
                    <span>{{ getRecordTypeText(record.type) }}</span>
                  </div>
                  <div class="record-time">{{ record.timestamp }}</div>
                </div>
                <div class="record-address">{{ record.address }}</div>
                <div class="record-coordinates">
                  经度: {{ record.longitude }} | 纬度: {{ record.latitude }}
                </div>
                <div class="record-meta">
                  <span class="record-accuracy">精度: {{ record.accuracy }}米</span>
                  <span class="record-method">{{ record.method }}</span>
                  <span v-if="record.note" class="record-note">备注: {{ record.note }}</span>
                </div>
              </div>
              <div class="record-actions">
                <a-button type="link" size="small" @click="navigateToLocation(record)">
                  导航
                </a-button>
                <a-button type="link" size="small" @click="showLocationDetail(record)">
                  详情
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="editLocationRecord(record)">编辑备注</a-menu-item>
                      <a-menu-item @click="shareLocation(record)">分享位置</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="deleteLocationRecord(record)" danger>删除记录</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
            <a-empty v-if="filteredLocationRecords.length === 0" description="暂无位置记录" />
          </div>
        </a-card>

        <!-- 轨迹分析 -->
        <a-card title="轨迹分析" class="location-card">
          <div class="trajectory-analysis">
            <div ref="trajectoryChart" class="chart-container"></div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 当前位置 -->
        <a-card title="当前位置" class="location-card">
          <div class="current-location">
            <div class="location-display">
              <div class="location-icon">
                <EnvironmentOutlined style="color:#1890ff; font-size: 32px;" />
              </div>
              <div class="location-details">
                <div class="location-address">{{ currentLocation.address }}</div>
                <div class="location-coords">
                  {{ currentLocation.latitude }}, {{ currentLocation.longitude }}
                </div>
                <div class="location-update">{{ currentLocation.timestamp }}</div>
              </div>
            </div>
            
            <div class="location-actions">
              <a-button block @click="saveCurrentLocation">
                <template #icon><BookOutlined /></template>
                保存位置
              </a-button>
              <a-button block @click="shareCurrentLocation">
                <template #icon><ShareAltOutlined /></template>
                分享位置
              </a-button>
              <a-button block @click="copyLocationInfo">
                <template #icon><CopyOutlined /></template>
                复制坐标
              </a-button>
            </div>
          </div>
        </a-card>

        <!-- 导航助手 -->
        <a-card title="导航助手" class="location-card">
          <div class="navigation-assistant">
            <div class="quick-destinations">
              <h4>快速导航</h4>
              <div class="destination-list">
                <div v-for="dest in quickDestinations" :key="dest.id" class="destination-item" @click="navigateToDestination(dest)">
                  <div class="destination-icon">
                    <component :is="dest.icon" />
                  </div>
                  <div class="destination-info">
                    <div class="destination-name">{{ dest.name }}</div>
                    <div class="destination-address">{{ dest.address }}</div>
                    <div class="destination-distance">{{ dest.distance }}km</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="navigation-tools">
              <h4>导航工具</h4>
              <div class="tool-list">
                <div v-for="tool in navigationTools" :key="tool.id" class="tool-item" @click="useNavigationTool(tool)">
                  <div class="tool-icon">
                    <component :is="tool.icon" />
                  </div>
                  <div class="tool-name">{{ tool.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 轨迹管理 -->
        <a-card title="轨迹管理" class="location-card">
          <template #extra>
            <a-switch 
              v-model:checked="trackingEnabled" 
              @change="toggleTracking"
              checked-children="记录中"
              un-checked-children="已停止"
            />
          </template>
          <div class="trajectory-management">
            <div class="tracking-status">
              <div class="status-item">
                <span class="status-label">记录状态:</span>
                <span class="status-value" :class="{ 'tracking': trackingEnabled }">
                  {{ trackingEnabled ? '正在记录' : '已停止' }}
                </span>
              </div>
              <div class="status-item">
                <span class="status-label">记录间隔:</span>
                <span class="status-value">{{ trackingInterval }}秒</span>
              </div>
              <div class="status-item">
                <span class="status-label">今日里程:</span>
                <span class="status-value">{{ todayMileage }}km</span>
              </div>
            </div>
            
            <div class="trajectory-list">
              <h4>轨迹记录</h4>
              <div v-for="trajectory in trajectoryList" :key="trajectory.id" class="trajectory-item">
                <div class="trajectory-header">
                  <span class="trajectory-name">{{ trajectory.name }}</span>
                  <span class="trajectory-date">{{ trajectory.date }}</span>
                </div>
                <div class="trajectory-stats">
                  <span class="trajectory-distance">{{ trajectory.distance }}km</span>
                  <span class="trajectory-duration">{{ trajectory.duration }}</span>
                  <span class="trajectory-points">{{ trajectory.points }}点</span>
                </div>
                <div class="trajectory-actions">
                  <a-button type="link" size="small" @click="viewTrajectory(trajectory)">
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="exportTrajectory(trajectory)">
                    导出
                  </a-button>
                  <a-button type="link" size="small" @click="deleteTrajectory(trajectory)" danger>
                    删除
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 位置服务设置 -->
        <a-card title="服务设置" class="location-card">
          <div class="location-settings">
            <a-form layout="vertical" size="small">
              <a-form-item label="定位模式">
                <a-radio-group v-model:value="locationSettings.mode">
                  <a-radio value="high">高精度</a-radio>
                  <a-radio value="balanced">平衡模式</a-radio>
                  <a-radio value="power">省电模式</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="自动更新">
                <a-switch v-model:checked="locationSettings.autoUpdate" />
                <span style="margin-left: 8px;">自动更新位置</span>
              </a-form-item>
              <a-form-item label="更新间隔">
                <a-slider 
                  v-model:value="locationSettings.updateInterval" 
                  :min="5" 
                  :max="300"
                  :marks="{ 5: '5s', 30: '30s', 60: '1min', 300: '5min' }"
                />
              </a-form-item>
              <a-form-item label="轨迹记录">
                <a-switch v-model:checked="locationSettings.trackingEnabled" />
                <span style="margin-left: 8px;">启用轨迹记录</span>
              </a-form-item>
              <a-button type="primary" block @click="saveLocationSettings">
                保存设置
              </a-button>
            </a-form>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 导航模态框 -->
    <a-modal
      v-model:open="showNavigationModal"
      title="导航设置"
      @ok="startNavigation"
    >
      <a-form layout="vertical">
        <a-form-item label="目的地" required>
          <a-input v-model:value="navigationForm.destination" placeholder="请输入目的地地址" />
        </a-form-item>
        <a-form-item label="导航方式">
          <a-radio-group v-model:value="navigationForm.mode">
            <a-radio value="driving">驾车</a-radio>
            <a-radio value="walking">步行</a-radio>
            <a-radio value="transit">公交</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="路线偏好">
          <a-checkbox-group v-model:value="navigationForm.preferences">
            <a-checkbox value="fastest">最快路线</a-checkbox>
            <a-checkbox value="shortest">最短路线</a-checkbox>
            <a-checkbox value="avoid_toll">避开收费</a-checkbox>
            <a-checkbox value="avoid_traffic">避开拥堵</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 位置设置模态框 -->
    <a-modal
      v-model:open="showLocationSettings"
      title="位置服务设置"
      @ok="saveAdvancedSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="GPS精度要求">
          <a-slider 
            v-model:value="advancedSettings.gpsAccuracy" 
            :min="1" 
            :max="50"
            :marks="{ 1: '1m', 10: '10m', 20: '20m', 50: '50m' }"
          />
        </a-form-item>
        <a-form-item label="网络辅助定位">
          <a-switch v-model:checked="advancedSettings.networkAssist" />
          <span style="margin-left: 8px;">启用A-GPS和网络定位</span>
        </a-form-item>
        <a-form-item label="地理围栏">
          <a-switch v-model:checked="advancedSettings.geofencing" />
          <span style="margin-left: 8px;">启用地理围栏提醒</span>
        </a-form-item>
        <a-form-item label="位置历史">
          <a-input-number 
            v-model:value="advancedSettings.historyDays" 
            :min="1" 
            :max="365"
            addon-after="天"
          />
          <div style="margin-top: 4px; font-size: 12px; color: #999;">位置历史保存天数</div>
        </a-form-item>
        <a-form-item label="隐私设置">
          <a-checkbox-group v-model:value="advancedSettings.privacy">
            <a-checkbox value="share_location">允许位置分享</a-checkbox>
            <a-checkbox value="location_history">保存位置历史</a-checkbox>
            <a-checkbox value="anonymous_data">匿名数据统计</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 保存位置模态框 -->
    <a-modal
      v-model:open="showSaveLocationModal"
      title="保存位置"
      @ok="confirmSaveLocation"
    >
      <a-form layout="vertical">
        <a-form-item label="位置名称" required>
          <a-input v-model:value="saveLocationForm.name" placeholder="请输入位置名称" />
        </a-form-item>
        <a-form-item label="位置类型">
          <a-select v-model:value="saveLocationForm.type">
            <a-select-option value="home">家</a-select-option>
            <a-select-option value="office">公司</a-select-option>
            <a-select-option value="customer">客户地址</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注说明">
          <a-textarea v-model:value="saveLocationForm.note" placeholder="位置备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined,
  EnvironmentOutlined,
  CompassOutlined,
  AimOutlined,
  CarOutlined,
  ArrowRightOutlined,
  LineOutlined,
  DownloadOutlined,
  DownOutlined,
  BookOutlined,
  ShareAltOutlined,
  CopyOutlined,
  ShopOutlined,
  UserOutlined,
  BankOutlined,
  CreditCardOutlined,
  SearchOutlined,
  HomeOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showNavigationModal = ref(false)
const showLocationSettings = ref(false)
const showSaveLocationModal = ref(false)
const selectedMapType = ref('street')
const selectedRecordFilter = ref('all')
const locating = ref(false)
const trackingEnabled = ref(true)
const trackingInterval = ref(30)
const todayMileage = ref(15.6)

// 图表引用
const trajectoryChart = ref(null)

// 位置统计
const locationStats = reactive({
  gpsStatus: '已开启',
  accuracy: 5.2,
  todayDistance: 15.6,
  recordCount: 42
})

// 当前位置
const currentLocation = reactive({
  latitude: 39.9042,
  longitude: 116.4074,
  altitude: 43,
  address: '北京市朝阳区建国门外大街1号',
  timestamp: '刚刚'
})

// 位置记录
const locationRecords = ref([
  {
    id: 1,
    type: 'work',
    address: '北京市朝阳区建国门外大街1号',
    latitude: 39.9042,
    longitude: 116.4074,
    accuracy: 3.5,
    method: 'GPS',
    timestamp: '2024-01-15 14:30',
    note: '公司总部'
  },
  {
    id: 2,
    type: 'visit',
    address: '北京市海淀区中关村大街27号',
    latitude: 39.9795,
    longitude: 116.3329,
    accuracy: 8.2,
    method: 'A-GPS',
    timestamp: '2024-01-15 10:15',
    note: '客户拜访 - 张三公司'
  },
  {
    id: 3,
    type: 'home',
    address: '北京市西城区西单北大街131号',
    latitude: 39.9139,
    longitude: 116.3820,
    accuracy: 5.0,
    method: 'GPS',
    timestamp: '2024-01-15 08:00',
    note: '住宅地址'
  },
  {
    id: 4,
    type: 'other',
    address: '北京市东城区王府井大街138号',
    latitude: 39.9097,
    longitude: 116.4180,
    accuracy: 12.3,
    method: '网络定位',
    timestamp: '2024-01-14 19:45',
    note: '商场购物'
  }
])

// 快速目的地
const quickDestinations = ref([
  {
    id: 1,
    name: '公司总部',
    address: '建国门外大街1号',
    distance: 2.5,
    icon: 'ShopOutlined'
  },
  {
    id: 2,
    name: '客户A公司',
    address: '中关村大街27号',
    distance: 8.3,
    icon: 'UserOutlined'
  },
  {
    id: 3,
    name: '法院',
    address: '朝阳区人民法院',
    distance: 5.7,
    icon: 'BankOutlined'
  },
  {
    id: 4,
    name: '银行网点',
    address: '工商银行支行',
    distance: 1.2,
    icon: 'CreditCardOutlined'
  }
])

// 导航工具
const navigationTools = ref([
  { id: 1, name: '测距', icon: 'LineOutlined' },
  { id: 2, name: '标记', icon: 'AimOutlined' },
  { id: 3, name: '搜索', icon: 'SearchOutlined' },
  { id: 4, name: '分享', icon: 'ShareAltOutlined' }
])

// 轨迹列表
const trajectoryList = ref([
  {
    id: 1,
    name: '今日工作轨迹',
    date: '2024-01-15',
    distance: 15.6,
    duration: '4小时32分',
    points: 156
  },
  {
    id: 2,
    name: '昨日外访轨迹',
    date: '2024-01-14',
    distance: 23.8,
    duration: '6小时15分',
    points: 238
  },
  {
    id: 3,
    name: '周末出行轨迹',
    date: '2024-01-13',
    distance: 45.2,
    duration: '8小时45分',
    points: 452
  }
])

// 表单数据
const navigationForm = reactive({
  destination: '',
  mode: 'driving',
  preferences: ['fastest']
})

const locationSettings = reactive({
  mode: 'high',
  autoUpdate: true,
  updateInterval: 30,
  trackingEnabled: true
})

const advancedSettings = reactive({
  gpsAccuracy: 5,
  networkAssist: true,
  geofencing: false,
  historyDays: 30,
  privacy: ['location_history']
})

const saveLocationForm = reactive({
  name: '',
  type: 'other',
  note: ''
})

// 计算属性
const filteredLocationRecords = computed(() => {
  if (selectedRecordFilter.value === 'all') return locationRecords.value
  if (selectedRecordFilter.value === 'today') {
    return locationRecords.value.filter(record => 
      record.timestamp.includes('2024-01-15')
    )
  }
  return locationRecords.value.filter(record => record.type === selectedRecordFilter.value)
})

// 方法定义
const getGpsStatusColor = () => {
  return locationStats.gpsStatus === '已开启' ? '#52c41a' : '#ff4d4f'
}

const getRecordTypeClass = (type) => {
  return `record-type-${type}`
}

const getRecordTypeIcon = (type) => {
  const icons = {
    work: 'ShopOutlined',
    visit: 'UserOutlined',
    home: 'HomeOutlined',
    other: 'EnvironmentOutlined'
  }
  return icons[type] || 'EnvironmentOutlined'
}

const getRecordTypeText = (type) => {
  const texts = {
    work: '工作地点',
    visit: '外访地点',
    home: '住宅地址',
    other: '其他位置'
  }
  return texts[type] || type
}

const refreshLocation = () => {
  console.log('刷新位置')
  currentLocation.timestamp = '刚刚'
}

const getCurrentLocation = async () => {
  locating.value = true
  console.log('开始定位')
  
  // 模拟定位过程
  setTimeout(() => {
    currentLocation.latitude = 39.9042 + (Math.random() - 0.5) * 0.01
    currentLocation.longitude = 116.4074 + (Math.random() - 0.5) * 0.01
    currentLocation.timestamp = new Date().toLocaleString('zh-CN')
    locating.value = false
    console.log('定位完成')
  }, 2000)
}

const changeMapType = (type) => {
  console.log('切换地图类型:', type)
}

const centerToCurrentLocation = () => {
  console.log('居中到当前位置')
}

const zoomIn = () => {
  console.log('地图放大')
}

const zoomOut = () => {
  console.log('地图缩小')
}

const addLocationMarker = () => {
  console.log('添加位置标记')
}

const measureDistance = () => {
  console.log('测量距离')
}

const filterLocationRecords = () => {
  console.log('筛选位置记录:', selectedRecordFilter.value)
}

const exportLocationData = () => {
  console.log('导出位置数据')
}

const navigateToLocation = (record) => {
  console.log('导航到位置:', record)
}

const showLocationDetail = (record) => {
  console.log('查看位置详情:', record)
}

const editLocationRecord = (record) => {
  console.log('编辑位置记录:', record)
}

const shareLocation = (record) => {
  console.log('分享位置:', record)
}

const deleteLocationRecord = (record) => {
  const index = locationRecords.value.findIndex(r => r.id === record.id)
  if (index > -1) {
    locationRecords.value.splice(index, 1)
  }
}

const saveCurrentLocation = () => {
  saveLocationForm.name = ''
  saveLocationForm.type = 'other'
  saveLocationForm.note = ''
  showSaveLocationModal.value = true
}

const shareCurrentLocation = () => {
  const locationText = `我的位置: ${currentLocation.address}\n坐标: ${currentLocation.latitude}, ${currentLocation.longitude}`
  console.log('分享当前位置:', locationText)
}

const copyLocationInfo = () => {
  const coords = `${currentLocation.latitude}, ${currentLocation.longitude}`
  console.log('复制坐标:', coords)
}

const navigateToDestination = (destination) => {
  navigationForm.destination = destination.address
  showNavigationModal.value = true
}

const useNavigationTool = (tool) => {
  console.log('使用导航工具:', tool)
}

const toggleTracking = (enabled) => {
  console.log('切换轨迹记录:', enabled)
}

const viewTrajectory = (trajectory) => {
  console.log('查看轨迹:', trajectory)
}

const exportTrajectory = (trajectory) => {
  console.log('导出轨迹:', trajectory)
}

const deleteTrajectory = (trajectory) => {
  const index = trajectoryList.value.findIndex(t => t.id === trajectory.id)
  if (index > -1) {
    trajectoryList.value.splice(index, 1)
  }
}

const saveLocationSettings = () => {
  console.log('保存位置设置:', locationSettings)
}

const startNavigation = () => {
  console.log('开始导航:', navigationForm)
  showNavigationModal.value = false
}

const saveAdvancedSettings = () => {
  console.log('保存高级设置:', advancedSettings)
  showLocationSettings.value = false
}

const confirmSaveLocation = () => {
  const newRecord = {
    id: Date.now(),
    type: saveLocationForm.type,
    address: currentLocation.address,
    latitude: currentLocation.latitude,
    longitude: currentLocation.longitude,
    accuracy: locationStats.accuracy,
    method: 'GPS',
    timestamp: new Date().toLocaleString('zh-CN'),
    note: saveLocationForm.note
  }
  locationRecords.value.unshift(newRecord)
  locationStats.recordCount++
  showSaveLocationModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (trajectoryChart.value) {
      const chart = echarts.init(trajectoryChart.value)
      chart.setOption({
        title: { text: '轨迹统计分析', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['移动距离', '停留时间', '定位精度'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00']
        },
        yAxis: [
          { 
            type: 'value',
            name: '距离(km)',
            position: 'left'
          },
          {
            type: 'value',
            name: '时间(min)/精度(m)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '移动距离',
            type: 'line',
            yAxisIndex: 0,
            data: [0, 3.2, 5.8, 8.5, 12.3, 15.6],
            itemStyle: { color: '#1890ff' },
            smooth: true
          },
          {
            name: '停留时间',
            type: 'bar',
            yAxisIndex: 1,
            data: [0, 15, 45, 30, 20, 10],
            itemStyle: { color: '#52c41a' }
          },
          {
            name: '定位精度',
            type: 'line',
            yAxisIndex: 1,
            data: [3.2, 5.1, 4.8, 6.2, 4.5, 5.2],
            itemStyle: { color: '#faad14' },
            smooth: true
          }
        ]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshLocation()
  initCharts()
})
</script>

<style scoped>
.location-service {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.location-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.location-status {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.location-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.map-container {
  height: 400px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.map-placeholder {
  height: 100%;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.map-info {
  text-align: center;
  margin-bottom: 24px;
}

.map-info h3 {
  margin: 0 0 16px 0;
  color: #1890ff;
}

.map-info p {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
}

.map-controls {
  display: flex;
  gap: 8px;
}

.location-records {
  max-height: 500px;
  overflow-y: auto;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
}

.record-content {
  flex: 1;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-type {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #262626;
}

.record-type-work { color: #1890ff; }
.record-type-visit { color: #52c41a; }
.record-type-home { color: #722ed1; }
.record-type-other { color: #faad14; }

.record-time {
  font-size: 12px;
  color: #999;
}

.record-address {
  color: #262626;
  margin-bottom: 4px;
}

.record-coordinates {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  margin-bottom: 8px;
}

.record-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #999;
}

.record-actions {
  display: flex;
  gap: 4px;
  margin-left: 16px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.current-location {
  padding: 8px 0;
}

.location-display {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 16px;
}

.location-icon {
  flex-shrink: 0;
}

.location-details {
  flex: 1;
}

.location-address {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.location-coords {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  margin-bottom: 4px;
}

.location-update {
  font-size: 11px;
  color: #999;
}

.location-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.navigation-assistant {
  padding: 8px 0;
}

.quick-destinations h4,
.navigation-tools h4 {
  margin: 0 0 12px 0;
  color: #262626;
}

.destination-list {
  margin-bottom: 24px;
}

.destination-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.destination-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.destination-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #1890ff;
}

.destination-info {
  flex: 1;
}

.destination-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.destination-address {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.destination-distance {
  font-size: 10px;
  color: #999;
}

.tool-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.tool-icon {
  font-size: 20px;
  color: #1890ff;
  margin-bottom: 6px;
}

.tool-name {
  font-size: 12px;
  color: #666;
}

.trajectory-management {
  padding: 8px 0;
}

.tracking-status {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #f5f5f5;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 13px;
  color: #666;
}

.status-value {
  font-size: 13px;
  color: #262626;
  font-weight: 500;
}

.status-value.tracking {
  color: #52c41a;
}

.trajectory-list h4 {
  margin: 0 0 12px 0;
  color: #262626;
}

.trajectory-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.trajectory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.trajectory-name {
  font-weight: 500;
  color: #262626;
}

.trajectory-date {
  font-size: 11px;
  color: #999;
}

.trajectory-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #666;
  margin-bottom: 8px;
}

.trajectory-actions {
  display: flex;
  gap: 8px;
}

.location-settings {
  padding: 8px 0;

  .ant-btn {
    height: 36px;
    margin-top: 16px;
    font-size: 14px;
  }
}
</style>