<template>
  <div class="offline-sync">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="sync-info">
          <h2>离线同步</h2>
          <p class="sync-desc">智能数据同步，离线工作无忧，冲突自动处理</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshSyncStatus">
            <template #icon><ReloadOutlined /></template>
            刷新状态
          </a-button>
          <a-button @click="showSyncSettings = true">
            <template #icon><SettingOutlined /></template>
            同步设置
          </a-button>
          <a-button type="primary" @click="manualSync" :loading="syncing">
            <template #icon><CloudSyncOutlined /></template>
            {{ syncing ? '同步中' : '立即同步' }}
          </a-button>
        </div>
      </div>
    </div>

    <!-- 同步状态 -->
    <div class="sync-status">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="网络状态" 
              :value="syncStats.networkStatus" 
              :value-style="{ color: getNetworkStatusColor() }"
            >
              <template #prefix><WifiOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="待同步" 
              :value="syncStats.pendingItems" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="同步冲突" 
              :value="syncStats.conflicts" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix><WarningOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="离线数据" 
              :value="syncStats.offlineData" 
              suffix="MB"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><DatabaseOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 同步队列 -->
        <a-card title="同步队列" class="sync-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedQueueFilter" style="width: 120px" @change="filterSyncQueue">
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="pending">待同步</a-select-option>
                <a-select-option value="syncing">同步中</a-select-option>
                <a-select-option value="failed">同步失败</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
              <a-button size="small" @click="clearCompletedItems">
                <template #icon><DeleteOutlined /></template>
                清理已完成
              </a-button>
            </a-space>
          </template>
          
          <div class="sync-queue">
            <div v-for="item in filteredSyncQueue" :key="item.id" class="sync-item">
              <div class="sync-item-content">
                <div class="sync-item-header">
                  <div class="sync-item-type" :class="getSyncTypeClass(item.type)">
                    <component :is="getSyncTypeIcon(item.type)" />
                    <span>{{ getSyncTypeText(item.type) }}</span>
                  </div>
                  <div class="sync-item-status">
                    <a-tag :color="getSyncStatusColor(item.status)">
                      {{ getSyncStatusText(item.status) }}
                    </a-tag>
                  </div>
                </div>
                <div class="sync-item-description">{{ item.description }}</div>
                <div class="sync-item-meta">
                  <span class="sync-item-time">{{ item.timestamp }}</span>
                  <span class="sync-item-size">{{ item.dataSize }}</span>
                  <span class="sync-item-priority">优先级: {{ getPriorityText(item.priority) }}</span>
                </div>
                <div v-if="item.status === 'syncing'" class="sync-progress">
                  <a-progress :percent="item.progress" size="small" />
                </div>
                <div v-if="item.error" class="sync-error">
                  <ExclamationCircleOutlined style="color: #ff4d4f; margin-right: 4px;" />
                  {{ item.error }}
                </div>
              </div>
              <div class="sync-item-actions">
                <a-button v-if="item.status === 'pending'" type="link" size="small" @click="syncItem(item)">
                  同步
                </a-button>
                <a-button v-if="item.status === 'failed'" type="link" size="small" @click="retrySyncItem(item)">
                  重试
                </a-button>
                <a-button v-if="item.status === 'syncing'" type="link" size="small" @click="pauseSyncItem(item)">
                  暂停
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewSyncDetail(item)">查看详情</a-menu-item>
                      <a-menu-item @click="changePriority(item)">修改优先级</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="removeSyncItem(item)" danger>移除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
            <a-empty v-if="filteredSyncQueue.length === 0" description="暂无同步任务" />
          </div>
        </a-card>

        <!-- 冲突解决 -->
        <a-card title="冲突解决" class="sync-card">
          <template #extra>
            <a-badge :count="conflicts.filter(c => !c.resolved).length" />
          </template>
          
          <div class="conflict-resolution">
            <div v-for="conflict in conflicts" :key="conflict.id" class="conflict-item" :class="{ 'resolved': conflict.resolved }">
              <div class="conflict-header">
                <div class="conflict-type">
                  <ExclamationCircleOutlined style="color: #faad14; margin-right: 8px;" />
                  {{ conflict.type }}冲突
                </div>
                <div class="conflict-time">{{ conflict.timestamp }}</div>
              </div>
              <div class="conflict-description">{{ conflict.description }}</div>
              <div class="conflict-details">
                <div class="conflict-versions">
                  <div class="version-item">
                    <h5>本地版本</h5>
                    <div class="version-content">{{ conflict.localVersion.content }}</div>
                    <div class="version-meta">
                      修改时间: {{ conflict.localVersion.timestamp }}
                      | 修改人: {{ conflict.localVersion.modifier }}
                    </div>
                  </div>
                  <div class="version-item">
                    <h5>服务器版本</h5>
                    <div class="version-content">{{ conflict.serverVersion.content }}</div>
                    <div class="version-meta">
                      修改时间: {{ conflict.serverVersion.timestamp }}
                      | 修改人: {{ conflict.serverVersion.modifier }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="conflict-actions" v-if="!conflict.resolved">
                <a-button type="primary" @click="resolveConflict(conflict, 'local')">
                  使用本地版本
                </a-button>
                <a-button @click="resolveConflict(conflict, 'server')">
                  使用服务器版本
                </a-button>
                <a-button @click="showMergeModal(conflict)">
                  手动合并
                </a-button>
                <a-button @click="viewConflictDetail(conflict)">
                  查看详情
                </a-button>
              </div>
              <div class="conflict-resolution-info" v-if="conflict.resolved">
                <CheckCircleOutlined style="color: #52c41a; margin-right: 4px;" />
                已解决: {{ conflict.resolution.method }} | {{ conflict.resolution.timestamp }}
              </div>
            </div>
            <a-empty v-if="conflicts.length === 0" description="暂无冲突" />
          </div>
        </a-card>

        <!-- 同步历史 -->
        <a-card title="同步历史" class="sync-card">
          <div class="sync-history">
            <div ref="syncHistoryChart" class="chart-container"></div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 离线模式 -->
        <a-card title="离线模式" class="sync-card">
          <div class="offline-mode">
            <div class="mode-status">
              <div class="status-indicator" :class="{ 'online': isOnline, 'offline': !isOnline }">
                <component :is="isOnline ? 'WifiOutlined' : 'DisconnectOutlined'" />
              </div>
              <div class="status-content">
                <div class="status-label">{{ isOnline ? '在线模式' : '离线模式' }}</div>
                <div class="status-desc">{{ isOnline ? '实时同步数据' : '本地缓存工作' }}</div>
              </div>
              <div class="mode-toggle">
                <a-switch 
                  v-model:checked="offlineModeEnabled" 
                  @change="toggleOfflineMode"
                  checked-children="离线"
                  un-checked-children="在线"
                />
              </div>
            </div>
            
            <div class="offline-capabilities">
              <h4>离线功能</h4>
              <div class="capability-list">
                <div v-for="capability in offlineCapabilities" :key="capability.id" class="capability-item">
                  <div class="capability-icon" :class="{ 'enabled': capability.enabled }">
                    <component :is="capability.icon" />
                  </div>
                  <div class="capability-content">
                    <div class="capability-name">{{ capability.name }}</div>
                    <div class="capability-desc">{{ capability.description }}</div>
                  </div>
                  <div class="capability-status">
                    <a-tag :color="capability.enabled ? 'green' : 'gray'">
                      {{ capability.enabled ? '可用' : '不可用' }}
                    </a-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 数据管理 -->
        <a-card title="数据管理" class="sync-card">
          <div class="data-management">
            <div class="storage-usage">
              <h4>存储使用情况</h4>
              <div class="storage-item">
                <div class="storage-label">本地缓存</div>
                <div class="storage-bar">
                  <a-progress 
                    :percent="Math.round((storageUsage.cache / storageUsage.total) * 100)" 
                    size="small"
                    :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }"
                  />
                </div>
                <div class="storage-size">{{ storageUsage.cache }}MB / {{ storageUsage.total }}MB</div>
              </div>
              
              <div class="storage-breakdown">
                <div v-for="item in storageBreakdown" :key="item.type" class="breakdown-item">
                  <div class="breakdown-type">{{ item.name }}</div>
                  <div class="breakdown-size">{{ item.size }}MB</div>
                  <div class="breakdown-percentage">{{ item.percentage }}%</div>
                </div>
              </div>
            </div>
            
            <div class="data-actions">
              <a-button block @click="clearCache">
                <template #icon><DeleteOutlined /></template>
                清理缓存
              </a-button>
              <a-button block @click="exportData">
                <template #icon><DownloadOutlined /></template>
                导出数据
              </a-button>
              <a-button block @click="compactData">
                <template #icon><CompressOutlined /></template>
                压缩数据
              </a-button>
            </div>
          </div>
        </a-card>

        <!-- 版本管理 -->
        <a-card title="版本管理" class="sync-card">
          <div class="version-management">
            <div class="current-version">
              <h4>当前版本</h4>
              <div class="version-info">
                <div class="version-number">v{{ currentVersion.number }}</div>
                <div class="version-date">{{ currentVersion.date }}</div>
                <div class="version-status">
                  <a-tag :color="currentVersion.isLatest ? 'green' : 'orange'">
                    {{ currentVersion.isLatest ? '最新版本' : '有更新' }}
                  </a-tag>
                </div>
              </div>
            </div>
            
            <div class="version-history">
              <h4>版本历史</h4>
              <div class="version-list">
                <div v-for="version in versionHistory" :key="version.id" class="version-item">
                  <div class="version-header">
                    <span class="version-name">v{{ version.number }}</span>
                    <span class="version-date">{{ version.date }}</span>
                  </div>
                  <div class="version-changes">{{ version.changes }}</div>
                  <div class="version-actions">
                    <a-button type="link" size="small" @click="viewVersionDetail(version)">
                      查看详情
                    </a-button>
                    <a-button v-if="!version.isCurrent" type="link" size="small" @click="rollbackVersion(version)">
                      回滚
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 同步配置 -->
        <a-card title="同步配置" class="sync-card">
          <div class="sync-config">
            <a-form layout="vertical" size="small">
              <a-form-item label="同步频率">
                <a-select v-model:value="syncConfig.frequency">
                  <a-select-option value="realtime">实时同步</a-select-option>
                  <a-select-option value="5min">每5分钟</a-select-option>
                  <a-select-option value="30min">每30分钟</a-select-option>
                  <a-select-option value="1hour">每小时</a-select-option>
                  <a-select-option value="manual">手动同步</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="同步范围">
                <a-checkbox-group v-model:value="syncConfig.scope">
                  <a-checkbox value="cases">案件数据</a-checkbox>
                  <a-checkbox value="customers">客户数据</a-checkbox>
                  <a-checkbox value="records">催收记录</a-checkbox>
                  <a-checkbox value="files">文件附件</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="冲突处理">
                <a-radio-group v-model:value="syncConfig.conflictResolution">
                  <a-radio value="manual">手动处理</a-radio>
                  <a-radio value="server">优先服务器</a-radio>
                  <a-radio value="local">优先本地</a-radio>
                  <a-radio value="timestamp">按时间戳</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="仅WiFi同步">
                <a-switch v-model:checked="syncConfig.wifiOnly" />
              </a-form-item>
              <a-button type="primary" block @click="saveSyncConfig">
                保存配置
              </a-button>
            </a-form>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 同步设置模态框 -->
    <a-modal
      v-model:open="showSyncSettings"
      title="同步设置"
      width="600px"
      @ok="saveAdvancedSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="最大缓存大小">
          <a-slider 
            v-model:value="advancedSettings.maxCacheSize" 
            :min="100" 
            :max="2000"
            :marks="{ 100: '100MB', 500: '500MB', 1000: '1GB', 2000: '2GB' }"
          />
        </a-form-item>
        <a-form-item label="同步超时时间">
          <a-input-number 
            v-model:value="advancedSettings.syncTimeout" 
            :min="10" 
            :max="300"
            addon-after="秒"
          />
        </a-form-item>
        <a-form-item label="重试次数">
          <a-input-number 
            v-model:value="advancedSettings.retryCount" 
            :min="1" 
            :max="10"
          />
        </a-form-item>
        <a-form-item label="压缩传输">
          <a-switch v-model:checked="advancedSettings.compression" />
          <span style="margin-left: 8px;">启用数据压缩传输</span>
        </a-form-item>
        <a-form-item label="增量同步">
          <a-switch v-model:checked="advancedSettings.incrementalSync" />
          <span style="margin-left: 8px;">仅同步变更数据</span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 合并模态框 -->
    <a-modal
      v-model:open="showMergeConflict"
      title="手动合并冲突"
      width="800px"
      @ok="performMerge"
    >
      <div v-if="selectedConflict" class="merge-interface">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>本地版本</h4>
            <a-textarea 
              v-model:value="mergeData.localContent" 
              :rows="10"
              placeholder="本地版本内容"
            />
          </a-col>
          <a-col :span="12">
            <h4>服务器版本</h4>
            <a-textarea 
              v-model:value="mergeData.serverContent" 
              :rows="10"
              placeholder="服务器版本内容"
            />
          </a-col>
        </a-row>
        <div style="margin-top: 16px;">
          <h4>合并结果</h4>
          <a-textarea 
            v-model:value="mergeData.mergedContent" 
            :rows="8"
            placeholder="请编辑合并后的内容"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined,
  CloudSyncOutlined,
  WifiOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  DatabaseOutlined,
  DeleteOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  DisconnectOutlined,
  DownloadOutlined,
  CompressOutlined,
  EyeOutlined,
  EditOutlined,
  CameraOutlined,
  UserOutlined,
  PaperClipOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showSyncSettings = ref(false)
const showMergeConflict = ref(false)
const selectedQueueFilter = ref('all')
const syncing = ref(false)
const isOnline = ref(true)
const offlineModeEnabled = ref(false)
const selectedConflict = ref(null)

// 图表引用
const syncHistoryChart = ref(null)

// 同步统计
const syncStats = reactive({
  networkStatus: '已连接',
  pendingItems: 12,
  conflicts: 3,
  offlineData: 156.7
})

// 同步队列
const syncQueue = ref([
  {
    id: 1,
    type: 'case',
    status: 'pending',
    description: '案件 C2024001 催收记录更新',
    timestamp: '2024-01-15 10:30',
    dataSize: '2.3KB',
    priority: 'high',
    progress: 0,
    error: null
  },
  {
    id: 2,
    type: 'customer',
    status: 'syncing',
    description: '客户张三联系方式修改',
    timestamp: '2024-01-15 10:25',
    dataSize: '1.2KB',
    priority: 'medium',
    progress: 65,
    error: null
  },
  {
    id: 3,
    type: 'file',
    status: 'failed',
    description: '催收录音文件上传',
    timestamp: '2024-01-15 10:20',
    dataSize: '8.7MB',
    priority: 'low',
    progress: 0,
    error: '网络连接超时'
  },
  {
    id: 4,
    type: 'record',
    status: 'completed',
    description: '外访记录同步',
    timestamp: '2024-01-15 10:15',
    dataSize: '5.1KB',
    priority: 'medium',
    progress: 100,
    error: null
  }
])

// 冲突列表
const conflicts = ref([
  {
    id: 1,
    type: '数据',
    description: '客户张三联系电话存在冲突',
    timestamp: '2024-01-15 10:35',
    resolved: false,
    localVersion: {
      content: '联系电话: 138****1234',
      timestamp: '2024-01-15 10:30',
      modifier: '李催收员'
    },
    serverVersion: {
      content: '联系电话: 139****5678',
      timestamp: '2024-01-15 10:32',
      modifier: '王催收员'
    },
    resolution: null
  },
  {
    id: 2,
    type: '文件',
    description: '催收记录文件版本冲突',
    timestamp: '2024-01-15 09:45',
    resolved: true,
    localVersion: {
      content: '催收结果: 客户承诺明日还款',
      timestamp: '2024-01-15 09:40',
      modifier: '张催收员'
    },
    serverVersion: {
      content: '催收结果: 客户要求延期一周',
      timestamp: '2024-01-15 09:42',
      modifier: '李催收员'
    },
    resolution: {
      method: '使用服务器版本',
      timestamp: '2024-01-15 09:50',
      resolver: '管理员'
    }
  }
])

// 离线功能
const offlineCapabilities = ref([
  { id: 1, name: '查看案件', description: '浏览已缓存的案件信息', icon: 'EyeOutlined', enabled: true },
  { id: 2, name: '添加记录', description: '新增催收记录到本地', icon: 'EditOutlined', enabled: true },
  { id: 3, name: '拍照取证', description: '现场拍照保存到本地', icon: 'CameraOutlined', enabled: true },
  { id: 4, name: 'GPS定位', description: '记录当前位置信息', icon: 'EnvironmentOutlined', enabled: false }
])

// 存储使用情况
const storageUsage = reactive({
  cache: 156.7,
  total: 512
})

const storageBreakdown = ref([
  { type: 'cases', name: '案件数据', size: 45.2, percentage: 29 },
  { type: 'customers', name: '客户资料', size: 38.6, percentage: 25 },
  { type: 'records', name: '催收记录', size: 32.1, percentage: 20 },
  { type: 'files', name: '文件附件', size: 25.8, percentage: 16 },
  { type: 'system', name: '系统缓存', size: 15.0, percentage: 10 }
])

// 版本管理
const currentVersion = reactive({
  number: '2.1.3',
  date: '2024-01-15',
  isLatest: false
})

const versionHistory = ref([
  {
    id: 1,
    number: '2.1.4',
    date: '2024-01-20',
    changes: '修复同步冲突处理问题，优化离线模式',
    isCurrent: false
  },
  {
    id: 2,
    number: '2.1.3',
    date: '2024-01-15',
    changes: '新增批量同步功能，改进数据压缩算法',
    isCurrent: true
  },
  {
    id: 3,
    number: '2.1.2',
    date: '2024-01-10',
    changes: '优化离线存储性能，修复数据丢失问题',
    isCurrent: false
  }
])

// 同步配置
const syncConfig = reactive({
  frequency: 'realtime',
  scope: ['cases', 'customers', 'records'],
  conflictResolution: 'manual',
  wifiOnly: true
})

// 高级设置
const advancedSettings = reactive({
  maxCacheSize: 500,
  syncTimeout: 60,
  retryCount: 3,
  compression: true,
  incrementalSync: true
})

// 合并数据
const mergeData = reactive({
  localContent: '',
  serverContent: '',
  mergedContent: ''
})

// 计算属性
const filteredSyncQueue = computed(() => {
  if (selectedQueueFilter.value === 'all') return syncQueue.value
  return syncQueue.value.filter(item => item.status === selectedQueueFilter.value)
})

// 方法定义
const getNetworkStatusColor = () => {
  return isOnline.value ? '#52c41a' : '#ff4d4f'
}

const getSyncTypeClass = (type) => {
  return `sync-type-${type}`
}

const getSyncTypeIcon = (type) => {
  const icons = {
    case: 'FileTextOutlined',
    customer: 'UserOutlined',
    record: 'EditOutlined',
    file: 'PaperClipOutlined'
  }
  return icons[type] || 'FileOutlined'
}

const getSyncTypeText = (type) => {
  const texts = {
    case: '案件',
    customer: '客户',
    record: '记录',
    file: '文件'
  }
  return texts[type] || type
}

const getSyncStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    syncing: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getSyncStatusText = (status) => {
  const texts = {
    pending: '待同步',
    syncing: '同步中',
    completed: '已完成',
    failed: '同步失败'
  }
  return texts[status] || status
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const refreshSyncStatus = () => {
  console.log('刷新同步状态')
  initCharts()
}

const manualSync = async () => {
  syncing.value = true
  console.log('开始手动同步')
  
  setTimeout(() => {
    syncing.value = false
    console.log('同步完成')
  }, 3000)
}

const filterSyncQueue = () => {
  console.log('筛选同步队列:', selectedQueueFilter.value)
}

const clearCompletedItems = () => {
  const completedCount = syncQueue.value.filter(item => item.status === 'completed').length
  syncQueue.value = syncQueue.value.filter(item => item.status !== 'completed')
  console.log(`已清理${completedCount}个完成项`)
}

const syncItem = (item) => {
  item.status = 'syncing'
  item.progress = 0
  
  const interval = setInterval(() => {
    item.progress += 10
    if (item.progress >= 100) {
      clearInterval(interval)
      item.status = 'completed'
    }
  }, 200)
}

const retrySyncItem = (item) => {
  item.status = 'pending'
  item.error = null
  syncItem(item)
}

const pauseSyncItem = (item) => {
  item.status = 'pending'
}

const removeSyncItem = (item) => {
  const index = syncQueue.value.findIndex(i => i.id === item.id)
  if (index > -1) {
    syncQueue.value.splice(index, 1)
  }
}

const viewSyncDetail = (item) => {
  console.log('查看同步详情:', item)
}

const changePriority = (item) => {
  console.log('修改优先级:', item)
}

const resolveConflict = (conflict, resolution) => {
  conflict.resolved = true
  conflict.resolution = {
    method: resolution === 'local' ? '使用本地版本' : '使用服务器版本',
    timestamp: new Date().toLocaleString('zh-CN'),
    resolver: '当前用户'
  }
  syncStats.conflicts--
}

const showMergeModal = (conflict) => {
  selectedConflict.value = conflict
  mergeData.localContent = conflict.localVersion.content
  mergeData.serverContent = conflict.serverVersion.content
  mergeData.mergedContent = ''
  showMergeConflict.value = true
}

const performMerge = () => {
  if (selectedConflict.value) {
    selectedConflict.value.resolved = true
    selectedConflict.value.resolution = {
      method: '手动合并',
      timestamp: new Date().toLocaleString('zh-CN'),
      resolver: '当前用户'
    }
    syncStats.conflicts--
  }
  showMergeConflict.value = false
}

const viewConflictDetail = (conflict) => {
  console.log('查看冲突详情:', conflict)
}

const toggleOfflineMode = (enabled) => {
  console.log('切换离线模式:', enabled)
  isOnline.value = !enabled
}

const clearCache = () => {
  console.log('清理缓存')
  storageUsage.cache = 0
}

const exportData = () => {
  console.log('导出数据')
}

const compactData = () => {
  console.log('压缩数据')
  storageUsage.cache = Math.max(0, storageUsage.cache - 20)
}

const viewVersionDetail = (version) => {
  console.log('查看版本详情:', version)
}

const rollbackVersion = (version) => {
  console.log('回滚版本:', version)
}

const saveSyncConfig = () => {
  console.log('保存同步配置:', syncConfig)
}

const saveAdvancedSettings = () => {
  console.log('保存高级设置:', advancedSettings)
  showSyncSettings.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (syncHistoryChart.value) {
      const chart = echarts.init(syncHistoryChart.value)
      chart.setOption({
        title: { text: '同步历史统计', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['成功', '失败', '冲突'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '成功',
            type: 'line',
            data: [89, 92, 95, 91, 94, 96],
            itemStyle: { color: '#52c41a' },
            smooth: true
          },
          {
            name: '失败',
            type: 'line',
            data: [8, 6, 4, 7, 5, 3],
            itemStyle: { color: '#ff4d4f' },
            smooth: true
          },
          {
            name: '冲突',
            type: 'line',
            data: [3, 2, 1, 2, 1, 1],
            itemStyle: { color: '#faad14' },
            smooth: true
          }
        ]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshSyncStatus()
})
</script>

<style scoped>
.offline-sync {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.sync-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.sync-status {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sync-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sync-queue {
  max-height: 500px;
  overflow-y: auto;
}

.sync-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
}

.sync-item-content {
  flex: 1;
}

.sync-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sync-item-type {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #262626;
}

.sync-type-case { color: #1890ff; }
.sync-type-customer { color: #52c41a; }
.sync-type-record { color: #faad14; }
.sync-type-file { color: #722ed1; }

.sync-item-description {
  color: #666;
  margin-bottom: 8px;
}

.sync-item-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.sync-progress {
  margin-bottom: 8px;
}

.sync-error {
  color: #ff4d4f;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.sync-item-actions {
  display: flex;
  gap: 4px;
  margin-left: 16px;
}

.conflict-resolution {
  max-height: 400px;
  overflow-y: auto;
}

.conflict-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.conflict-item.resolved {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.conflict-type {
  font-weight: 500;
  color: #262626;
  display: flex;
  align-items: center;
}

.conflict-time {
  font-size: 12px;
  color: #999;
}

.conflict-description {
  color: #666;
  margin-bottom: 16px;
}

.conflict-details {
  margin-bottom: 16px;
}

.conflict-versions {
  display: flex;
  gap: 16px;
}

.version-item {
  flex: 1;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
}

.version-item h5 {
  margin: 0 0 8px 0;
  color: #262626;
}

.version-content {
  background-color: #fafafa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-family: monospace;
}

.version-meta {
  font-size: 11px;
  color: #999;
}

.conflict-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.conflict-resolution-info {
  color: #52c41a;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.offline-mode {
  padding: 8px 0;
}

.mode-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 16px;
}

.status-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.status-indicator.online {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-indicator.offline {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-content {
  flex: 1;
}

.status-label {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.status-desc {
  font-size: 12px;
  color: #666;
}

.offline-capabilities h4 {
  margin: 0 0 12px 0;
  color: #262626;
}

.capability-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.capability-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.capability-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #999;
}

.capability-icon.enabled {
  color: #52c41a;
}

.capability-content {
  flex: 1;
}

.capability-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.capability-desc {
  font-size: 11px;
  color: #666;
}

.data-management {
  padding: 8px 0;
}

.storage-usage h4 {
  margin: 0 0 12px 0;
  color: #262626;
}

.storage-item {
  margin-bottom: 16px;
}

.storage-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.storage-bar {
  margin-bottom: 4px;
}

.storage-size {
  font-size: 11px;
  color: #999;
  text-align: right;
}

.storage-breakdown {
  margin-bottom: 16px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
}

.breakdown-type {
  color: #666;
}

.breakdown-size {
  color: #262626;
}

.breakdown-percentage {
  color: #999;
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.version-management {
  padding: 8px 0;
}

.current-version h4,
.version-history h4 {
  margin: 0 0 12px 0;
  color: #262626;
}

.version-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 16px;
}

.version-number {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.version-date {
  font-size: 12px;
  color: #666;
}

.version-list {
  max-height: 200px;
  overflow-y: auto;
}

.version-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.version-name {
  font-weight: 500;
  color: #262626;
}

.version-changes {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.version-actions {
  display: flex;
  gap: 8px;
}

.sync-config {
  padding: 8px 0;

  .ant-btn {
    height: 36px;
    margin-top: 16px;
    font-size: 14px;
  }
}

.merge-interface h4 {
  margin: 0 0 8px 0;
  color: #262626;
}
</style>