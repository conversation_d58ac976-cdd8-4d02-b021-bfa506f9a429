<template>
  <div class="realtime-communication">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="communication-info">
          <h2>实时通讯</h2>
          <p class="communication-desc">即时通讯、语音视频、协作办公一体化平台</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshStatus">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showSettingsModal = true">
            <template #icon><SettingOutlined /></template>
            设置
          </a-button>
          <a-button type="primary" @click="showNewChatModal = true">
            <template #icon><MessageOutlined /></template>
            新建对话
          </a-button>
        </div>
      </div>
    </div>

    <!-- 通讯状态 -->
    <div class="communication-status">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="在线用户" 
              :value="communicationStats.onlineUsers" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><UserOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="活跃对话" 
              :value="communicationStats.activeChats" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><MessageOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="通话中" 
              :value="communicationStats.activeCalls" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><PhoneOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="会议室" 
              :value="communicationStats.meetingRooms" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><VideoCameraOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 即时消息 -->
        <a-card title="即时消息" class="communication-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="selectedChatFilter" style="width: 120px" @change="filterChats">
                <a-select-option value="all">全部对话</a-select-option>
                <a-select-option value="unread">未读消息</a-select-option>
                <a-select-option value="group">群组对话</a-select-option>
                <a-select-option value="private">私人对话</a-select-option>
              </a-select>
              <a-button size="small" @click="markAllAsRead">
                <template #icon><CheckOutlined /></template>
                全部已读
              </a-button>
            </a-space>
          </template>
          
          <div class="chat-container">
            <div class="chat-list">
              <div 
                v-for="chat in filteredChats" 
                :key="chat.id" 
                class="chat-item" 
                :class="{ 'active': selectedChat?.id === chat.id, 'unread': chat.unreadCount > 0 }"
                @click="selectChat(chat)"
              >
                <div class="chat-avatar">
                  <a-avatar :size="40" :src="chat.avatar">
                    {{ chat.name.charAt(0) }}
                  </a-avatar>
                  <div v-if="chat.isOnline" class="online-indicator"></div>
                </div>
                <div class="chat-content">
                  <div class="chat-header">
                    <span class="chat-name">{{ chat.name }}</span>
                    <span class="chat-time">{{ chat.lastMessageTime }}</span>
                  </div>
                  <div class="chat-preview">{{ chat.lastMessage }}</div>
                  <div class="chat-meta">
                    <a-tag v-if="chat.type === 'group'" size="small">群组</a-tag>
                    <a-badge v-if="chat.unreadCount > 0" :count="chat.unreadCount" />
                  </div>
                </div>
              </div>
            </div>
            
            <div class="chat-window" v-if="selectedChat">
              <div class="chat-window-header">
                <div class="chat-info">
                  <a-avatar :src="selectedChat.avatar">{{ selectedChat.name.charAt(0) }}</a-avatar>
                  <div class="chat-details">
                    <h4>{{ selectedChat.name }}</h4>
                    <span class="chat-status">{{ selectedChat.isOnline ? '在线' : '离线' }}</span>
                  </div>
                </div>
                <div class="chat-actions">
                  <a-button type="text" @click="startVoiceCall(selectedChat)">
                    <template #icon><PhoneOutlined /></template>
                  </a-button>
                  <a-button type="text" @click="startVideoCall(selectedChat)">
                    <template #icon><VideoCameraOutlined /></template>
                  </a-button>
                  <a-button type="text" @click="showChatInfo = true">
                    <template #icon><InfoCircleOutlined /></template>
                  </a-button>
                </div>
              </div>
              
              <div class="chat-messages">
                <div v-for="message in selectedChat.messages" :key="message.id" class="message-item" :class="{ 'own-message': message.isOwn }">
                  <div class="message-content">
                    <div class="message-bubble">
                      <div v-if="message.type === 'text'" class="message-text">{{ message.content }}</div>
                      <div v-else-if="message.type === 'image'" class="message-image">
                        <img :src="message.content" alt="图片消息" />
                      </div>
                      <div v-else-if="message.type === 'file'" class="message-file">
                        <FileOutlined />
                        <span>{{ message.fileName }}</span>
                      </div>
                    </div>
                    <div class="message-time">{{ message.time }}</div>
                  </div>
                </div>
              </div>
              
              <div class="chat-input">
                <div class="input-toolbar">
                  <a-button type="text" @click="showEmojiPicker = !showEmojiPicker">
                    <template #icon><SmileOutlined /></template>
                  </a-button>
                  <a-upload :show-upload-list="false" @change="sendFile">
                    <a-button type="text">
                      <template #icon><PaperClipOutlined /></template>
                    </a-button>
                  </a-upload>
                  <a-button type="text" @click="sendImage">
                    <template #icon><PictureOutlined /></template>
                  </a-button>
                </div>
                <div class="input-area">
                  <a-textarea 
                    v-model:value="messageInput" 
                    placeholder="输入消息..." 
                    :rows="2"
                    @press-enter="sendMessage"
                  />
                  <a-button type="primary" @click="sendMessage">发送</a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 通话管理 -->
        <a-card title="通话管理" class="communication-card">
          <div class="call-management">
            <div class="active-calls" v-if="activeCalls.length > 0">
              <h4>进行中的通话</h4>
              <div v-for="call in activeCalls" :key="call.id" class="call-item">
                <div class="call-info">
                  <a-avatar :src="call.participant.avatar">{{ call.participant.name.charAt(0) }}</a-avatar>
                  <div class="call-details">
                    <div class="call-name">{{ call.participant.name }}</div>
                    <div class="call-duration">{{ call.duration }}</div>
                  </div>
                  <div class="call-type">
                    <component :is="call.type === 'voice' ? 'PhoneOutlined' : 'VideoCameraOutlined'" />
                  </div>
                </div>
                <div class="call-controls">
                  <a-button danger @click="endCall(call)">挂断</a-button>
                  <a-button @click="muteCall(call)">
                    {{ call.isMuted ? '取消静音' : '静音' }}
                  </a-button>
                </div>
              </div>
            </div>
            
            <div class="call-history">
              <h4>通话记录</h4>
              <div class="call-log">
                <div v-for="log in callHistory" :key="log.id" class="log-item">
                  <div class="log-icon" :class="getCallStatusClass(log.status)">
                    <component :is="log.type === 'voice' ? 'PhoneOutlined' : 'VideoCameraOutlined'" />
                  </div>
                  <div class="log-content">
                    <div class="log-name">{{ log.participant }}</div>
                    <div class="log-time">{{ log.time }}</div>
                  </div>
                  <div class="log-duration">{{ log.duration }}</div>
                  <div class="log-actions">
                    <a-button type="link" size="small" @click="callBack(log)">回拨</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 在线用户 -->
        <a-card title="在线用户" class="communication-card">
          <template #extra>
            <a-badge :count="onlineUsers.length" />
          </template>
          <div class="online-users">
            <div v-for="user in onlineUsers" :key="user.id" class="user-item" @click="startChat(user)">
              <a-avatar :src="user.avatar">{{ user.name.charAt(0) }}</a-avatar>
              <div class="user-info">
                <div class="user-name">{{ user.name }}</div>
                <div class="user-status">{{ user.department }}</div>
              </div>
              <div class="user-actions">
                <a-button type="text" size="small" @click.stop="startVoiceCall(user)">
                  <template #icon><PhoneOutlined /></template>
                </a-button>
                <a-button type="text" size="small" @click.stop="startVideoCall(user)">
                  <template #icon><VideoCameraOutlined /></template>
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 会议室 -->
        <a-card title="会议室" class="communication-card">
          <template #extra>
            <a-button size="small" @click="showCreateMeetingModal = true">
              <template #icon><PlusOutlined /></template>
              创建
            </a-button>
          </template>
          <div class="meeting-rooms">
            <div v-for="room in meetingRooms" :key="room.id" class="room-item">
              <div class="room-header">
                <h4>{{ room.name }}</h4>
                <a-tag :color="getRoomStatusColor(room.status)">{{ getRoomStatusText(room.status) }}</a-tag>
              </div>
              <div class="room-info">
                <div class="room-participants">
                  <UserOutlined style="margin-right: 4px;" />
                  {{ room.participants }}/{{ room.maxParticipants }}
                </div>
                <div class="room-time">{{ room.startTime }}</div>
              </div>
              <div class="room-actions">
                <a-button type="primary" size="small" @click="joinMeeting(room)">
                  {{ room.status === 'active' ? '加入' : '预约' }}
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 快速操作 -->
        <a-card title="快速操作" class="communication-card">
          <div class="quick-actions">
            <div class="action-grid">
              <div v-for="action in quickActions" :key="action.id" class="action-item" @click="performAction(action)">
                <div class="action-icon" :style="{ backgroundColor: action.color }">
                  <component :is="action.icon" />
                </div>
                <div class="action-name">{{ action.name }}</div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 协作工具 -->
        <a-card title="协作工具" class="communication-card">
          <div class="collaboration-tools">
            <div v-for="tool in collaborationTools" :key="tool.id" class="tool-item" @click="useTool(tool)">
              <div class="tool-icon">
                <component :is="tool.icon" />
              </div>
              <div class="tool-content">
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-desc">{{ tool.description }}</div>
              </div>
              <div class="tool-status" v-if="tool.isActive">
                <a-tag color="green">使用中</a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 新建对话模态框 -->
    <a-modal
      v-model:open="showNewChatModal"
      title="新建对话"
      @ok="createNewChat"
    >
      <a-form layout="vertical">
        <a-form-item label="对话类型" required>
          <a-radio-group v-model:value="newChatForm.type">
            <a-radio value="private">私人对话</a-radio>
            <a-radio value="group">群组对话</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="参与者" required>
          <a-select 
            v-model:value="newChatForm.participants" 
            mode="multiple" 
            placeholder="选择参与者"
            :filter-option="filterUsers"
          >
            <a-select-option v-for="user in allUsers" :key="user.id" :value="user.id">
              {{ user.name }} - {{ user.department }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="newChatForm.type === 'group'" label="群组名称">
          <a-input v-model:value="newChatForm.groupName" placeholder="请输入群组名称" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建会议模态框 -->
    <a-modal
      v-model:open="showCreateMeetingModal"
      title="创建会议"
      @ok="createMeeting"
    >
      <a-form layout="vertical">
        <a-form-item label="会议主题" required>
          <a-input v-model:value="meetingForm.title" placeholder="请输入会议主题" />
        </a-form-item>
        <a-form-item label="开始时间" required>
          <a-date-picker 
            v-model:value="meetingForm.startTime" 
            show-time 
            format="YYYY-MM-DD HH:mm"
            placeholder="选择开始时间"
          />
        </a-form-item>
        <a-form-item label="持续时间">
          <a-select v-model:value="meetingForm.duration">
            <a-select-option value="30">30分钟</a-select-option>
            <a-select-option value="60">1小时</a-select-option>
            <a-select-option value="120">2小时</a-select-option>
            <a-select-option value="240">4小时</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="参会人员">
          <a-select 
            v-model:value="meetingForm.participants" 
            mode="multiple" 
            placeholder="选择参会人员"
          >
            <a-select-option v-for="user in allUsers" :key="user.id" :value="user.id">
              {{ user.name }} - {{ user.department }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="会议密码">
          <a-input v-model:value="meetingForm.password" placeholder="设置会议密码(可选)" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettingsModal"
      title="通讯设置"
      @ok="saveSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="消息通知">
          <a-checkbox-group v-model:value="settings.notifications">
            <a-checkbox value="sound">声音提醒</a-checkbox>
            <a-checkbox value="vibration">震动提醒</a-checkbox>
            <a-checkbox value="desktop">桌面通知</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="自动状态">
          <a-switch v-model:checked="settings.autoStatus" />
          <span style="margin-left: 8px;">自动更新在线状态</span>
        </a-form-item>
        <a-form-item label="消息加密">
          <a-switch v-model:checked="settings.encryption" />
          <span style="margin-left: 8px;">启用端到端加密</span>
        </a-form-item>
        <a-form-item label="文件自动下载">
          <a-radio-group v-model:value="settings.autoDownload">
            <a-radio value="always">总是下载</a-radio>
            <a-radio value="wifi">仅WiFi下载</a-radio>
            <a-radio value="never">从不自动下载</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import {
  ReloadOutlined,
  SettingOutlined,
  MessageOutlined,
  UserOutlined,
  PhoneOutlined,
  VideoCameraOutlined,
  CheckOutlined,
  InfoCircleOutlined,
  SmileOutlined,
  PaperClipOutlined,
  PictureOutlined,
  FileOutlined,
  PlusOutlined,
  LaptopOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showNewChatModal = ref(false)
const showCreateMeetingModal = ref(false)
const showSettingsModal = ref(false)
const showChatInfo = ref(false)
const showEmojiPicker = ref(false)
const selectedChatFilter = ref('all')
const selectedChat = ref(null)
const messageInput = ref('')

// 通讯统计
const communicationStats = reactive({
  onlineUsers: 48,
  activeChats: 12,
  activeCalls: 3,
  meetingRooms: 5
})

// 聊天列表
const chatList = ref([
  {
    id: 1,
    name: '张主管',
    avatar: null,
    type: 'private',
    isOnline: true,
    lastMessage: '今天的案件处理情况如何？',
    lastMessageTime: '10:30',
    unreadCount: 2,
    messages: [
      { id: 1, content: '今天的案件处理情况如何？', time: '10:30', isOwn: false, type: 'text' },
      { id: 2, content: '目前已处理完成15个案件，还有3个需要跟进', time: '10:32', isOwn: true, type: 'text' }
    ]
  },
  {
    id: 2,
    name: '催收一组',
    avatar: null,
    type: 'group',
    isOnline: true,
    lastMessage: '李明: 客户张三今天还款成功',
    lastMessageTime: '09:45',
    unreadCount: 5,
    messages: [
      { id: 1, content: '大家早上好！', time: '09:00', isOwn: false, type: 'text' },
      { id: 2, content: '客户张三今天还款成功', time: '09:45', isOwn: false, type: 'text' }
    ]
  },
  {
    id: 3,
    name: '王同事',
    avatar: null,
    type: 'private',
    isOnline: false,
    lastMessage: '下午的会议记得参加',
    lastMessageTime: '昨天',
    unreadCount: 0,
    messages: [
      { id: 1, content: '下午的会议记得参加', time: '昨天 16:30', isOwn: false, type: 'text' },
      { id: 2, content: '好的，已经安排好了', time: '昨天 16:35', isOwn: true, type: 'text' }
    ]
  }
])

// 在线用户
const onlineUsers = ref([
  { id: 1, name: '张主管', avatar: null, department: '催收部', status: 'online' },
  { id: 2, name: '李明', avatar: null, department: '催收一组', status: 'online' },
  { id: 3, name: '王小丽', avatar: null, department: '催收二组', status: 'online' },
  { id: 4, name: '赵强', avatar: null, department: '风控部', status: 'online' }
])

// 全部用户
const allUsers = ref([
  { id: 1, name: '张主管', department: '催收部' },
  { id: 2, name: '李明', department: '催收一组' },
  { id: 3, name: '王小丽', department: '催收二组' },
  { id: 4, name: '赵强', department: '风控部' },
  { id: 5, name: '孙经理', department: '业务部' }
])

// 活跃通话
const activeCalls = ref([
  {
    id: 1,
    type: 'voice',
    participant: { name: '客户张三', avatar: null },
    duration: '03:45',
    isMuted: false
  },
  {
    id: 2,
    type: 'video',
    participant: { name: '李主管', avatar: null },
    duration: '15:22',
    isMuted: true
  }
])

// 通话记录
const callHistory = ref([
  {
    id: 1,
    type: 'voice',
    participant: '客户王五',
    time: '10:30',
    duration: '05:23',
    status: 'completed'
  },
  {
    id: 2,
    type: 'video',
    participant: '张主管',
    time: '09:15',
    duration: '12:45',
    status: 'completed'
  },
  {
    id: 3,
    type: 'voice',
    participant: '客户李四',
    time: '昨天',
    duration: '00:00',
    status: 'missed'
  }
])

// 会议室
const meetingRooms = ref([
  {
    id: 1,
    name: '催收部周例会',
    status: 'active',
    participants: 8,
    maxParticipants: 12,
    startTime: '14:00'
  },
  {
    id: 2,
    name: '业务培训',
    status: 'scheduled',
    participants: 0,
    maxParticipants: 20,
    startTime: '16:00'
  },
  {
    id: 3,
    name: '客户沟通会',
    status: 'waiting',
    participants: 3,
    maxParticipants: 8,
    startTime: '10:30'
  }
])

// 快速操作
const quickActions = ref([
  { id: 1, name: '语音通话', icon: 'PhoneOutlined', color: '#1890ff' },
  { id: 2, name: '视频会议', icon: 'VideoCameraOutlined', color: '#52c41a' },
  { id: 3, name: '屏幕共享', icon: 'LaptopOutlined', color: '#faad14' },
  { id: 4, name: '文件传输', icon: 'FileOutlined', color: '#722ed1' }
])

// 协作工具
const collaborationTools = ref([
  {
    id: 1,
    name: '白板协作',
    description: '实时协作白板',
    icon: 'EditOutlined',
    isActive: false
  },
  {
    id: 2,
    name: '文档共享',
    description: '在线文档编辑',
    icon: 'FileTextOutlined',
    isActive: true
  },
  {
    id: 3,
    name: '屏幕共享',
    description: '共享桌面画面',
    icon: 'LaptopOutlined',
    isActive: false
  },
  {
    id: 4,
    name: '远程控制',
    description: '远程桌面控制',
    icon: 'SettingOutlined',
    isActive: false
  }
])

// 表单数据
const newChatForm = reactive({
  type: 'private',
  participants: [],
  groupName: ''
})

const meetingForm = reactive({
  title: '',
  startTime: null,
  duration: '60',
  participants: [],
  password: ''
})

const settings = reactive({
  notifications: ['sound', 'desktop'],
  autoStatus: true,
  encryption: true,
  autoDownload: 'wifi'
})

// 计算属性
const filteredChats = computed(() => {
  if (selectedChatFilter.value === 'all') return chatList.value
  if (selectedChatFilter.value === 'unread') return chatList.value.filter(chat => chat.unreadCount > 0)
  if (selectedChatFilter.value === 'group') return chatList.value.filter(chat => chat.type === 'group')
  if (selectedChatFilter.value === 'private') return chatList.value.filter(chat => chat.type === 'private')
  return chatList.value
})

// 方法定义
const refreshStatus = () => {
  console.log('刷新通讯状态')
}

const filterChats = () => {
  console.log('筛选对话:', selectedChatFilter.value)
}

const markAllAsRead = () => {
  chatList.value.forEach(chat => {
    chat.unreadCount = 0
  })
}

const selectChat = (chat) => {
  selectedChat.value = chat
  chat.unreadCount = 0
}

const startChat = (user) => {
  console.log('开始对话:', user)
}

const startVoiceCall = (user) => {
  console.log('发起语音通话:', user)
}

const startVideoCall = (user) => {
  console.log('发起视频通话:', user)
}

const sendMessage = () => {
  if (!messageInput.value.trim() || !selectedChat.value) return
  
  const newMessage = {
    id: Date.now(),
    content: messageInput.value,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    isOwn: true,
    type: 'text'
  }
  
  selectedChat.value.messages.push(newMessage)
  selectedChat.value.lastMessage = messageInput.value
  selectedChat.value.lastMessageTime = newMessage.time
  messageInput.value = ''
}

const sendFile = (info) => {
  console.log('发送文件:', info)
}

const sendImage = () => {
  console.log('发送图片')
}

const endCall = (call) => {
  const index = activeCalls.value.findIndex(c => c.id === call.id)
  if (index > -1) {
    activeCalls.value.splice(index, 1)
  }
}

const muteCall = (call) => {
  call.isMuted = !call.isMuted
}

const callBack = (log) => {
  console.log('回拨:', log)
}

const getCallStatusClass = (status) => {
  const classes = {
    completed: 'status-completed',
    missed: 'status-missed',
    rejected: 'status-rejected'
  }
  return classes[status] || ''
}

const getRoomStatusColor = (status) => {
  const colors = {
    active: 'green',
    scheduled: 'blue',
    waiting: 'orange',
    ended: 'gray'
  }
  return colors[status] || 'default'
}

const getRoomStatusText = (status) => {
  const texts = {
    active: '进行中',
    scheduled: '已安排',
    waiting: '等待中',
    ended: '已结束'
  }
  return texts[status] || status
}

const joinMeeting = (room) => {
  console.log('加入会议:', room)
}

const performAction = (action) => {
  console.log('执行操作:', action)
}

const useTool = (tool) => {
  tool.isActive = !tool.isActive
  console.log('使用工具:', tool)
}

const createNewChat = () => {
  console.log('创建新对话:', newChatForm)
  showNewChatModal.value = false
}

const createMeeting = () => {
  console.log('创建会议:', meetingForm)
  showCreateMeetingModal.value = false
}

const saveSettings = () => {
  console.log('保存设置:', settings)
  showSettingsModal.value = false
}

const filterUsers = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 生命周期
onMounted(() => {
  refreshStatus()
})
</script>

<style scoped>
.realtime-communication {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.communication-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.communication-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.communication-status {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.communication-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chat-container {
  display: flex;
  height: 500px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.chat-list {
  width: 300px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
}

.chat-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.chat-item:hover {
  background-color: #f5f5f5;
}

.chat-item.active {
  background-color: #e6f7ff;
}

.chat-item.unread {
  font-weight: 500;
}

.chat-avatar {
  position: relative;
  margin-right: 12px;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: #52c41a;
  border-radius: 50%;
  border: 2px solid white;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.chat-name {
  font-weight: 500;
  color: #262626;
}

.chat-time {
  font-size: 11px;
  color: #999;
}

.chat-preview {
  color: #666;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.chat-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-details h4 {
  margin: 0;
  color: #262626;
}

.chat-status {
  font-size: 12px;
  color: #52c41a;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #fafafa;
}

.message-item {
  margin-bottom: 16px;
}

.message-item.own-message {
  display: flex;
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.own-message .message-bubble {
  background-color: #1890ff;
  color: white;
}

.message-text {
  word-wrap: break-word;
}

.message-image img {
  max-width: 200px;
  border-radius: 6px;
}

.message-file {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-time {
  font-size: 11px;
  color: #999;
  text-align: center;
  margin-top: 4px;
}

.chat-input {
  border-top: 1px solid #f0f0f0;
  background-color: white;
}

.input-toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

.input-area {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.input-area .ant-input {
  flex: 1;
}

.call-management {
  padding: 8px 0;
}

.call-management h4 {
  margin: 0 0 12px 0;
  color: #262626;
}

.call-item, .log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.call-info, .log-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.call-details {
  flex: 1;
}

.call-name, .log-name {
  font-weight: 500;
  color: #262626;
}

.call-duration, .log-time {
  font-size: 12px;
  color: #666;
}

.call-type {
  font-size: 16px;
  color: #1890ff;
}

.call-controls {
  display: flex;
  gap: 8px;
}

.call-log {
  max-height: 300px;
  overflow-y: auto;
}

.log-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 14px;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-missed {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-rejected {
  background-color: #fff7e6;
  color: #faad14;
}

.log-duration {
  font-size: 12px;
  color: #999;
  min-width: 60px;
}

.online-users {
  max-height: 300px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
  margin-left: 12px;
}

.user-name {
  font-weight: 500;
  color: #262626;
}

.user-status {
  font-size: 12px;
  color: #666;
}

.user-actions {
  display: flex;
  gap: 4px;
}

.meeting-rooms {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.room-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.room-header h4 {
  margin: 0;
  color: #262626;
}

.room-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.room-participants {
  display: flex;
  align-items: center;
}

.room-actions {
  text-align: right;
}

.quick-actions {
  padding: 8px 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-bottom: 8px;
}

.action-name {
  font-size: 12px;
  color: #666;
}

.collaboration-tools {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.tool-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background-color: #f0f2f5;
  color: #1890ff;
  font-size: 14px;
  margin-right: 12px;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.tool-desc {
  font-size: 12px;
  color: #666;
}

.tool-status {
  margin-left: auto;
}
</style>