<template>
  <div class="process-optimization">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>流程优化</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="startOptimization">
            <template #icon><RocketOutlined /></template>
            开始优化
          </a-button>
        </div>
      </div>
    </div>

    <!-- 优化概览 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="流程总数" 
              :value="overview.totalProcesses" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ApartmentOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="发现瓶颈" 
              :value="overview.bottlenecks" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><FunnelPlotOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="优化建议" 
              :value="overview.suggestions" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><BulbOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="预计提升" 
              :value="overview.improvement" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><RiseOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <a-row :gutter="16">
      <!-- 左侧流程分析 -->
      <a-col :span="12">
        <a-card title="流程效率分析" class="analysis-card">
          <div class="process-analysis">
            <div class="analysis-filter">
              <a-select v-model:value="selectedProcess" placeholder="选择流程" style="width: 200px">
                <a-select-option value="debt_reduction">债务减免流程</a-select-option>
                <a-select-option value="case_allocation">案件分配流程</a-select-option>
                <a-select-option value="payment_plan">还款计划流程</a-select-option>
                <a-select-option value="case_transfer">案件转移流程</a-select-option>
              </a-select>
              <a-button type="primary" size="small" @click="analyzeProcess">分析</a-button>
            </div>
            
            <div v-if="currentAnalysis" class="analysis-result">
              <h4>{{ currentAnalysis.name }}</h4>
              
              <!-- 效率指标 -->
              <div class="metrics-grid">
                <div class="metric-item">
                  <div class="metric-value">{{ currentAnalysis.avgDuration }}</div>
                  <div class="metric-label">平均耗时(小时)</div>
                </div>
                <div class="metric-item">
                  <div class="metric-value">{{ currentAnalysis.successRate }}%</div>
                  <div class="metric-label">成功率</div>
                </div>
                <div class="metric-item">
                  <div class="metric-value">{{ currentAnalysis.throughput }}</div>
                  <div class="metric-label">日吞吐量</div>
                </div>
                <div class="metric-item">
                  <div class="metric-value">{{ currentAnalysis.efficiency }}%</div>
                  <div class="metric-label">效率评分</div>
                </div>
              </div>
              
              <!-- 流程步骤分析 -->
              <div class="steps-analysis">
                <h5>步骤分析</h5>
                <div class="steps-list">
                  <div 
                    v-for="step in currentAnalysis.steps" 
                    :key="step.id"
                    class="step-item"
                    :class="{ 'bottleneck': step.isBottleneck }"
                  >
                    <div class="step-info">
                      <span class="step-name">{{ step.name }}</span>
                      <a-tag v-if="step.isBottleneck" color="red" size="small">瓶颈</a-tag>
                    </div>
                    <div class="step-metrics">
                      <span>平均耗时: {{ step.avgTime }}分钟</span>
                      <span>等待时间: {{ step.waitTime }}分钟</span>
                    </div>
                    <div class="step-chart">
                      <a-progress 
                        :percent="step.timePercent" 
                        :stroke-color="step.isBottleneck ? '#ff4d4f' : '#52c41a'"
                        size="small"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧优化建议 -->
      <a-col :span="12">
        <a-card title="优化建议" class="suggestions-card">
          <div class="suggestions-list">
            <div v-for="suggestion in suggestions" :key="suggestion.id" class="suggestion-item">
              <div class="suggestion-header">
                <div class="suggestion-info">
                  <h5>{{ suggestion.title }}</h5>
                  <a-tag :color="getPriorityColor(suggestion.priority)">
                    {{ getPriorityText(suggestion.priority) }}
                  </a-tag>
                </div>
                <div class="suggestion-impact">
                  <span class="impact-label">预计提升</span>
                  <span class="impact-value">{{ suggestion.expectedImprovement }}%</span>
                </div>
              </div>
              
              <div class="suggestion-content">
                <p class="suggestion-desc">{{ suggestion.description }}</p>
                
                <div class="suggestion-details">
                  <div class="detail-item">
                    <strong>问题分析:</strong>
                    <p>{{ suggestion.problem }}</p>
                  </div>
                  <div class="detail-item">
                    <strong>解决方案:</strong>
                    <p>{{ suggestion.solution }}</p>
                  </div>
                  <div class="detail-item">
                    <strong>实施步骤:</strong>
                    <ul>
                      <li v-for="step in suggestion.steps" :key="step">{{ step }}</li>
                    </ul>
                  </div>
                </div>
                
                <div class="suggestion-actions">
                  <a-button type="primary" size="small" @click="applySuggestion(suggestion)">
                    应用建议
                  </a-button>
                  <a-button size="small" @click="viewDetails(suggestion)">
                    查看详情
                  </a-button>
                  <a-button size="small" @click="ignoreSuggestion(suggestion)">
                    忽略
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 优化历史 -->
    <a-card title="优化历史" class="history-card">
      <a-table 
        :columns="historyColumns" 
        :data-source="optimizationHistory" 
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'improvement'">
            <span :style="{ color: record.improvement > 0 ? '#52c41a' : '#ff4d4f' }">
              {{ record.improvement > 0 ? '+' : '' }}{{ record.improvement }}%
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewOptimizationDetail(record)">
                查看详情
              </a-button>
              <a-button v-if="record.status === 'completed'" type="link" size="small" @click="rollbackOptimization(record)">
                回滚
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 优化图表 -->
    <a-card title="优化效果图表" class="chart-card">
      <a-tabs v-model:activeKey="chartTab">
        <a-tab-pane key="efficiency" tab="效率趋势">
          <div ref="efficiencyChart" class="chart-container"></div>
        </a-tab-pane>
        <a-tab-pane key="duration" tab="处理时长对比">
          <div ref="durationChart" class="chart-container"></div>
        </a-tab-pane>
        <a-tab-pane key="bottleneck" tab="瓶颈分析">
          <div ref="bottleneckChart" class="chart-container"></div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 优化详情模态框 -->
    <a-modal
      v-model:open="showOptimizationModal"
      title="优化详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedOptimization" class="optimization-detail">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="优化类型">{{ selectedOptimization.type }}</a-descriptions-item>
          <a-descriptions-item label="目标流程">{{ selectedOptimization.targetProcess }}</a-descriptions-item>
          <a-descriptions-item label="实施时间">{{ selectedOptimization.implementTime }}</a-descriptions-item>
          <a-descriptions-item label="实施人员">{{ selectedOptimization.implementer }}</a-descriptions-item>
          <a-descriptions-item label="效果评估">
            <span :style="{ color: selectedOptimization.improvement > 0 ? '#52c41a' : '#ff4d4f' }">
              {{ selectedOptimization.improvement > 0 ? '+' : '' }}{{ selectedOptimization.improvement }}%
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(selectedOptimization.status)">
              {{ getStatusText(selectedOptimization.status) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>优化前后对比</h4>
        <a-row :gutter="16">
          <a-col :span="12">
            <h5>优化前</h5>
            <ul>
              <li>平均处理时长: {{ selectedOptimization.before.avgDuration }}小时</li>
              <li>成功率: {{ selectedOptimization.before.successRate }}%</li>
              <li>日吞吐量: {{ selectedOptimization.before.throughput }}个</li>
              <li>效率评分: {{ selectedOptimization.before.efficiency }}%</li>
            </ul>
          </a-col>
          <a-col :span="12">
            <h5>优化后</h5>
            <ul>
              <li>平均处理时长: {{ selectedOptimization.after.avgDuration }}小时</li>
              <li>成功率: {{ selectedOptimization.after.successRate }}%</li>
              <li>日吞吐量: {{ selectedOptimization.after.throughput }}个</li>
              <li>效率评分: {{ selectedOptimization.after.efficiency }}%</li>
            </ul>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  RocketOutlined,
  ApartmentOutlined,
  FunnelPlotOutlined,
  BulbOutlined,
  RiseOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedProcess = ref('debt_reduction')
const currentAnalysis = ref(null)
const chartTab = ref('efficiency')
const showOptimizationModal = ref(false)
const selectedOptimization = ref(null)

// 图表引用
const efficiencyChart = ref(null)
const durationChart = ref(null) 
const bottleneckChart = ref(null)

// 概览数据
const overview = reactive({
  totalProcesses: 12,
  bottlenecks: 8,
  suggestions: 15,
  improvement: 25.6
})

// 流程分析数据
const processAnalysisData = {
  debt_reduction: {
    name: '债务减免流程',
    avgDuration: 4.2,
    successRate: 85,
    throughput: 45,
    efficiency: 72,
    steps: [
      { id: 1, name: '申请提交', avgTime: 15, waitTime: 5, timePercent: 20, isBottleneck: false },
      { id: 2, name: '初审', avgTime: 45, waitTime: 120, timePercent: 65, isBottleneck: true },
      { id: 3, name: '复审', avgTime: 30, waitTime: 10, timePercent: 40, isBottleneck: false },
      { id: 4, name: '审批', avgTime: 25, waitTime: 60, timePercent: 35, isBottleneck: false },
      { id: 5, name: '结果通知', avgTime: 10, waitTime: 5, timePercent: 15, isBottleneck: false }
    ]
  },
  case_allocation: {
    name: '案件分配流程',
    avgDuration: 2.8,
    successRate: 92,
    throughput: 120,
    efficiency: 86,
    steps: [
      { id: 1, name: '案件录入', avgTime: 20, waitTime: 5, timePercent: 25, isBottleneck: false },
      { id: 2, name: '规则匹配', avgTime: 10, waitTime: 2, timePercent: 12, isBottleneck: false },
      { id: 3, name: '人员分配', avgTime: 35, waitTime: 90, timePercent: 75, isBottleneck: true },
      { id: 4, name: '通知确认', avgTime: 15, waitTime: 30, timePercent: 30, isBottleneck: false }
    ]
  }
}

// 优化建议数据
const suggestions = ref([
  {
    id: 1,
    title: '简化债务减免初审流程',
    priority: 'high',
    expectedImprovement: 35,
    description: '通过引入AI预审和并行处理，减少人工初审时间',
    problem: '初审环节平均等待时间过长，成为整个流程的主要瓶颈',
    solution: '引入AI智能预审系统，对标准化案件进行自动审核，复杂案件转人工处理',
    steps: [
      '部署AI预审模型',
      '设置自动审核规则',
      '建立人工复核机制',
      '优化审核流程'
    ]
  },
  {
    id: 2,
    title: '优化案件分配算法',
    priority: 'medium',
    expectedImprovement: 20,
    description: '改进分配算法，提高分配效率和公平性',
    problem: '当前分配算法考虑因素单一，导致分配不均衡',
    solution: '采用多因子权重算法，综合考虑工作量、专业能力、历史绩效等因素',
    steps: [
      '分析分配历史数据',
      '设计新的算法模型',
      '测试和调优',
      '正式上线部署'
    ]
  },
  {
    id: 3,
    title: '建立流程监控预警',
    priority: 'low',
    expectedImprovement: 15,
    description: '实时监控流程执行状态，及时发现异常',
    problem: '缺乏实时监控机制，无法及时发现和处理流程异常',
    solution: '建立实时监控dashboard，设置关键指标预警阈值',
    steps: [
      '确定监控指标',
      '开发监控界面',
      '设置预警规则',
      '建立响应机制'
    ]
  }
])

// 优化历史数据
const optimizationHistory = ref([
  {
    id: 1,
    type: '流程重构',
    targetProcess: '债务减免流程',
    implementTime: '2024-01-10',
    implementer: '张工程师',
    status: 'completed',
    improvement: 28.5,
    description: '重构审批流程，引入并行处理'
  },
  {
    id: 2,
    type: '算法优化',
    targetProcess: '案件分配流程',
    implementTime: '2024-01-08',
    implementer: '李分析师',
    status: 'completed',
    improvement: 18.2,
    description: '优化分配算法，提高分配效率'
  },
  {
    id: 3,
    type: '自动化改进',
    targetProcess: '还款计划流程',
    implementTime: '2024-01-05',
    implementer: '王开发者',
    status: 'failed',
    improvement: -5.1,
    description: '尝试引入自动化工具，但效果不佳'
  }
])

// 表格列配置
const historyColumns = [
  { title: '优化类型', dataIndex: 'type', key: 'type' },
  { title: '目标流程', dataIndex: 'targetProcess', key: 'targetProcess' },
  { title: '实施时间', dataIndex: 'implementTime', key: 'implementTime' },
  { title: '实施人员', dataIndex: 'implementer', key: 'implementer' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '效果', dataIndex: 'improvement', key: 'improvement' },
  { title: '操作', key: 'action' }
]

// 方法定义
const getPriorityColor = (priority) => {
  const colors = { high: 'red', medium: 'orange', low: 'blue' }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = { high: '高', medium: '中', low: '低' }
  return texts[priority] || priority
}

const getStatusColor = (status) => {
  const colors = { completed: 'green', failed: 'red', pending: 'orange' }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = { completed: '已完成', failed: '失败', pending: '进行中' }
  return texts[status] || status
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const startOptimization = () => {
  console.log('开始优化流程')
}

const analyzeProcess = () => {
  currentAnalysis.value = processAnalysisData[selectedProcess.value]
}

const applySuggestion = (suggestion) => {
  console.log('应用建议:', suggestion)
}

const viewDetails = (suggestion) => {
  console.log('查看详情:', suggestion)
}

const ignoreSuggestion = (suggestion) => {
  console.log('忽略建议:', suggestion)
  const index = suggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    suggestions.value.splice(index, 1)
  }
}

const viewOptimizationDetail = (record) => {
  selectedOptimization.value = {
    ...record,
    before: {
      avgDuration: 5.2,
      successRate: 78,
      throughput: 35,
      efficiency: 65
    },
    after: {
      avgDuration: 3.7,
      successRate: 89,
      throughput: 45,
      efficiency: 83
    }
  }
  showOptimizationModal.value = true
}

const rollbackOptimization = (record) => {
  console.log('回滚优化:', record)
}

// 图表初始化
const initCharts = () => {
  nextTick(() => {
    // 效率趋势图表
    if (efficiencyChart.value) {
      const chart1 = echarts.init(efficiencyChart.value)
      chart1.setOption({
        title: { text: '流程效率趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value', name: '效率%' },
        series: [
          {
            name: '债务减免',
            type: 'line',
            data: [65, 68, 72, 75, 78, 82],
            smooth: true
          },
          {
            name: '案件分配',
            type: 'line',
            data: [78, 82, 85, 87, 89, 91],
            smooth: true
          },
          {
            name: '还款计划',
            type: 'line',
            data: [72, 74, 76, 78, 80, 83],
            smooth: true
          }
        ]
      })
    }

    // 处理时长对比图表
    if (durationChart.value) {
      const chart2 = echarts.init(durationChart.value)
      chart2.setOption({
        title: { text: '优化前后处理时长对比', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['债务减免', '案件分配', '还款计划', '案件转移', '呆账核销']
        },
        yAxis: { type: 'value', name: '小时' },
        series: [
          {
            name: '优化前',
            type: 'bar',
            data: [5.2, 3.5, 4.1, 2.8, 6.3],
            itemStyle: { color: '#ff7875' }
          },
          {
            name: '优化后',
            type: 'bar',
            data: [3.7, 2.8, 3.2, 2.1, 4.9],
            itemStyle: { color: '#73d13d' }
          }
        ]
      })
    }

    // 瓶颈分析图表
    if (bottleneckChart.value) {
      const chart3 = echarts.init(bottleneckChart.value)
      chart3.setOption({
        title: { text: '流程瓶颈分析', left: 'center' },
        tooltip: { trigger: 'item' },
        series: [{
          name: '瓶颈类型',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 35, name: '人工审核' },
            { value: 25, name: '系统处理' },
            { value: 20, name: '等待时间' },
            { value: 12, name: '流程复杂' },
            { value: 8, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  currentAnalysis.value = processAnalysisData[selectedProcess.value]
  refreshData()
  initCharts()
})
</script>

<style scoped>
.process-optimization {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.overview-section {
  margin-bottom: 16px;
}

.analysis-card,
.suggestions-card,
.history-card,
.chart-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 16px;
}

.analysis-filter {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin: 16px 0;
}

.metric-item {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.steps-analysis {
  margin-top: 24px;
}

.steps-list {
  max-height: 300px;
  overflow-y: auto;
}

.step-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.step-item.bottleneck {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-name {
  font-weight: 500;
}

.step-metrics {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.suggestions-list {
  max-height: 600px;
  overflow-y: auto;
}

.suggestion-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 16px;
  overflow: hidden;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-info h5 {
  margin: 0 8px 0 0;
  display: inline-block;
}

.suggestion-impact {
  text-align: right;
}

.impact-label {
  display: block;
  font-size: 12px;
  color: #666;
}

.impact-value {
  font-size: 18px;
  font-weight: bold;
  color: #52c41a;
}

.suggestion-content {
  padding: 16px;
}

.suggestion-desc {
  margin-bottom: 16px;
  color: #666;
}

.suggestion-details {
  margin-bottom: 16px;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item strong {
  color: #262626;
}

.detail-item p {
  margin: 4px 0;
  color: #666;
}

.detail-item ul {
  margin: 4px 0;
  padding-left: 20px;
}

.detail-item li {
  color: #666;
  margin: 2px 0;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.optimization-detail {
  max-height: 600px;
  overflow-y: auto;
}
</style>