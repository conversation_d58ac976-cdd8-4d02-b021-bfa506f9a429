<template>
  <div class="process-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>流程监控</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showAlertModal = true">
            <template #icon><BellOutlined /></template>
            告警设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 实时统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="运行中流程" 
              :value="stats.running" 
              :value-style="{ color: '#1890ff' }"
              suffix="个"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="待处理任务" 
              :value="stats.pending" 
              :value-style="{ color: '#faad14' }"
              suffix="个"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="异常流程" 
              :value="stats.error" 
              :value-style="{ color: '#ff4d4f' }"
              suffix="个"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均处理时长" 
              :value="stats.avgDuration" 
              :value-style="{ color: '#52c41a' }"
              suffix="小时"
            >
              <template #prefix><FieldTimeOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 监控面板 -->
    <a-row :gutter="16">
      <!-- 左侧流程列表 -->
      <a-col :span="8">
        <a-card title="流程实例" :body-style="{ padding: '12px' }">
          <template #extra>
            <a-select v-model:value="processFilter" placeholder="筛选状态" style="width: 120px" size="small">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="running">运行中</a-select-option>
              <a-select-option value="pending">待处理</a-select-option>
              <a-select-option value="error">异常</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
            </a-select>
          </template>
          
          <div class="process-list">
            <div 
              v-for="process in filteredProcesses" 
              :key="process.id"
              class="process-item"
              :class="{ active: selectedProcess?.id === process.id }"
              @click="selectProcess(process)"
            >
              <div class="process-header">
                <span class="process-name">{{ process.name }}</span>
                <a-tag :color="getStatusColor(process.status)" size="small">
                  {{ getStatusText(process.status) }}
                </a-tag>
              </div>
              <div class="process-info">
                <div class="info-item">
                  <UserOutlined />
                  <span>{{ process.initiator }}</span>
                </div>
                <div class="info-item">
                  <ClockCircleOutlined />
                  <span>{{ process.duration }}h</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧详情和图表 -->
      <a-col :span="16">
        <div class="right-panel">
          <!-- 流程详情 -->
          <a-card v-if="selectedProcess" title="流程详情" class="detail-card">
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="流程ID">{{ selectedProcess.id }}</a-descriptions-item>
              <a-descriptions-item label="流程名称">{{ selectedProcess.name }}</a-descriptions-item>
              <a-descriptions-item label="发起人">{{ selectedProcess.initiator }}</a-descriptions-item>
              <a-descriptions-item label="当前状态">
                <a-tag :color="getStatusColor(selectedProcess.status)">
                  {{ getStatusText(selectedProcess.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="开始时间">{{ selectedProcess.startTime }}</a-descriptions-item>
              <a-descriptions-item label="运行时长">{{ selectedProcess.duration }}小时</a-descriptions-item>
              <a-descriptions-item label="当前节点">{{ selectedProcess.currentNode }}</a-descriptions-item>
              <a-descriptions-item label="处理人">{{ selectedProcess.currentHandler }}</a-descriptions-item>
            </a-descriptions>
            
            <a-divider />
            
            <!-- 流程图 -->
            <div class="process-diagram">
              <h4>流程执行图</h4>
              <div class="diagram-container">
                <div class="node-path">
                  <div 
                    v-for="(node, index) in selectedProcess.nodes" 
                    :key="node.id"
                    class="path-node"
                    :class="getNodeClass(node.status)"
                  >
                    <div class="node-content">
                      <div class="node-icon">
                        <component :is="getNodeIcon(node.type)" />
                      </div>
                      <div class="node-info">
                        <div class="node-name">{{ node.name }}</div>
                        <div class="node-time">{{ node.completeTime || '进行中' }}</div>
                      </div>
                    </div>
                    <div v-if="index < selectedProcess.nodes.length - 1" class="node-connector">
                      <ArrowRightOutlined />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>

          <!-- 性能监控图表 -->
          <a-card title="性能监控" class="chart-card">
            <a-tabs v-model:activeKey="chartTab">
              <a-tab-pane key="throughput" tab="吞吐量">
                <div ref="throughputChart" class="chart-container"></div>
              </a-tab-pane>
              <a-tab-pane key="duration" tab="处理时长">
                <div ref="durationChart" class="chart-container"></div>
              </a-tab-pane>
              <a-tab-pane key="error" tab="错误率">
                <div ref="errorChart" class="chart-container"></div>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </div>
      </a-col>
    </a-row>

    <!-- 异常告警 -->
    <a-card title="异常告警" class="alert-card">
      <a-list 
        :data-source="alerts" 
        size="small"
        :pagination="{ pageSize: 5, size: 'small' }"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-badge :status="getAlertStatus(item.level)" />
              </template>
              <template #title>
                <span :class="`alert-${item.level}`">{{ item.title }}</span>
              </template>
              <template #description>
                <div class="alert-desc">
                  <span>{{ item.description }}</span>
                  <span class="alert-time">{{ item.time }}</span>
                </div>
              </template>
            </a-list-item-meta>
            <template #actions>
              <a @click="handleAlert(item)">处理</a>
              <a @click="ignoreAlert(item)">忽略</a>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 告警设置模态框 -->
    <a-modal
      v-model:open="showAlertModal"
      title="告警设置"
      width="600px"
      @ok="saveAlertSettings"
    >
      <a-form :model="alertSettings" layout="vertical">
        <a-form-item label="流程超时告警">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input-number 
                v-model:value="alertSettings.timeoutHours" 
                placeholder="超时小时数" 
                :min="1" 
                style="width: 100%" 
              />
            </a-col>
            <a-col :span="12">
              <a-switch v-model:checked="alertSettings.timeoutEnabled" checked-children="开启" un-checked-children="关闭" />
            </a-col>
          </a-row>
        </a-form-item>
        
        <a-form-item label="错误率告警">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input-number 
                v-model:value="alertSettings.errorRate" 
                placeholder="错误率百分比" 
                :min="1" 
                :max="100" 
                style="width: 100%" 
              />
            </a-col>
            <a-col :span="12">
              <a-switch v-model:checked="alertSettings.errorEnabled" checked-children="开启" un-checked-children="关闭" />
            </a-col>
          </a-row>
        </a-form-item>
        
        <a-form-item label="积压任务告警">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input-number 
                v-model:value="alertSettings.backlogCount" 
                placeholder="积压任务数量" 
                :min="1" 
                style="width: 100%" 
              />
            </a-col>
            <a-col :span="12">
              <a-switch v-model:checked="alertSettings.backlogEnabled" checked-children="开启" un-checked-children="关闭" />
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  BellOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined,
  PlaySquareOutlined,
  FileTextOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const showAlertModal = ref(false)
const processFilter = ref('')
const selectedProcess = ref(null)
const chartTab = ref('throughput')

// 图表引用
const throughputChart = ref(null)
const durationChart = ref(null)
const errorChart = ref(null)

// 统计数据
const stats = reactive({
  running: 45,
  pending: 128,
  error: 3,
  avgDuration: 2.5
})

// 告警设置
const alertSettings = reactive({
  timeoutHours: 24,
  timeoutEnabled: true,
  errorRate: 10,
  errorEnabled: true,
  backlogCount: 100,
  backlogEnabled: true
})

// 流程实例数据
const processes = ref([
  {
    id: 'PROC001',
    name: '债务减免审批',
    initiator: '李催收',
    status: 'running',
    duration: 1.5,
    startTime: '2024-01-15 09:30:00',
    currentNode: '经理审批',
    currentHandler: '张经理',
    nodes: [
      { id: 1, name: '发起申请', type: 'start', status: 'completed', completeTime: '09:30' },
      { id: 2, name: '主管审批', type: 'approval', status: 'completed', completeTime: '10:15' },
      { id: 3, name: '经理审批', type: 'approval', status: 'running', completeTime: null },
      { id: 4, name: '完成', type: 'end', status: 'pending', completeTime: null }
    ]
  },
  {
    id: 'PROC002',
    name: '案件分配流程',
    initiator: '王分配',
    status: 'error',
    duration: 4.2,
    startTime: '2024-01-14 14:20:00',
    currentNode: '系统分配',
    currentHandler: '系统',
    nodes: [
      { id: 1, name: '发起申请', type: 'start', status: 'completed', completeTime: '14:20' },
      { id: 2, name: '参数验证', type: 'system', status: 'completed', completeTime: '14:21' },
      { id: 3, name: '系统分配', type: 'system', status: 'error', completeTime: null },
      { id: 4, name: '完成', type: 'end', status: 'pending', completeTime: null }
    ]
  },
  {
    id: 'PROC003',
    name: '还款计划制定',
    initiator: '赵催收',
    status: 'pending',
    duration: 0.5,
    startTime: '2024-01-15 15:00:00',
    currentNode: '主管审批',
    currentHandler: '李主管',
    nodes: [
      { id: 1, name: '发起申请', type: 'start', status: 'completed', completeTime: '15:00' },
      { id: 2, name: '主管审批', type: 'approval', status: 'pending', completeTime: null },
      { id: 3, name: '客户确认', type: 'manual', status: 'pending', completeTime: null },
      { id: 4, name: '完成', type: 'end', status: 'pending', completeTime: null }
    ]
  }
])

// 告警数据
const alerts = ref([
  {
    id: 1,
    title: '流程执行异常',
    description: '案件分配流程(PROC002)执行失败，错误代码: ASSIGN_ERROR',
    time: '2024-01-15 16:30:15',
    level: 'error'
  },
  {
    id: 2,
    title: '流程执行超时',
    description: '债务减免审批流程超过预期处理时间24小时',
    time: '2024-01-15 16:25:30',
    level: 'warning'
  },
  {
    id: 3,
    title: '积压任务过多',
    description: '当前待处理任务数量达到128个，超过告警阈值',
    time: '2024-01-15 16:20:45',
    level: 'info'
  }
])

// 计算属性
const filteredProcesses = computed(() => {
  if (!processFilter.value) return processes.value
  return processes.value.filter(p => p.status === processFilter.value)
})

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    running: 'blue',
    pending: 'orange',
    error: 'red',
    completed: 'green'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    pending: '待处理',
    error: '异常',
    completed: '已完成'
  }
  return texts[status] || status
}

const getNodeClass = (status) => {
  return {
    'node-completed': status === 'completed',
    'node-running': status === 'running',
    'node-error': status === 'error',
    'node-pending': status === 'pending'
  }
}

const getNodeIcon = (type) => {
  const icons = {
    start: PlaySquareOutlined,
    approval: UserOutlined,
    system: SettingOutlined,
    manual: FileTextOutlined,
    end: CheckCircleOutlined
  }
  return icons[type] || FileTextOutlined
}

const getAlertStatus = (level) => {
  const statuses = {
    error: 'error',
    warning: 'warning',
    info: 'processing'
  }
  return statuses[level] || 'default'
}

const selectProcess = (process) => {
  selectedProcess.value = process
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleAlert = (alert) => {
  console.log('处理告警:', alert)
}

const ignoreAlert = (alert) => {
  console.log('忽略告警:', alert)
  const index = alerts.value.findIndex(a => a.id === alert.id)
  if (index > -1) {
    alerts.value.splice(index, 1)
  }
}

const saveAlertSettings = () => {
  console.log('保存告警设置:', alertSettings)
  showAlertModal.value = false
}

// 图表初始化
const initCharts = () => {
  nextTick(() => {
    // 吞吐量图表
    if (throughputChart.value) {
      const chart1 = echarts.init(throughputChart.value)
      chart1.setOption({
        title: { text: '流程吞吐量', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '完成数量',
          type: 'line',
          data: [12, 8, 25, 36, 28, 15],
          smooth: true,
          areaStyle: {}
        }]
      })
    }

    // 处理时长图表
    if (durationChart.value) {
      const chart2 = echarts.init(durationChart.value)
      chart2.setOption({
        title: { text: '平均处理时长', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['债务减免', '案件分配', '还款计划', '案件转移', '呆账核销']
        },
        yAxis: { type: 'value', name: '小时' },
        series: [{
          name: '处理时长',
          type: 'bar',
          data: [2.5, 1.8, 3.2, 1.5, 4.8],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      })
    }

    // 错误率图表
    if (errorChart.value) {
      const chart3 = echarts.init(errorChart.value)
      chart3.setOption({
        title: { text: '流程错误率', left: 'center' },
        tooltip: { trigger: 'item' },
        series: [{
          name: '错误率',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 95, name: '成功' },
            { value: 3, name: '超时' },
            { value: 2, name: '失败' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  selectedProcess.value = processes.value[0]
  refreshData()
  initCharts()
})
</script>

<style scoped>
.process-monitoring {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-section {
  margin-bottom: 16px;
}

.right-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card,
.chart-card,
.alert-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.process-list {
  max-height: 500px;
  overflow-y: auto;
}

.process-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.process-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24,144,255,0.2);
}

.process-item.active {
  border-color: #1890ff;
  background: #f6ffed;
}

.process-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.process-name {
  font-weight: 500;
  color: #262626;
}

.process-info {
  display: flex;
  gap: 16px;
  color: #666;
  font-size: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.process-diagram {
  margin-top: 16px;
}

.diagram-container {
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.node-path {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.path-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  border: 2px solid #d9d9d9;
  background: white;
  min-width: 80px;
}

.node-completed .node-content {
  border-color: #52c41a;
  background: #f6ffed;
}

.node-running .node-content {
  border-color: #1890ff;
  background: #e6f7ff;
  animation: pulse 2s infinite;
}

.node-error .node-content {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.node-pending .node-content {
  border-color: #d9d9d9;
  background: #fafafa;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(24, 144, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
}

.node-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.node-info {
  text-align: center;
}

.node-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.node-time {
  font-size: 10px;
  color: #666;
}

.node-connector {
  color: #1890ff;
  font-size: 16px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.alert-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-time {
  color: #999;
  font-size: 12px;
}

.alert-error {
  color: #ff4d4f;
}

.alert-warning {
  color: #faad14;
}

.alert-info {
  color: #1890ff;
}

.alert-card {
  margin-top: 16px;
}
</style>