<template>
  <div class="workflow-design">
    <div class="header-section">
      <div class="title-area">
        <h2>工作流设计</h2>
        <p>可视化流程设计与配置</p>
      </div>
      <div class="action-area">
        <a-space>
          <a-button @click="showTemplateModal = true">
            <FileAddOutlined />
            从模板创建
          </a-button>
          <a-button @click="importWorkflow">
            <ImportOutlined />
            导入流程
          </a-button>
          <a-button @click="saveWorkflow">
            <SaveOutlined />
            保存
          </a-button>
          <a-button type="primary" @click="publishWorkflow">
            <SendOutlined />
            发布流程
          </a-button>
        </a-space>
      </div>
    </div>

    <div class="design-workspace">
      <!-- 左侧组件面板 -->
      <div class="components-panel">
        <div class="panel-header">
          <h4>流程组件</h4>
        </div>
        <div class="component-groups">
          <a-collapse v-model:activeKey="activeComponentKeys" ghost>
            <a-collapse-panel key="basic" header="基础节点">
              <div class="component-list">
                <div 
                  v-for="component in basicComponents" 
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="onDragStart($event, component)"
                >
                  <component :is="component.icon" class="component-icon" />
                  <span class="component-name">{{ component.name }}</span>
                </div>
              </div>
            </a-collapse-panel>
            <a-collapse-panel key="approval" header="审批节点">
              <div class="component-list">
                <div 
                  v-for="component in approvalComponents" 
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="onDragStart($event, component)"
                >
                  <component :is="component.icon" class="component-icon" />
                  <span class="component-name">{{ component.name }}</span>
                </div>
              </div>
            </a-collapse-panel>
            <a-collapse-panel key="system" header="系统节点">
              <div class="component-list">
                <div 
                  v-for="component in systemComponents" 
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="onDragStart($event, component)"
                >
                  <component :is="component.icon" class="component-icon" />
                  <span class="component-name">{{ component.name }}</span>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>

      <!-- 中间设计画布 -->
      <div class="design-canvas">
        <div class="canvas-toolbar">
          <a-space>
            <a-button size="small" @click="zoomIn">
              <ZoomInOutlined />
            </a-button>
            <a-button size="small" @click="zoomOut">
              <ZoomOutOutlined />
            </a-button>
            <a-button size="small" @click="resetZoom">
              <CompressOutlined />
              适应画布
            </a-button>
            <a-divider type="vertical" />
            <a-button size="small" @click="undoAction">
              <UndoOutlined />
            </a-button>
            <a-button size="small" @click="redoAction">
              <RedoOutlined />
            </a-button>
            <a-divider type="vertical" />
            <a-button size="small" @click="clearCanvas">
              <ClearOutlined />
              清空
            </a-button>
          </a-space>
          <div class="zoom-info">缩放: {{ Math.round(zoomLevel * 100) }}%</div>
        </div>
        
        <div 
          class="canvas-area"
          :style="{ transform: `scale(${zoomLevel})` }"
          @drop="onDrop"
          @dragover="onDragOver"
          @click="onCanvasClick"
        >
          <!-- 网格背景 -->
          <div class="grid-background"></div>
          
          <!-- 流程节点 -->
          <div 
            v-for="node in workflowNodes" 
            :key="node.id"
            class="workflow-node"
            :class="{ active: selectedNode?.id === node.id }"
            :style="{ 
              left: node.x + 'px', 
              top: node.y + 'px',
              backgroundColor: getNodeColor(node.type)
            }"
            @click="selectNode(node)"
            @mousedown="startDrag($event, node)"
          >
            <component :is="getNodeIcon(node.type)" class="node-icon" />
            <div class="node-title">{{ node.title }}</div>
            <div class="node-type">{{ getNodeTypeName(node.type) }}</div>
            
            <!-- 连接点 -->
            <div class="connection-points">
              <div class="input-point" @click.stop="startConnection($event, node, 'input')"></div>
              <div class="output-point" @click.stop="startConnection($event, node, 'output')"></div>
            </div>
            
            <!-- 删除按钮 -->
            <div class="node-actions">
              <a-button size="small" type="text" danger @click.stop="deleteNode(node)">
                <DeleteOutlined />
              </a-button>
            </div>
          </div>
          
          <!-- 连接线 -->
          <svg class="connections-layer">
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
               refX="10" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
              </marker>
            </defs>
            <path 
              v-for="connection in connections" 
              :key="connection.id"
              :d="getConnectionPath(connection)"
              stroke="#666"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
              @click="selectConnection(connection)"
              class="connection-line"
              :class="{ active: selectedConnection?.id === connection.id }"
            />
          </svg>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h4>属性配置</h4>
        </div>
        
        <!-- 流程属性 -->
        <div v-if="!selectedNode && !selectedConnection" class="workflow-properties">
          <a-form :model="workflowConfig" layout="vertical">
            <a-form-item label="流程名称">
              <a-input v-model:value="workflowConfig.name" />
            </a-form-item>
            <a-form-item label="流程描述">
              <a-textarea v-model:value="workflowConfig.description" :rows="3" />
            </a-form-item>
            <a-form-item label="流程分类">
              <a-select v-model:value="workflowConfig.category">
                <a-select-option value="approval">审批流程</a-select-option>
                <a-select-option value="business">业务流程</a-select-option>
                <a-select-option value="system">系统流程</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="优先级">
              <a-select v-model:value="workflowConfig.priority">
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
        
        <!-- 节点属性 -->
        <div v-else-if="selectedNode" class="node-properties">
          <a-form :model="selectedNode" layout="vertical">
            <a-form-item label="节点名称">
              <a-input v-model:value="selectedNode.title" />
            </a-form-item>
            <a-form-item label="节点描述">
              <a-textarea v-model:value="selectedNode.description" :rows="3" />
            </a-form-item>
            
            <!-- 审批节点特有配置 -->
            <template v-if="selectedNode.type === 'approval'">
              <a-form-item label="审批人设置">
                <a-select v-model:value="selectedNode.config.approverType">
                  <a-select-option value="designated">指定人员</a-select-option>
                  <a-select-option value="role">按角色</a-select-option>
                  <a-select-option value="dept">按部门</a-select-option>
                  <a-select-option value="initiator">发起人</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="审批方式">
                <a-select v-model:value="selectedNode.config.approvalMode">
                  <a-select-option value="single">单人审批</a-select-option>
                  <a-select-option value="multiple">多人审批</a-select-option>
                  <a-select-option value="sequential">依次审批</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="超时设置">
                <a-input-number 
                  v-model:value="selectedNode.config.timeout" 
                  :min="1" 
                  addon-after="小时"
                />
              </a-form-item>
            </template>
            
            <!-- 条件节点特有配置 -->
            <template v-if="selectedNode.type === 'condition'">
              <a-form-item label="条件表达式">
                <a-textarea v-model:value="selectedNode.config.expression" :rows="4" />
              </a-form-item>
              <a-form-item label="条件类型">
                <a-select v-model:value="selectedNode.config.conditionType">
                  <a-select-option value="field">字段条件</a-select-option>
                  <a-select-option value="script">脚本条件</a-select-option>
                  <a-select-option value="custom">自定义条件</a-select-option>
                </a-select>
              </a-form-item>
            </template>
            
            <!-- 系统节点特有配置 -->
            <template v-if="selectedNode.type === 'system'">
              <a-form-item label="执行类型">
                <a-select v-model:value="selectedNode.config.actionType">
                  <a-select-option value="email">发送邮件</a-select-option>
                  <a-select-option value="sms">发送短信</a-select-option>
                  <a-select-option value="api">调用API</a-select-option>
                  <a-select-option value="script">执行脚本</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="执行参数">
                <a-textarea v-model:value="selectedNode.config.parameters" :rows="4" />
              </a-form-item>
            </template>
          </a-form>
        </div>
        
        <!-- 连接线属性 -->
        <div v-else-if="selectedConnection" class="connection-properties">
          <a-form :model="selectedConnection" layout="vertical">
            <a-form-item label="连接名称">
              <a-input v-model:value="selectedConnection.label" />
            </a-form-item>
            <a-form-item label="执行条件">
              <a-textarea v-model:value="selectedConnection.condition" :rows="3" />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" danger @click="deleteConnection">
                删除连接
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="toolbar-left">
        <a-space>
          <span>节点数: {{ workflowNodes.length }}</span>
          <span>连接数: {{ connections.length }}</span>
          <span>最后保存: {{ lastSaveTime || '未保存' }}</span>
        </a-space>
      </div>
      <div class="toolbar-right">
        <a-space>
          <a-button @click="previewWorkflow">
            <EyeOutlined />
            预览
          </a-button>
          <a-button @click="validateWorkflow">
            <CheckCircleOutlined />
            验证流程
          </a-button>
          <a-button @click="exportWorkflow">
            <ExportOutlined />
            导出
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 模板选择模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="选择流程模板"
      width="800px"
      :footer="null"
    >
      <div class="template-grid">
        <a-row :gutter="[16, 16]">
          <a-col 
            v-for="template in flowTemplates" 
            :key="template.id" 
            :span="8"
          >
            <a-card
              :hoverable="true"
              class="template-card"
              @click="createFromTemplate(template)"
            >
              <template #cover>
                <div class="template-cover">
                  <component :is="template.icon" class="template-icon" />
                </div>
              </template>
              <a-card-meta :title="template.name">
                <template #description>
                  <div class="template-desc">
                    <p>{{ template.description }}</p>
                    <div class="template-meta">
                      <span>{{ template.nodes }}个节点</span>
                      <span>{{ template.category }}</span>
                    </div>
                  </div>
                </template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 流程预览模态框 -->
    <a-modal
      v-model:open="showPreviewModal"
      title="流程预览"
      width="1000px"
      :footer="null"
    >
      <div class="preview-content">
        <div class="preview-info">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="流程名称">
              {{ workflowConfig.name }}
            </a-descriptions-item>
            <a-descriptions-item label="流程分类">
              {{ getCategoryText(workflowConfig.category) }}
            </a-descriptions-item>
            <a-descriptions-item label="节点数量">
              {{ workflowNodes.length }}
            </a-descriptions-item>
            <a-descriptions-item label="连接数量">
              {{ connections.length }}
            </a-descriptions-item>
            <a-descriptions-item label="流程描述" :span="2">
              {{ workflowConfig.description }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div class="preview-flow">
          <h4>流程图预览</h4>
          <div class="flow-preview-area">
            <!-- 这里可以显示流程图的只读版本 -->
            <div class="preview-placeholder">
              <FileSearchOutlined style="font-size: 48px; color: #ccc;" />
              <p>流程图预览</p>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { 
  FileAddOutlined,
  ImportOutlined,
  SaveOutlined,
  SendOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  CompressOutlined,
  UndoOutlined,
  RedoOutlined,
  ClearOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExportOutlined,
  PlayCircleOutlined,
  CheckSquareOutlined,
  BranchesOutlined,
  SettingOutlined,
  MailOutlined,
  ApiOutlined,
  FileSearchOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showTemplateModal = ref(false)
const showPreviewModal = ref(false)
const selectedNode = ref(null)
const selectedConnection = ref(null)
const zoomLevel = ref(1)
const lastSaveTime = ref('')
const activeComponentKeys = ref(['basic'])

// 拖拽相关
const isDragging = ref(false)
const dragNode = ref(null)
const dragOffset = ref({ x: 0, y: 0 })
const isConnecting = ref(false)
const connectionStart = ref(null)

// 流程配置
const workflowConfig = reactive({
  name: '新建流程',
  description: '',
  category: 'approval',
  priority: 'medium'
})

// 工作流节点
const workflowNodes = ref([])

// 连接线
const connections = ref([])

// 基础组件
const basicComponents = ref([
  { type: 'start', name: '开始', icon: 'PlayCircleOutlined' },
  { type: 'end', name: '结束', icon: 'CheckSquareOutlined' },
  { type: 'task', name: '任务', icon: 'CheckSquareOutlined' },
  { type: 'condition', name: '条件', icon: 'BranchesOutlined' }
])

// 审批组件
const approvalComponents = ref([
  { type: 'approval', name: '审批', icon: 'CheckSquareOutlined' },
  { type: 'review', name: '会签', icon: 'CheckSquareOutlined' },
  { type: 'countersign', name: '并签', icon: 'CheckSquareOutlined' }
])

// 系统组件
const systemComponents = ref([
  { type: 'system', name: '系统任务', icon: 'SettingOutlined' },
  { type: 'email', name: '邮件通知', icon: 'MailOutlined' },
  { type: 'api', name: 'API调用', icon: 'ApiOutlined' }
])

// 流程模板
const flowTemplates = ref([
  {
    id: 1,
    name: '请假审批流程',
    description: '员工请假申请的标准审批流程',
    nodes: 5,
    category: '人事管理',
    icon: 'CheckSquareOutlined'
  },
  {
    id: 2,
    name: '报销审批流程',
    description: '费用报销申请的审批流程',
    nodes: 4,
    category: '财务管理',
    icon: 'CheckSquareOutlined'
  },
  {
    id: 3,
    name: '采购审批流程',
    description: '物资采购申请的审批流程',
    nodes: 6,
    category: '采购管理',
    icon: 'CheckSquareOutlined'
  }
])

// 方法定义
const onDragStart = (event, component) => {
  event.dataTransfer.setData('component', JSON.stringify(component))
}

const onDragOver = (event) => {
  event.preventDefault()
}

const onDrop = (event) => {
  event.preventDefault()
  const componentData = JSON.parse(event.dataTransfer.getData('component'))
  const rect = event.currentTarget.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value
  const y = (event.clientY - rect.top) / zoomLevel.value
  
  createNode(componentData, x, y)
}

const createNode = (component, x, y) => {
  const node = {
    id: `node_${Date.now()}`,
    type: component.type,
    title: component.name,
    description: '',
    x: x - 50, // 节点宽度的一半
    y: y - 30, // 节点高度的一半
    config: {}
  }
  
  // 根据节点类型设置默认配置
  if (component.type === 'approval') {
    node.config = {
      approverType: 'designated',
      approvalMode: 'single',
      timeout: 24
    }
  } else if (component.type === 'condition') {
    node.config = {
      expression: '',
      conditionType: 'field'
    }
  } else if (component.type === 'system') {
    node.config = {
      actionType: 'email',
      parameters: ''
    }
  }
  
  workflowNodes.value.push(node)
  selectNode(node)
}

const selectNode = (node) => {
  selectedNode.value = node
  selectedConnection.value = null
}

const selectConnection = (connection) => {
  selectedConnection.value = connection
  selectedNode.value = null
}

const onCanvasClick = (event) => {
  if (event.target.classList.contains('canvas-area') || 
      event.target.classList.contains('grid-background')) {
    selectedNode.value = null
    selectedConnection.value = null
  }
}

const startDrag = (event, node) => {
  if (event.button === 0) { // 左键
    isDragging.value = true
    dragNode.value = node
    dragOffset.value = {
      x: event.offsetX,
      y: event.offsetY
    }
    
    const onMouseMove = (e) => {
      if (isDragging.value && dragNode.value) {
        const rect = document.querySelector('.canvas-area').getBoundingClientRect()
        const x = (e.clientX - rect.left) / zoomLevel.value - dragOffset.value.x
        const y = (e.clientY - rect.top) / zoomLevel.value - dragOffset.value.y
        
        dragNode.value.x = Math.max(0, x)
        dragNode.value.y = Math.max(0, y)
      }
    }
    
    const onMouseUp = () => {
      isDragging.value = false
      dragNode.value = null
      document.removeEventListener('mousemove', onMouseMove)
      document.removeEventListener('mouseup', onMouseUp)
    }
    
    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
  }
}

const startConnection = (event, node, type) => {
  isConnecting.value = true
  connectionStart.value = { node, type }
  
  // 这里可以添加连接线的交互逻辑
  const onMouseUp = (e) => {
    // 检查是否连接到另一个节点
    const targetElement = e.target.closest('.workflow-node')
    if (targetElement && targetElement !== event.target.closest('.workflow-node')) {
      const targetNodeId = targetElement.getAttribute('data-node-id')
      const targetNode = workflowNodes.value.find(n => n.id === targetNodeId)
      
      if (targetNode) {
        createConnection(connectionStart.value.node, targetNode)
      }
    }
    
    isConnecting.value = false
    connectionStart.value = null
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mouseup', onMouseUp)
}

const createConnection = (fromNode, toNode) => {
  const connection = {
    id: `conn_${Date.now()}`,
    from: fromNode.id,
    to: toNode.id,
    label: '',
    condition: ''
  }
  
  connections.value.push(connection)
}

const getConnectionPath = (connection) => {
  const fromNode = workflowNodes.value.find(n => n.id === connection.from)
  const toNode = workflowNodes.value.find(n => n.id === connection.to)
  
  if (!fromNode || !toNode) return ''
  
  const fromX = fromNode.x + 50
  const fromY = fromNode.y + 30
  const toX = toNode.x + 50
  const toY = toNode.y + 30
  
  return `M ${fromX} ${fromY} L ${toX} ${toY}`
}

const deleteNode = (node) => {
  const index = workflowNodes.value.findIndex(n => n.id === node.id)
  if (index > -1) {
    workflowNodes.value.splice(index, 1)
    // 删除相关连接
    connections.value = connections.value.filter(c => c.from !== node.id && c.to !== node.id)
    if (selectedNode.value?.id === node.id) {
      selectedNode.value = null
    }
  }
}

const deleteConnection = () => {
  if (selectedConnection.value) {
    const index = connections.value.findIndex(c => c.id === selectedConnection.value.id)
    if (index > -1) {
      connections.value.splice(index, 1)
      selectedConnection.value = null
    }
  }
}

const getNodeColor = (type) => {
  const colors = {
    start: '#52c41a',
    end: '#f5222d',
    task: '#1890ff',
    approval: '#fa8c16',
    condition: '#722ed1',
    system: '#13c2c2'
  }
  return colors[type] || '#1890ff'
}

const getNodeIcon = (type) => {
  const icons = {
    start: 'PlayCircleOutlined',
    end: 'CheckSquareOutlined',
    task: 'CheckSquareOutlined',
    approval: 'CheckSquareOutlined',
    condition: 'BranchesOutlined',
    system: 'SettingOutlined'
  }
  return icons[type] || 'CheckSquareOutlined'
}

const getNodeTypeName = (type) => {
  const names = {
    start: '开始',
    end: '结束',
    task: '任务',
    approval: '审批',
    condition: '条件',
    system: '系统'
  }
  return names[type] || type
}

const getCategoryText = (category) => {
  const texts = {
    approval: '审批流程',
    business: '业务流程',
    system: '系统流程'
  }
  return texts[category] || category
}

const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value += 0.1
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value -= 0.1
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const undoAction = () => {
  message.info('撤销操作')
}

const redoAction = () => {
  message.info('重做操作')
}

const clearCanvas = () => {
  workflowNodes.value = []
  connections.value = []
  selectedNode.value = null
  selectedConnection.value = null
  message.success('画布已清空')
}

const saveWorkflow = () => {
  lastSaveTime.value = new Date().toLocaleTimeString()
  message.success('流程已保存')
}

const publishWorkflow = () => {
  if (workflowNodes.value.length === 0) {
    message.error('请先添加流程节点')
    return
  }
  message.success('流程发布成功')
}

const previewWorkflow = () => {
  showPreviewModal.value = true
}

const validateWorkflow = () => {
  if (workflowNodes.value.length === 0) {
    message.error('流程中没有节点')
    return
  }
  
  const startNodes = workflowNodes.value.filter(n => n.type === 'start')
  const endNodes = workflowNodes.value.filter(n => n.type === 'end')
  
  if (startNodes.length === 0) {
    message.error('流程缺少开始节点')
    return
  }
  
  if (endNodes.length === 0) {
    message.error('流程缺少结束节点')
    return
  }
  
  message.success('流程验证通过')
}

const exportWorkflow = () => {
  const workflowData = {
    config: workflowConfig,
    nodes: workflowNodes.value,
    connections: connections.value
  }
  
  const blob = new Blob([JSON.stringify(workflowData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${workflowConfig.name}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('流程导出成功')
}

const importWorkflow = () => {
  message.info('导入流程功能开发中...')
}

const createFromTemplate = (template) => {
  showTemplateModal.value = false
  message.success(`已选择模板：${template.name}`)
  // 这里可以根据模板创建流程
}

// 组件挂载
onMounted(() => {
  // 初始化一些示例节点
  nextTick(() => {
    createNode({ type: 'start', name: '开始' }, 100, 100)
    createNode({ type: 'approval', name: '审批' }, 300, 100)
    createNode({ type: 'end', name: '结束' }, 500, 100)
  })
})
</script>

<style scoped>
.workflow-design {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.title-area h2 {
  margin: 0;
  color: #262626;
}

.title-area p {
  margin: 4px 0 0;
  color: #8c8c8c;
}

.design-workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.components-panel {
  width: 280px;
  background: white;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h4 {
  margin: 0;
}

.component-groups {
  padding: 16px;
}

.component-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: #40a9ff;
  background-color: #f6ffed;
}

.component-item:active {
  cursor: grabbing;
}

.component-icon {
  font-size: 16px;
  color: #1890ff;
}

.component-name {
  font-size: 12px;
  color: #262626;
}

.design-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.canvas-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.zoom-info {
  font-size: 12px;
  color: #8c8c8c;
}

.canvas-area {
  flex: 1;
  position: relative;
  overflow: auto;
  background: white;
  transform-origin: top left;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, #ddd 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.workflow-node {
  position: absolute;
  width: 100px;
  height: 60px;
  background: #1890ff;
  border: 2px solid transparent;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: move;
  color: white;
  font-size: 12px;
  user-select: none;
  transition: all 0.3s;
}

.workflow-node:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.workflow-node.active {
  border-color: #40a9ff;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
}

.node-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.node-title {
  font-weight: 500;
  margin-bottom: 2px;
}

.node-type {
  font-size: 10px;
  opacity: 0.8;
}

.connection-points {
  position: absolute;
}

.input-point {
  position: absolute;
  left: -6px;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
  transform: translateY(-50%);
  cursor: crosshair;
}

.output-point {
  position: absolute;
  right: -6px;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #f5222d;
  border-radius: 50%;
  transform: translateY(-50%);
  cursor: crosshair;
}

.node-actions {
  position: absolute;
  top: -8px;
  right: -8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.workflow-node:hover .node-actions {
  opacity: 1;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  pointer-events: all;
  cursor: pointer;
  stroke-width: 2;
  transition: stroke-width 0.3s;
}

.connection-line:hover {
  stroke-width: 4;
}

.connection-line.active {
  stroke: #40a9ff;
  stroke-width: 3;
}

.properties-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #f0f0f0;
  overflow-y: auto;
}

.workflow-properties,
.node-properties,
.connection-properties {
  padding: 16px;
}

.bottom-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.toolbar-left {
  font-size: 12px;
  color: #8c8c8c;
}

.template-grid {
  max-height: 500px;
  overflow-y: auto;
}

.template-card {
  height: 200px;
  cursor: pointer;
}

.template-cover {
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-icon {
  font-size: 32px;
  color: white;
}

.template-desc p {
  margin-bottom: 8px;
  color: #595959;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.preview-content {
  max-height: 600px;
  overflow-y: auto;
}

.preview-info {
  margin-bottom: 24px;
}

.flow-preview-area {
  height: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.preview-placeholder {
  text-align: center;
  color: #8c8c8c;
}
</style>