<template>
  <div class="process-template">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>流程模板</h2>
        <div class="header-actions">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索模板"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建模板
          </a-button>
        </div>
      </div>
    </div>

    <!-- 模板分类和统计 -->
    <div class="template-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="模板总数" 
              :value="overview.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><FileOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已发布" 
              :value="overview.published" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="草稿" 
              :value="overview.draft" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><EditOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="本月使用" 
              :value="overview.monthlyUsage" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><EyeOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-space>
        <a-select v-model:value="filterCategory" placeholder="选择分类" style="width: 150px" allowClear>
          <a-select-option value="debt_collection">催收流程</a-select-option>
          <a-select-option value="approval">审批流程</a-select-option>
          <a-select-option value="payment">还款流程</a-select-option>
          <a-select-option value="case_management">案件管理</a-select-option>
          <a-select-option value="system">系统流程</a-select-option>
        </a-select>
        
        <a-select v-model:value="filterStatus" placeholder="选择状态" style="width: 120px" allowClear>
          <a-select-option value="published">已发布</a-select-option>
          <a-select-option value="draft">草稿</a-select-option>
          <a-select-option value="archived">已归档</a-select-option>
        </a-select>
        
        <a-select v-model:value="sortBy" placeholder="排序方式" style="width: 150px">
          <a-select-option value="created_desc">创建时间↓</a-select-option>
          <a-select-option value="created_asc">创建时间↑</a-select-option>
          <a-select-option value="usage_desc">使用频率↓</a-select-option>
          <a-select-option value="name_asc">名称A-Z</a-select-option>
        </a-select>
        
        <a-button type="primary" @click="applyFilters">筛选</a-button>
        <a-button @click="resetFilters">重置</a-button>
      </a-space>
    </div>

    <!-- 模板列表 -->
    <div class="template-list">
      <a-row :gutter="[16, 16]">
        <a-col 
          v-for="template in filteredTemplates" 
          :key="template.id"
          :span="8"
        >
          <a-card 
            class="template-card"
            :hoverable="true"
            @click="viewTemplate(template)"
          >
            <template #cover>
              <div class="template-cover">
                <div class="template-icon">
                  <component :is="getTemplateIcon(template.category)" />
                </div>
                <div class="template-overlay">
                  <a-space>
                    <a-button type="primary" size="small" @click.stop="useTemplate(template)">
                      使用模板
                    </a-button>
                    <a-button size="small" @click.stop="previewTemplate(template)">
                      预览
                    </a-button>
                  </a-space>
                </div>
              </div>
            </template>
            
            <a-card-meta 
              :title="template.name"
              :description="template.description"
            >
              <template #avatar>
                <a-badge :status="getStatusBadge(template.status)" />
              </template>
            </a-card-meta>
            
            <div class="template-info">
              <div class="template-meta">
                <a-tag :color="getCategoryColor(template.category)" size="small">
                  {{ getCategoryText(template.category) }}
                </a-tag>
                <span class="template-version">v{{ template.version }}</span>
              </div>
              
              <div class="template-stats">
                <span><EyeOutlined /> {{ template.usageCount }}</span>
                <span><StarOutlined /> {{ template.rating }}</span>
                <span><DownloadOutlined /> {{ template.downloadCount }}</span>
              </div>
              
              <div class="template-footer">
                <span class="template-author">{{ template.author }}</span>
                <span class="template-date">{{ template.updatedAt }}</span>
              </div>
            </div>
            
            <template #actions>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="editTemplate(template)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="copyTemplate(template)">
                      <CopyOutlined />
                      复制
                    </a-menu-item>
                    <a-menu-item @click="shareTemplate(template)">
                      <ShareAltOutlined />
                      分享
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="exportTemplate(template)">
                      <ExportOutlined />
                      导出
                    </a-menu-item>
                    <a-menu-item @click="deleteTemplate(template)" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total) => `共 ${total} 个模板`"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 创建模板模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建流程模板"
      width="800px"
      @ok="handleCreateSubmit"
      @cancel="resetCreateForm"
    >
      <a-form :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="模板名称" required>
          <a-input v-model:value="createForm.name" placeholder="请输入模板名称" />
        </a-form-item>
        
        <a-form-item label="模板分类" required>
          <a-select v-model:value="createForm.category" placeholder="请选择分类">
            <a-select-option value="debt_collection">催收流程</a-select-option>
            <a-select-option value="approval">审批流程</a-select-option>
            <a-select-option value="payment">还款流程</a-select-option>
            <a-select-option value="case_management">案件管理</a-select-option>
            <a-select-option value="system">系统流程</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="模板描述" required>
          <a-textarea v-model:value="createForm.description" placeholder="请输入模板描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="模板标签">
          <a-select 
            v-model:value="createForm.tags" 
            mode="tags" 
            placeholder="请输入标签"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="是否公开">
          <a-switch 
            v-model:checked="createForm.isPublic" 
            checked-children="公开" 
            un-checked-children="私有" 
          />
        </a-form-item>
        
        <a-form-item label="基于模板">
          <a-select v-model:value="createForm.baseTemplate" placeholder="选择基础模板(可选)" allowClear>
            <a-select-option value="basic_approval">基础审批模板</a-select-option>
            <a-select-option value="debt_reduction">债务减免模板</a-select-option>
            <a-select-option value="case_allocation">案件分配模板</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 模板预览模态框 -->
    <a-modal
      v-model:open="showPreviewModal"
      title="模板预览"
      width="1000px"
      :footer="null"
    >
      <div v-if="previewTemplate" class="template-preview">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="模板名称">{{ previewTemplate.name }}</a-descriptions-item>
          <a-descriptions-item label="版本">v{{ previewTemplate.version }}</a-descriptions-item>
          <a-descriptions-item label="分类">{{ getCategoryText(previewTemplate.category) }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(previewTemplate.status)">
              {{ getStatusText(previewTemplate.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="作者">{{ previewTemplate.author }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ previewTemplate.updatedAt }}</a-descriptions-item>
          <a-descriptions-item label="使用统计" :span="2">
            使用次数: {{ previewTemplate.usageCount }} | 评分: {{ previewTemplate.rating }} | 下载: {{ previewTemplate.downloadCount }}
          </a-descriptions-item>
          <a-descriptions-item label="模板描述" :span="2">{{ previewTemplate.description }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>流程结构</h4>
        <div class="process-structure">
          <div class="structure-diagram">
            <div v-for="(step, index) in previewTemplate.structure" :key="step.id" class="structure-step">
              <div class="step-node">
                <div class="step-icon">
                  <component :is="getStepIcon(step.type)" />
                </div>
                <div class="step-content">
                  <div class="step-name">{{ step.name }}</div>
                  <div class="step-desc">{{ step.description }}</div>
                </div>
              </div>
              <div v-if="index < previewTemplate.structure.length - 1" class="step-connector">
                <ArrowRightOutlined />
              </div>
            </div>
          </div>
        </div>
        
        <a-divider />
        
        <div class="template-actions">
          <a-space>
            <a-button type="primary" @click="useTemplate(previewTemplate)">
              使用此模板
            </a-button>
            <a-button @click="copyTemplate(previewTemplate)">
              复制模板
            </a-button>
            <a-button @click="exportTemplate(previewTemplate)">
              导出模板
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 模板市场 -->
    <a-card title="模板市场" class="template-market" v-if="showMarket">
      <template #extra>
        <a-button type="link" @click="showMarket = false">收起</a-button>
      </template>
      
      <div class="market-content">
        <a-tabs>
          <a-tab-pane key="hot" tab="热门推荐">
            <a-row :gutter="[16, 16]">
              <a-col v-for="template in marketTemplates.hot" :key="template.id" :span="6">
                <a-card size="small" :hoverable="true">
                  <a-card-meta :title="template.name" :description="template.description" />
                  <template #actions>
                    <a-button type="link" size="small" @click="importTemplate(template)">
                      导入
                    </a-button>
                    <a-button type="link" size="small" @click="previewMarketTemplate(template)">
                      预览
                    </a-button>
                  </template>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="latest" tab="最新发布">
            <a-row :gutter="[16, 16]">
              <a-col v-for="template in marketTemplates.latest" :key="template.id" :span="6">
                <a-card size="small" :hoverable="true">
                  <a-card-meta :title="template.name" :description="template.description" />
                  <template #actions>
                    <a-button type="link" size="small" @click="importTemplate(template)">
                      导入
                    </a-button>
                    <a-button type="link" size="small" @click="previewMarketTemplate(template)">
                      预览
                    </a-button>
                  </template>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="category" tab="分类浏览">
            <a-tree
              :tree-data="categoryTree"
              :default-expanded-keys="['debt_collection']"
              @select="selectCategory"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  ReloadOutlined,
  PlusOutlined,
  FileOutlined,
  CheckCircleOutlined,
  EditOutlined,
  EyeOutlined,
  StarOutlined,
  DownloadOutlined,
  CopyOutlined,
  ShareAltOutlined,
  ExportOutlined,
  DeleteOutlined,
  MoreOutlined,
  ArrowRightOutlined,
  PlaySquareOutlined,
  UserOutlined,
  SettingOutlined,
  ApartmentOutlined,
  DollarOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const showCreateModal = ref(false)
const showPreviewModal = ref(false)
const showMarket = ref(false)
const previewTemplate = ref(null)

// 筛选条件
const filterCategory = ref('')
const filterStatus = ref('')
const sortBy = ref('created_desc')

// 创建表单
const createForm = reactive({
  name: '',
  category: '',
  description: '',
  tags: [],
  isPublic: true,
  baseTemplate: ''
})

// 概览数据
const overview = reactive({
  total: 45,
  published: 32,
  draft: 8,
  monthlyUsage: 156
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 45
})

// 模板数据
const templates = ref([
  {
    id: 1,
    name: '债务减免审批流程',
    description: '标准化的债务减免申请和审批流程模板',
    category: 'debt_collection',
    status: 'published',
    version: '2.1.0',
    author: '张工程师',
    updatedAt: '2024-01-15',
    usageCount: 45,
    rating: 4.8,
    downloadCount: 128,
    tags: ['审批', '减免', '标准流程'],
    structure: [
      { id: 1, name: '申请提交', type: 'start', description: '客户提交减免申请' },
      { id: 2, name: '初审', type: 'approval', description: '催收员初审' },
      { id: 3, name: '复审', type: 'approval', description: '主管复审' },
      { id: 4, name: '终审', type: 'approval', description: '经理终审' },
      { id: 5, name: '结果通知', type: 'system', description: '系统通知结果' }
    ]
  },
  {
    id: 2,
    name: '案件自动分配流程',
    description: '基于规则引擎的案件智能分配流程',
    category: 'case_management',
    status: 'published',
    version: '1.5.2',
    author: '李分析师',
    updatedAt: '2024-01-12',
    usageCount: 89,
    rating: 4.6,
    downloadCount: 205,
    tags: ['自动化', '分配', '规则'],
    structure: [
      { id: 1, name: '案件录入', type: 'start', description: '录入新案件信息' },
      { id: 2, name: '规则匹配', type: 'system', description: '匹配分配规则' },
      { id: 3, name: '负载均衡', type: 'system', description: '平衡工作量' },
      { id: 4, name: '分配执行', type: 'system', description: '执行分配' },
      { id: 5, name: '通知确认', type: 'manual', description: '通知相关人员' }
    ]
  },
  {
    id: 3,
    name: '还款计划制定流程',
    description: '个性化还款计划制定和确认流程',
    category: 'payment',
    status: 'draft',
    version: '0.8.0',
    author: '王顾问',
    updatedAt: '2024-01-10',
    usageCount: 12,
    rating: 4.2,
    downloadCount: 34,
    tags: ['还款', '计划', '个性化'],
    structure: [
      { id: 1, name: '客户评估', type: 'manual', description: '评估客户还款能力' },
      { id: 2, name: '方案制定', type: 'manual', description: '制定还款方案' },
      { id: 3, name: '客户确认', type: 'approval', description: '客户确认方案' },
      { id: 4, name: '合同签署', type: 'manual', description: '签署还款协议' }
    ]
  }
])

// 市场模板数据
const marketTemplates = reactive({
  hot: [
    { id: 101, name: '标准催收流程', description: '行业标准催收流程模板' },
    { id: 102, name: '风险评估流程', description: '客户风险评估流程' },
    { id: 103, name: '法务处理流程', description: '法务案件处理流程' },
    { id: 104, name: '客户回访流程', description: '定期客户回访流程' }
  ],
  latest: [
    { id: 201, name: 'AI智能催收', description: 'AI辅助的智能催收流程' },
    { id: 202, name: '多渠道沟通', description: '多渠道客户沟通流程' },
    { id: 203, name: '绩效考核流程', description: '员工绩效考核流程' },
    { id: 204, name: '数据分析流程', description: '业务数据分析流程' }
  ]
})

// 分类树数据
const categoryTree = ref([
  {
    title: '催收流程',
    key: 'debt_collection',
    children: [
      { title: '电话催收', key: 'phone_collection' },
      { title: '上门催收', key: 'visit_collection' },
      { title: '法务催收', key: 'legal_collection' }
    ]
  },
  {
    title: '审批流程',
    key: 'approval',
    children: [
      { title: '减免审批', key: 'reduction_approval' },
      { title: '分期审批', key: 'installment_approval' },
      { title: '核销审批', key: 'writeoff_approval' }
    ]
  }
])

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates.value

  // 搜索过滤
  if (searchKeyword.value) {
    result = result.filter(t => 
      t.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      t.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 分类过滤
  if (filterCategory.value) {
    result = result.filter(t => t.category === filterCategory.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    result = result.filter(t => t.status === filterStatus.value)
  }

  // 排序
  switch (sortBy.value) {
    case 'created_asc':
      result.sort((a, b) => a.updatedAt.localeCompare(b.updatedAt))
      break
    case 'usage_desc':
      result.sort((a, b) => b.usageCount - a.usageCount)
      break
    case 'name_asc':
      result.sort((a, b) => a.name.localeCompare(b.name))
      break
    default: // created_desc
      result.sort((a, b) => b.updatedAt.localeCompare(a.updatedAt))
  }

  return result
})

// 方法定义
const getTemplateIcon = (category) => {
  const icons = {
    debt_collection: DollarOutlined,
    approval: UserOutlined,
    payment: DollarOutlined,
    case_management: ApartmentOutlined,
    system: SettingOutlined
  }
  return icons[category] || FileOutlined
}

const getStepIcon = (type) => {
  const icons = {
    start: PlaySquareOutlined,
    approval: UserOutlined,
    system: SettingOutlined,
    manual: EditOutlined
  }
  return icons[type] || FileOutlined
}

const getCategoryColor = (category) => {
  const colors = {
    debt_collection: 'blue',
    approval: 'green',
    payment: 'orange',
    case_management: 'purple',
    system: 'gray'
  }
  return colors[category] || 'default'
}

const getCategoryText = (category) => {
  const texts = {
    debt_collection: '催收流程',
    approval: '审批流程',
    payment: '还款流程',
    case_management: '案件管理',
    system: '系统流程'
  }
  return texts[category] || category
}

const getStatusBadge = (status) => {
  const badges = {
    published: 'success',
    draft: 'warning',
    archived: 'default'
  }
  return badges[status] || 'default'
}

const getStatusColor = (status) => {
  const colors = {
    published: 'green',
    draft: 'orange',
    archived: 'gray'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    published: '已发布',
    draft: '草稿',
    archived: '已归档'
  }
  return texts[status] || status
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleSearch = () => {
  pagination.current = 1
}

const applyFilters = () => {
  pagination.current = 1
}

const resetFilters = () => {
  filterCategory.value = ''
  filterStatus.value = ''
  sortBy.value = 'created_desc'
  searchKeyword.value = ''
  pagination.current = 1
}

const handlePageChange = () => {
  // 页面变化处理
}

const viewTemplate = (template) => {
  previewTemplate.value = template
  showPreviewModal.value = true
}

const useTemplate = (template) => {
  console.log('使用模板:', template)
}

const editTemplate = (template) => {
  console.log('编辑模板:', template)
}

const copyTemplate = (template) => {
  console.log('复制模板:', template)
}

const shareTemplate = (template) => {
  console.log('分享模板:', template)
}

const exportTemplate = (template) => {
  console.log('导出模板:', template)
}

const deleteTemplate = (template) => {
  console.log('删除模板:', template)
}

const handleCreateSubmit = () => {
  console.log('创建模板:', createForm)
  showCreateModal.value = false
  resetCreateForm()
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    category: '',
    description: '',
    tags: [],
    isPublic: true,
    baseTemplate: ''
  })
}

const importTemplate = (template) => {
  console.log('导入模板:', template)
}

const previewMarketTemplate = (template) => {
  console.log('预览市场模板:', template)
}

const selectCategory = (keys) => {
  console.log('选择分类:', keys)
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.process-template {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.template-overview {
  margin-bottom: 16px;
}

.filter-section {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.template-list {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 16px;
}

.template-card {
  height: 100%;
  transition: all 0.3s;
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.template-cover {
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.template-icon {
  font-size: 48px;
  opacity: 0.8;
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.template-info {
  margin-top: 12px;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-version {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.template-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.template-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.pagination-wrapper {
  margin-top: 24px;
  text-align: center;
}

.template-preview {
  max-height: 600px;
  overflow-y: auto;
}

.process-structure {
  margin: 16px 0;
}

.structure-diagram {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  overflow-x: auto;
}

.structure-step {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-node {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 2px solid #e6e6e6;
  min-width: 200px;
}

.step-icon {
  font-size: 20px;
  color: #1890ff;
}

.step-content {
  flex: 1;
}

.step-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 12px;
  color: #666;
}

.step-connector {
  color: #1890ff;
  font-size: 18px;
}

.template-actions {
  text-align: center;
  margin-top: 16px;
}

.template-market {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.market-content {
  min-height: 300px;
}
</style>