<template>
  <div class="approval-process">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>审批流程</h2>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            发起审批
          </a-button>
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="审批状态">
          <a-select v-model:value="filterForm.status" placeholder="请选择状态" style="width: 150px" allowClear>
            <a-select-option value="pending">待审批</a-select-option>
            <a-select-option value="approved">已通过</a-select-option>
            <a-select-option value="rejected">已拒绝</a-select-option>
            <a-select-option value="cancelled">已取消</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="流程类型">
          <a-select v-model:value="filterForm.processType" placeholder="请选择类型" style="width: 150px" allowClear>
            <a-select-option value="case_allocation">案件分配</a-select-option>
            <a-select-option value="debt_reduction">债务减免</a-select-option>
            <a-select-option value="payment_plan">还款计划</a-select-option>
            <a-select-option value="case_transfer">案件转移</a-select-option>
            <a-select-option value="write_off">呆账核销</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="申请人">
          <a-input v-model:value="filterForm.applicant" placeholder="请输入申请人" style="width: 150px" />
        </a-form-item>
        <a-form-item label="申请时间">
          <a-range-picker v-model:value="filterForm.dateRange" style="width: 240px" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="resetFilter">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic title="待审批" :value="stats.pending" :value-style="{ color: '#faad14' }">
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="已通过" :value="stats.approved" :value-style="{ color: '#52c41a' }">
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="已拒绝" :value="stats.rejected" :value-style="{ color: '#ff4d4f' }">
              <template #prefix><CloseCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="本月处理" :value="stats.monthlyProcessed" :value-style="{ color: '#1890ff' }">
              <template #prefix><BarChartOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 审批列表 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="approvalList"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'processType'">
            <span>{{ getProcessTypeText(record.processType) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看详情
              </a-button>
              <a-button v-if="record.status === 'pending'" type="link" size="small" @click="handleApproval(record)">
                审批
              </a-button>
              <a-button type="link" size="small" @click="viewHistory(record)">
                审批历史
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建审批流程模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="发起审批"
      width="800px"
      @ok="handleCreateSubmit"
      @cancel="resetCreateForm"
    >
      <a-form :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="流程类型" required>
          <a-select v-model:value="createForm.processType" placeholder="请选择流程类型">
            <a-select-option value="case_allocation">案件分配审批</a-select-option>
            <a-select-option value="debt_reduction">债务减免审批</a-select-option>
            <a-select-option value="payment_plan">还款计划审批</a-select-option>
            <a-select-option value="case_transfer">案件转移审批</a-select-option>
            <a-select-option value="write_off">呆账核销审批</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="审批标题" required>
          <a-input v-model:value="createForm.title" placeholder="请输入审批标题" />
        </a-form-item>
        <a-form-item label="优先级" required>
          <a-select v-model:value="createForm.priority" placeholder="请选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="审批人" required>
          <a-select v-model:value="createForm.approvers" mode="multiple" placeholder="请选择审批人">
            <a-select-option value="user1">张经理</a-select-option>
            <a-select-option value="user2">李主任</a-select-option>
            <a-select-option value="user3">王总监</a-select-option>
            <a-select-option value="user4">赵部长</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="申请说明" required>
          <a-textarea v-model:value="createForm.description" placeholder="请输入申请说明" :rows="4" />
        </a-form-item>
        <a-form-item label="相关附件">
          <a-upload
            v-model:file-list="createForm.attachments"
            :before-upload="() => false"
            multiple
          >
            <a-button>
              <template #icon><UploadOutlined /></template>
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 审批处理模态框 -->
    <a-modal
      v-model:open="showApprovalModal"
      title="审批处理"
      width="600px"
      @ok="handleApprovalSubmit"
      @cancel="resetApprovalForm"
    >
      <div class="approval-detail">
        <a-descriptions title="申请信息" :column="2" bordered>
          <a-descriptions-item label="申请标题">{{ currentApproval?.title }}</a-descriptions-item>
          <a-descriptions-item label="流程类型">{{ getProcessTypeText(currentApproval?.processType) }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentApproval?.applicant }}</a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ currentApproval?.createTime }}</a-descriptions-item>
          <a-descriptions-item label="申请说明" :span="2">{{ currentApproval?.description }}</a-descriptions-item>
        </a-descriptions>
      </div>
      
      <a-divider />
      
      <a-form :model="approvalForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="审批结果" required>
          <a-radio-group v-model:value="approvalForm.result">
            <a-radio value="approved">通过</a-radio>
            <a-radio value="rejected">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审批意见" required>
          <a-textarea v-model:value="approvalForm.comment" placeholder="请输入审批意见" :rows="4" />
        </a-form-item>
        <a-form-item v-if="approvalForm.result === 'approved'" label="执行时间">
          <a-date-picker v-model:value="approvalForm.executeTime" show-time placeholder="请选择执行时间" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="审批详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentApproval" class="detail-content">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="申请标题">{{ currentApproval.title }}</a-descriptions-item>
          <a-descriptions-item label="流程类型">{{ getProcessTypeText(currentApproval.processType) }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentApproval.applicant }}</a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(currentApproval.status)">
              {{ getStatusText(currentApproval.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentApproval.priority)">
              {{ getPriorityText(currentApproval.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ currentApproval.createTime }}</a-descriptions-item>
          <a-descriptions-item label="申请说明" :span="2">{{ currentApproval.description }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>审批流程</h4>
        <a-steps :current="getCurrentStep(currentApproval.status)" direction="vertical" size="small">
          <a-step title="提交申请" :description="`${currentApproval.applicant} - ${currentApproval.createTime}`" />
          <a-step 
            v-for="step in currentApproval.approvalSteps" 
            :key="step.id"
            :title="step.title"
            :description="step.description"
            :status="step.status"
          />
        </a-steps>
      </div>
    </a-modal>

    <!-- 审批历史模态框 -->
    <a-modal
      v-model:open="showHistoryModal"
      title="审批历史"
      width="800px"
      :footer="null"
    >
      <a-timeline>
        <a-timeline-item v-for="item in approvalHistory" :key="item.id" :color="getTimelineColor(item.action)">
          <template #dot>
            <component :is="getTimelineIcon(item.action)" />
          </template>
          <div class="timeline-content">
            <div class="timeline-header">
              <span class="action">{{ item.action }}</span>
              <span class="time">{{ item.time }}</span>
            </div>
            <div class="timeline-body">
              <p><strong>操作人：</strong>{{ item.operator }}</p>
              <p v-if="item.comment"><strong>备注：</strong>{{ item.comment }}</p>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  PlusOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  BarChartOutlined,
  UploadOutlined,
  UserOutlined,
  FileTextOutlined,
  AuditOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showApprovalModal = ref(false)
const showDetailModal = ref(false)
const showHistoryModal = ref(false)
const currentApproval = ref(null)

// 筛选表单
const filterForm = reactive({
  status: undefined,
  processType: undefined,
  applicant: '',
  dateRange: []
})

// 创建表单
const createForm = reactive({
  processType: '',
  title: '',
  priority: '',
  approvers: [],
  description: '',
  attachments: []
})

// 审批表单
const approvalForm = reactive({
  result: '',
  comment: '',
  executeTime: null
})

// 统计数据
const stats = reactive({
  pending: 24,
  approved: 156,
  rejected: 12,
  monthlyProcessed: 89
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '审批编号',
    dataIndex: 'approvalNo',
    key: 'approvalNo',
    width: 120,
    fixed: 'left'
  },
  {
    title: '审批标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '流程类型',
    dataIndex: 'processType',
    key: 'processType',
    width: 120
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    key: 'applicant',
    width: 100
  },
  {
    title: '当前状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 80
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 审批列表数据
const approvalList = ref([
  {
    id: 1,
    approvalNo: 'AP2024010001',
    title: '案件CASE001债务减免申请',
    processType: 'debt_reduction',
    applicant: '李催收',
    status: 'pending',
    priority: 'high',
    createTime: '2024-01-15 09:30:00',
    updateTime: '2024-01-15 09:30:00',
    description: '客户因特殊情况申请债务减免，减免金额5000元',
    approvalSteps: [
      { id: 1, title: '主管审批', description: '张主管 - 待审批', status: 'process' },
      { id: 2, title: '经理审批', description: '待张主管审批通过', status: 'wait' },
      { id: 3, title: '完成', description: '流程结束', status: 'wait' }
    ]
  },
  {
    id: 2,
    approvalNo: 'AP2024010002',
    title: '案件分配方案调整',
    processType: 'case_allocation',
    applicant: '王分配',
    status: 'approved',
    priority: 'medium',
    createTime: '2024-01-14 14:20:00',
    updateTime: '2024-01-15 10:15:00',
    description: '调整案件分配规则，优化工作量平衡',
    approvalSteps: [
      { id: 1, title: '主管审批', description: '张主管 - 已通过', status: 'finish' },
      { id: 2, title: '经理审批', description: '李经理 - 已通过', status: 'finish' },
      { id: 3, title: '完成', description: '流程结束', status: 'finish' }
    ]
  },
  {
    id: 3,
    approvalNo: 'AP2024010003',
    title: '呆账核销申请',
    processType: 'write_off',
    applicant: '赵核销',
    status: 'rejected',
    priority: 'high',
    createTime: '2024-01-13 16:45:00',
    updateTime: '2024-01-14 11:30:00',
    description: '客户失联超过1年，申请核销处理',
    approvalSteps: [
      { id: 1, title: '主管审批', description: '张主管 - 已通过', status: 'finish' },
      { id: 2, title: '经理审批', description: '李经理 - 已拒绝', status: 'error' },
      { id: 3, title: '完成', description: '流程结束', status: 'wait' }
    ]
  }
])

// 审批历史数据
const approvalHistory = ref([
  {
    id: 1,
    action: '提交申请',
    operator: '李催收',
    time: '2024-01-15 09:30:00',
    comment: '提交债务减免申请'
  },
  {
    id: 2,
    action: '审批通过',
    operator: '张主管',
    time: '2024-01-15 11:15:00',
    comment: '同意减免申请，金额合理'
  },
  {
    id: 3,
    action: '流程完成',
    operator: '系统',
    time: '2024-01-15 11:16:00',
    comment: '审批流程执行完成'
  }
])

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    approved: 'green',
    rejected: 'red',
    cancelled: 'gray'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getProcessTypeText = (type) => {
  const texts = {
    case_allocation: '案件分配',
    debt_reduction: '债务减免',
    payment_plan: '还款计划',
    case_transfer: '案件转移',
    write_off: '呆账核销'
  }
  return texts[type] || type
}

const getCurrentStep = (status) => {
  const stepMap = {
    pending: 0,
    approved: 2,
    rejected: 1,
    cancelled: 0
  }
  return stepMap[status] || 0
}

const getTimelineColor = (action) => {
  const colors = {
    '提交申请': 'blue',
    '审批通过': 'green',
    '审批拒绝': 'red',
    '流程完成': 'green'
  }
  return colors[action] || 'blue'
}

const getTimelineIcon = (action) => {
  const icons = {
    '提交申请': FileTextOutlined,
    '审批通过': CheckCircleOutlined,
    '审批拒绝': CloseCircleOutlined,
    '流程完成': AuditOutlined
  }
  return icons[action] || UserOutlined
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleSearch = () => {
  pagination.current = 1
  refreshData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    status: undefined,
    processType: undefined,
    applicant: '',
    dateRange: []
  })
  handleSearch()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  refreshData()
}

const viewDetail = (record) => {
  currentApproval.value = record
  showDetailModal.value = true
}

const handleApproval = (record) => {
  currentApproval.value = record
  showApprovalModal.value = true
}

const viewHistory = (record) => {
  currentApproval.value = record
  showHistoryModal.value = true
}

const handleCreateSubmit = () => {
  console.log('创建审批:', createForm)
  showCreateModal.value = false
  resetCreateForm()
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    processType: '',
    title: '',
    priority: '',
    approvers: [],
    description: '',
    attachments: []
  })
}

const handleApprovalSubmit = () => {
  console.log('审批处理:', approvalForm)
  showApprovalModal.value = false
  resetApprovalForm()
}

const resetApprovalForm = () => {
  Object.assign(approvalForm, {
    result: '',
    comment: '',
    executeTime: null
  })
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.approval-process {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-section {
  margin-bottom: 16px;
}

.table-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.approval-detail {
  margin-bottom: 16px;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-header .action {
  font-weight: 500;
  color: #1890ff;
}

.timeline-header .time {
  color: #999;
  font-size: 12px;
}

.timeline-body {
  color: #666;
  font-size: 14px;
}

.timeline-body p {
  margin: 4px 0;
}
</style>