<template>
  <div class="training-archive">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>培训档案</h2>
        <div class="header-actions">
          <a-button @click="exportArchive">
            <ExportOutlined /> 导出档案
          </a-button>
          <a-button @click="generateReport">
            <FileTextOutlined /> 生成报告
          </a-button>
          <a-button @click="manageCertificates">
            <SafetyCertificateOutlined /> 证书管理
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总培训记录" 
              :value="stats.totalRecords" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已颁发证书" 
              :value="stats.totalCertificates" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="累计培训时长" 
              :value="stats.totalHours" 
              suffix="小时"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均通过率" 
              :value="stats.avgPassRate" 
              suffix="%"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <a-card class="search-card">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="员工姓名">
          <a-input 
            v-model:value="searchForm.name" 
            placeholder="请输入员工姓名"
            style="width: 150px"
          />
        </a-form-item>
        <a-form-item label="部门">
          <a-select 
            v-model:value="searchForm.department" 
            placeholder="请选择部门"
            style="width: 120px"
            allowClear
          >
            <a-select-option value="催收一部">催收一部</a-select-option>
            <a-select-option value="催收二部">催收二部</a-select-option>
            <a-select-option value="催收三部">催收三部</a-select-option>
            <a-select-option value="法务部">法务部</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="培训类型">
          <a-select 
            v-model:value="searchForm.trainingType" 
            placeholder="请选择类型"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="入职培训">入职培训</a-select-option>
            <a-select-option value="技能培训">技能培训</a-select-option>
            <a-select-option value="合规培训">合规培训</a-select-option>
            <a-select-option value="考试认证">考试认证</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时间范围">
          <a-range-picker 
            v-model:value="searchForm.dateRange"
            format="YYYY-MM-DD"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="证书状态">
          <a-select 
            v-model:value="searchForm.certificateStatus" 
            placeholder="证书状态"
            style="width: 120px"
            allowClear
          >
            <a-select-option value="已获得">已获得</a-select-option>
            <a-select-option value="未获得">未获得</a-select-option>
            <a-select-option value="已过期">已过期</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined /> 搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 培训档案表格 -->
    <a-card>
      <div class="table-toolbar">
        <div class="toolbar-left">
          <a-checkbox 
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchExport"
            style="margin-left: 16px"
          >
            批量导出
          </a-button>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchGenerateCertificate"
            style="margin-left: 8px"
          >
            批量证书
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-select 
            v-model:value="viewMode"
            style="width: 120px; margin-right: 8px"
          >
            <a-select-option value="table">表格视图</a-select-option>
            <a-select-option value="card">卡片视图</a-select-option>
            <a-select-option value="timeline">时间线视图</a-select-option>
          </a-select>
          <a-input-search
            placeholder="快速搜索档案"
            style="width: 250px"
            @search="quickSearch"
          />
        </div>
      </div>

      <!-- 表格视图 -->
      <a-table
        v-if="viewMode === 'table'"
        :columns="columns"
        :dataSource="filteredArchives"
        :rowSelection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
        :expandedRowKeys="expandedRowKeys"
        @expand="onExpand"
      >
        <template #name="{ record }">
          <div class="employee-info">
            <a-avatar :size="32" style="margin-right: 8px">
              {{ record.name.charAt(0) }}
            </a-avatar>
            <div>
              <div>{{ record.name }}</div>
              <small style="color: #666">{{ record.department }}</small>
            </div>
          </div>
        </template>
        <template #trainingType="{ text }">
          <a-tag :color="getTrainingTypeColor(text)">{{ text }}</a-tag>
        </template>
        <template #duration="{ text }">
          {{ text }} 小时
        </template>
        <template #score="{ text }">
          <span :style="{ color: text >= 80 ? '#52c41a' : text >= 60 ? '#fa8c16' : '#ff4d4f' }">
            {{ text }}分
          </span>
        </template>
        <template #certificateStatus="{ text }">
          <a-badge 
            :status="getCertificateStatusBadge(text)" 
            :text="text" 
          />
        </template>
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="viewDetail(record)">
              查看详情
            </a-button>
            <a-button type="link" size="small" @click="downloadCertificate(record)">
              下载证书
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="viewProgress(record)">
                    <BarChartOutlined /> 学习进度
                  </a-menu-item>
                  <a-menu-item @click="editRecord(record)">
                    <EditOutlined /> 编辑记录
                  </a-menu-item>
                  <a-menu-item @click="addNote(record)">
                    <CommentOutlined /> 添加备注
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
        <template #expandedRowRender="{ record }">
          <div class="expanded-content">
            <a-descriptions :column="3" size="small">
              <a-descriptions-item label="培训课程">{{ record.courseName }}</a-descriptions-item>
              <a-descriptions-item label="培训讲师">{{ record.instructor }}</a-descriptions-item>
              <a-descriptions-item label="培训地点">{{ record.location }}</a-descriptions-item>
              <a-descriptions-item label="开始时间">{{ record.startDate }}</a-descriptions-item>
              <a-descriptions-item label="结束时间">{{ record.endDate }}</a-descriptions-item>
              <a-descriptions-item label="学习进度">
                <a-progress :percent="record.progress" size="small" />
              </a-descriptions-item>
            </a-descriptions>
            <div v-if="record.notes" style="margin-top: 8px">
              <strong>备注：</strong>{{ record.notes }}
            </div>
          </div>
        </template>
      </a-table>

      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="card-view">
        <a-row :gutter="[16, 16]">
          <a-col :span="8" v-for="record in filteredArchives" :key="record.id">
            <a-card 
              :hoverable="true"
              @click="viewDetail(record)"
              class="archive-card"
            >
              <template #title>
                <div class="card-title">
                  <a-avatar :size="32">{{ record.name.charAt(0) }}</a-avatar>
                  <div style="margin-left: 8px">
                    <div>{{ record.name }}</div>
                    <small>{{ record.department }}</small>
                  </div>
                </div>
              </template>
              <template #extra>
                <a-tag :color="getTrainingTypeColor(record.trainingType)">
                  {{ record.trainingType }}
                </a-tag>
              </template>
              <div class="card-content">
                <p><strong>课程：</strong>{{ record.courseName }}</p>
                <p><strong>时长：</strong>{{ record.duration }} 小时</p>
                <p><strong>成绩：</strong>
                  <span :style="{ color: record.score >= 80 ? '#52c41a' : record.score >= 60 ? '#fa8c16' : '#ff4d4f' }">
                    {{ record.score }}分
                  </span>
                </p>
                <p><strong>证书：</strong>
                  <a-badge 
                    :status="getCertificateStatusBadge(record.certificateStatus)" 
                    :text="record.certificateStatus" 
                  />
                </p>
                <div class="card-actions">
                  <a-button type="link" size="small" @click.stop="downloadCertificate(record)">
                    下载证书
                  </a-button>
                  <a-button type="link" size="small" @click.stop="viewProgress(record)">
                    学习进度
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 时间线视图 -->
      <div v-if="viewMode === 'timeline'" class="timeline-view">
        <a-timeline>
          <a-timeline-item 
            v-for="record in timelineData" 
            :key="record.id"
            :color="getTimelineColor(record)"
          >
            <template #dot>
              <a-avatar :size="24">{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <div class="timeline-content">
              <div class="timeline-header">
                <h4>{{ record.name }} - {{ record.courseName }}</h4>
                <span class="timeline-date">{{ record.completedDate }}</span>
              </div>
              <div class="timeline-details">
                <a-tag :color="getTrainingTypeColor(record.trainingType)">{{ record.trainingType }}</a-tag>
                <span>成绩：{{ record.score }}分</span>
                <span>时长：{{ record.duration }}小时</span>
                <a-badge 
                  :status="getCertificateStatusBadge(record.certificateStatus)" 
                  :text="record.certificateStatus" 
                />
              </div>
              <div v-if="record.notes" class="timeline-notes">
                {{ record.notes }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="培训档案详情"
      width="900px"
      :footer="null"
    >
      <div v-if="selectedRecord" class="record-detail">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="employee-profile">
              <a-avatar :size="80" style="margin-bottom: 16px">
                {{ selectedRecord.name.charAt(0) }}
              </a-avatar>
              <h3>{{ selectedRecord.name }}</h3>
              <p>{{ selectedRecord.department }} | {{ selectedRecord.position }}</p>
            </div>
          </a-col>
          <a-col :span="16">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="培训课程" :span="2">
                {{ selectedRecord.courseName }}
              </a-descriptions-item>
              <a-descriptions-item label="培训类型">
                <a-tag :color="getTrainingTypeColor(selectedRecord.trainingType)">
                  {{ selectedRecord.trainingType }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="培训时长">
                {{ selectedRecord.duration }} 小时
              </a-descriptions-item>
              <a-descriptions-item label="开始时间">
                {{ selectedRecord.startDate }}
              </a-descriptions-item>
              <a-descriptions-item label="结束时间">
                {{ selectedRecord.endDate }}
              </a-descriptions-item>
              <a-descriptions-item label="培训讲师">
                {{ selectedRecord.instructor }}
              </a-descriptions-item>
              <a-descriptions-item label="培训地点">
                {{ selectedRecord.location }}
              </a-descriptions-item>
              <a-descriptions-item label="考试成绩">
                <span :style="{ color: selectedRecord.score >= 80 ? '#52c41a' : selectedRecord.score >= 60 ? '#fa8c16' : '#ff4d4f' }">
                  {{ selectedRecord.score }}分
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="学习进度">
                <a-progress :percent="selectedRecord.progress" />
              </a-descriptions-item>
              <a-descriptions-item label="证书状态">
                <a-badge 
                  :status="getCertificateStatusBadge(selectedRecord.certificateStatus)" 
                  :text="selectedRecord.certificateStatus" 
                />
              </a-descriptions-item>
              <a-descriptions-item label="证书编号" v-if="selectedRecord.certificateNumber">
                {{ selectedRecord.certificateNumber }}
              </a-descriptions-item>
              <a-descriptions-item label="证书有效期" v-if="selectedRecord.certificateExpiry">
                {{ selectedRecord.certificateExpiry }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>

        <a-divider />

        <a-tabs>
          <a-tab-pane key="materials" tab="学习资料">
            <a-list
              :data-source="selectedRecord.materials"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a @click="downloadMaterial(item)">{{ item.name }}</a>
                    </template>
                    <template #description>
                      {{ item.type }} | {{ item.size }} | 下载次数: {{ item.downloads }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
          <a-tab-pane key="assessments" tab="考核记录">
            <a-table
              :columns="assessmentColumns"
              :data-source="selectedRecord.assessments"
              :pagination="false"
              size="small"
            >
              <template #score="{ text }">
                <span :style="{ color: text >= 80 ? '#52c41a' : text >= 60 ? '#fa8c16' : '#ff4d4f' }">
                  {{ text }}分
                </span>
              </template>
              <template #result="{ text }">
                <a-tag :color="text === '通过' ? 'green' : 'red'">{{ text }}</a-tag>
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="notes" tab="备注记录">
            <div class="notes-section">
              <a-textarea 
                v-model:value="newNote"
                placeholder="添加新备注..."
                :rows="3"
                style="margin-bottom: 8px"
              />
              <a-button type="primary" @click="saveNote">添加备注</a-button>
              
              <a-divider />
              
              <a-list
                :data-source="selectedRecord.noteHistory"
                size="small"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>
                        {{ item.author }} - {{ item.date }}
                      </template>
                      <template #description>
                        {{ item.content }}
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 证书管理弹窗 -->
    <a-modal
      v-model:open="certificateModalVisible"
      title="证书管理"
      width="1000px"
      :footer="null"
    >
      <div class="certificate-management">
        <div class="certificate-toolbar">
          <a-button type="primary" @click="generateCertificate">
            <SafetyCertificateOutlined /> 生成证书
          </a-button>
          <a-button @click="importCertificates" style="margin-left: 8px">
            <ImportOutlined /> 批量导入
          </a-button>
          <a-select 
            v-model:value="certificateFilter.status"
            placeholder="证书状态"
            style="width: 120px; margin-left: 16px"
            allowClear
          >
            <a-select-option value="已获得">已获得</a-select-option>
            <a-select-option value="未获得">未获得</a-select-option>
            <a-select-option value="已过期">已过期</a-select-option>
          </a-select>
        </div>

        <a-table
          :columns="certificateColumns"
          :data-source="filteredCertificates"
          :pagination="certificatePagination"
          rowKey="id"
          size="small"
        >
          <template #status="{ text }">
            <a-badge :status="getCertificateStatusBadge(text)" :text="text" />
          </template>
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="downloadCertificate(record)">下载</a-button>
              <a-button type="link" size="small" @click="previewCertificate(record)">预览</a-button>
              <a-button type="link" size="small" @click="sendCertificate(record)">发送</a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 学习进度弹窗 -->
    <a-modal
      v-model:open="progressModalVisible"
      title="学习进度详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedProgressRecord" class="progress-detail">
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="12">
            <a-card>
              <a-statistic title="总学习时长" :value="selectedProgressRecord.totalHours" suffix="小时" />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card>
              <a-statistic title="学习进度" :value="selectedProgressRecord.progress" suffix="%" />
            </a-card>
          </a-col>
        </a-row>

        <div class="progress-chart">
          <div ref="progressChartRef" style="width: 100%; height: 300px"></div>
        </div>

        <a-divider />

        <h4>学习明细</h4>
        <a-table
          :columns="progressColumns"
          :data-source="selectedProgressRecord.details"
          :pagination="false"
          size="small"
        >
          <template #duration="{ text }">
            {{ text }} 分钟
          </template>
          <template #status="{ text }">
            <a-tag :color="text === '已完成' ? 'green' : text === '进行中' ? 'blue' : 'default'">
              {{ text }}
            </a-tag>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import { 
  ExportOutlined, 
  SearchOutlined, 
  DownOutlined,
  FileTextOutlined,
  SafetyCertificateOutlined,
  BarChartOutlined,
  EditOutlined,
  CommentOutlined,
  ImportOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)
const expandedRowKeys = ref([])
const viewMode = ref('table')

// 统计数据
const stats = ref({
  totalRecords: 1256,
  totalCertificates: 892,
  totalHours: 8945,
  avgPassRate: 87.3
})

// 搜索表单
const searchForm = reactive({
  name: '',
  department: '',
  trainingType: '',
  dateRange: [],
  certificateStatus: ''
})

// 培训档案数据
const archives = ref([
  {
    id: 1,
    name: '张三',
    department: '催收一部',
    position: '催收专员',
    courseName: '债务催收法律法规培训',
    trainingType: '合规培训',
    duration: 8,
    startDate: '2024-01-15',
    endDate: '2024-01-17',
    completedDate: '2024-01-17',
    instructor: '张老师',
    location: '培训中心A座',
    score: 89,
    progress: 100,
    certificateStatus: '已获得',
    certificateNumber: 'CERT-2024-001',
    certificateExpiry: '2026-01-17',
    notes: '表现优秀，积极参与讨论',
    materials: [
      { id: 1, name: '法律法规手册.pdf', type: 'PDF', size: '2.5MB', downloads: 15 },
      { id: 2, name: '案例分析.pptx', type: 'PPT', size: '5.8MB', downloads: 12 }
    ],
    assessments: [
      { id: 1, name: '理论考试', date: '2024-01-16', score: 85, result: '通过' },
      { id: 2, name: '实操考核', date: '2024-01-17', score: 92, result: '通过' }
    ],
    noteHistory: [
      { id: 1, author: '培训主管', date: '2024-01-17', content: '学员表现优秀，积极参与课堂讨论' },
      { id: 2, author: '张老师', date: '2024-01-16', content: '理论基础扎实，实操能力强' }
    ]
  },
  {
    id: 2,
    name: '李四',
    department: '催收二部',
    position: '催收主管',
    courseName: '客户沟通技巧培训',
    trainingType: '技能培训',
    duration: 6,
    startDate: '2024-01-10',
    endDate: '2024-01-12',
    completedDate: '2024-01-12',
    instructor: '李老师',
    location: '培训中心B座',
    score: 95,
    progress: 100,
    certificateStatus: '已获得',
    certificateNumber: 'CERT-2024-002',
    certificateExpiry: '2025-01-12',
    notes: '沟通技巧提升明显',
    materials: [
      { id: 1, name: '沟通技巧指南.pdf', type: 'PDF', size: '1.8MB', downloads: 20 },
      { id: 2, name: '实战案例.mp4', type: '视频', size: '128MB', downloads: 8 }
    ],
    assessments: [
      { id: 1, name: '角色扮演', date: '2024-01-11', score: 90, result: '通过' },
      { id: 2, name: '综合评估', date: '2024-01-12', score: 98, result: '通过' }
    ],
    noteHistory: [
      { id: 1, author: '李老师', date: '2024-01-12', content: '沟通技巧娴熟，善于处理复杂情况' }
    ]
  },
  {
    id: 3,
    name: '王五',
    department: '催收一部',
    position: '催收专员',
    courseName: '新员工入职培训',
    trainingType: '入职培训',
    duration: 16,
    startDate: '2024-01-08',
    endDate: '2024-01-10',
    completedDate: '2024-01-10',
    instructor: '王老师',
    location: '总部大楼',
    score: 78,
    progress: 100,
    certificateStatus: '已获得',
    certificateNumber: 'CERT-2024-003',
    certificateExpiry: '2025-01-10',
    notes: '需要加强业务知识学习',
    materials: [
      { id: 1, name: '公司制度手册.pdf', type: 'PDF', size: '3.2MB', downloads: 25 },
      { id: 2, name: '业务流程图.png', type: '图片', size: '1.1MB', downloads: 18 }
    ],
    assessments: [
      { id: 1, name: '制度考试', date: '2024-01-09', score: 75, result: '通过' },
      { id: 2, name: '业务考核', date: '2024-01-10', score: 80, result: '通过' }
    ],
    noteHistory: [
      { id: 1, author: '人事主管', date: '2024-01-10', content: '基础知识掌握良好，建议加强实践练习' }
    ]
  }
])

// 证书数据
const certificates = ref([
  {
    id: 1,
    employeeName: '张三',
    courseName: '债务催收法律法规培训',
    certificateNumber: 'CERT-2024-001',
    issueDate: '2024-01-17',
    expiryDate: '2026-01-17',
    status: '已获得'
  },
  {
    id: 2,
    employeeName: '李四',
    courseName: '客户沟通技巧培训',
    certificateNumber: 'CERT-2024-002',
    issueDate: '2024-01-12',
    expiryDate: '2025-01-12',
    status: '已获得'
  }
])

// 弹窗相关
const detailModalVisible = ref(false)
const certificateModalVisible = ref(false)
const progressModalVisible = ref(false)
const selectedRecord = ref(null)
const selectedProgressRecord = ref(null)
const newNote = ref('')

// 图表引用
const progressChartRef = ref(null)

// 筛选器
const certificateFilter = reactive({
  status: ''
})

// 表格列定义
const columns = [
  { 
    title: '员工信息', 
    dataIndex: 'name', 
    key: 'name', 
    slots: { customRender: 'name' },
    width: 200
  },
  { title: '培训课程', dataIndex: 'courseName', key: 'courseName' },
  { 
    title: '培训类型', 
    dataIndex: 'trainingType', 
    key: 'trainingType', 
    slots: { customRender: 'trainingType' },
    width: 120
  },
  { 
    title: '时长', 
    dataIndex: 'duration', 
    key: 'duration', 
    slots: { customRender: 'duration' },
    width: 80
  },
  { 
    title: '成绩', 
    dataIndex: 'score', 
    key: 'score', 
    slots: { customRender: 'score' },
    width: 80
  },
  { title: '完成时间', dataIndex: 'completedDate', key: 'completedDate', width: 120 },
  { 
    title: '证书状态', 
    dataIndex: 'certificateStatus', 
    key: 'certificateStatus', 
    slots: { customRender: 'certificateStatus' },
    width: 120
  },
  { 
    title: '操作', 
    key: 'action', 
    slots: { customRender: 'action' }, 
    width: 200, 
    fixed: 'right' 
  }
]

// 考核记录列定义
const assessmentColumns = [
  { title: '考核名称', dataIndex: 'name', key: 'name' },
  { title: '考核日期', dataIndex: 'date', key: 'date' },
  { title: '成绩', dataIndex: 'score', key: 'score', slots: { customRender: 'score' } },
  { title: '结果', dataIndex: 'result', key: 'result', slots: { customRender: 'result' } }
]

// 证书列定义
const certificateColumns = [
  { title: '员工姓名', dataIndex: 'employeeName', key: 'employeeName' },
  { title: '课程名称', dataIndex: 'courseName', key: 'courseName' },
  { title: '证书编号', dataIndex: 'certificateNumber', key: 'certificateNumber' },
  { title: '颁发日期', dataIndex: 'issueDate', key: 'issueDate' },
  { title: '有效期', dataIndex: 'expiryDate', key: 'expiryDate' },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' } },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 150 }
]

// 学习进度列定义
const progressColumns = [
  { title: '章节名称', dataIndex: 'chapter', key: 'chapter' },
  { title: '学习时长', dataIndex: 'duration', key: 'duration', slots: { customRender: 'duration' } },
  { title: '完成时间', dataIndex: 'completedAt', key: 'completedAt' },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' } }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

const certificatePagination = reactive({
  current: 1,
  pageSize: 8,
  total: 0,
  showSizeChanger: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
    checkAll.value = keys.length === filteredArchives.value.length
    indeterminate.value = keys.length > 0 && keys.length < filteredArchives.value.length
  }
}))

// 过滤后的档案数据
const filteredArchives = computed(() => {
  let filtered = archives.value
  
  if (searchForm.name) {
    filtered = filtered.filter(record => 
      record.name.toLowerCase().includes(searchForm.name.toLowerCase())
    )
  }
  
  if (searchForm.department) {
    filtered = filtered.filter(record => record.department === searchForm.department)
  }
  
  if (searchForm.trainingType) {
    filtered = filtered.filter(record => record.trainingType === searchForm.trainingType)
  }
  
  if (searchForm.certificateStatus) {
    filtered = filtered.filter(record => record.certificateStatus === searchForm.certificateStatus)
  }
  
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    const [startDate, endDate] = searchForm.dateRange
    filtered = filtered.filter(record => 
      record.completedDate >= startDate && record.completedDate <= endDate
    )
  }
  
  pagination.total = filtered.length
  return filtered
})

// 过滤后的证书数据
const filteredCertificates = computed(() => {
  let filtered = certificates.value
  
  if (certificateFilter.status) {
    filtered = filtered.filter(cert => cert.status === certificateFilter.status)
  }
  
  certificatePagination.total = filtered.length
  return filtered
})

// 时间线数据
const timelineData = computed(() => {
  return filteredArchives.value
    .sort((a, b) => new Date(b.completedDate) - new Date(a.completedDate))
    .slice(0, 20) // 显示最近20条记录
})

// 工具函数
const getTrainingTypeColor = (type) => {
  const colors = {
    '入职培训': 'blue',
    '技能培训': 'green',
    '合规培训': 'orange',
    '考试认证': 'purple'
  }
  return colors[type] || 'default'
}

const getCertificateStatusBadge = (status) => {
  const badges = {
    '已获得': 'success',
    '未获得': 'default',
    '已过期': 'error'
  }
  return badges[status] || 'default'
}

const getTimelineColor = (record) => {
  if (record.score >= 90) return 'green'
  if (record.score >= 80) return 'blue'
  if (record.score >= 60) return 'orange'
  return 'red'
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'dateRange') {
      searchForm[key] = []
    } else {
      searchForm[key] = ''
    }
  })
  pagination.current = 1
}

const quickSearch = (value) => {
  searchForm.name = value
  handleSearch()
}

const onCheckAllChange = (e) => {
  selectedRowKeys.value = e.target.checked ? filteredArchives.value.map(item => item.id) : []
  indeterminate.value = false
  checkAll.value = e.target.checked
}

const onExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value.push(record.id)
  } else {
    const index = expandedRowKeys.value.indexOf(record.id)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}

const viewDetail = (record) => {
  selectedRecord.value = record
  detailModalVisible.value = true
}

const viewProgress = (record) => {
  selectedProgressRecord.value = {
    ...record,
    totalHours: record.duration,
    details: [
      { chapter: '第一章：基础知识', duration: 120, completedAt: '2024-01-15 10:30', status: '已完成' },
      { chapter: '第二章：实务操作', duration: 150, completedAt: '2024-01-16 14:20', status: '已完成' },
      { chapter: '第三章：案例分析', duration: 90, completedAt: '2024-01-17 09:15', status: '已完成' },
      { chapter: '第四章：考核测试', duration: 60, completedAt: '2024-01-17 16:00', status: '已完成' }
    ]
  }
  progressModalVisible.value = true
  
  nextTick(() => {
    initProgressChart()
  })
}

const initProgressChart = () => {
  if (!progressChartRef.value) return
  
  const chart = echarts.init(progressChartRef.value)
  const option = {
    title: {
      text: '学习进度统计'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['第一章', '第二章', '第三章', '第四章']
    },
    yAxis: {
      type: 'value',
      name: '学习时长(分钟)'
    },
    series: [{
      data: [120, 150, 90, 60],
      type: 'bar',
      itemStyle: {
        color: '#1890ff'
      }
    }]
  }
  chart.setOption(option)
}

const downloadCertificate = (record) => {
  message.success(`正在下载 ${record.name || record.employeeName} 的证书`)
}

const downloadMaterial = (material) => {
  message.success(`正在下载 ${material.name}`)
}

const editRecord = (record) => {
  message.info('编辑培训记录功能')
}

const addNote = (record) => {
  message.info('添加备注功能')
}

const saveNote = () => {
  if (!newNote.value.trim()) {
    message.warning('请输入备注内容')
    return
  }
  
  const note = {
    id: Date.now(),
    author: '当前用户',
    date: new Date().toISOString().split('T')[0],
    content: newNote.value
  }
  
  selectedRecord.value.noteHistory.unshift(note)
  selectedRecord.value.notes = newNote.value
  newNote.value = ''
  message.success('备注添加成功')
}

const batchExport = () => {
  message.success(`成功导出 ${selectedRowKeys.value.length} 条培训记录`)
  selectedRowKeys.value = []
}

const batchGenerateCertificate = () => {
  message.success(`成功为 ${selectedRowKeys.value.length} 人生成证书`)
  selectedRowKeys.value = []
}

const exportArchive = () => {
  message.success('培训档案导出成功')
}

const generateReport = () => {
  message.success('培训报告生成成功')
}

const manageCertificates = () => {
  certificateModalVisible.value = true
}

const generateCertificate = () => {
  message.info('生成证书功能')
}

const importCertificates = () => {
  message.info('批量导入证书功能')
}

const previewCertificate = (record) => {
  message.info(`预览 ${record.employeeName} 的证书`)
}

const sendCertificate = (record) => {
  message.success(`证书已发送给 ${record.employeeName}`)
}

// 组件挂载
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.training-archive {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 24px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.employee-info {
  display: flex;
  align-items: center;
}

.expanded-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.card-view {
  padding: 16px 0;
}

.archive-card {
  cursor: pointer;
  transition: all 0.3s;
}

.archive-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-title {
  display: flex;
  align-items: center;
}

.card-content p {
  margin-bottom: 8px;
}

.card-actions {
  margin-top: 16px;
  text-align: right;
}

.timeline-view {
  padding: 16px;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-header h4 {
  margin: 0;
  color: #1890ff;
}

.timeline-date {
  color: #666;
  font-size: 12px;
}

.timeline-details {
  margin-bottom: 8px;
}

.timeline-details > * {
  margin-right: 16px;
}

.timeline-notes {
  color: #666;
  font-style: italic;
}

.record-detail {
  padding: 16px 0;
}

.employee-profile {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.employee-profile h3 {
  margin: 8px 0 4px 0;
  color: #1890ff;
}

.employee-profile p {
  margin: 0;
  color: #666;
}

.notes-section {
  padding: 16px 0;
}

.certificate-management {
  padding: 16px 0;
}

.certificate-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.progress-detail {
  padding: 16px 0;
}

.progress-chart {
  margin: 24px 0;
}

:deep(.ant-descriptions-item-label) {
  font-weight: bold;
}

:deep(.ant-timeline-item-content) {
  margin-left: 16px;
}
</style>