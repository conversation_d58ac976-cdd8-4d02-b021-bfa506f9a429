<template>
  <div class="exam-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>考试管理</h2>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined /> 创建考试
          </a-button>
          <a-button @click="manageQuestionBank">
            <DatabaseOutlined /> 题库管理
          </a-button>
          <a-button @click="exportResults">
            <ExportOutlined /> 导出成绩
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总考试数" 
              :value="stats.totalExams" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中考试" 
              :value="stats.activeExams" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="题库总题数" 
              :value="stats.totalQuestions" 
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均通过率" 
              :value="stats.avgPassRate" 
              suffix="%"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <a-card class="search-card">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="考试名称">
          <a-input 
            v-model:value="searchForm.name" 
            placeholder="请输入考试名称"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="考试类型">
          <a-select 
            v-model:value="searchForm.type" 
            placeholder="请选择类型"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="入职考试">入职考试</a-select-option>
            <a-select-option value="定期考核">定期考核</a-select-option>
            <a-select-option value="技能认证">技能认证</a-select-option>
            <a-select-option value="合规测试">合规测试</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="考试状态">
          <a-select 
            v-model:value="searchForm.status" 
            placeholder="请选择状态"
            style="width: 120px"
            allowClear
          >
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="published">已发布</a-select-option>
            <a-select-option value="ongoing">进行中</a-select-option>
            <a-select-option value="ended">已结束</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="创建人">
          <a-input 
            v-model:value="searchForm.creator" 
            placeholder="请输入创建人"
            style="width: 150px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined /> 搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 考试列表 -->
    <a-card>
      <div class="table-toolbar">
        <div class="toolbar-left">
          <a-checkbox 
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchPublish"
            style="margin-left: 16px"
          >
            批量发布
          </a-button>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchDelete"
            danger
            style="margin-left: 8px"
          >
            批量删除
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-input-search
            placeholder="快速搜索考试"
            style="width: 250px"
            @search="quickSearch"
          />
        </div>
      </div>

      <a-table
        :columns="columns"
        :dataSource="filteredExams"
        :rowSelection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
      >
        <template #type="{ text }">
          <a-tag :color="getTypeColor(text)">{{ text }}</a-tag>
        </template>
        <template #status="{ text }">
          <a-badge :status="getStatusBadge(text)" :text="getStatusText(text)" />
        </template>
        <template #duration="{ text }">
          {{ text }} 分钟
        </template>
        <template #passRate="{ text }">
          <a-progress 
            :percent="text" 
            size="small" 
            :stroke-color="text >= 80 ? '#52c41a' : text >= 60 ? '#fa8c16' : '#ff4d4f'"
          />
        </template>
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="viewExam(record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="editExam(record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="manageQuestions(record)">
                    <QuestionCircleOutlined /> 题目管理
                  </a-menu-item>
                  <a-menu-item @click="viewResults(record)">
                    <BarChartOutlined /> 成绩分析
                  </a-menu-item>
                  <a-menu-item @click="copyExam(record)">
                    <CopyOutlined /> 复制考试
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="deleteExam(record)" style="color: #ff4d4f">
                    <DeleteOutlined /> 删除考试
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑考试弹窗 -->
    <a-modal
      v-model:open="examModalVisible"
      :title="modalTitle"
      width="900px"
      :confirmLoading="modalLoading"
      @ok="handleExamSubmit"
      @cancel="closeExamModal"
    >
      <a-form :model="examForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="考试名称" required>
          <a-input v-model:value="examForm.name" placeholder="请输入考试名称" />
        </a-form-item>
        <a-form-item label="考试类型" required>
          <a-select v-model:value="examForm.type" placeholder="请选择类型">
            <a-select-option value="入职考试">入职考试</a-select-option>
            <a-select-option value="定期考核">定期考核</a-select-option>
            <a-select-option value="技能认证">技能认证</a-select-option>
            <a-select-option value="合规测试">合规测试</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="考试时长">
          <a-input-number 
            v-model:value="examForm.duration" 
            :min="10" 
            :step="10"
            placeholder="分钟"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="总分">
          <a-input-number 
            v-model:value="examForm.totalScore" 
            :min="1" 
            placeholder="总分"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="及格分数">
          <a-input-number 
            v-model:value="examForm.passScore" 
            :min="1" 
            :max="examForm.totalScore"
            placeholder="及格分数"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="考试说明">
          <a-textarea 
            v-model:value="examForm.description" 
            :rows="4"
            placeholder="请输入考试说明"
          />
        </a-form-item>
        <a-form-item label="考试时间">
          <a-range-picker 
            v-model:value="examForm.timeRange"
            show-time
            format="YYYY-MM-DD HH:mm"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="参考人员">
          <a-select 
            v-model:value="examForm.participants"
            mode="multiple"
            placeholder="请选择参考人员"
            :options="participantOptions"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="考试设置">
          <a-space direction="vertical" style="width: 100%">
            <a-checkbox v-model:checked="examForm.shuffleQuestions">
              随机打乱题目顺序
            </a-checkbox>
            <a-checkbox v-model:checked="examForm.allowReview">
              允许考生查看答案
            </a-checkbox>
            <a-checkbox v-model:checked="examForm.showScore">
              考试结束后显示分数
            </a-checkbox>
            <a-checkbox v-model:checked="examForm.limitAttempts">
              限制考试次数
            </a-checkbox>
            <div v-if="examForm.limitAttempts" style="margin-left: 24px">
              <a-input-number 
                v-model:value="examForm.maxAttempts" 
                :min="1" 
                :max="10"
                placeholder="最大次数"
              />
            </div>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 考试详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="考试详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedExam" class="exam-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="考试名称" :span="2">
            {{ selectedExam.name }}
          </a-descriptions-item>
          <a-descriptions-item label="考试类型">
            <a-tag :color="getTypeColor(selectedExam.type)">{{ selectedExam.type }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="考试状态">
            <a-badge :status="getStatusBadge(selectedExam.status)" :text="getStatusText(selectedExam.status)" />
          </a-descriptions-item>
          <a-descriptions-item label="考试时长">
            {{ selectedExam.duration }} 分钟
          </a-descriptions-item>
          <a-descriptions-item label="总分">
            {{ selectedExam.totalScore }} 分
          </a-descriptions-item>
          <a-descriptions-item label="及格分数">
            {{ selectedExam.passScore }} 分
          </a-descriptions-item>
          <a-descriptions-item label="通过率">
            {{ selectedExam.passRate }}%
          </a-descriptions-item>
          <a-descriptions-item label="参与人数">
            {{ selectedExam.participants.length }} 人
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedExam.createdAt }}
          </a-descriptions-item>
          <a-descriptions-item label="考试说明" :span="2">
            {{ selectedExam.description }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <a-tabs>
          <a-tab-pane key="questions" tab="考试题目">
            <div class="questions-list">
              <div v-for="(question, index) in selectedExam.questions" :key="question.id" class="question-item">
                <div class="question-header">
                  <span class="question-number">第{{ index + 1 }}题</span>
                  <a-tag :color="getQuestionTypeColor(question.type)">{{ question.type }}</a-tag>
                  <span class="question-score">{{ question.score }}分</span>
                </div>
                <div class="question-content">{{ question.content }}</div>
                <div v-if="question.type !== '简答题'" class="question-options">
                  <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-item">
                    <span :class="{ 'correct-answer': question.correctAnswer.includes(optIndex) }">
                      {{ String.fromCharCode(65 + optIndex) }}. {{ option }}
                    </span>
                  </div>
                </div>
                <div v-if="question.type === '简答题'" class="answer-template">
                  <strong>参考答案：</strong>{{ question.referenceAnswer }}
                </div>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="results" tab="成绩统计">
            <div class="results-stats">
              <a-row :gutter="16" style="margin-bottom: 24px">
                <a-col :span="8">
                  <a-card>
                    <a-statistic title="平均分" :value="selectedExam.avgScore" :precision="1" suffix="分" />
                  </a-card>
                </a-col>
                <a-col :span="8">
                  <a-card>
                    <a-statistic title="最高分" :value="selectedExam.maxScore" suffix="分" />
                  </a-card>
                </a-col>
                <a-col :span="8">
                  <a-card>
                    <a-statistic title="最低分" :value="selectedExam.minScore" suffix="分" />
                  </a-card>
                </a-col>
              </a-row>
              
              <div class="score-chart" style="height: 300px">
                <div ref="scoreChartRef" style="width: 100%; height: 100%"></div>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="participants" tab="参与人员">
            <a-table
              :columns="participantColumns"
              :data-source="selectedExam.participantResults"
              :pagination="false"
              size="small"
            >
              <template #score="{ text, record }">
                <span :style="{ color: text >= record.passScore ? '#52c41a' : '#ff4d4f' }">
                  {{ text }}
                </span>
              </template>
              <template #status="{ text }">
                <a-tag :color="text === '已通过' ? 'green' : text === '未通过' ? 'red' : 'orange'">
                  {{ text }}
                </a-tag>
              </template>
              <template #duration="{ text }">
                {{ Math.floor(text / 60) }}分{{ text % 60 }}秒
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 题库管理弹窗 -->
    <a-modal
      v-model:open="questionBankVisible"
      title="题库管理"
      width="1200px"
      :footer="null"
    >
      <div class="question-bank">
        <div class="bank-toolbar">
          <a-button type="primary" @click="addQuestion">
            <PlusOutlined /> 添加题目
          </a-button>
          <a-button @click="importQuestions" style="margin-left: 8px">
            <ImportOutlined /> 批量导入
          </a-button>
          <a-select 
            v-model:value="questionFilter.type"
            placeholder="题目类型"
            style="width: 120px; margin-left: 16px"
            allowClear
          >
            <a-select-option value="单选题">单选题</a-select-option>
            <a-select-option value="多选题">多选题</a-select-option>
            <a-select-option value="判断题">判断题</a-select-option>
            <a-select-option value="简答题">简答题</a-select-option>
          </a-select>
        </div>

        <a-table
          :columns="questionColumns"
          :data-source="filteredQuestions"
          :pagination="questionPagination"
          rowKey="id"
          size="small"
        >
          <template #type="{ text }">
            <a-tag :color="getQuestionTypeColor(text)">{{ text }}</a-tag>
          </template>
          <template #content="{ text }">
            <div class="question-preview">{{ text.substring(0, 50) }}...</div>
          </template>
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="editQuestion(record)">编辑</a-button>
              <a-button type="link" size="small" @click="deleteQuestion(record)" danger>删除</a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 添加/编辑题目弹窗 -->
    <a-modal
      v-model:open="questionModalVisible"
      :title="questionModalTitle"
      width="800px"
      :confirmLoading="questionModalLoading"
      @ok="handleQuestionSubmit"
      @cancel="closeQuestionModal"
    >
      <a-form :model="questionForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="题型" required>
          <a-select v-model:value="questionForm.type" @change="onQuestionTypeChange">
            <a-select-option value="单选题">单选题</a-select-option>
            <a-select-option value="多选题">多选题</a-select-option>
            <a-select-option value="判断题">判断题</a-select-option>
            <a-select-option value="简答题">简答题</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="题目" required>
          <a-textarea 
            v-model:value="questionForm.content" 
            :rows="3"
            placeholder="请输入题目内容"
          />
        </a-form-item>
        <a-form-item v-if="questionForm.type !== '简答题'" label="选项">
          <div class="options-config">
            <div v-for="(option, index) in questionForm.options" :key="index" class="option-item">
              <a-input 
                v-model:value="option.text" 
                :placeholder="`选项${String.fromCharCode(65 + index)}`"
                style="flex: 1"
              />
              <a-checkbox 
                v-model:checked="option.isCorrect"
                style="margin-left: 8px"
                @change="onAnswerChange(index)"
              >
                正确答案
              </a-checkbox>
              <a-button 
                v-if="questionForm.options.length > 2"
                type="text" 
                danger 
                @click="removeOption(index)"
                style="margin-left: 8px"
              >
                <DeleteOutlined />
              </a-button>
            </div>
            <a-button 
              v-if="questionForm.options.length < 6"
              type="dashed" 
              block 
              @click="addOption" 
              style="margin-top: 8px"
            >
              <PlusOutlined /> 添加选项
            </a-button>
          </div>
        </a-form-item>
        <a-form-item v-if="questionForm.type === '简答题'" label="参考答案">
          <a-textarea 
            v-model:value="questionForm.referenceAnswer" 
            :rows="4"
            placeholder="请输入参考答案"
          />
        </a-form-item>
        <a-form-item label="分值">
          <a-input-number 
            v-model:value="questionForm.score" 
            :min="1" 
            :max="50"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="难度">
          <a-select v-model:value="questionForm.difficulty">
            <a-select-option value="简单">简单</a-select-option>
            <a-select-option value="中等">中等</a-select-option>
            <a-select-option value="困难">困难</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import { 
  PlusOutlined, 
  ExportOutlined, 
  SearchOutlined, 
  DownOutlined,
  CopyOutlined,
  DeleteOutlined,
  DatabaseOutlined,
  QuestionCircleOutlined,
  BarChartOutlined,
  ImportOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)

// 统计数据
const stats = ref({
  totalExams: 89,
  activeExams: 23,
  totalQuestions: 567,
  avgPassRate: 78.5
})

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  status: '',
  creator: ''
})

// 考试数据
const exams = ref([
  {
    id: 1,
    name: '新员工入职考试',
    type: '入职考试',
    duration: 90,
    totalScore: 100,
    passScore: 80,
    status: 'published',
    passRate: 85,
    participants: ['张三', '李四', '王五'],
    creator: '管理员',
    description: '新员工入职必须通过的基础知识考试',
    timeRange: ['2024-01-15 09:00', '2024-01-15 18:00'],
    avgScore: 87.5,
    maxScore: 98,
    minScore: 72,
    questions: [
      {
        id: 1,
        type: '单选题',
        content: '催收工作中，以下哪种行为是不当的？',
        options: ['耐心解释还款义务', '威胁使用暴力', '提供分期方案', '记录沟通内容'],
        correctAnswer: [1],
        score: 5
      },
      {
        id: 2,
        type: '多选题',
        content: '有效的催收策略包括以下哪些？',
        options: ['了解客户情况', '制定还款计划', '保持专业态度', '频繁骚扰客户'],
        correctAnswer: [0, 1, 2],
        score: 10
      }
    ],
    participantResults: [
      { name: '张三', department: '催收一部', score: 85, status: '已通过', duration: 1200, submitTime: '2024-01-15 10:30' },
      { name: '李四', department: '催收二部', score: 92, status: '已通过', duration: 1050, submitTime: '2024-01-15 11:15' }
    ],
    shuffleQuestions: true,
    allowReview: false,
    showScore: true,
    limitAttempts: true,
    maxAttempts: 3,
    createdAt: '2024-01-10'
  },
  {
    id: 2,
    name: '催收技能认证考试',
    type: '技能认证',
    duration: 120,
    totalScore: 150,
    passScore: 105,
    status: 'ongoing',
    passRate: 72,
    participants: ['赵六', '孙七'],
    creator: '培训主管',
    description: '催收人员技能水平认证考试',
    timeRange: ['2024-01-20 14:00', '2024-01-20 16:00'],
    avgScore: 118.5,
    maxScore: 145,
    minScore: 89,
    questions: [],
    participantResults: [],
    shuffleQuestions: false,
    allowReview: true,
    showScore: false,
    limitAttempts: false,
    maxAttempts: 1,
    createdAt: '2024-01-18'
  }
])

// 题库数据
const questionBank = ref([
  {
    id: 1,
    type: '单选题',
    content: '根据相关法律法规，催收人员在催收过程中不得有以下哪种行为？',
    options: [
      { text: '向债务人说明逾期的后果', isCorrect: false },
      { text: '要求债务人按时还款', isCorrect: false },
      { text: '威胁债务人人身安全', isCorrect: true },
      { text: '记录催收过程', isCorrect: false }
    ],
    correctAnswer: [2],
    score: 5,
    difficulty: '简单',
    category: '法律法规'
  },
  {
    id: 2,
    type: '多选题',
    content: '有效的客户沟通技巧包括：',
    options: [
      { text: '积极倾听', isCorrect: true },
      { text: '保持耐心', isCorrect: true },
      { text: '大声训斥', isCorrect: false },
      { text: '换位思考', isCorrect: true }
    ],
    correctAnswer: [0, 1, 3],
    score: 8,
    difficulty: '中等',
    category: '沟通技巧'
  }
])

// 弹窗相关
const examModalVisible = ref(false)
const detailModalVisible = ref(false)
const questionBankVisible = ref(false)
const questionModalVisible = ref(false)
const modalLoading = ref(false)
const questionModalLoading = ref(false)
const modalTitle = ref('')
const questionModalTitle = ref('')
const selectedExam = ref(null)
const editingExam = ref(null)
const editingQuestion = ref(null)

// 图表引用
const scoreChartRef = ref(null)

// 表单数据
const examForm = reactive({
  name: '',
  type: '',
  duration: 60,
  totalScore: 100,
  passScore: 60,
  description: '',
  timeRange: [],
  participants: [],
  shuffleQuestions: false,
  allowReview: true,
  showScore: true,
  limitAttempts: false,
  maxAttempts: 1
})

const questionForm = reactive({
  type: '单选题',
  content: '',
  options: [
    { text: '', isCorrect: false },
    { text: '', isCorrect: false }
  ],
  correctAnswer: [],
  referenceAnswer: '',
  score: 5,
  difficulty: '中等',
  category: ''
})

// 筛选器
const questionFilter = reactive({
  type: ''
})

// 参与人员选项
const participantOptions = ref([
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' },
  { label: '赵六', value: '赵六' },
  { label: '孙七', value: '孙七' }
])

// 表格列定义
const columns = [
  { title: '考试名称', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type', slots: { customRender: 'type' }, width: 120 },
  { title: '时长', dataIndex: 'duration', key: 'duration', slots: { customRender: 'duration' }, width: 80 },
  { title: '总分', dataIndex: 'totalScore', key: 'totalScore', width: 80 },
  { title: '参与人数', dataIndex: 'participants', key: 'participants', width: 100, customRender: ({ text }) => text.length },
  { title: '通过率', dataIndex: 'passRate', key: 'passRate', slots: { customRender: 'passRate' }, width: 120 },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' }, width: 100 },
  { title: '创建人', dataIndex: 'creator', key: 'creator', width: 100 },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 180, fixed: 'right' }
]

// 参与人员列表列定义
const participantColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '得分', dataIndex: 'score', key: 'score', slots: { customRender: 'score' } },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' } },
  { title: '用时', dataIndex: 'duration', key: 'duration', slots: { customRender: 'duration' } },
  { title: '提交时间', dataIndex: 'submitTime', key: 'submitTime' }
]

// 题库表格列定义
const questionColumns = [
  { title: '题型', dataIndex: 'type', key: 'type', slots: { customRender: 'type' }, width: 80 },
  { title: '题目内容', dataIndex: 'content', key: 'content', slots: { customRender: 'content' } },
  { title: '分值', dataIndex: 'score', key: 'score', width: 60 },
  { title: '难度', dataIndex: 'difficulty', key: 'difficulty', width: 80 },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 120 }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

const questionPagination = reactive({
  current: 1,
  pageSize: 8,
  total: 0,
  showSizeChanger: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
    checkAll.value = keys.length === filteredExams.value.length
    indeterminate.value = keys.length > 0 && keys.length < filteredExams.value.length
  }
}))

// 过滤后的考试数据
const filteredExams = computed(() => {
  let filtered = exams.value
  
  if (searchForm.name) {
    filtered = filtered.filter(exam => 
      exam.name.toLowerCase().includes(searchForm.name.toLowerCase())
    )
  }
  
  if (searchForm.type) {
    filtered = filtered.filter(exam => exam.type === searchForm.type)
  }
  
  if (searchForm.status) {
    filtered = filtered.filter(exam => exam.status === searchForm.status)
  }
  
  if (searchForm.creator) {
    filtered = filtered.filter(exam => 
      exam.creator.toLowerCase().includes(searchForm.creator.toLowerCase())
    )
  }
  
  pagination.total = filtered.length
  return filtered
})

// 过滤后的题目数据
const filteredQuestions = computed(() => {
  let filtered = questionBank.value
  
  if (questionFilter.type) {
    filtered = filtered.filter(question => question.type === questionFilter.type)
  }
  
  questionPagination.total = filtered.length
  return filtered
})

// 工具函数
const getTypeColor = (type) => {
  const colors = {
    '入职考试': 'blue',
    '定期考核': 'green',
    '技能认证': 'orange',
    '合规测试': 'purple'
  }
  return colors[type] || 'default'
}

const getStatusBadge = (status) => {
  const badges = {
    'draft': 'default',
    'published': 'success',
    'ongoing': 'processing',
    'ended': 'warning'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    'draft': '草稿',
    'published': '已发布',
    'ongoing': '进行中',
    'ended': '已结束'
  }
  return texts[status] || '未知'
}

const getQuestionTypeColor = (type) => {
  const colors = {
    '单选题': 'blue',
    '多选题': 'green',
    '判断题': 'orange',
    '简答题': 'purple'
  }
  return colors[type] || 'default'
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
}

const quickSearch = (value) => {
  searchForm.name = value
  handleSearch()
}

const onCheckAllChange = (e) => {
  selectedRowKeys.value = e.target.checked ? filteredExams.value.map(item => item.id) : []
  indeterminate.value = false
  checkAll.value = e.target.checked
}

const showCreateModal = () => {
  modalTitle.value = '创建考试'
  editingExam.value = null
  resetExamForm()
  examModalVisible.value = true
}

const resetExamForm = () => {
  Object.keys(examForm).forEach(key => {
    if (key === 'timeRange' || key === 'participants') {
      examForm[key] = []
    } else if (typeof examForm[key] === 'boolean') {
      examForm[key] = ['allowReview', 'showScore'].includes(key)
    } else if (typeof examForm[key] === 'number') {
      const defaults = { duration: 60, totalScore: 100, passScore: 60, maxAttempts: 1 }
      examForm[key] = defaults[key] || 0
    } else {
      examForm[key] = ''
    }
  })
}

const editExam = (record) => {
  modalTitle.value = '编辑考试'
  editingExam.value = record
  
  Object.keys(examForm).forEach(key => {
    if (key in record) {
      examForm[key] = record[key]
    }
  })
  
  examModalVisible.value = true
}

const handleExamSubmit = async () => {
  modalLoading.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingExam.value) {
      Object.assign(editingExam.value, examForm)
      message.success('考试更新成功')
    } else {
      const newExam = {
        id: Date.now(),
        ...examForm,
        passRate: 0,
        avgScore: 0,
        maxScore: 0,
        minScore: 0,
        questions: [],
        participantResults: [],
        creator: '当前用户',
        createdAt: new Date().toISOString().split('T')[0]
      }
      exams.value.unshift(newExam)
      message.success('考试创建成功')
    }
    
    closeExamModal()
  } catch (error) {
    message.error('操作失败')
  } finally {
    modalLoading.value = false
  }
}

const closeExamModal = () => {
  examModalVisible.value = false
  editingExam.value = null
  resetExamForm()
}

const viewExam = (record) => {
  selectedExam.value = record
  detailModalVisible.value = true
  
  nextTick(() => {
    initScoreChart()
  })
}

const initScoreChart = () => {
  if (!scoreChartRef.value) return
  
  const chart = echarts.init(scoreChartRef.value)
  const option = {
    title: {
      text: '成绩分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['0-60', '60-70', '70-80', '80-90', '90-100']
    },
    yAxis: {
      type: 'value',
      name: '人数'
    },
    series: [{
      data: [2, 5, 8, 12, 6],
      type: 'bar',
      itemStyle: {
        color: '#1890ff'
      }
    }]
  }
  chart.setOption(option)
}

const copyExam = (record) => {
  const newExam = {
    ...record,
    id: Date.now(),
    name: `${record.name} (副本)`,
    status: 'draft',
    passRate: 0,
    participantResults: [],
    createdAt: new Date().toISOString().split('T')[0]
  }
  exams.value.unshift(newExam)
  message.success('考试复制成功')
}

const manageQuestions = (record) => {
  message.info('题目管理功能')
}

const viewResults = (record) => {
  message.info('成绩分析功能')
}

const deleteExam = (record) => {
  const index = exams.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    exams.value.splice(index, 1)
    message.success('考试删除成功')
  }
}

const batchPublish = () => {
  selectedRowKeys.value.forEach(id => {
    const exam = exams.value.find(item => item.id === id)
    if (exam) {
      exam.status = 'published'
    }
  })
  message.success(`成功发布 ${selectedRowKeys.value.length} 场考试`)
  selectedRowKeys.value = []
}

const batchDelete = () => {
  selectedRowKeys.value.forEach(id => {
    const index = exams.value.findIndex(item => item.id === id)
    if (index > -1) {
      exams.value.splice(index, 1)
    }
  })
  message.success(`成功删除 ${selectedRowKeys.value.length} 场考试`)
  selectedRowKeys.value = []
}

const exportResults = () => {
  message.success('成绩数据导出成功')
}

// 题库管理相关函数
const manageQuestionBank = () => {
  questionBankVisible.value = true
}

const addQuestion = () => {
  questionModalTitle.value = '添加题目'
  editingQuestion.value = null
  resetQuestionForm()
  questionModalVisible.value = true
}

const resetQuestionForm = () => {
  questionForm.type = '单选题'
  questionForm.content = ''
  questionForm.options = [
    { text: '', isCorrect: false },
    { text: '', isCorrect: false }
  ]
  questionForm.correctAnswer = []
  questionForm.referenceAnswer = ''
  questionForm.score = 5
  questionForm.difficulty = '中等'
  questionForm.category = ''
}

const editQuestion = (record) => {
  questionModalTitle.value = '编辑题目'
  editingQuestion.value = record
  
  Object.keys(questionForm).forEach(key => {
    if (key in record) {
      questionForm[key] = record[key]
    }
  })
  
  questionModalVisible.value = true
}

const handleQuestionSubmit = async () => {
  questionModalLoading.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingQuestion.value) {
      Object.assign(editingQuestion.value, questionForm)
      message.success('题目更新成功')
    } else {
      const newQuestion = {
        id: Date.now(),
        ...questionForm
      }
      questionBank.value.unshift(newQuestion)
      message.success('题目添加成功')
    }
    
    closeQuestionModal()
  } catch (error) {
    message.error('操作失败')
  } finally {
    questionModalLoading.value = false
  }
}

const closeQuestionModal = () => {
  questionModalVisible.value = false
  editingQuestion.value = null
  resetQuestionForm()
}

const deleteQuestion = (record) => {
  const index = questionBank.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    questionBank.value.splice(index, 1)
    message.success('题目删除成功')
  }
}

const importQuestions = () => {
  message.info('批量导入功能')
}

const onQuestionTypeChange = (type) => {
  if (type === '判断题') {
    questionForm.options = [
      { text: '正确', isCorrect: false },
      { text: '错误', isCorrect: false }
    ]
  } else if (type === '简答题') {
    questionForm.options = []
  } else {
    questionForm.options = [
      { text: '', isCorrect: false },
      { text: '', isCorrect: false }
    ]
  }
  questionForm.correctAnswer = []
}

const addOption = () => {
  questionForm.options.push({ text: '', isCorrect: false })
}

const removeOption = (index) => {
  questionForm.options.splice(index, 1)
  questionForm.correctAnswer = questionForm.correctAnswer.filter(i => i !== index)
}

const onAnswerChange = (index) => {
  const option = questionForm.options[index]
  if (questionForm.type === '单选题' || questionForm.type === '判断题') {
    // 单选题只能选一个正确答案
    questionForm.options.forEach((opt, i) => {
      if (i !== index) {
        opt.isCorrect = false
      }
    })
    questionForm.correctAnswer = option.isCorrect ? [index] : []
  } else {
    // 多选题可以选多个正确答案
    if (option.isCorrect) {
      if (!questionForm.correctAnswer.includes(index)) {
        questionForm.correctAnswer.push(index)
      }
    } else {
      const idx = questionForm.correctAnswer.indexOf(index)
      if (idx > -1) {
        questionForm.correctAnswer.splice(idx, 1)
      }
    }
  }
}

// 组件挂载
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.exam-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 24px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.exam-detail {
  padding: 16px 0;
}

.questions-list {
  max-height: 400px;
  overflow-y: auto;
}

.question-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.question-number {
  font-weight: bold;
  color: #1890ff;
}

.question-score {
  color: #52c41a;
  font-weight: bold;
}

.question-content {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.5;
}

.question-options {
  margin-left: 16px;
}

.option-item {
  margin-bottom: 4px;
}

.correct-answer {
  color: #52c41a;
  font-weight: bold;
}

.answer-template {
  margin-top: 8px;
  padding: 8px;
  background: #e6f7ff;
  border-radius: 4px;
}

.results-stats {
  padding: 16px 0;
}

.question-bank {
  padding: 16px 0;
}

.bank-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.question-preview {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.options-config {
  max-height: 200px;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: bold;
}
</style>