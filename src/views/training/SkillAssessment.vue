<template>
  <div class="skill-assessment">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>技能评估</h2>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined /> 创建评估
          </a-button>
          <a-button @click="manageSkillMatrix">
            <AppstoreOutlined /> 技能矩阵
          </a-button>
          <a-button @click="exportReport">
            <ExportOutlined /> 导出报告
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总评估数" 
              :value="stats.totalAssessments" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已完成评估" 
              :value="stats.completedAssessments" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="技能覆盖率" 
              :value="stats.skillCoverage" 
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均技能分" 
              :value="stats.avgSkillScore" 
              :precision="1"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <a-card class="search-card">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="评估标题">
          <a-input 
            v-model:value="searchForm.title" 
            placeholder="请输入评估标题"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="评估类型">
          <a-select 
            v-model:value="searchForm.type" 
            placeholder="请选择类型"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="360度评估">360度评估</a-select-option>
            <a-select-option value="自我评估">自我评估</a-select-option>
            <a-select-option value="上级评估">上级评估</a-select-option>
            <a-select-option value="同事评估">同事评估</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="技能类别">
          <a-select 
            v-model:value="searchForm.category" 
            placeholder="请选择类别"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="沟通技能">沟通技能</a-select-option>
            <a-select-option value="谈判技能">谈判技能</a-select-option>
            <a-select-option value="分析技能">分析技能</a-select-option>
            <a-select-option value="领导技能">领导技能</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="评估状态">
          <a-select 
            v-model:value="searchForm.status" 
            placeholder="请选择状态"
            style="width: 120px"
            allowClear
          >
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="active">进行中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined /> 搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 评估列表 -->
    <a-card>
      <div class="table-toolbar">
        <div class="toolbar-left">
          <a-checkbox 
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchStart"
            style="margin-left: 16px"
          >
            批量启动
          </a-button>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchComplete"
            style="margin-left: 8px"
          >
            批量完成
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-input-search
            placeholder="快速搜索评估"
            style="width: 250px"
            @search="quickSearch"
          />
        </div>
      </div>

      <a-table
        :columns="columns"
        :dataSource="filteredAssessments"
        :rowSelection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
      >
        <template #type="{ text }">
          <a-tag :color="getTypeColor(text)">{{ text }}</a-tag>
        </template>
        <template #status="{ text }">
          <a-badge :status="getStatusBadge(text)" :text="getStatusText(text)" />
        </template>
        <template #progress="{ text }">
          <a-progress :percent="text" size="small" />
        </template>
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="viewAssessment(record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="editAssessment(record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="viewReport(record)">
                    <BarChartOutlined /> 评估报告
                  </a-menu-item>
                  <a-menu-item @click="exportResult(record)">
                    <ExportOutlined /> 导出结果
                  </a-menu-item>
                  <a-menu-item @click="copyAssessment(record)">
                    <CopyOutlined /> 复制评估
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="deleteAssessment(record)" style="color: #ff4d4f">
                    <DeleteOutlined /> 删除评估
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑评估弹窗 -->
    <a-modal
      v-model:open="assessmentModalVisible"
      :title="modalTitle"
      width="900px"
      :confirmLoading="modalLoading"
      @ok="handleAssessmentSubmit"
      @cancel="closeAssessmentModal"
    >
      <a-form :model="assessmentForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="评估标题" required>
          <a-input v-model:value="assessmentForm.title" placeholder="请输入评估标题" />
        </a-form-item>
        <a-form-item label="评估类型" required>
          <a-select v-model:value="assessmentForm.type" placeholder="请选择类型">
            <a-select-option value="360度评估">360度评估</a-select-option>
            <a-select-option value="自我评估">自我评估</a-select-option>
            <a-select-option value="上级评估">上级评估</a-select-option>
            <a-select-option value="同事评估">同事评估</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="评估说明">
          <a-textarea 
            v-model:value="assessmentForm.description" 
            :rows="3"
            placeholder="请输入评估说明"
          />
        </a-form-item>
        <a-form-item label="评估时间">
          <a-range-picker 
            v-model:value="assessmentForm.timeRange"
            format="YYYY-MM-DD"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="被评估人">
          <a-select 
            v-model:value="assessmentForm.participants"
            mode="multiple"
            placeholder="请选择被评估人"
            :options="participantOptions"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="技能维度">
          <div class="skills-config">
            <div v-for="(skill, index) in assessmentForm.skills" :key="index" class="skill-item">
              <a-row :gutter="16" align="middle">
                <a-col :span="8">
                  <a-input v-model:value="skill.name" placeholder="技能名称" />
                </a-col>
                <a-col :span="6">
                  <a-select v-model:value="skill.category" placeholder="技能类别">
                    <a-select-option value="沟通技能">沟通技能</a-select-option>
                    <a-select-option value="谈判技能">谈判技能</a-select-option>
                    <a-select-option value="分析技能">分析技能</a-select-option>
                    <a-select-option value="领导技能">领导技能</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-input-number 
                    v-model:value="skill.weight" 
                    :min="1" 
                    :max="100"
                    placeholder="权重"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="4">
                  <a-input-number 
                    v-model:value="skill.maxScore" 
                    :min="1" 
                    :max="10"
                    placeholder="最高分"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="2">
                  <a-button 
                    type="text" 
                    danger 
                    @click="removeSkill(index)"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-col>
              </a-row>
            </div>
            <a-button type="dashed" block @click="addSkill" style="margin-top: 8px">
              <PlusOutlined /> 添加技能维度
            </a-button>
          </div>
        </a-form-item>
        <a-form-item label="评估设置">
          <a-space direction="vertical" style="width: 100%">
            <a-checkbox v-model:checked="assessmentForm.allowSelfAssess">
              允许自我评估
            </a-checkbox>
            <a-checkbox v-model:checked="assessmentForm.anonymousEvaluation">
              匿名评估
            </a-checkbox>
            <a-checkbox v-model:checked="assessmentForm.showResults">
              评估后显示结果
            </a-checkbox>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 评估详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="评估详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedAssessment" class="assessment-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="评估标题" :span="2">
            {{ selectedAssessment.title }}
          </a-descriptions-item>
          <a-descriptions-item label="评估类型">
            <a-tag :color="getTypeColor(selectedAssessment.type)">{{ selectedAssessment.type }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="评估状态">
            <a-badge :status="getStatusBadge(selectedAssessment.status)" :text="getStatusText(selectedAssessment.status)" />
          </a-descriptions-item>
          <a-descriptions-item label="参与人数">
            {{ selectedAssessment.participants.length }} 人
          </a-descriptions-item>
          <a-descriptions-item label="完成进度">
            {{ selectedAssessment.progress }}%
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedAssessment.createdAt }}
          </a-descriptions-item>
          <a-descriptions-item label="评估周期">
            {{ selectedAssessment.startDate }} ~ {{ selectedAssessment.endDate }}
          </a-descriptions-item>
          <a-descriptions-item label="评估说明" :span="2">
            {{ selectedAssessment.description }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <a-tabs>
          <a-tab-pane key="skills" tab="技能维度">
            <a-table
              :columns="skillColumns"
              :data-source="selectedAssessment.skills"
              :pagination="false"
              size="small"
            >
              <template #category="{ text }">
                <a-tag :color="getCategoryColor(text)">{{ text }}</a-tag>
              </template>
              <template #weight="{ text }">
                {{ text }}%
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="participants" tab="参与人员">
            <a-table
              :columns="participantColumns"
              :data-source="selectedAssessment.participantResults"
              :pagination="false"
              size="small"
            >
              <template #overallScore="{ text }">
                <a-rate 
                  :value="text / 2" 
                  disabled 
                  allow-half 
                  style="font-size: 14px"
                />
                <span style="margin-left: 8px">{{ text }}/10</span>
              </template>
              <template #status="{ text }">
                <a-tag :color="text === '已完成' ? 'green' : text === '进行中' ? 'blue' : 'orange'">
                  {{ text }}
                </a-tag>
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="results" tab="评估结果">
            <div class="results-charts">
              <a-row :gutter="24">
                <a-col :span="12">
                  <div class="chart-container">
                    <h4>技能分布雷达图</h4>
                    <div ref="radarChartRef" style="width: 100%; height: 300px"></div>
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="chart-container">
                    <h4>评估完成度</h4>
                    <div ref="progressChartRef" style="width: 100%; height: 300px"></div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 技能矩阵弹窗 -->
    <a-modal
      v-model:open="skillMatrixVisible"
      title="技能矩阵管理"
      width="1200px"
      :footer="null"
    >
      <div class="skill-matrix">
        <div class="matrix-toolbar">
          <a-button type="primary" @click="addSkillDimension">
            <PlusOutlined /> 添加技能维度
          </a-button>
          <a-button @click="importSkillMatrix" style="margin-left: 8px">
            <ImportOutlined /> 导入矩阵
          </a-button>
          <a-select 
            v-model:value="matrixFilter.category"
            placeholder="技能类别"
            style="width: 150px; margin-left: 16px"
            allowClear
          >
            <a-select-option value="沟通技能">沟通技能</a-select-option>
            <a-select-option value="谈判技能">谈判技能</a-select-option>
            <a-select-option value="分析技能">分析技能</a-select-option>
            <a-select-option value="领导技能">领导技能</a-select-option>
          </a-select>
        </div>

        <a-table
          :columns="matrixColumns"
          :data-source="filteredSkillMatrix"
          :pagination="matrixPagination"
          rowKey="id"
          size="small"
        >
          <template #category="{ text }">
            <a-tag :color="getCategoryColor(text)">{{ text }}</a-tag>
          </template>
          <template #level="{ text }">
            <a-rate :value="text" disabled style="font-size: 14px" />
          </template>
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="editSkillDimension(record)">编辑</a-button>
              <a-button type="link" size="small" @click="deleteSkillDimension(record)" danger>删除</a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 评估报告弹窗 -->
    <a-modal
      v-model:open="reportModalVisible"
      title="评估报告"
      width="1200px"
      :footer="null"
    >
      <div v-if="selectedReportData" class="assessment-report">
        <div class="report-header">
          <h3>{{ selectedReportData.title }} - 评估报告</h3>
          <a-button type="primary" @click="downloadReport">
            <DownloadOutlined /> 下载报告
          </a-button>
        </div>

        <a-row :gutter="24" style="margin-bottom: 24px">
          <a-col :span="6">
            <a-card>
              <a-statistic title="整体平均分" :value="selectedReportData.overallAvg" :precision="1" suffix="/10" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="最高分" :value="selectedReportData.maxScore" :precision="1" suffix="/10" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="最低分" :value="selectedReportData.minScore" :precision="1" suffix="/10" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="完成率" :value="selectedReportData.completionRate" suffix="%" />
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="技能强项与弱项分析">
              <div class="strengths-weaknesses">
                <div class="strength-section">
                  <h4 style="color: #52c41a">技能强项</h4>
                  <a-list size="small">
                    <a-list-item v-for="strength in selectedReportData.strengths" :key="strength.skill">
                      <span>{{ strength.skill }}</span>
                      <span style="color: #52c41a; font-weight: bold">{{ strength.score }}/10</span>
                    </a-list-item>
                  </a-list>
                </div>
                <a-divider />
                <div class="weakness-section">
                  <h4 style="color: #ff4d4f">技能弱项</h4>
                  <a-list size="small">
                    <a-list-item v-for="weakness in selectedReportData.weaknesses" :key="weakness.skill">
                      <span>{{ weakness.skill }}</span>
                      <span style="color: #ff4d4f; font-weight: bold">{{ weakness.score }}/10</span>
                    </a-list-item>
                  </a-list>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="改进建议">
              <a-list size="small">
                <a-list-item v-for="(suggestion, index) in selectedReportData.suggestions" :key="index">
                  <a-list-item-meta>
                    <template #title>
                      <span>{{ suggestion.title }}</span>
                    </template>
                    <template #description>
                      {{ suggestion.content }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </a-list>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import { 
  PlusOutlined, 
  ExportOutlined, 
  SearchOutlined, 
  DownOutlined,
  CopyOutlined,
  DeleteOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  ImportOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)

// 统计数据
const stats = ref({
  totalAssessments: 45,
  completedAssessments: 32,
  skillCoverage: 87.5,
  avgSkillScore: 7.2
})

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  category: '',
  status: ''
})

// 评估数据
const assessments = ref([
  {
    id: 1,
    title: '2024年度催收团队技能评估',
    type: '360度评估',
    description: '全面评估催收团队成员的各项技能水平',
    status: 'active',
    progress: 75,
    participants: ['张三', '李四', '王五', '赵六'],
    startDate: '2024-01-15',
    endDate: '2024-01-30',
    createdAt: '2024-01-10',
    skills: [
      { id: 1, name: '客户沟通', category: '沟通技能', weight: 25, maxScore: 10 },
      { id: 2, name: '谈判技巧', category: '谈判技能', weight: 30, maxScore: 10 },
      { id: 3, name: '案例分析', category: '分析技能', weight: 25, maxScore: 10 },
      { id: 4, name: '团队协作', category: '领导技能', weight: 20, maxScore: 10 }
    ],
    participantResults: [
      { id: 1, name: '张三', department: '催收一部', overallScore: 8.5, status: '已完成', completedAt: '2024-01-20' },
      { id: 2, name: '李四', department: '催收二部', overallScore: 7.8, status: '已完成', completedAt: '2024-01-22' },
      { id: 3, name: '王五', department: '催收一部', overallScore: 0, status: '进行中', completedAt: null },
      { id: 4, name: '赵六', department: '催收三部', overallScore: 9.1, status: '已完成', completedAt: '2024-01-25' }
    ],
    allowSelfAssess: true,
    anonymousEvaluation: false,
    showResults: true
  },
  {
    id: 2,
    title: '新员工技能摸底评估',
    type: '自我评估',
    description: '新入职员工的技能基础评估',
    status: 'completed',
    progress: 100,
    participants: ['孙七', '周八'],
    startDate: '2024-01-01',
    endDate: '2024-01-10',
    createdAt: '2023-12-25',
    skills: [
      { id: 1, name: '基础沟通', category: '沟通技能', weight: 40, maxScore: 10 },
      { id: 2, name: '学习能力', category: '分析技能', weight: 35, maxScore: 10 },
      { id: 3, name: '抗压能力', category: '领导技能', weight: 25, maxScore: 10 }
    ],
    participantResults: [
      { id: 1, name: '孙七', department: '催收一部', overallScore: 6.8, status: '已完成', completedAt: '2024-01-05' },
      { id: 2, name: '周八', department: '催收二部', overallScore: 7.2, status: '已完成', completedAt: '2024-01-07' }
    ],
    allowSelfAssess: true,
    anonymousEvaluation: true,
    showResults: false
  }
])

// 技能矩阵数据
const skillMatrix = ref([
  {
    id: 1,
    name: '客户沟通',
    category: '沟通技能',
    level: 4,
    description: '与客户进行有效沟通的能力',
    criteria: '能够清晰表达、耐心倾听、准确理解客户需求'
  },
  {
    id: 2,
    name: '谈判技巧',
    category: '谈判技能',
    level: 5,
    description: '在催收过程中的谈判能力',
    criteria: '能够制定谈判策略、掌握谈判节奏、达成双赢结果'
  },
  {
    id: 3,
    name: '案例分析',
    category: '分析技能',
    level: 3,
    description: '分析客户情况和制定催收策略的能力',
    criteria: '能够准确分析客户背景、评估风险、制定针对性方案'
  }
])

// 弹窗相关
const assessmentModalVisible = ref(false)
const detailModalVisible = ref(false)
const skillMatrixVisible = ref(false)
const reportModalVisible = ref(false)
const modalLoading = ref(false)
const modalTitle = ref('')
const selectedAssessment = ref(null)
const selectedReportData = ref(null)
const editingAssessment = ref(null)

// 图表引用
const radarChartRef = ref(null)
const progressChartRef = ref(null)

// 表单数据
const assessmentForm = reactive({
  title: '',
  type: '',
  description: '',
  timeRange: [],
  participants: [],
  skills: [
    { name: '', category: '', weight: 25, maxScore: 10 }
  ],
  allowSelfAssess: true,
  anonymousEvaluation: false,
  showResults: true
})

// 筛选器
const matrixFilter = reactive({
  category: ''
})

// 参与人员选项
const participantOptions = ref([
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' },
  { label: '赵六', value: '赵六' },
  { label: '孙七', value: '孙七' },
  { label: '周八', value: '周八' }
])

// 表格列定义
const columns = [
  { title: '评估标题', dataIndex: 'title', key: 'title' },
  { title: '类型', dataIndex: 'type', key: 'type', slots: { customRender: 'type' }, width: 120 },
  { title: '参与人数', dataIndex: 'participants', key: 'participants', width: 100, customRender: ({ text }) => text.length },
  { title: '完成进度', dataIndex: 'progress', key: 'progress', slots: { customRender: 'progress' }, width: 120 },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' }, width: 100 },
  { title: '开始时间', dataIndex: 'startDate', key: 'startDate', width: 120 },
  { title: '结束时间', dataIndex: 'endDate', key: 'endDate', width: 120 },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 180, fixed: 'right' }
]

// 技能维度列定义
const skillColumns = [
  { title: '技能名称', dataIndex: 'name', key: 'name' },
  { title: '技能类别', dataIndex: 'category', key: 'category', slots: { customRender: 'category' } },
  { title: '权重', dataIndex: 'weight', key: 'weight', slots: { customRender: 'weight' } },
  { title: '最高分', dataIndex: 'maxScore', key: 'maxScore' }
]

// 参与人员列定义
const participantColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '综合评分', dataIndex: 'overallScore', key: 'overallScore', slots: { customRender: 'overallScore' } },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' } },
  { title: '完成时间', dataIndex: 'completedAt', key: 'completedAt' }
]

// 技能矩阵列定义
const matrixColumns = [
  { title: '技能名称', dataIndex: 'name', key: 'name' },
  { title: '技能类别', dataIndex: 'category', key: 'category', slots: { customRender: 'category' } },
  { title: '难度等级', dataIndex: 'level', key: 'level', slots: { customRender: 'level' } },
  { title: '技能描述', dataIndex: 'description', key: 'description' },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 120 }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

const matrixPagination = reactive({
  current: 1,
  pageSize: 8,
  total: 0,
  showSizeChanger: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
    checkAll.value = keys.length === filteredAssessments.value.length
    indeterminate.value = keys.length > 0 && keys.length < filteredAssessments.value.length
  }
}))

// 过滤后的评估数据
const filteredAssessments = computed(() => {
  let filtered = assessments.value
  
  if (searchForm.title) {
    filtered = filtered.filter(assessment => 
      assessment.title.toLowerCase().includes(searchForm.title.toLowerCase())
    )
  }
  
  if (searchForm.type) {
    filtered = filtered.filter(assessment => assessment.type === searchForm.type)
  }
  
  if (searchForm.status) {
    filtered = filtered.filter(assessment => assessment.status === searchForm.status)
  }
  
  if (searchForm.category) {
    filtered = filtered.filter(assessment => 
      assessment.skills.some(skill => skill.category === searchForm.category)
    )
  }
  
  pagination.total = filtered.length
  return filtered
})

// 过滤后的技能矩阵数据
const filteredSkillMatrix = computed(() => {
  let filtered = skillMatrix.value
  
  if (matrixFilter.category) {
    filtered = filtered.filter(skill => skill.category === matrixFilter.category)
  }
  
  matrixPagination.total = filtered.length
  return filtered
})

// 工具函数
const getTypeColor = (type) => {
  const colors = {
    '360度评估': 'blue',
    '自我评估': 'green',
    '上级评估': 'orange',
    '同事评估': 'purple'
  }
  return colors[type] || 'default'
}

const getStatusBadge = (status) => {
  const badges = {
    'draft': 'default',
    'active': 'processing',
    'completed': 'success'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    'draft': '草稿',
    'active': '进行中',
    'completed': '已完成'
  }
  return texts[status] || '未知'
}

const getCategoryColor = (category) => {
  const colors = {
    '沟通技能': 'blue',
    '谈判技能': 'green',
    '分析技能': 'orange',
    '领导技能': 'purple'
  }
  return colors[category] || 'default'
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
}

const quickSearch = (value) => {
  searchForm.title = value
  handleSearch()
}

const onCheckAllChange = (e) => {
  selectedRowKeys.value = e.target.checked ? filteredAssessments.value.map(item => item.id) : []
  indeterminate.value = false
  checkAll.value = e.target.checked
}

const showCreateModal = () => {
  modalTitle.value = '创建评估'
  editingAssessment.value = null
  resetAssessmentForm()
  assessmentModalVisible.value = true
}

const resetAssessmentForm = () => {
  assessmentForm.title = ''
  assessmentForm.type = ''
  assessmentForm.description = ''
  assessmentForm.timeRange = []
  assessmentForm.participants = []
  assessmentForm.skills = [{ name: '', category: '', weight: 25, maxScore: 10 }]
  assessmentForm.allowSelfAssess = true
  assessmentForm.anonymousEvaluation = false
  assessmentForm.showResults = true
}

const editAssessment = (record) => {
  modalTitle.value = '编辑评估'
  editingAssessment.value = record
  
  Object.keys(assessmentForm).forEach(key => {
    if (key === 'timeRange') {
      assessmentForm[key] = [record.startDate, record.endDate]
    } else if (key in record) {
      assessmentForm[key] = record[key]
    }
  })
  
  assessmentModalVisible.value = true
}

const handleAssessmentSubmit = async () => {
  modalLoading.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingAssessment.value) {
      Object.assign(editingAssessment.value, {
        ...assessmentForm,
        startDate: assessmentForm.timeRange[0],
        endDate: assessmentForm.timeRange[1]
      })
      message.success('评估更新成功')
    } else {
      const newAssessment = {
        id: Date.now(),
        ...assessmentForm,
        startDate: assessmentForm.timeRange[0],
        endDate: assessmentForm.timeRange[1],
        status: 'draft',
        progress: 0,
        participantResults: [],
        createdAt: new Date().toISOString().split('T')[0]
      }
      assessments.value.unshift(newAssessment)
      message.success('评估创建成功')
    }
    
    closeAssessmentModal()
  } catch (error) {
    message.error('操作失败')
  } finally {
    modalLoading.value = false
  }
}

const closeAssessmentModal = () => {
  assessmentModalVisible.value = false
  editingAssessment.value = null
  resetAssessmentForm()
}

const viewAssessment = (record) => {
  selectedAssessment.value = record
  detailModalVisible.value = true
  
  nextTick(() => {
    initCharts()
  })
}

const initCharts = () => {
  initRadarChart()
  initProgressChart()
}

const initRadarChart = () => {
  if (!radarChartRef.value) return
  
  const chart = echarts.init(radarChartRef.value)
  const option = {
    title: {
      text: '技能雷达图'
    },
    radar: {
      indicator: [
        { name: '沟通技能', max: 10 },
        { name: '谈判技能', max: 10 },
        { name: '分析技能', max: 10 },
        { name: '领导技能', max: 10 }
      ]
    },
    series: [{
      name: '技能评估',
      type: 'radar',
      data: [{
        value: [8.5, 7.8, 6.9, 7.2],
        name: '平均技能水平'
      }]
    }]
  }
  chart.setOption(option)
}

const initProgressChart = () => {
  if (!progressChartRef.value) return
  
  const chart = echarts.init(progressChartRef.value)
  const option = {
    title: {
      text: '评估完成度'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      name: '完成度',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 75, name: '已完成' },
        { value: 25, name: '未完成' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
}

const viewReport = (record) => {
  selectedReportData.value = {
    title: record.title,
    overallAvg: 7.8,
    maxScore: 9.1,
    minScore: 6.8,
    completionRate: 85,
    strengths: [
      { skill: '客户沟通', score: 8.5 },
      { skill: '谈判技巧', score: 8.2 },
      { skill: '团队协作', score: 7.9 }
    ],
    weaknesses: [
      { skill: '案例分析', score: 6.5 },
      { skill: '抗压能力', score: 6.8 }
    ],
    suggestions: [
      { title: '加强分析能力培训', content: '建议参加数据分析相关课程，提升逻辑思维能力' },
      { title: '压力管理训练', content: '通过心理辅导和压力管理技巧培训，提升抗压能力' }
    ]
  }
  reportModalVisible.value = true
}

const copyAssessment = (record) => {
  const newAssessment = {
    ...record,
    id: Date.now(),
    title: `${record.title} (副本)`,
    status: 'draft',
    progress: 0,
    participantResults: [],
    createdAt: new Date().toISOString().split('T')[0]
  }
  assessments.value.unshift(newAssessment)
  message.success('评估复制成功')
}

const deleteAssessment = (record) => {
  const index = assessments.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    assessments.value.splice(index, 1)
    message.success('评估删除成功')
  }
}

const batchStart = () => {
  selectedRowKeys.value.forEach(id => {
    const assessment = assessments.value.find(item => item.id === id)
    if (assessment && assessment.status === 'draft') {
      assessment.status = 'active'
    }
  })
  message.success(`成功启动 ${selectedRowKeys.value.length} 个评估`)
  selectedRowKeys.value = []
}

const batchComplete = () => {
  selectedRowKeys.value.forEach(id => {
    const assessment = assessments.value.find(item => item.id === id)
    if (assessment && assessment.status === 'active') {
      assessment.status = 'completed'
      assessment.progress = 100
    }
  })
  message.success(`成功完成 ${selectedRowKeys.value.length} 个评估`)
  selectedRowKeys.value = []
}

const exportResult = (record) => {
  message.success('评估结果导出成功')
}

const exportReport = () => {
  message.success('评估报告导出成功')
}

const downloadReport = () => {
  message.success('报告下载成功')
}

// 技能维度相关函数
const addSkill = () => {
  assessmentForm.skills.push({ name: '', category: '', weight: 25, maxScore: 10 })
}

const removeSkill = (index) => {
  if (assessmentForm.skills.length > 1) {
    assessmentForm.skills.splice(index, 1)
  }
}

// 技能矩阵管理相关函数
const manageSkillMatrix = () => {
  skillMatrixVisible.value = true
}

const addSkillDimension = () => {
  message.info('添加技能维度功能')
}

const editSkillDimension = (record) => {
  message.info('编辑技能维度功能')
}

const deleteSkillDimension = (record) => {
  const index = skillMatrix.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    skillMatrix.value.splice(index, 1)
    message.success('技能维度删除成功')
  }
}

const importSkillMatrix = () => {
  message.info('导入技能矩阵功能')
}

// 组件挂载
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.skill-assessment {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 24px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.assessment-detail {
  padding: 16px 0;
}

.skills-config {
  max-height: 300px;
  overflow-y: auto;
}

.skill-item {
  margin-bottom: 8px;
}

.results-charts {
  padding: 16px 0;
}

.chart-container {
  text-align: center;
}

.chart-container h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.skill-matrix {
  padding: 16px 0;
}

.matrix-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.assessment-report {
  padding: 16px 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.report-header h3 {
  margin: 0;
  color: #1890ff;
}

.strengths-weaknesses {
  padding: 16px 0;
}

.strength-section,
.weakness-section {
  margin-bottom: 16px;
}

.strength-section h4,
.weakness-section h4 {
  margin-bottom: 12px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: bold;
}

:deep(.ant-list-item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>