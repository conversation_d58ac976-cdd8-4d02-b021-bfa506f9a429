<template>
  <div class="training-plan">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>培训计划</h2>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          创建计划
        </a-button>
        <a-button @click="importPlans">
          <template #icon><ImportOutlined /></template>
          导入计划
        </a-button>
        <a-button @click="exportData">
          <template #icon><ExportOutlined /></template>
          导出数据
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="培训计划总数" 
              :value="stats.totalPlans" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中计划" 
              :value="stats.activePlans" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="参训人数" 
              :value="stats.totalParticipants" 
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="完成率" 
              :value="stats.completionRate" 
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 培训进度概览 -->
    <div class="progress-overview">
      <a-card title="培训进度概览">
        <div class="chart-container">
          <div ref="progressChart" style="width: 100%; height: 300px;"></div>
        </div>
      </a-card>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="计划名称">
            <a-input 
              v-model:value="searchForm.name" 
              placeholder="请输入计划名称"
              allowClear
            />
          </a-form-item>
          <a-form-item label="培训类型">
            <a-select v-model:value="searchForm.type" placeholder="选择类型" allowClear>
              <a-select-option value="onboarding">新员工培训</a-select-option>
              <a-select-option value="skill">技能提升</a-select-option>
              <a-select-option value="compliance">合规培训</a-select-option>
              <a-select-option value="management">管理培训</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="选择状态" allowClear>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="published">已发布</a-select-option>
              <a-select-option value="ongoing">进行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="cancelled">已取消</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="负责人">
            <a-select v-model:value="searchForm.instructor" placeholder="选择负责人" allowClear>
              <a-select-option value="1">张三</a-select-option>
              <a-select-option value="2">李四</a-select-option>
              <a-select-option value="3">王五</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间范围">
            <a-range-picker 
              v-model:value="searchForm.dateRange"
              :placeholder="['开始时间', '结束时间']"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 培训计划列表 -->
    <div class="table-section">
      <a-card>
        <div class="table-header">
          <div class="table-actions">
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchPublish"
            >
              批量发布
            </a-button>
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchStart"
            >
              批量启动
            </a-button>
            <a-button 
              danger
              :disabled="!selectedRowKeys.length"
              @click="batchDelete"
            >
              批量删除
            </a-button>
          </div>
        </div>

        <a-table
          :columns="columns"
          :data-source="planList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: (record) => ({
              disabled: record.status === 'completed'
            })
          }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'participants'">
              <a-avatar-group :max-count="3">
                <a-avatar v-for="participant in record.participants.slice(0, 3)" :key="participant.id">
                  {{ participant.name.charAt(0) }}
                </a-avatar>
              </a-avatar-group>
              <span style="margin-left: 8px">{{ record.participants.length }}人</span>
            </template>
            <template v-else-if="column.key === 'progress'">
              <a-progress 
                :percent="record.progress" 
                size="small"
                :status="record.progress === 100 ? 'success' : 'active'"
              />
            </template>
            <template v-else-if="column.key === 'instructor'">
              <div class="instructor-info">
                <a-avatar size="small">{{ record.instructor.charAt(0) }}</a-avatar>
                <span style="margin-left: 8px">{{ record.instructor }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="editPlan(record)"
                  :disabled="record.status === 'completed'"
                >
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="record.status === 'draft'" @click="publishPlan(record)">
                        发布计划
                      </a-menu-item>
                      <a-menu-item v-if="record.status === 'published'" @click="startPlan(record)">
                        启动培训
                      </a-menu-item>
                      <a-menu-item @click="duplicatePlan(record)">
                        复制计划
                      </a-menu-item>
                      <a-menu-item @click="viewParticipants(record)">
                        参训人员
                      </a-menu-item>
                      <a-menu-item @click="viewSchedule(record)">
                        培训安排
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="deletePlan(record)">
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑计划模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      :title="editingPlan ? '编辑培训计划' : '创建培训计划'"
      width="1000px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="计划名称" required>
          <a-input v-model:value="formData.name" placeholder="请输入计划名称" />
        </a-form-item>
        <a-form-item label="培训类型" required>
          <a-select v-model:value="formData.type" placeholder="选择培训类型">
            <a-select-option value="onboarding">新员工培训</a-select-option>
            <a-select-option value="skill">技能提升</a-select-option>
            <a-select-option value="compliance">合规培训</a-select-option>
            <a-select-option value="management">管理培训</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="培训周期" required>
          <a-range-picker 
            v-model:value="formData.dateRange" 
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
          />
        </a-form-item>
        <a-form-item label="培训讲师" required>
          <a-select v-model:value="formData.instructor" placeholder="选择培训讲师">
            <a-select-option value="张三">张三</a-select-option>
            <a-select-option value="李四">李四</a-select-option>
            <a-select-option value="王五">王五</a-select-option>
            <a-select-option value="外部讲师">外部讲师</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="培训地点">
          <a-select v-model:value="formData.location" placeholder="选择培训地点">
            <a-select-option value="会议室A">会议室A</a-select-option>
            <a-select-option value="会议室B">会议室B</a-select-option>
            <a-select-option value="培训室">培训室</a-select-option>
            <a-select-option value="在线培训">在线培训</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="参训对象">
          <a-select 
            v-model:value="formData.targetAudience" 
            mode="multiple"
            placeholder="选择参训对象"
          >
            <a-select-option value="new_employees">新员工</a-select-option>
            <a-select-option value="collection_staff">催收员</a-select-option>
            <a-select-option value="customer_service">客服人员</a-select-option>
            <a-select-option value="managers">管理人员</a-select-option>
            <a-select-option value="all_staff">全体员工</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="培训内容">
          <div class="content-config">
            <div v-for="(content, index) in formData.contents" :key="index" class="content-item">
              <a-row :gutter="16" align="middle">
                <a-col :span="6">
                  <a-input v-model:value="content.title" placeholder="内容标题" />
                </a-col>
                <a-col :span="4">
                  <a-input-number 
                    v-model:value="content.duration" 
                    placeholder="时长(小时)"
                    :min="0.5"
                    :step="0.5"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="content.format" placeholder="培训形式">
                    <a-select-option value="lecture">讲座</a-select-option>
                    <a-select-option value="workshop">实操</a-select-option>
                    <a-select-option value="discussion">讨论</a-select-option>
                    <a-select-option value="online">在线</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-textarea v-model:value="content.description" placeholder="内容描述" :rows="1" />
                </a-col>
                <a-col :span="2">
                  <a-button 
                    type="text" 
                    danger 
                    @click="removeContent(index)"
                    :disabled="formData.contents.length <= 1"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-col>
              </a-row>
            </div>
            <a-button type="dashed" block @click="addContent" style="margin-top: 8px">
              <PlusOutlined /> 添加培训内容
            </a-button>
          </div>
        </a-form-item>
        <a-form-item label="培训目标">
          <a-textarea 
            v-model:value="formData.objectives" 
            :rows="3"
            placeholder="请输入培训目标"
          />
        </a-form-item>
        <a-form-item label="考核方式">
          <a-checkbox-group v-model:value="formData.assessmentMethods">
            <a-checkbox value="exam">笔试</a-checkbox>
            <a-checkbox value="practical">实操考核</a-checkbox>
            <a-checkbox value="project">项目作业</a-checkbox>
            <a-checkbox value="attendance">出勤考核</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="备注说明">
          <a-textarea 
            v-model:value="formData.remarks" 
            :rows="2"
            placeholder="请输入备注说明"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 计划详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="培训计划详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="selectedPlan" class="plan-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="计划名称">
            {{ selectedPlan.name }}
          </a-descriptions-item>
          <a-descriptions-item label="培训类型">
            <a-tag :color="getTypeColor(selectedPlan.type)">
              {{ getTypeText(selectedPlan.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedPlan.status)">
              {{ getStatusText(selectedPlan.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="培训讲师">
            {{ selectedPlan.instructor }}
          </a-descriptions-item>
          <a-descriptions-item label="培训时间">
            {{ selectedPlan.startDate }} ~ {{ selectedPlan.endDate }}
          </a-descriptions-item>
          <a-descriptions-item label="培训地点">
            {{ selectedPlan.location }}
          </a-descriptions-item>
          <a-descriptions-item label="参训人数">
            {{ selectedPlan.participants.length }}人
          </a-descriptions-item>
          <a-descriptions-item label="完成进度">
            {{ selectedPlan.progress }}%
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>培训内容</a-divider>
        <a-table
          :columns="contentColumns"
          :data-source="selectedPlan.trainingContents"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'format'">
              <a-tag>{{ getFormatText(record.format) }}</a-tag>
            </template>
          </template>
        </a-table>

        <a-divider>参训人员</a-divider>
        <a-table
          :columns="participantColumns"
          :data-source="selectedPlan.participants"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar size="small">{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getParticipantStatusColor(record.status)">
                {{ getParticipantStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'progress'">
              <a-progress :percent="record.progress" size="small" />
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 参训人员模态框 -->
    <a-modal
      v-model:open="participantsModalVisible"
      title="参训人员管理"
      width="800px"
      @ok="saveParticipants"
      @cancel="cancelParticipants"
    >
      <div class="participants-management">
        <div class="participants-actions">
          <a-button type="primary" @click="showAddParticipants">
            <PlusOutlined /> 添加人员
          </a-button>
          <a-button @click="importParticipants">
            <ImportOutlined /> 批量导入
          </a-button>
        </div>
        <a-table
          :columns="participantManageColumns"
          :data-source="currentParticipants"
          :pagination="{ pageSize: 5 }"
          size="small"
          style="margin-top: 16px"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar size="small">{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button type="link" size="small" danger @click="removeParticipant(record)">
                移除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ExportOutlined, 
  ImportOutlined,
  DownOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

const loading = ref(false)
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const participantsModalVisible = ref(false)
const editingPlan = ref(null)
const selectedPlan = ref(null)
const selectedRowKeys = ref([])
const currentParticipants = ref([])
const progressChart = ref(null)

const stats = reactive({
  totalPlans: 45,
  activePlans: 12,
  totalParticipants: 186,
  completionRate: 78.5
})

const searchForm = reactive({
  name: '',
  type: undefined,
  status: undefined,
  instructor: undefined,
  dateRange: []
})

const formData = reactive({
  name: '',
  type: undefined,
  dateRange: [],
  instructor: undefined,
  location: undefined,
  targetAudience: [],
  contents: [
    {
      title: '',
      duration: undefined,
      format: undefined,
      description: ''
    }
  ],
  objectives: '',
  assessmentMethods: [],
  remarks: ''
})

const planList = ref([
  {
    id: 1,
    name: '新员工入职培训计划',
    type: 'onboarding',
    status: 'ongoing',
    instructor: '张三',
    location: '培训室',
    startDate: '2024-03-01',
    endDate: '2024-03-15',
    progress: 75,
    participants: [
      { id: 1, name: '新员工A', department: '催收部', status: 'completed', progress: 100 },
      { id: 2, name: '新员工B', department: '客服部', status: 'ongoing', progress: 60 },
      { id: 3, name: '新员工C', department: '风控部', status: 'ongoing', progress: 80 }
    ],
    trainingContents: [
      { title: '公司介绍', duration: 2, format: 'lecture', description: '了解公司历史、文化和组织架构' },
      { title: '业务流程培训', duration: 4, format: 'workshop', description: '学习催收业务的基本流程和操作' },
      { title: '系统操作培训', duration: 3, format: 'practical', description: '熟悉业务系统的操作方法' }
    ],
    objectives: '帮助新员工快速适应工作环境，掌握基本业务技能',
    createTime: '2024-02-25 09:00:00'
  },
  {
    id: 2,
    name: '催收技能提升培训',
    type: 'skill',
    status: 'published',
    instructor: '李四',
    location: '会议室A',
    startDate: '2024-03-20',
    endDate: '2024-04-10',
    progress: 0,
    participants: [
      { id: 4, name: '催收员A', department: '催收部', status: 'registered', progress: 0 },
      { id: 5, name: '催收员B', department: '催收部', status: 'registered', progress: 0 },
      { id: 6, name: '催收员C', department: '催收部', status: 'registered', progress: 0 }
    ],
    trainingContents: [
      { title: '催收心理学', duration: 3, format: 'lecture', description: '学习客户心理分析和沟通技巧' },
      { title: '谈判技巧', duration: 4, format: 'workshop', description: '掌握有效的谈判策略和方法' },
      { title: '案例分析', duration: 2, format: 'discussion', description: '分析实际催收案例，总结经验' }
    ],
    objectives: '提升催收人员的专业技能和工作效率',
    createTime: '2024-02-20 14:30:00'
  },
  {
    id: 3,
    name: '合规培训专项计划',
    type: 'compliance',
    status: 'completed',
    instructor: '王五',
    location: '在线培训',
    startDate: '2024-02-01',
    endDate: '2024-02-28',
    progress: 100,
    participants: [
      { id: 7, name: '员工A', department: '催收部', status: 'completed', progress: 100 },
      { id: 8, name: '员工B', department: '客服部', status: 'completed', progress: 100 },
      { id: 9, name: '员工C', department: '风控部', status: 'completed', progress: 100 }
    ],
    trainingContents: [
      { title: '法律法规培训', duration: 4, format: 'online', description: '学习相关法律法规要求' },
      { title: '合规操作规范', duration: 3, format: 'online', description: '掌握合规操作的具体要求' },
      { title: '风险防控', duration: 2, format: 'online', description: '了解风险识别和防控措施' }
    ],
    objectives: '确保员工了解并遵守相关法律法规和公司制度',
    createTime: '2024-01-25 10:00:00'
  }
])

const columns = [
  {
    title: '计划名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '培训类型',
    key: 'type',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '培训讲师',
    key: 'instructor',
    width: 120
  },
  {
    title: '参训人员',
    key: 'participants',
    width: 150
  },
  {
    title: '培训进度',
    key: 'progress',
    width: 120
  },
  {
    title: '培训时间',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 180,
    customRender: ({ record }) => `${record.startDate} ~ ${record.endDate}`
  },
  {
    title: '培训地点',
    dataIndex: 'location',
    key: 'location',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

const contentColumns = [
  { title: '内容标题', dataIndex: 'title', key: 'title' },
  { title: '时长(小时)', dataIndex: 'duration', key: 'duration' },
  { title: '培训形式', key: 'format' },
  { title: '内容描述', dataIndex: 'description', key: 'description' }
]

const participantColumns = [
  { title: '头像', key: 'avatar', width: 80 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '状态', key: 'status' },
  { title: '进度', key: 'progress' }
]

const participantManageColumns = [
  { title: '头像', key: 'avatar', width: 80 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '职位', dataIndex: 'position', key: 'position' },
  { title: '操作', key: 'action', width: 80 }
]

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

const getTypeColor = (type) => {
  const colors = {
    onboarding: 'blue',
    skill: 'green',
    compliance: 'orange',
    management: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    onboarding: '新员工培训',
    skill: '技能提升',
    compliance: '合规培训',
    management: '管理培训'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'gray',
    published: 'blue',
    ongoing: 'green',
    completed: 'cyan',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    ongoing: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getFormatText = (format) => {
  const texts = {
    lecture: '讲座',
    workshop: '实操',
    discussion: '讨论',
    online: '在线',
    practical: '实践'
  }
  return texts[format] || format
}

const getParticipantStatusColor = (status) => {
  const colors = {
    registered: 'blue',
    ongoing: 'orange',
    completed: 'green',
    absent: 'red'
  }
  return colors[status] || 'default'
}

const getParticipantStatusText = (status) => {
  const texts = {
    registered: '已报名',
    ongoing: '培训中',
    completed: '已完成',
    absent: '缺席'
  }
  return texts[status] || status
}

const initChart = () => {
  if (!progressChart.value) return
  
  const chart = echarts.init(progressChart.value)
  const option = {
    title: {
      text: '培训计划进度统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '培训状态',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 12, name: '进行中', itemStyle: { color: '#52c41a' } },
          { value: 8, name: '已发布', itemStyle: { color: '#1890ff' } },
          { value: 20, name: '已完成', itemStyle: { color: '#722ed1' } },
          { value: 3, name: '草稿', itemStyle: { color: '#d9d9d9' } },
          { value: 2, name: '已取消', itemStyle: { color: '#ff4d4f' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
  
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

const showCreateModal = () => {
  editingPlan.value = null
  resetForm()
  createModalVisible.value = true
}

const editPlan = (record) => {
  editingPlan.value = record
  Object.assign(formData, {
    name: record.name,
    type: record.type,
    dateRange: [record.startDate, record.endDate],
    instructor: record.instructor,
    location: record.location,
    targetAudience: record.targetAudience || [],
    contents: [...(record.trainingContents || [{ title: '', duration: undefined, format: undefined, description: '' }])],
    objectives: record.objectives,
    assessmentMethods: record.assessmentMethods || [],
    remarks: record.remarks || ''
  })
  createModalVisible.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: undefined,
    dateRange: [],
    instructor: undefined,
    location: undefined,
    targetAudience: [],
    contents: [
      {
        title: '',
        duration: undefined,
        format: undefined,
        description: ''
      }
    ],
    objectives: '',
    assessmentMethods: [],
    remarks: ''
  })
}

const addContent = () => {
  formData.contents.push({
    title: '',
    duration: undefined,
    format: undefined,
    description: ''
  })
}

const removeContent = (index) => {
  formData.contents.splice(index, 1)
}

const handleSubmit = () => {
  if (editingPlan.value) {
    message.success('培训计划更新成功')
  } else {
    message.success('培训计划创建成功')
  }
  createModalVisible.value = false
  resetForm()
}

const handleCancel = () => {
  createModalVisible.value = false
  resetForm()
}

const viewDetails = (record) => {
  selectedPlan.value = record
  detailModalVisible.value = true
}

const publishPlan = (record) => {
  Modal.confirm({
    title: '确认发布计划',
    content: `确定要发布培训计划"${record.name}"吗？`,
    onOk() {
      message.success('计划发布成功')
    }
  })
}

const startPlan = (record) => {
  Modal.confirm({
    title: '确认启动培训',
    content: `确定要启动培训计划"${record.name}"吗？`,
    onOk() {
      message.success('培训启动成功')
    }
  })
}

const duplicatePlan = (record) => {
  message.success('计划复制成功')
}

const viewParticipants = (record) => {
  selectedPlan.value = record
  currentParticipants.value = [...record.participants]
  participantsModalVisible.value = true
}

const viewSchedule = (record) => {
  message.info(`查看${record.name}的培训安排`)
}

const deletePlan = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除培训计划"${record.name}"吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('删除成功')
    }
  })
}

const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys
}

const batchPublish = () => {
  Modal.confirm({
    title: '批量发布计划',
    content: `确定要发布选中的 ${selectedRowKeys.value.length} 个计划吗？`,
    onOk() {
      message.success('批量发布成功')
      selectedRowKeys.value = []
    }
  })
}

const batchStart = () => {
  Modal.confirm({
    title: '批量启动培训',
    content: `确定要启动选中的 ${selectedRowKeys.value.length} 个培训吗？`,
    onOk() {
      message.success('批量启动成功')
      selectedRowKeys.value = []
    }
  })
}

const batchDelete = () => {
  Modal.confirm({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个计划吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('批量删除成功')
      selectedRowKeys.value = []
    }
  })
}

const showAddParticipants = () => {
  message.info('添加参训人员功能')
}

const importParticipants = () => {
  message.info('批量导入参训人员功能')
}

const removeParticipant = (record) => {
  const index = currentParticipants.value.findIndex(p => p.id === record.id)
  if (index > -1) {
    currentParticipants.value.splice(index, 1)
    message.success('移除成功')
  }
}

const saveParticipants = () => {
  message.success('参训人员保存成功')
  participantsModalVisible.value = false
}

const cancelParticipants = () => {
  participantsModalVisible.value = false
}

const handleSearch = () => {
  message.info('搜索功能已触发')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    type: undefined,
    status: undefined,
    instructor: undefined,
    dateRange: []
  })
  message.info('搜索条件已重置')
}

const importPlans = () => {
  message.info('导入培训计划功能')
}

const exportData = () => {
  message.success('数据导出成功')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.training-plan {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.progress-overview {
  margin-bottom: 24px;
}

.chart-container {
  padding: 20px;
}

.search-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.instructor-info {
  display: flex;
  align-items: center;
}

.content-config {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.content-item {
  margin-bottom: 8px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.plan-detail {
  padding: 8px 0;
}

.participants-management {
  padding: 8px 0;
}

.participants-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}
</style>