<template>
  <div class="course-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>课程管理</h2>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined /> 创建课程
          </a-button>
          <a-button @click="exportCourses">
            <ExportOutlined /> 导出课程
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总课程数" 
              :value="stats.totalCourses" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中课程" 
              :value="stats.activeCourses" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="累计学员" 
              :value="stats.totalStudents" 
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均评分" 
              :value="stats.avgRating" 
              :precision="1"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <a-card class="search-card">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="课程名称">
          <a-input 
            v-model:value="searchForm.name" 
            placeholder="请输入课程名称"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="课程分类">
          <a-select 
            v-model:value="searchForm.category" 
            placeholder="请选择分类"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="业务培训">业务培训</a-select-option>
            <a-select-option value="技能培训">技能培训</a-select-option>
            <a-select-option value="合规培训">合规培训</a-select-option>
            <a-select-option value="新员工培训">新员工培训</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="课程状态">
          <a-select 
            v-model:value="searchForm.status" 
            placeholder="请选择状态"
            style="width: 120px"
            allowClear
          >
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="published">已发布</a-select-option>
            <a-select-option value="archived">已归档</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="讲师">
          <a-input 
            v-model:value="searchForm.instructor" 
            placeholder="请输入讲师姓名"
            style="width: 150px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined /> 搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 课程列表 -->
    <a-card>
      <div class="table-toolbar">
        <div class="toolbar-left">
          <a-checkbox 
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchPublish"
            style="margin-left: 16px"
          >
            批量发布
          </a-button>
          <a-button 
            :disabled="selectedRowKeys.length === 0"
            @click="batchArchive"
            style="margin-left: 8px"
          >
            批量归档
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-input-search
            placeholder="快速搜索课程"
            style="width: 250px"
            @search="quickSearch"
          />
        </div>
      </div>

      <a-table
        :columns="columns"
        :dataSource="filteredCourses"
        :rowSelection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
      >
        <template #coverUrl="{ text }">
          <a-image
            :width="60"
            :height="40"
            :src="text"
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1xkE8M/2EkS4iGfqHNwbCJHgqKPQUYhwB6FjyCB4BzoInaAr8GlIcATYIcgcxBkEOQjdwVsEy/DqMPQfzqzxD8Xqr6HQoynF4BuKyZnprNb72/TQfVlf5jcFYFJu4+9ZfWHOnN6k8lffHuTk7Bf5/w73vz3Ift9vWq6j1t95s30AAA=="
          />
        </template>
        <template #category="{ text }">
          <a-tag :color="getCategoryColor(text)">{{ text }}</a-tag>
        </template>
        <template #status="{ text }">
          <a-badge :status="getStatusBadge(text)" :text="getStatusText(text)" />
        </template>
        <template #rating="{ text }">
          <a-rate :value="text" disabled allow-half style="font-size: 14px" />
          <span style="margin-left: 8px">{{ text }}</span>
        </template>
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="viewCourse(record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="editCourse(record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="copyCourse(record)">
                    <CopyOutlined /> 复制课程
                  </a-menu-item>
                  <a-menu-item @click="manageLessons(record)">
                    <BookOutlined /> 课程内容
                  </a-menu-item>
                  <a-menu-item @click="viewStudents(record)">
                    <UserOutlined /> 学员管理
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="deleteCourse(record)" style="color: #ff4d4f">
                    <DeleteOutlined /> 删除课程
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑课程弹窗 -->
    <a-modal
      v-model:open="courseModalVisible"
      :title="modalTitle"
      width="800px"
      :confirmLoading="modalLoading"
      @ok="handleCourseSubmit"
      @cancel="closeCourseModal"
    >
      <a-form :model="courseForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="课程名称" required>
          <a-input v-model:value="courseForm.name" placeholder="请输入课程名称" />
        </a-form-item>
        <a-form-item label="课程分类" required>
          <a-select v-model:value="courseForm.category" placeholder="请选择分类">
            <a-select-option value="业务培训">业务培训</a-select-option>
            <a-select-option value="技能培训">技能培训</a-select-option>
            <a-select-option value="合规培训">合规培训</a-select-option>
            <a-select-option value="新员工培训">新员工培训</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="课程讲师" required>
          <a-select v-model:value="courseForm.instructor" placeholder="请选择讲师">
            <a-select-option value="张老师">张老师</a-select-option>
            <a-select-option value="李老师">李老师</a-select-option>
            <a-select-option value="王老师">王老师</a-select-option>
            <a-select-option value="刘老师">刘老师</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="课程时长">
          <a-input-number 
            v-model:value="courseForm.duration" 
            :min="0.5" 
            :step="0.5"
            placeholder="小时"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="课程封面">
          <a-upload
            v-model:file-list="courseForm.coverList"
            list-type="picture-card"
            :before-upload="() => false"
            :max-count="1"
          >
            <div v-if="courseForm.coverList.length < 1">
              <PlusOutlined />
              <div style="margin-top: 8px">上传封面</div>
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item label="课程描述">
          <a-textarea 
            v-model:value="courseForm.description" 
            :rows="4"
            placeholder="请输入课程描述"
          />
        </a-form-item>
        <a-form-item label="学习目标">
          <div class="objectives-config">
            <div v-for="(objective, index) in courseForm.objectives" :key="index" class="objective-item">
              <a-input 
                v-model:value="objective.content" 
                placeholder="请输入学习目标"
                style="flex: 1"
              />
              <a-button 
                type="text" 
                danger 
                @click="removeObjective(index)"
                style="margin-left: 8px"
              >
                <DeleteOutlined />
              </a-button>
            </div>
            <a-button type="dashed" block @click="addObjective" style="margin-top: 8px">
              <PlusOutlined /> 添加学习目标
            </a-button>
          </div>
        </a-form-item>
        <a-form-item label="课程设置">
          <a-space direction="vertical" style="width: 100%">
            <a-checkbox v-model:checked="courseForm.allowDiscussion">
              允许学员讨论
            </a-checkbox>
            <a-checkbox v-model:checked="courseForm.requireCompletion">
              要求完成所有课时
            </a-checkbox>
            <a-checkbox v-model:checked="courseForm.enableCertificate">
              完成后颁发证书
            </a-checkbox>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 课程详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="课程详情"
      width="900px"
      :footer="null"
    >
      <div v-if="selectedCourse" class="course-detail">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-image
              :width="240"
              :height="160"
              :src="selectedCourse.coverUrl"
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1xkE8M/2EkS4iGfqHNwbCJHgqKPQUYhwB6FjyCB4BzoInaAr8GlIcATYIcgcxBkEOQjdwVsEy/DqMPQfzqzxD8Xqr6HQoynF4BuKyZnprNb72/TQfVlf5jcFYFJu4+9ZfWHOnN6k8lffHuTk7Bf5/w73vz3Ift9vWq6j1t95s30AAA=="
            />
          </a-col>
          <a-col :span="16">
            <h3>{{ selectedCourse.name }}</h3>
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="课程分类">
                <a-tag :color="getCategoryColor(selectedCourse.category)">
                  {{ selectedCourse.category }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="课程讲师">
                {{ selectedCourse.instructor }}
              </a-descriptions-item>
              <a-descriptions-item label="课程时长">
                {{ selectedCourse.duration }} 小时
              </a-descriptions-item>
              <a-descriptions-item label="学员数量">
                {{ selectedCourse.studentCount }} 人
              </a-descriptions-item>
              <a-descriptions-item label="课程评分">
                <a-rate :value="selectedCourse.rating" disabled allow-half style="font-size: 14px" />
                <span style="margin-left: 8px">{{ selectedCourse.rating }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="课程状态">
                <a-badge :status="getStatusBadge(selectedCourse.status)" :text="getStatusText(selectedCourse.status)" />
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>
        
        <a-divider />
        
        <a-tabs>
          <a-tab-pane key="description" tab="课程介绍">
            <p>{{ selectedCourse.description }}</p>
          </a-tab-pane>
          <a-tab-pane key="objectives" tab="学习目标">
            <ul>
              <li v-for="objective in selectedCourse.objectives" :key="objective.id">
                {{ objective.content }}
              </li>
            </ul>
          </a-tab-pane>
          <a-tab-pane key="lessons" tab="课程内容">
            <a-list
              item-layout="horizontal"
              :data-source="selectedCourse.lessons"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.title"
                    :description="`时长: ${item.duration}分钟`"
                  >
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: '#1890ff' }">
                        {{ item.order }}
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
          <a-tab-pane key="students" tab="学员列表">
            <a-table
              :columns="studentColumns"
              :data-source="selectedCourse.students"
              :pagination="false"
              size="small"
            >
              <template #progress="{ text }">
                <a-progress :percent="text" size="small" />
              </template>
              <template #status="{ text }">
                <a-tag :color="text === '已完成' ? 'green' : text === '进行中' ? 'blue' : 'default'">
                  {{ text }}
                </a-tag>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 课程内容管理弹窗 -->
    <a-modal
      v-model:open="lessonModalVisible"
      title="课程内容管理"
      width="900px"
      :footer="null"
    >
      <div class="lesson-management">
        <div class="lesson-toolbar">
          <a-button type="primary" @click="addLesson">
            <PlusOutlined /> 添加课时
          </a-button>
          <a-button @click="sortLessons" style="margin-left: 8px">
            <SortAscendingOutlined /> 排序课时
          </a-button>
        </div>
        
        <a-table
          :columns="lessonColumns"
          :data-source="lessons"
          :pagination="false"
          rowKey="id"
        >
          <template #order="{ text, record }">
            <a-input-number 
              v-model:value="record.order" 
              :min="1" 
              size="small"
              @change="updateLessonOrder(record)"
            />
          </template>
          <template #duration="{ text }">
            {{ text }} 分钟
          </template>
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="editLesson(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="deleteLesson(record)" danger>
                删除
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ExportOutlined, 
  SearchOutlined, 
  DownOutlined,
  CopyOutlined,
  BookOutlined,
  UserOutlined,
  DeleteOutlined,
  SortAscendingOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)

// 统计数据
const stats = ref({
  totalCourses: 156,
  activeCourses: 89,
  totalStudents: 2456,
  avgRating: 4.7
})

// 搜索表单
const searchForm = reactive({
  name: '',
  category: '',
  status: '',
  instructor: ''
})

// 课程数据
const courses = ref([
  {
    id: 1,
    name: '债务催收法律法规培训',
    category: '合规培训',
    instructor: '张老师',
    duration: 8,
    studentCount: 245,
    rating: 4.8,
    status: 'published',
    coverUrl: 'https://picsum.photos/300/200?random=1',
    description: '本课程深入讲解债务催收相关的法律法规，帮助催收人员合规开展工作。',
    objectives: [
      { id: 1, content: '掌握债务催收的基本法律框架' },
      { id: 2, content: '了解催收过程中的合规要求' },
      { id: 3, content: '学会识别和规避法律风险' }
    ],
    lessons: [
      { id: 1, title: '债务催收法律基础', duration: 120, order: 1 },
      { id: 2, title: '催收行为规范', duration: 90, order: 2 },
      { id: 3, title: '案例分析与实践', duration: 60, order: 3 }
    ],
    students: [
      { id: 1, name: '李小明', department: '催收一部', progress: 85, status: '进行中' },
      { id: 2, name: '王小红', department: '催收二部', progress: 100, status: '已完成' }
    ],
    allowDiscussion: true,
    requireCompletion: true,
    enableCertificate: true,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20'
  },
  {
    id: 2,
    name: '客户沟通技巧培训',
    category: '技能培训',
    instructor: '李老师',
    duration: 6,
    studentCount: 189,
    rating: 4.6,
    status: 'published',
    coverUrl: 'https://picsum.photos/300/200?random=2',
    description: '提升催收人员与客户的沟通技巧和谈判能力。',
    objectives: [
      { id: 1, content: '掌握有效的沟通技巧' },
      { id: 2, content: '提升谈判和说服能力' },
      { id: 3, content: '学会处理客户异议' }
    ],
    lessons: [
      { id: 1, title: '沟通基础理论', duration: 90, order: 1 },
      { id: 2, title: '实用沟通技巧', duration: 120, order: 2 }
    ],
    students: [
      { id: 1, name: '张三', department: '催收一部', progress: 60, status: '进行中' }
    ],
    allowDiscussion: true,
    requireCompletion: false,
    enableCertificate: true,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18'
  }
])

// 弹窗相关
const courseModalVisible = ref(false)
const detailModalVisible = ref(false)
const lessonModalVisible = ref(false)
const modalLoading = ref(false)
const modalTitle = ref('')
const selectedCourse = ref(null)
const editingCourse = ref(null)

// 表单数据
const courseForm = reactive({
  name: '',
  category: '',
  instructor: '',
  duration: null,
  description: '',
  objectives: [{ content: '' }],
  coverList: [],
  allowDiscussion: true,
  requireCompletion: false,
  enableCertificate: true
})

// 课时数据
const lessons = ref([])
const lessonColumns = [
  { title: '序号', dataIndex: 'order', key: 'order', slots: { customRender: 'order' }, width: 80 },
  { title: '课时名称', dataIndex: 'title', key: 'title' },
  { title: '时长', dataIndex: 'duration', key: 'duration', slots: { customRender: 'duration' }, width: 100 },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 120 }
]

// 表格列定义
const columns = [
  { title: '课程封面', dataIndex: 'coverUrl', key: 'coverUrl', slots: { customRender: 'coverUrl' }, width: 100 },
  { title: '课程名称', dataIndex: 'name', key: 'name' },
  { title: '分类', dataIndex: 'category', key: 'category', slots: { customRender: 'category' }, width: 120 },
  { title: '讲师', dataIndex: 'instructor', key: 'instructor', width: 100 },
  { title: '时长(h)', dataIndex: 'duration', key: 'duration', width: 80 },
  { title: '学员数', dataIndex: 'studentCount', key: 'studentCount', width: 80 },
  { title: '评分', dataIndex: 'rating', key: 'rating', slots: { customRender: 'rating' }, width: 120 },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' }, width: 100 },
  { title: '操作', key: 'action', slots: { customRender: 'action' }, width: 180, fixed: 'right' }
]

// 学员列表列定义
const studentColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '学习进度', dataIndex: 'progress', key: 'progress', slots: { customRender: 'progress' } },
  { title: '状态', dataIndex: 'status', key: 'status', slots: { customRender: 'status' } }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
    checkAll.value = keys.length === filteredCourses.value.length
    indeterminate.value = keys.length > 0 && keys.length < filteredCourses.value.length
  }
}))

// 过滤后的课程数据
const filteredCourses = computed(() => {
  let filtered = courses.value
  
  if (searchForm.name) {
    filtered = filtered.filter(course => 
      course.name.toLowerCase().includes(searchForm.name.toLowerCase())
    )
  }
  
  if (searchForm.category) {
    filtered = filtered.filter(course => course.category === searchForm.category)
  }
  
  if (searchForm.status) {
    filtered = filtered.filter(course => course.status === searchForm.status)
  }
  
  if (searchForm.instructor) {
    filtered = filtered.filter(course => 
      course.instructor.toLowerCase().includes(searchForm.instructor.toLowerCase())
    )
  }
  
  pagination.total = filtered.length
  return filtered
})

// 工具函数
const getCategoryColor = (category) => {
  const colors = {
    '业务培训': 'blue',
    '技能培训': 'green',
    '合规培训': 'orange',
    '新员工培训': 'purple'
  }
  return colors[category] || 'default'
}

const getStatusBadge = (status) => {
  const badges = {
    'draft': 'default',
    'published': 'success',
    'archived': 'warning'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    'draft': '草稿',
    'published': '已发布',
    'archived': '已归档'
  }
  return texts[status] || '未知'
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
}

const quickSearch = (value) => {
  searchForm.name = value
  handleSearch()
}

const onCheckAllChange = (e) => {
  selectedRowKeys.value = e.target.checked ? filteredCourses.value.map(item => item.id) : []
  indeterminate.value = false
  checkAll.value = e.target.checked
}

const showCreateModal = () => {
  modalTitle.value = '创建课程'
  editingCourse.value = null
  resetCourseForm()
  courseModalVisible.value = true
}

const resetCourseForm = () => {
  Object.keys(courseForm).forEach(key => {
    if (key === 'objectives') {
      courseForm[key] = [{ content: '' }]
    } else if (key === 'coverList') {
      courseForm[key] = []
    } else if (typeof courseForm[key] === 'boolean') {
      courseForm[key] = key === 'allowDiscussion'
    } else {
      courseForm[key] = ''
    }
  })
}

const editCourse = (record) => {
  modalTitle.value = '编辑课程'
  editingCourse.value = record
  
  Object.keys(courseForm).forEach(key => {
    if (key in record) {
      courseForm[key] = record[key]
    }
  })
  
  courseModalVisible.value = true
}

const handleCourseSubmit = async () => {
  modalLoading.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingCourse.value) {
      Object.assign(editingCourse.value, courseForm)
      message.success('课程更新成功')
    } else {
      const newCourse = {
        id: Date.now(),
        ...courseForm,
        studentCount: 0,
        rating: 0,
        status: 'draft',
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0]
      }
      courses.value.unshift(newCourse)
      message.success('课程创建成功')
    }
    
    closeCourseModal()
  } catch (error) {
    message.error('操作失败')
  } finally {
    modalLoading.value = false
  }
}

const closeCourseModal = () => {
  courseModalVisible.value = false
  editingCourse.value = null
  resetCourseForm()
}

const viewCourse = (record) => {
  selectedCourse.value = record
  detailModalVisible.value = true
}

const copyCourse = (record) => {
  const newCourse = {
    ...record,
    id: Date.now(),
    name: `${record.name} (副本)`,
    status: 'draft',
    studentCount: 0,
    createdAt: new Date().toISOString().split('T')[0],
    updatedAt: new Date().toISOString().split('T')[0]
  }
  courses.value.unshift(newCourse)
  message.success('课程复制成功')
}

const manageLessons = (record) => {
  selectedCourse.value = record
  lessons.value = [...record.lessons]
  lessonModalVisible.value = true
}

const viewStudents = (record) => {
  selectedCourse.value = record
  detailModalVisible.value = true
  // 可以在这里设置默认激活的tab为学员列表
}

const deleteCourse = (record) => {
  const index = courses.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    courses.value.splice(index, 1)
    message.success('课程删除成功')
  }
}

const batchPublish = () => {
  selectedRowKeys.value.forEach(id => {
    const course = courses.value.find(item => item.id === id)
    if (course) {
      course.status = 'published'
    }
  })
  message.success(`成功发布 ${selectedRowKeys.value.length} 门课程`)
  selectedRowKeys.value = []
}

const batchArchive = () => {
  selectedRowKeys.value.forEach(id => {
    const course = courses.value.find(item => item.id === id)
    if (course) {
      course.status = 'archived'
    }
  })
  message.success(`成功归档 ${selectedRowKeys.value.length} 门课程`)
  selectedRowKeys.value = []
}

const exportCourses = () => {
  message.success('课程数据导出成功')
}

// 学习目标相关函数
const addObjective = () => {
  courseForm.objectives.push({ content: '' })
}

const removeObjective = (index) => {
  if (courseForm.objectives.length > 1) {
    courseForm.objectives.splice(index, 1)
  }
}

// 课时管理相关函数
const addLesson = () => {
  const newLesson = {
    id: Date.now(),
    title: '',
    duration: 60,
    order: lessons.value.length + 1
  }
  lessons.value.push(newLesson)
}

const editLesson = (record) => {
  // 这里可以打开编辑课时的弹窗
  message.info('编辑课时功能')
}

const deleteLesson = (record) => {
  const index = lessons.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    lessons.value.splice(index, 1)
    message.success('课时删除成功')
  }
}

const updateLessonOrder = (record) => {
  // 更新课时顺序
  message.success('课时顺序已更新')
}

const sortLessons = () => {
  lessons.value.sort((a, b) => a.order - b.order)
  message.success('课时排序完成')
}

// 组件挂载
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.course-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 24px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.course-detail {
  padding: 16px 0;
}

.objectives-config {
  max-height: 200px;
  overflow-y: auto;
}

.objective-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.lesson-management {
  padding: 16px 0;
}

.lesson-toolbar {
  margin-bottom: 16px;
}

:deep(.ant-upload-list-picture-card) {
  width: 104px;
  height: 104px;
}

:deep(.ant-upload-select-picture-card) {
  width: 104px;
  height: 104px;
}
</style>