<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>风险分析报表</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="风险维度">
                <a-select v-model:value="searchForm.riskDimension" placeholder="请选择维度" allow-clear>
                  <a-select-option value="overall">综合风险</a-select-option>
                  <a-select-option value="customer">客户风险</a-select-option>
                  <a-select-option value="case">案件风险</a-select-option>
                  <a-select-option value="portfolio">组合风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="风险等级">
                <a-select v-model:value="searchForm.riskLevel" placeholder="请选择等级" allow-clear>
                  <a-select-option value="all">全部等级</a-select-option>
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="all">全部部门</a-select-option>
                  <a-select-option value="dept1">催收一部</a-select-option>
                  <a-select-option value="dept2">催收二部</a-select-option>
                  <a-select-option value="dept3">法务部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportReport">
                      <download-outlined />
                      导出报表
                    </a-button>
                    <a-button @click="generateRiskModel">
                      <experiment-outlined />
                      风险建模
                    </a-button>
                    <a-button @click="riskAlert">
                      <alert-outlined />
                      风险预警
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 风险概览卡片 -->
      <a-row :gutter="16" class="risk-overview">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="高风险案件数" 
              :value="riskData.highRiskCases" 
              :value-style="{ color: '#ff4d4f' }"
              suffix="件"
            />
            <div class="risk-trend">
              <span class="trend-text">较上期 <span :class="getTrendClass(riskData.highRiskTrend)">{{ formatTrend(riskData.highRiskTrend) }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="风险敞口金额" 
              :value="riskData.riskExposure" 
              :precision="0"
              prefix="¥"
              :value-style="{ color: '#faad14' }"
            />
            <div class="risk-trend">
              <span class="trend-text">较上期 <span :class="getTrendClass(riskData.exposureTrend)">{{ formatTrend(riskData.exposureTrend) }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="预期损失率" 
              :value="riskData.expectedLossRate" 
              :precision="2"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
            <div class="risk-trend">
              <span class="trend-text">较上期 <span :class="getTrendClass(riskData.lossRateTrend)">{{ formatTrend(riskData.lossRateTrend) }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="风险覆盖率" 
              :value="riskData.riskCoverage" 
              :precision="1"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
            <div class="risk-trend">
              <span class="trend-text">较上期 <span :class="getTrendClass(riskData.coverageTrend)">{{ formatTrend(riskData.coverageTrend) }}%</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表分析 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="风险等级分布" :body-style="{ padding: '20px' }">
            <div ref="riskDistributionChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="风险趋势分析" :body-style="{ padding: '20px' }">
            <div ref="riskTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="16">
          <a-card title="风险因子分析" :body-style="{ padding: '20px' }">
            <div ref="riskFactorChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="行业风险对比" :body-style="{ padding: '20px' }">
            <div ref="industryRiskChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 风险预警列表 -->
      <a-card title="风险预警列表">
        <a-table 
          :columns="alertColumns" 
          :data-source="riskAlerts" 
          :pagination="alertPagination"
          :loading="loading"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'alertType'">
              <a-tag :color="getAlertTypeColor(record.alertType)">
                {{ getAlertTypeText(record.alertType) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'amount'">
              ¥{{ record.amount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'severity'">
              <a-progress 
                :percent="record.severity" 
                size="small" 
                :stroke-color="getSeverityColor(record.severity)"
              />
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewRiskDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="handleAlert(record)">处理</a-button>
                <a-button type="link" size="small" @click="generatePlan(record)">制定方案</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 风险模型评估 -->
      <a-card title="风险模型评估" class="model-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>模型表现指标</h4>
            <a-descriptions size="small" bordered>
              <a-descriptions-item label="准确率" span="2">
                {{ modelMetrics.accuracy }}%
              </a-descriptions-item>
              <a-descriptions-item label="召回率" span="2">
                {{ modelMetrics.recall }}%
              </a-descriptions-item>
              <a-descriptions-item label="精确率" span="2">
                {{ modelMetrics.precision }}%
              </a-descriptions-item>
              <a-descriptions-item label="F1分数" span="2">
                {{ modelMetrics.f1Score }}
              </a-descriptions-item>
              <a-descriptions-item label="AUC值" span="2">
                {{ modelMetrics.auc }}
              </a-descriptions-item>
              <a-descriptions-item label="模型版本" span="2">
                {{ modelMetrics.version }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="12">
            <h4>风险控制建议</h4>
            <a-list size="small" :data-source="riskRecommendations">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: item.color }">{{ item.type }}</a-avatar>
                    </template>
                    <template #title>
                      {{ item.title }}
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
        </a-row>
      </a-card>

      <!-- 风险详情弹窗 -->
      <a-modal
        v-model:open="riskDetailVisible"
        title="风险详情分析"
        width="1000px"
        :footer="null"
      >
        <div v-if="currentRisk">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-descriptions title="基础信息" bordered size="small">
                <a-descriptions-item label="风险编号">{{ currentRisk.riskId }}</a-descriptions-item>
                <a-descriptions-item label="客户姓名">{{ currentRisk.customerName }}</a-descriptions-item>
                <a-descriptions-item label="案件编号">{{ currentRisk.caseNumber }}</a-descriptions-item>
                <a-descriptions-item label="风险等级">
                  <a-tag :color="getRiskLevelColor(currentRisk.riskLevel)">
                    {{ getRiskLevelText(currentRisk.riskLevel) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="风险金额">¥{{ currentRisk.amount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="识别时间">{{ currentRisk.detectedTime }}</a-descriptions-item>
              </a-descriptions>
            </a-col>
            <a-col :span="12">
              <a-descriptions title="风险评分" bordered size="small">
                <a-descriptions-item label="违约概率" span="2">{{ currentRisk.defaultProbability }}%</a-descriptions-item>
                <a-descriptions-item label="损失严重度" span="2">{{ currentRisk.lossSeverity }}%</a-descriptions-item>
                <a-descriptions-item label="风险敞口" span="2">¥{{ currentRisk.exposure?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="预期损失" span="2">¥{{ currentRisk.expectedLoss?.toLocaleString() }}</a-descriptions-item>
              </a-descriptions>
            </a-col>
          </a-row>

          <a-divider>风险因子分析</a-divider>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-table 
                :columns="factorColumns" 
                :data-source="currentRisk.riskFactors" 
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'impact'">
                    <a-progress 
                      :percent="record.impact" 
                      size="small" 
                      :stroke-color="getImpactColor(record.impact)"
                    />
                  </template>
                  <template v-if="column.key === 'trend'">
                    <span :class="getTrendClass(record.trend)">
                      {{ record.trend > 0 ? '↗' : record.trend < 0 ? '↘' : '→' }}
                      {{ Math.abs(record.trend) }}%
                    </span>
                  </template>
                </template>
              </a-table>
            </a-col>
          </a-row>

          <a-divider>处理历史</a-divider>
          <a-timeline>
            <a-timeline-item 
              v-for="history in currentRisk.handleHistory" 
              :key="history.id"
              :color="getHistoryColor(history.action)"
            >
              <p><strong>{{ history.action }}</strong> - {{ history.handler }}</p>
              <p>{{ history.description }}</p>
              <p class="history-time">{{ history.time }}</p>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined, ExperimentOutlined, AlertOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  riskDimension: 'overall',
  riskLevel: 'all',
  dateRange: [dayjs().subtract(1, 'month'), dayjs()],
  department: 'all'
})

// 日期预设
const datePresets = {
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '上月': [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
  '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 风险数据
const riskData = reactive({
  highRiskCases: 156,
  highRiskTrend: 12.5,
  riskExposure: 12800000,
  exposureTrend: -5.8,
  expectedLossRate: 8.45,
  lossRateTrend: 2.3,
  riskCoverage: 85.6,
  coverageTrend: 7.9
})

// 模型指标
const modelMetrics = reactive({
  accuracy: 91.2,
  recall: 88.5,
  precision: 89.7,
  f1Score: 0.891,
  auc: 0.923,
  version: 'v2.1.0'
})

// 风险控制建议
const riskRecommendations = ref([
  {
    type: '预',
    color: '#1890ff',
    title: '加强预警监控',
    description: '建议对高风险客户加强日常监控，及时发现风险信号'
  },
  {
    type: '控',
    color: '#52c41a',
    title: '优化风险控制',
    description: '完善风险控制措施，降低风险敞口和损失概率'
  },
  {
    type: '模',
    color: '#faad14',
    title: '模型优化升级',
    description: '基于最新数据更新风险模型，提高预测准确性'
  },
  {
    type: '策',
    color: '#722ed1',
    title: '调整催收策略',
    description: '针对不同风险等级客户制定差异化催收策略'
  }
])

// 风险预警表格配置
const alertColumns = [
  {
    title: '风险编号',
    dataIndex: 'riskId',
    key: 'riskId',
    width: 120
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '预警类型',
    key: 'alertType',
    width: 120
  },
  {
    title: '风险金额',
    key: 'amount',
    width: 120
  },
  {
    title: '严重程度',
    key: 'severity',
    width: 120
  },
  {
    title: '预警状态',
    key: 'status',
    width: 100
  },
  {
    title: '识别时间',
    dataIndex: 'detectedTime',
    key: 'detectedTime',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 风险预警数据
const riskAlerts = ref([
  {
    id: 1,
    riskId: 'RISK2024001',
    customerName: '张三',
    caseNumber: '*********',
    riskLevel: 'high',
    alertType: 'payment_default',
    amount: 100000,
    severity: 95,
    status: 'active',
    detectedTime: '2024-01-15',
    defaultProbability: 85.6,
    lossSeverity: 78.9,
    exposure: 100000,
    expectedLoss: 67500,
    riskFactors: [
      { factor: '逾期天数', value: '365天', impact: 85, trend: 15.2 },
      { factor: '联系状态', value: '失联', impact: 90, trend: 8.5 },
      { factor: '还款意愿', value: '拒绝', impact: 75, trend: -5.3 },
      { factor: '资产状况', value: '恶化', impact: 80, trend: 12.1 }
    ],
    handleHistory: [
      { id: 1, action: '风险识别', handler: '系统自动', description: '检测到高风险信号', time: '2024-01-15 09:00' },
      { id: 2, action: '人工核查', handler: '李催收', description: '确认客户失联，风险较高', time: '2024-01-15 14:30' }
    ]
  },
  {
    id: 2,
    riskId: 'RISK2024002',
    customerName: '李四',
    caseNumber: '*********',
    riskLevel: 'medium',
    alertType: 'liquidity_risk',
    amount: 80000,
    severity: 68,
    status: 'handled',
    detectedTime: '2024-01-10',
    defaultProbability: 45.3,
    lossSeverity: 52.7,
    exposure: 80000,
    expectedLoss: 19100,
    riskFactors: [
      { factor: '收入变化', value: '下降50%', impact: 70, trend: 8.9 },
      { factor: '负债率', value: '85%', impact: 65, trend: 12.5 },
      { factor: '行业风险', value: '中等', impact: 45, trend: -2.1 },
      { factor: '信用历史', value: '良好', impact: 25, trend: -1.5 }
    ],
    handleHistory: [
      { id: 1, action: '风险识别', handler: '系统自动', description: '流动性风险上升', time: '2024-01-10 10:00' },
      { id: 2, action: '风险评估', handler: '王风控', description: '评估为中等风险', time: '2024-01-10 15:00' },
      { id: 3, action: '制定方案', handler: '张主管', description: '制定分期还款方案', time: '2024-01-11 09:00' }
    ]
  }
])

// 风险因子表格配置
const factorColumns = [
  {
    title: '风险因子',
    dataIndex: 'factor',
    key: 'factor',
    width: 120
  },
  {
    title: '当前值',
    dataIndex: 'value',
    key: 'value',
    width: 120
  },
  {
    title: '影响程度',
    key: 'impact',
    width: 150
  },
  {
    title: '变化趋势',
    key: 'trend',
    width: 100
  }
]

// 分页配置
const alertPagination = reactive({
  current: 1,
  pageSize: 10,
  total: riskAlerts.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 弹窗状态
const riskDetailVisible = ref(false)
const currentRisk = ref(null)

// 图表引用
const riskDistributionChart = ref()
const riskTrendChart = ref()
const riskFactorChart = ref()
const industryRiskChart = ref()

// 状态相关方法
const getRiskLevelColor = (level) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || '未知'
}

const getAlertTypeColor = (type) => {
  const colors = {
    payment_default: 'red',
    liquidity_risk: 'orange',
    credit_risk: 'blue',
    market_risk: 'purple'
  }
  return colors[type] || 'default'
}

const getAlertTypeText = (type) => {
  const texts = {
    payment_default: '违约风险',
    liquidity_risk: '流动性风险',
    credit_risk: '信用风险',
    market_risk: '市场风险'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    active: 'red',
    handled: 'green',
    ignored: 'gray'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    handled: '已处理',
    ignored: '已忽略'
  }
  return texts[status] || '未知'
}

const getSeverityColor = (severity) => {
  if (severity >= 80) return '#ff4d4f'
  if (severity >= 60) return '#faad14'
  if (severity >= 40) return '#1890ff'
  return '#52c41a'
}

const getImpactColor = (impact) => {
  if (impact >= 80) return '#ff4d4f'
  if (impact >= 60) return '#faad14'
  return '#52c41a'
}

const getTrendClass = (trend) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

const formatTrend = (trend) => {
  return trend > 0 ? `+${trend}` : trend.toString()
}

const getHistoryColor = (action) => {
  const colors = {
    '风险识别': 'blue',
    '人工核查': 'orange',
    '风险评估': 'purple',
    '制定方案': 'green',
    '风险处理': 'green'
  }
  return colors[action] || 'default'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('风险数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.riskDimension = 'overall'
  searchForm.riskLevel = 'all'
  searchForm.dateRange = [dayjs().subtract(1, 'month'), dayjs()]
  searchForm.department = 'all'
  handleSearch()
}

const exportReport = () => {
  message.success('风险分析报表导出成功')
}

const generateRiskModel = () => {
  message.success('正在生成风险模型...')
}

const riskAlert = () => {
  message.info('风险预警功能')
}

const viewRiskDetail = (record) => {
  currentRisk.value = record
  riskDetailVisible.value = true
}

const handleAlert = (record) => {
  message.success(`正在处理风险预警 ${record.riskId}`)
}

const generatePlan = (record) => {
  message.success(`正在为 ${record.riskId} 制定风险处理方案`)
}

// 图表初始化
const initCharts = () => {
  // 风险等级分布图
  const distributionChart = echarts.init(riskDistributionChart.value)
  const distributionOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: 0
    },
    series: [
      {
        name: '风险分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 156, name: '高风险', itemStyle: { color: '#ff4d4f' } },
          { value: 234, name: '中风险', itemStyle: { color: '#faad14' } },
          { value: 412, name: '低风险', itemStyle: { color: '#52c41a' } }
        ]
      }
    ]
  }
  distributionChart.setOption(distributionOption)

  // 风险趋势图
  const trendChart = echarts.init(riskTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '风险案件数'
    },
    series: [
      {
        name: '高风险',
        type: 'line',
        data: [120, 132, 145, 156, 162, 156],
        smooth: true,
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '中风险',
        type: 'line',
        data: [200, 210, 220, 234, 228, 234],
        smooth: true,
        itemStyle: { color: '#faad14' }
      },
      {
        name: '低风险',
        type: 'line',
        data: [380, 390, 400, 412, 408, 412],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 风险因子分析图
  const factorChart = echarts.init(riskFactorChart.value)
  const factorOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['逾期天数', '联系状态', '还款意愿', '资产状况', '收入变化', '负债率', '行业风险', '信用历史']
    },
    yAxis: {
      type: 'value',
      name: '影响权重'
    },
    series: [
      {
        name: '风险权重',
        type: 'bar',
        data: [0.25, 0.20, 0.18, 0.15, 0.10, 0.08, 0.06, 0.04],
        itemStyle: {
          color: function(params) {
            const colors = ['#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7', '#faad14', '#ffc53d', '#52c41a', '#73d13d']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  factorChart.setOption(factorOption)

  // 行业风险对比图
  const industryChart = echarts.init(industryRiskChart.value)
  const industryOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['制造业', '服务业', '建筑业', '零售业', '科技业'],
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '风险指数'
    },
    series: [
      {
        name: '风险指数',
        type: 'bar',
        data: [75, 68, 82, 71, 59],
        itemStyle: { color: '#1890ff' }
      }
    ]
  }
  industryChart.setOption(industryOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    distributionChart.resize()
    trendChart.resize()
    factorChart.resize()
    industryChart.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.risk-overview {
  margin-bottom: 16px;
}

.chart-section {
  margin-bottom: 16px;
}

.model-card {
  margin-top: 16px;
}

.model-card h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.risk-trend {
  margin-top: 8px;
  font-size: 12px;
}

.trend-text {
  color: #666;
}

.trend-up {
  color: #ff4d4f;
}

.trend-down {
  color: #52c41a;
}

.trend-stable {
  color: #666;
}

.history-time {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-descriptions-title {
  font-weight: 500;
}
</style>
