<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>风险监控</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="风险类型">
                <a-select v-model:value="searchForm.riskType" placeholder="请选择风险类型" allow-clear>
                  <a-select-option value="overdue">逾期风险</a-select-option>
                  <a-select-option value="fraud">欺诈风险</a-select-option>
                  <a-select-option value="operation">操作风险</a-select-option>
                  <a-select-option value="credit">信用风险</a-select-option>
                  <a-select-option value="legal">法律风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="风险等级">
                <a-select v-model:value="searchForm.riskLevel" placeholder="请选择风险等级" allow-clear>
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="监控时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  @change="handleDateChange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="业务部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="all">全部部门</a-select-option>
                  <a-select-option value="dept1">催收一部</a-select-option>
                  <a-select-option value="dept2">催收二部</a-select-option>
                  <a-select-option value="dept3">法务部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                  <a-button @click="toggleAutoRefresh" :type="autoRefresh ? 'default' : 'dashed'">
                    <sync-outlined :spin="autoRefresh" />
                    {{ autoRefresh ? '停止刷新' : '自动刷新' }}
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportRiskReport">
                      <download-outlined />
                      导出报告
                    </a-button>
                    <a-button @click="showRiskConfig">
                      <setting-outlined />
                      风险配置
                    </a-button>
                    <a-button type="primary" @click="showAlertHistory">
                      <alert-outlined />
                      预警记录
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 风险概览卡片 -->
      <a-row :gutter="16" class="overview-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总体风险指数" 
              :value="overviewData.riskIndex" 
              :precision="1" 
              :value-style="{ color: getRiskIndexColor(overviewData.riskIndex) }"
            >
              <template #suffix>
                <span style="font-size: 14px">分</span>
              </template>
            </a-statistic>
            <div class="trend-indicator">
              <arrow-up-outlined v-if="overviewData.riskTrend > 0" style="color: #ff4d4f" />
              <arrow-down-outlined v-else style="color: #52c41a" />
              <span>{{ Math.abs(overviewData.riskTrend) }}%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="高风险案件" 
              :value="overviewData.highRiskCases" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <span style="font-size: 14px">件</span>
              </template>
            </a-statistic>
            <div class="sub-info">占比 {{ overviewData.highRiskRate }}%</div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="风险金额" 
              :value="overviewData.riskAmount" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#faad14' }"
            />
            <div class="sub-info">较昨日 +{{ overviewData.riskAmountChange }}%</div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="预警触发" 
              :value="overviewData.alertCount" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #suffix>
                <span style="font-size: 14px">次</span>
              </template>
            </a-statistic>
            <div class="sub-info">待处理 {{ overviewData.pendingAlerts }} 条</div>
          </a-card>
        </a-col>
      </a-row>

      <!-- AI风险预测卡片 -->
      <a-row :gutter="16" class="ai-cards">
        <a-col :span="8">
          <a-card title="AI风险预测" :hoverable="true">
            <div class="ai-prediction">
              <div class="prediction-item">
                <span class="label">24小时风险预测：</span>
                <span class="value" :style="{ color: '#ff4d4f' }">{{ aiPrediction.next24h }}%上升</span>
              </div>
              <div class="prediction-item">
                <span class="label">一周风险趋势：</span>
                <span class="value">{{ aiPrediction.weekTrend }}</span>
              </div>
              <div class="prediction-item">
                <span class="label">重点关注区域：</span>
                <span class="value">{{ aiPrediction.focusArea }}</span>
              </div>
              <a-button type="link" @click="showAIPredictionDetail">
                查看详细预测
                <arrow-right-outlined />
              </a-button>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="风险热点分析" :hoverable="true">
            <div class="risk-hotspot">
              <a-tag v-for="hotspot in riskHotspots" :key="hotspot.id" 
                :color="hotspot.level === 'high' ? 'red' : hotspot.level === 'medium' ? 'orange' : 'blue'"
                style="margin: 4px"
              >
                {{ hotspot.name }} ({{ hotspot.count }})
              </a-tag>
              <a-divider style="margin: 12px 0" />
              <div class="hotspot-summary">
                <small>发现 {{ riskHotspots.length }} 个风险集中点，建议重点监控</small>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="智能预警建议" :hoverable="true">
            <div class="alert-suggestions">
              <a-list size="small" :data-source="alertSuggestions">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <warning-outlined :style="{ color: item.color, marginRight: '8px' }" />
                    {{ item.text }}
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 实时监控图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="实时风险监控" :body-style="{ padding: '20px' }">
            <div class="chart-controls">
              <a-radio-group v-model:value="monitoringView" @change="updateMonitoringChart">
                <a-radio-button value="realtime">实时数据</a-radio-button>
                <a-radio-button value="trend">趋势分析</a-radio-button>
                <a-radio-button value="distribution">分布情况</a-radio-button>
              </a-radio-group>
              <a-space>
                <a-switch v-model:checked="showAlertLine" @change="updateMonitoringChart">
                  <template #checkedChildren>显示预警线</template>
                  <template #unCheckedChildren>隐藏预警线</template>
                </a-switch>
              </a-space>
            </div>
            <div ref="realtimeMonitorChart" style="height: 400px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 多维度风险分析图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="风险类型分布" :body-style="{ padding: '20px' }">
            <div ref="riskTypeChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="风险等级分布" :body-style="{ padding: '20px' }">
            <div ref="riskLevelChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="部门风险对比" :body-style="{ padding: '20px' }">
            <div ref="departmentRiskChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="风险趋势预测" :body-style="{ padding: '20px' }">
            <div ref="riskTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="风险地图" :body-style="{ padding: '20px' }">
            <div ref="riskMapChart" style="height: 400px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 风险详情表格 -->
      <a-card title="风险案件明细">
        <template #extra>
          <a-space>
            <a-badge :count="newRiskCount" :number-style="{ backgroundColor: '#ff4d4f' }">
              <a-button size="small" @click="refreshRiskList">
                <sync-outlined />
                刷新
              </a-button>
            </a-badge>
            <a-button size="small" type="primary" @click="batchHandleRisks">
              批量处理
            </a-button>
          </a-space>
        </template>
        
        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :row-selection="rowSelection"
          :scroll="{ x: 1800 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'riskScore'">
              <a-progress 
                :percent="record.riskScore" 
                :stroke-color="getRiskScoreColor(record.riskScore)"
                :format="percent => `${percent}分`"
                size="small"
                :style="{ width: '100px' }"
              />
            </template>
            <template v-if="column.key === 'status'">
              <a-badge :status="getStatusBadge(record.status)" :text="getStatusText(record.status)" />
            </template>
            <template v-if="column.key === 'amount'">
              ¥{{ record.amount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'alertTime'">
              <span :style="{ color: isRecentAlert(record.alertTime) ? '#ff4d4f' : '' }">
                {{ formatDate(record.alertTime) }}
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewRiskDetail(record)">
                  查看详情
                </a-button>
                <a-button type="link" size="small" @click="handleRisk(record)">
                  处理
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMoreAction(key, record)">
                      <a-menu-item key="analysis">
                        <bar-chart-outlined />
                        风险分析
                      </a-menu-item>
                      <a-menu-item key="history">
                        <history-outlined />
                        历史记录
                      </a-menu-item>
                      <a-menu-item key="export">
                        <export-outlined />
                        导出报告
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 风险配置弹窗 -->
      <a-modal
        v-model:open="configModalVisible"
        title="风险监控配置"
        width="800px"
        @ok="saveRiskConfig"
      >
        <a-form :model="riskConfig" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
          <a-tabs>
            <a-tab-pane key="threshold" tab="预警阈值">
              <a-form-item label="高风险阈值">
                <a-input-number v-model:value="riskConfig.highThreshold" :min="0" :max="100" suffix="分" />
              </a-form-item>
              <a-form-item label="中风险阈值">
                <a-input-number v-model:value="riskConfig.mediumThreshold" :min="0" :max="100" suffix="分" />
              </a-form-item>
              <a-form-item label="逾期天数预警">
                <a-input-number v-model:value="riskConfig.overdueDays" :min="1" suffix="天" />
              </a-form-item>
              <a-form-item label="金额预警线">
                <a-input-number v-model:value="riskConfig.amountThreshold" :min="0" prefix="¥" />
              </a-form-item>
            </a-tab-pane>
            <a-tab-pane key="notification" tab="通知设置">
              <a-form-item label="通知方式">
                <a-checkbox-group v-model:value="riskConfig.notificationMethods">
                  <a-checkbox value="system">系统通知</a-checkbox>
                  <a-checkbox value="email">邮件通知</a-checkbox>
                  <a-checkbox value="sms">短信通知</a-checkbox>
                  <a-checkbox value="wechat">微信通知</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="通知频率">
                <a-select v-model:value="riskConfig.notificationFrequency">
                  <a-select-option value="realtime">实时通知</a-select-option>
                  <a-select-option value="hourly">每小时汇总</a-select-option>
                  <a-select-option value="daily">每日汇总</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="接收人员">
                <a-select v-model:value="riskConfig.receivers" mode="multiple" placeholder="请选择接收人员">
                  <a-select-option value="manager">部门经理</a-select-option>
                  <a-select-option value="risk">风控人员</a-select-option>
                  <a-select-option value="admin">系统管理员</a-select-option>
                </a-select>
              </a-form-item>
            </a-tab-pane>
            <a-tab-pane key="rules" tab="监控规则">
              <a-form-item label="监控规则">
                <a-checkbox-group v-model:value="riskConfig.monitorRules">
                  <a-row>
                    <a-col :span="12">
                      <a-checkbox value="overdue">逾期监控</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="fraud">欺诈检测</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="behavior">异常行为</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="credit">信用变化</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="operation">操作风险</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="compliance">合规风险</a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="AI模型">
                <a-switch v-model:checked="riskConfig.enableAI" />
                <span style="margin-left: 8px">启用AI风险预测</span>
              </a-form-item>
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </a-modal>

      <!-- 预警历史弹窗 -->
      <a-modal
        v-model:open="alertHistoryVisible"
        title="预警历史记录"
        width="1000px"
        :footer="null"
      >
        <a-table 
          :columns="alertHistoryColumns" 
          :data-source="alertHistoryData"
          :pagination="{ pageSize: 10 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'level'">
              <a-tag :color="getRiskLevelColor(record.level)">
                {{ getRiskLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'handled' ? 'green' : 'orange'">
                {{ record.status === 'handled' ? '已处理' : '待处理' }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  DownloadOutlined, SearchOutlined, ReloadOutlined, SettingOutlined,
  AlertOutlined, SyncOutlined, ArrowUpOutlined, ArrowDownOutlined,
  ArrowRightOutlined, WarningOutlined, BarChartOutlined, HistoryOutlined,
  ExportOutlined, DownOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const refreshInterval = ref(null)
const newRiskCount = ref(3)
const monitoringView = ref('realtime')
const showAlertLine = ref(true)
const configModalVisible = ref(false)
const alertHistoryVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  riskType: undefined,
  riskLevel: undefined,
  dateRange: [dayjs().subtract(7, 'day'), dayjs()],
  department: 'all'
})

// 日期预设
const datePresets = {
  '今日': [dayjs().startOf('day'), dayjs()],
  '本周': [dayjs().startOf('week'), dayjs()],
  '本月': [dayjs().startOf('month'), dayjs()],
  '近7天': [dayjs().subtract(7, 'day'), dayjs()],
  '近30天': [dayjs().subtract(30, 'day'), dayjs()]
}

// 概览数据
const overviewData = reactive({
  riskIndex: 72.5,
  riskTrend: 3.2,
  highRiskCases: 156,
  highRiskRate: 12.5,
  riskAmount: 3456789.23,
  riskAmountChange: 5.6,
  alertCount: 89,
  pendingAlerts: 23
})

// AI预测数据
const aiPrediction = reactive({
  next24h: 8.5,
  weekTrend: '持续上升',
  focusArea: '个人信贷、小微企业',
  confidence: 92
})

// 风险热点
const riskHotspots = ref([
  { id: 1, name: '逾期30天以上', count: 45, level: 'high' },
  { id: 2, name: '失联客户', count: 32, level: 'high' },
  { id: 3, name: '恶意拖欠', count: 28, level: 'medium' },
  { id: 4, name: '多头借贷', count: 25, level: 'medium' },
  { id: 5, name: '收入下降', count: 21, level: 'low' },
  { id: 6, name: '还款意愿低', count: 18, level: 'low' }
])

// 智能预警建议
const alertSuggestions = ref([
  { id: 1, text: '建议对逾期15天的案件加强催收', color: '#ff4d4f' },
  { id: 2, text: '关注近期失业人群的还款能力', color: '#faad14' },
  { id: 3, text: '优化早期催收策略以降低风险', color: '#1890ff' }
])

// 风险配置
const riskConfig = reactive({
  highThreshold: 80,
  mediumThreshold: 60,
  overdueDays: 30,
  amountThreshold: 50000,
  notificationMethods: ['system', 'email'],
  notificationFrequency: 'hourly',
  receivers: ['manager', 'risk'],
  monitorRules: ['overdue', 'fraud', 'behavior', 'credit'],
  enableAI: true
})

// 表格配置
const columns = [
  {
    title: '案件编号',
    dataIndex: 'caseNo',
    key: 'caseNo',
    width: 120,
    fixed: 'left'
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '风险类型',
    dataIndex: 'riskType',
    key: 'riskType',
    width: 100,
    filters: [
      { text: '逾期风险', value: 'overdue' },
      { text: '欺诈风险', value: 'fraud' },
      { text: '操作风险', value: 'operation' },
      { text: '信用风险', value: 'credit' }
    ]
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100,
    filters: [
      { text: '高风险', value: 'high' },
      { text: '中风险', value: 'medium' },
      { text: '低风险', value: 'low' }
    ]
  },
  {
    title: '风险评分',
    key: 'riskScore',
    width: 120,
    sorter: true
  },
  {
    title: '涉及金额',
    key: 'amount',
    width: 120,
    sorter: true
  },
  {
    title: '预警时间',
    dataIndex: 'alertTime',
    key: 'alertTime',
    width: 160,
    sorter: true
  },
  {
    title: '负责人',
    dataIndex: 'handler',
    key: 'handler',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    filters: [
      { text: '待处理', value: 'pending' },
      { text: '处理中', value: 'processing' },
      { text: '已处理', value: 'handled' }
    ]
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 预警历史列
const alertHistoryColumns = [
  {
    title: '预警时间',
    dataIndex: 'time',
    key: 'time',
    width: 160
  },
  {
    title: '案件编号',
    dataIndex: 'caseNo',
    key: 'caseNo',
    width: 120
  },
  {
    title: '风险类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '风险等级',
    key: 'level',
    width: 100
  },
  {
    title: '预警内容',
    dataIndex: 'content',
    key: 'content',
    width: 200
  },
  {
    title: '处理人',
    dataIndex: 'handler',
    key: 'handler',
    width: 100
  },
  {
    title: '处理时间',
    dataIndex: 'handleTime',
    key: 'handleTime',
    width: 160
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    caseNo: 'CASE202401001',
    customerName: '张三',
    riskType: 'overdue',
    riskLevel: 'high',
    riskScore: 92,
    amount: 58900,
    alertTime: '2024-01-20 14:23:45',
    handler: '李催收',
    status: 'pending'
  },
  {
    id: 2,
    caseNo: 'CASE202401002',
    customerName: '李四',
    riskType: 'fraud',
    riskLevel: 'high',
    riskScore: 88,
    amount: 125600,
    alertTime: '2024-01-20 13:15:22',
    handler: '王催收',
    status: 'processing'
  },
  {
    id: 3,
    caseNo: 'CASE202401003',
    customerName: '王五',
    riskType: 'credit',
    riskLevel: 'medium',
    riskScore: 65,
    amount: 32400,
    alertTime: '2024-01-20 11:30:18',
    handler: '张催收',
    status: 'handled'
  }
])

// 预警历史数据
const alertHistoryData = ref([
  {
    id: 1,
    time: '2024-01-20 14:23:45',
    caseNo: 'CASE202401001',
    type: '逾期风险',
    level: 'high',
    content: '逾期超过30天，多次联系未果',
    handler: '李催收',
    handleTime: '2024-01-20 15:30:00',
    status: 'handled'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 图表引用
const realtimeMonitorChart = ref()
const riskTypeChart = ref()
const riskLevelChart = ref()
const departmentRiskChart = ref()
const riskTrendChart = ref()
const riskMapChart = ref()

// 工具方法
const getRiskIndexColor = (value) => {
  if (value >= 80) return '#ff4d4f'
  if (value >= 60) return '#faad14'
  return '#52c41a'
}

const getRiskLevelColor = (level) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || '未知'
}

const getRiskScoreColor = (score) => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#faad14'
  return '#52c41a'
}

const getStatusBadge = (status) => {
  const badges = {
    pending: 'error',
    processing: 'processing',
    handled: 'success'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    handled: '已处理'
  }
  return texts[status] || '未知'
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const isRecentAlert = (time) => {
  return dayjs().diff(dayjs(time), 'hour') < 1
}

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('风险数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.riskType = undefined
  searchForm.riskLevel = undefined
  searchForm.dateRange = [dayjs().subtract(7, 'day'), dayjs()]
  searchForm.department = 'all'
  handleSearch()
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    message.success('已开启自动刷新，每30秒更新一次')
    refreshInterval.value = setInterval(() => {
      refreshRiskList()
    }, 30000)
  } else {
    message.info('已关闭自动刷新')
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
  }
}

const handleDateChange = (dates) => {
  if (dates) {
    console.log('日期范围变更:', dates)
  }
}

const exportRiskReport = () => {
  message.success('风险报告导出成功')
}

const showRiskConfig = () => {
  configModalVisible.value = true
}

const saveRiskConfig = () => {
  configModalVisible.value = false
  message.success('风险配置已保存')
}

const showAlertHistory = () => {
  alertHistoryVisible.value = true
}

const showAIPredictionDetail = () => {
  message.info('查看AI预测详情')
}

const refreshRiskList = () => {
  newRiskCount.value = Math.floor(Math.random() * 5) + 1
  message.success('风险列表已刷新')
}

const batchHandleRisks = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要处理的风险案件')
    return
  }
  message.success(`已批量处理 ${selectedRowKeys.value.length} 个风险案件`)
  selectedRowKeys.value = []
}

const viewRiskDetail = (record) => {
  message.info(`查看风险详情: ${record.caseNo}`)
}

const handleRisk = (record) => {
  message.success(`处理风险案件: ${record.caseNo}`)
}

const handleMoreAction = (action, record) => {
  const actions = {
    analysis: () => message.info(`风险分析: ${record.caseNo}`),
    history: () => message.info(`查看历史记录: ${record.caseNo}`),
    export: () => message.success(`导出报告: ${record.caseNo}`)
  }
  actions[action]?.()
}

const handleTableChange = (paginationInfo, filters, sorter) => {
  console.log('表格变化:', { paginationInfo, filters, sorter })
  pagination.current = paginationInfo.current
  pagination.pageSize = paginationInfo.pageSize
  handleSearch()
}

const updateMonitoringChart = () => {
  // 更新监控图表
  if (realtimeMonitorChart.value) {
    const chart = echarts.getInstanceByDom(realtimeMonitorChart.value)
    if (chart) {
      // 根据视图类型更新图表配置
      initCharts()
    }
  }
}

// 图表初始化
const initCharts = () => {
  // 实时监控图
  const monitorChart = echarts.init(realtimeMonitorChart.value)
  const monitorOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      bottom: 0,
      data: ['风险指数', '预警数量', '处理效率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: [
      {
        type: 'value',
        name: '风险指数',
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '数量',
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '风险指数',
        type: 'line',
        smooth: true,
        data: [65, 68, 72, 78, 75, 73, 70],
        itemStyle: { color: '#ff4d4f' },
        areaStyle: {
          opacity: 0.2
        },
        markLine: showAlertLine.value ? {
          silent: true,
          data: [
            {
              yAxis: 80,
              label: {
                formatter: '高风险预警线'
              },
              lineStyle: {
                color: '#ff4d4f',
                type: 'dashed'
              }
            },
            {
              yAxis: 60,
              label: {
                formatter: '中风险预警线'
              },
              lineStyle: {
                color: '#faad14',
                type: 'dashed'
              }
            }
          ]
        } : undefined
      },
      {
        name: '预警数量',
        type: 'bar',
        yAxisIndex: 1,
        data: [12, 15, 23, 28, 22, 18, 15],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '处理效率',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        data: [85, 82, 78, 80, 83, 86, 88],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  monitorChart.setOption(monitorOption)

  // 风险类型分布
  const typeChart = echarts.init(riskTypeChart.value)
  const typeOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '风险类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45, name: '逾期风险' },
          { value: 32, name: '欺诈风险' },
          { value: 28, name: '操作风险' },
          { value: 25, name: '信用风险' },
          { value: 20, name: '法律风险' }
        ]
      }
    ]
  }
  typeChart.setOption(typeOption)

  // 风险等级分布
  const levelChart = echarts.init(riskLevelChart.value)
  const levelOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['低风险', '中风险', '高风险']
    },
    series: [
      {
        name: '案件数量',
        type: 'bar',
        data: [
          {
            value: 234,
            itemStyle: { color: '#52c41a' }
          },
          {
            value: 156,
            itemStyle: { color: '#faad14' }
          },
          {
            value: 89,
            itemStyle: { color: '#ff4d4f' }
          }
        ],
        label: {
          show: true,
          position: 'right',
          formatter: '{c} 件'
        }
      }
    ]
  }
  levelChart.setOption(levelOption)

  // 部门风险对比
  const deptChart = echarts.init(departmentRiskChart.value)
  const deptOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['催收一部', '催收二部', '法务部', '客服部']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '高风险',
        type: 'bar',
        stack: 'total',
        data: [23, 18, 15, 8],
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '中风险',
        type: 'bar',
        stack: 'total',
        data: [45, 38, 32, 25],
        itemStyle: { color: '#faad14' }
      },
      {
        name: '低风险',
        type: 'bar',
        stack: 'total',
        data: [78, 65, 52, 45],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  deptChart.setOption(deptOption)

  // 风险趋势预测
  const trendChart = echarts.init(riskTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月(预测)']
    },
    yAxis: {
      type: 'value',
      name: '风险指数'
    },
    series: [
      {
        name: '实际值',
        type: 'line',
        data: [65, 68, 70, 72, 75, null],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '预测值',
        type: 'line',
        data: [null, null, null, null, 75, 78],
        lineStyle: {
          type: 'dashed'
        },
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '置信区间',
        type: 'line',
        data: [null, null, null, null, 73, 75],
        lineStyle: {
          opacity: 0
        },
        stack: 'confidence',
        areaStyle: {
          color: 'rgba(255, 77, 79, 0.1)'
        },
        symbol: 'none'
      },
      {
        name: '置信区间',
        type: 'line',
        data: [null, null, null, null, 77, 81],
        lineStyle: {
          opacity: 0
        },
        stack: 'confidence',
        areaStyle: {
          color: 'rgba(255, 77, 79, 0.1)'
        },
        symbol: 'none'
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 风险地图
  const mapChart = echarts.init(riskMapChart.value)
  const mapOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} 件'
    },
    visualMap: {
      min: 0,
      max: 100,
      left: 'left',
      top: 'bottom',
      text: ['高', '低'],
      calculable: true,
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
      }
    },
    series: [
      {
        name: '风险分布',
        type: 'scatter',
        coordinateSystem: 'geo',
        data: [
          { name: '北京', value: [116.46, 39.92, 89] },
          { name: '上海', value: [121.48, 31.22, 76] },
          { name: '广州', value: [113.23, 23.16, 65] },
          { name: '深圳', value: [114.07, 22.62, 58] },
          { name: '成都', value: [104.06, 30.67, 45] }
        ],
        symbolSize: function (val) {
          return val[2] / 2
        },
        encode: {
          value: 2
        },
        label: {
          formatter: '{b}',
          position: 'right',
          show: true
        },
        itemStyle: {
          color: '#ff4d4f'
        },
        emphasis: {
          label: {
            show: true
          }
        }
      }
    ],
    geo: {
      map: 'china',
      roam: true,
      label: {
        emphasis: {
          show: false
        }
      },
      itemStyle: {
        normal: {
          areaColor: '#f3f3f3',
          borderColor: '#999'
        },
        emphasis: {
          areaColor: '#e6f7ff'
        }
      }
    }
  }
  
  // 模拟中国地图数据
  mapOption.series[0].type = 'heatmap'
  mapOption.series[0].data = generateHeatmapData()
  mapOption.xAxis = { type: 'category', data: ['东', '南', '西', '北', '中'] }
  mapOption.yAxis = { type: 'category', data: ['一区', '二区', '三区', '四区'] }
  mapOption.visualMap = {
    min: 0,
    max: 100,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '15%'
  }
  mapOption.geo = undefined
  
  mapChart.setOption(mapOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    monitorChart.resize()
    typeChart.resize()
    levelChart.resize()
    deptChart.resize()
    trendChart.resize()
    mapChart.resize()
  })
}

// 生成热力图数据
const generateHeatmapData = () => {
  const data = []
  const xAxis = ['东', '南', '西', '北', '中']
  const yAxis = ['一区', '二区', '三区', '四区']
  
  for (let i = 0; i < xAxis.length; i++) {
    for (let j = 0; j < yAxis.length; j++) {
      data.push([i, j, Math.floor(Math.random() * 100)])
    }
  }
  
  return data
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.overview-cards {
  margin-bottom: 16px;
}

.overview-cards .ant-statistic {
  text-align: center;
}

.trend-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.sub-info {
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.ai-cards {
  margin-bottom: 16px;
}

.ai-prediction .prediction-item {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-prediction .label {
  color: #666;
}

.ai-prediction .value {
  font-weight: 500;
}

.risk-hotspot {
  min-height: 120px;
}

.hotspot-summary {
  text-align: center;
  color: #666;
}

.alert-suggestions {
  min-height: 120px;
}

.chart-section {
  margin-bottom: 16px;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-badge {
  cursor: pointer;
}

:deep(.ant-table-wrapper) {
  .ant-table-row {
    cursor: pointer;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>