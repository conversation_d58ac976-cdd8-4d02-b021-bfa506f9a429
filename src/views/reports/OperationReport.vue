<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>运营报表</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="报表类型">
                <a-select v-model:value="searchForm.reportType" placeholder="请选择报表类型" allow-clear>
                  <a-select-option value="daily">日报</a-select-option>
                  <a-select-option value="weekly">周报</a-select-option>
                  <a-select-option value="monthly">月报</a-select-option>
                  <a-select-option value="quarterly">季报</a-select-option>
                  <a-select-option value="yearly">年报</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange"
                  :presets="datePresets"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="业务线">
                <a-select v-model:value="searchForm.businessLine" placeholder="请选择业务线" allow-clear>
                  <a-select-option value="all">全部业务线</a-select-option>
                  <a-select-option value="collection">催收业务</a-select-option>
                  <a-select-option value="legal">法务业务</a-select-option>
                  <a-select-option value="customer">客户服务</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="数据维度">
                  <a-select v-model:value="searchForm.dimension" placeholder="请选择维度" allow-clear>
                    <a-select-option value="department">部门</a-select-option>
                    <a-select-option value="team">团队</a-select-option>
                    <a-select-option value="individual">个人</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="指标类型">
                  <a-select v-model:value="searchForm.metricType" placeholder="请选择指标" allow-clear>
                    <a-select-option value="all">全部指标</a-select-option>
                    <a-select-option value="efficiency">效率指标</a-select-option>
                    <a-select-option value="quality">质量指标</a-select-option>
                    <a-select-option value="performance">绩效指标</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="对比模式">
                  <a-select v-model:value="searchForm.compareMode" placeholder="请选择对比模式" allow-clear>
                    <a-select-option value="none">无对比</a-select-option>
                    <a-select-option value="period">同期对比</a-select-option>
                    <a-select-option value="chain">环比对比</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="导出格式">
                  <a-select v-model:value="searchForm.exportFormat" placeholder="请选择格式" allow-clear>
                    <a-select-option value="excel">Excel</a-select-option>
                    <a-select-option value="pdf">PDF</a-select-option>
                    <a-select-option value="csv">CSV</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总处理案件数" 
              :value="operationData.totalCases" 
              :value-style="{ color: '#1890ff' }"
              suffix="件"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="getChangeClass(operationData.casesChange)">{{ formatChange(operationData.casesChange) }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均处理时长" 
              :value="operationData.avgProcessTime" 
              :value-style="{ color: '#52c41a' }"
              suffix="天"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="getChangeClass(-operationData.timeChange)">{{ formatChange(-operationData.timeChange) }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="整体效率指数" 
              :value="operationData.efficiencyIndex" 
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="getChangeClass(operationData.efficiencyChange)">{{ formatChange(operationData.efficiencyChange) }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="客户满意度" 
              :value="operationData.satisfaction" 
              :precision="1"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="getChangeClass(operationData.satisfactionChange)">{{ formatChange(operationData.satisfactionChange) }}%</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space wrap>
            <a-button type="primary" @click="generateReport">
              <FileTextOutlined />
              生成报表
            </a-button>
            <a-button @click="exportReport">
              <DownloadOutlined />
              导出报表
            </a-button>
            <a-button @click="scheduleReport">
              <ScheduleOutlined />
              定时报表
            </a-button>
            <a-button @click="shareReport">
              <ShareAltOutlined />
              分享报表
            </a-button>
            <a-button @click="printReport">
              <PrinterOutlined />
              打印报表
            </a-button>
            <a-button @click="showAnalysisModal = true">
              <BarChartOutlined />
              数据分析
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 图表展示区域 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="工作量趋势" :body-style="{ padding: '20px' }">
            <div ref="workloadTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="效率分析" :body-style="{ padding: '20px' }">
            <div ref="efficiencyChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="16">
          <a-card title="部门运营对比" :body-style="{ padding: '20px' }">
            <div ref="departmentComparisonChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="业务分布" :body-style="{ padding: '20px' }">
            <div ref="businessDistributionChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细数据表格 -->
      <a-card title="运营数据明细">
        <a-table 
          :columns="columns" 
          :data-source="operationTable" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'efficiency'">
              <a-progress 
                :percent="record.efficiency" 
                size="small" 
                :stroke-color="getEfficiencyColor(record.efficiency)"
              />
            </template>
            <template v-if="column.key === 'satisfaction'">
              <a-rate :value="record.satisfaction / 20" disabled />
            </template>
            <template v-if="column.key === 'trend'">
              <span :class="getChangeClass(record.trend)">
                {{ record.trend > 0 ? '↗' : record.trend < 0 ? '↘' : '→' }}
                {{ Math.abs(record.trend) }}%
              </span>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="analyze(record)">分析</a-button>
                <a-button type="link" size="small" @click="optimize(record)">优化</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 运营分析 -->
      <a-card title="运营分析与建议" class="analysis-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>关键指标分析</h4>
            <a-descriptions size="small" bordered>
              <a-descriptions-item label="工作负荷指数" span="2">
                {{ analysisData.workloadIndex }}
              </a-descriptions-item>
              <a-descriptions-item label="资源利用率" span="2">
                {{ analysisData.resourceUtilization }}%
              </a-descriptions-item>
              <a-descriptions-item label="流程效率" span="2">
                {{ analysisData.processEfficiency }}%
              </a-descriptions-item>
              <a-descriptions-item label="质量得分" span="2">
                {{ analysisData.qualityScore }}/100
              </a-descriptions-item>
              <a-descriptions-item label="成本效益比" span="2">
                {{ analysisData.costEffectiveness }}
              </a-descriptions-item>
              <a-descriptions-item label="系统稳定性" span="2">
                {{ analysisData.systemStability }}%
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="12">
            <h4>优化建议</h4>
            <a-list size="small" :data-source="suggestions">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: item.color }">{{ item.type }}</a-avatar>
                    </template>
                    <template #title>{{ item.title }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
        </a-row>
      </a-card>

      <!-- 数据分析弹窗 -->
      <a-modal
        v-model:open="showAnalysisModal"
        title="深度数据分析"
        width="800px"
        @ok="handleAnalysisSubmit"
        @cancel="showAnalysisModal = false"
      >
        <a-tabs v-model:activeKey="analysisActiveTab">
          <a-tab-pane key="trend" tab="趋势分析">
            <div class="analysis-content">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-statistic title="增长率" :value="12.5" suffix="%" />
                </a-col>
                <a-col :span="12">
                  <a-statistic title="波动率" :value="8.2" suffix="%" />
                </a-col>
              </a-row>
              <a-divider />
              <div ref="trendAnalysisChart" style="height: 200px; background: #fafafa; border: 1px dashed #d9d9d9; display: flex; align-items: center; justify-content: center; color: #999;">
                趋势分析图表
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="correlation" tab="相关性分析">
            <div class="analysis-content">
              <a-list size="small">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>效率与满意度</template>
                    <template #description>相关系数: 0.78 (强正相关)</template>
                  </a-list-item-meta>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>工作量与质量</template>
                    <template #description>相关系数: -0.32 (弱负相关)</template>
                  </a-list-item-meta>
                </a-list-item>
              </a-list>
            </div>
          </a-tab-pane>
          <a-tab-pane key="forecast" tab="预测分析">
            <div class="analysis-content">
              <a-alert message="基于历史数据的预测结果" type="info" show-icon style="margin-bottom: 16px;" />
              <a-descriptions size="small" bordered>
                <a-descriptions-item label="下月预计案件量">2,350件</a-descriptions-item>
                <a-descriptions-item label="预计处理时长">15.2天</a-descriptions-item>
                <a-descriptions-item label="资源需求">增加15%</a-descriptions-item>
              </a-descriptions>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined,
  FileTextOutlined,
  DownloadOutlined,
  ScheduleOutlined,
  ShareAltOutlined,
  PrinterOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const showAnalysisModal = ref(false)
const analysisActiveTab = ref('trend')

// 搜索表单
const searchForm = reactive({
  reportType: 'monthly',
  dateRange: [dayjs().startOf('month'), dayjs().endOf('month')],
  businessLine: 'all',
  dimension: undefined,
  metricType: undefined,
  compareMode: undefined,
  exportFormat: undefined
})

// 日期预设
const datePresets = {
  '今天': [dayjs(), dayjs()],
  '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 运营数据
const operationData = reactive({
  totalCases: 2345,
  casesChange: 8.5,
  avgProcessTime: 15.2,
  timeChange: 12.3,
  efficiencyIndex: 85.6,
  efficiencyChange: 5.8,
  satisfaction: 92.3,
  satisfactionChange: 3.2
})

// 分析数据
const analysisData = reactive({
  workloadIndex: '85.6',
  resourceUtilization: 78.5,
  processEfficiency: 82.3,
  qualityScore: 89,
  costEffectiveness: '1.25',
  systemStability: 95.8
})

// 优化建议
const suggestions = ref([
  {
    type: '效',
    color: '#1890ff',
    title: '提升处理效率',
    description: '建议优化工作流程，减少重复环节，预计可提升15%效率'
  },
  {
    type: '质',
    color: '#52c41a',
    title: '强化质量控制',
    description: '建立多层次质检机制，确保服务质量稳步提升'
  },
  {
    type: '本',
    color: '#faad14',
    title: '控制运营成本',
    description: '通过自动化工具减少人工操作，降低运营成本'
  },
  {
    type: '源',
    color: '#722ed1',
    title: '优化资源配置',
    description: '根据业务量动态调整人员配置，提高资源利用率'
  }
])

// 表格配置
const columns = [
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120,
    fixed: 'left'
  },
  {
    title: '团队',
    dataIndex: 'team',
    key: 'team',
    width: 100
  },
  {
    title: '处理案件数',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 120
  },
  {
    title: '平均时长(天)',
    dataIndex: 'avgDuration',
    key: 'avgDuration',
    width: 120
  },
  {
    title: '效率指数',
    key: 'efficiency',
    width: 120
  },
  {
    title: '质量得分',
    dataIndex: 'qualityScore',
    key: 'qualityScore',
    width: 100
  },
  {
    title: '满意度',
    key: 'satisfaction',
    width: 120
  },
  {
    title: '成本(万元)',
    dataIndex: 'cost',
    key: 'cost',
    width: 100
  },
  {
    title: '趋势',
    key: 'trend',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 表格数据
const operationTable = ref([
  {
    id: 1,
    department: '催收一部',
    team: 'A组',
    caseCount: 856,
    avgDuration: 14.5,
    efficiency: 88,
    qualityScore: 92,
    satisfaction: 85,
    cost: 45.6,
    trend: 8.2,
    status: 'excellent'
  },
  {
    id: 2,
    department: '催收二部',
    team: 'B组',
    caseCount: 734,
    avgDuration: 16.8,
    efficiency: 82,
    qualityScore: 89,
    satisfaction: 80,
    cost: 42.3,
    trend: -2.5,
    status: 'good'
  },
  {
    id: 3,
    department: '法务部',
    team: 'C组',
    caseCount: 234,
    avgDuration: 25.6,
    efficiency: 75,
    qualityScore: 95,
    satisfaction: 95,
    cost: 38.9,
    trend: 5.8,
    status: 'good'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: operationTable.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const workloadTrendChart = ref()
const efficiencyChart = ref()
const departmentComparisonChart = ref()
const businessDistributionChart = ref()
const trendAnalysisChart = ref()

// 状态相关方法
const getChangeClass = (change) => {
  if (change > 0) return 'change-increase'
  if (change < 0) return 'change-decrease'
  return 'change-stable'
}

const formatChange = (change) => {
  return change > 0 ? `+${change}` : change.toString()
}

const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 85) return '#52c41a'
  if (efficiency >= 70) return '#faad14'
  return '#ff4d4f'
}

const getStatusColor = (status) => {
  const colors = {
    excellent: 'green',
    good: 'blue',
    average: 'orange',
    poor: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return texts[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('运营数据已更新')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else if (key === 'reportType') {
      searchForm[key] = 'monthly'
    } else if (key === 'dateRange') {
      searchForm[key] = [dayjs().startOf('month'), dayjs().endOf('month')]
    } else if (key === 'businessLine') {
      searchForm[key] = 'all'
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

const generateReport = () => {
  message.success('正在生成运营报表...')
}

const exportReport = () => {
  message.success('运营报表导出成功')
}

const scheduleReport = () => {
  message.info('定时报表设置功能')
}

const shareReport = () => {
  message.success('报表分享链接已生成')
}

const printReport = () => {
  message.success('报表打印成功')
}

const handleAnalysisSubmit = () => {
  message.success('数据分析完成')
  showAnalysisModal.value = false
}

const viewDetail = (record) => {
  message.info(`查看 ${record.department} ${record.team} 的详细信息`)
}

const analyze = (record) => {
  message.info(`分析 ${record.department} ${record.team} 的运营情况`)
}

const optimize = (record) => {
  message.info(`为 ${record.department} ${record.team} 制定优化方案`)
}

// 图表初始化
const initCharts = () => {
  // 工作量趋势图
  const workloadChart = echarts.init(workloadTrendChart.value)
  const workloadOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '案件数'
    },
    series: [
      {
        name: '新增案件',
        type: 'bar',
        data: [1200, 1350, 1180, 1420, 1580, 1390],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '处理案件',
        type: 'line',
        data: [1100, 1280, 1150, 1380, 1520, 1350],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  workloadChart.setOption(workloadOption)

  // 效率分析图
  const effChart = echarts.init(efficiencyChart.value)
  const effOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['催收一部', '催收二部', '法务部', '客服部']
    },
    yAxis: [
      {
        type: 'value',
        name: '效率指数',
        min: 0,
        max: 100
      },
      {
        type: 'value',
        name: '满意度(%)',
        min: 0,
        max: 100
      }
    ],
    series: [
      {
        name: '效率指数',
        type: 'bar',
        data: [88, 82, 75, 90],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '满意度',
        type: 'line',
        yAxisIndex: 1,
        data: [85, 80, 95, 88],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  effChart.setOption(effOption)

  // 部门运营对比图
  const comparisonChart = echarts.init(departmentComparisonChart.value)
  const comparisonOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['案件处理量', '平均时长', '质量得分', '成本控制', '客户满意度']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '催收一部',
        type: 'bar',
        data: [856, 14.5, 92, 45.6, 85],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '催收二部',
        type: 'bar',
        data: [734, 16.8, 89, 42.3, 80],
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '法务部',
        type: 'bar',
        data: [234, 25.6, 95, 38.9, 95],
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  comparisonChart.setOption(comparisonOption)

  // 业务分布图
  const distributionChart = echarts.init(businessDistributionChart.value)
  const distributionOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '业务分布',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1200, name: '电话催收' },
          { value: 800, name: '短信催收' },
          { value: 300, name: '外访催收' },
          { value: 200, name: '法务催收' },
          { value: 150, name: '其他业务' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  distributionChart.setOption(distributionOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    workloadChart.resize()
    effChart.resize()
    comparisonChart.resize()
    distributionChart.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.chart-section {
  margin-bottom: 16px;
}

.analysis-card {
  margin-top: 16px;
}

.analysis-card h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.analysis-content {
  padding: 16px 0;
}

.stat-change {
  margin-top: 8px;
  font-size: 12px;
}

.change-text {
  color: #666;
}

.change-increase {
  color: #52c41a;
}

.change-decrease {
  color: #ff4d4f;
}

.change-stable {
  color: #666;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>