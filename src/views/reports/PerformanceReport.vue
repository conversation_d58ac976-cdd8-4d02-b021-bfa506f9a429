<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>人员绩效报表</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="考核周期">
                <a-select v-model:value="searchForm.period" placeholder="请选择考核周期" allow-clear>
                  <a-select-option value="monthly">月度</a-select-option>
                  <a-select-option value="quarterly">季度</a-select-option>
                  <a-select-option value="yearly">年度</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="all">全部部门</a-select-option>
                  <a-select-option value="dept1">催收一部</a-select-option>
                  <a-select-option value="dept2">催收二部</a-select-option>
                  <a-select-option value="dept3">法务部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="绩效等级">
                <a-select v-model:value="searchForm.grade" placeholder="请选择等级" allow-clear>
                  <a-select-option value="all">全部等级</a-select-option>
                  <a-select-option value="excellent">优秀</a-select-option>
                  <a-select-option value="good">良好</a-select-option>
                  <a-select-option value="average">一般</a-select-option>
                  <a-select-option value="poor">较差</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportReport">
                      <download-outlined />
                      导出报表
                    </a-button>
                    <a-button @click="generateRanking">
                      <trophy-outlined />
                      生成排名
                    </a-button>
                    <a-button @click="kpiSettings">
                      <setting-outlined />
                      KPI设置
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 整体绩效概览 -->
      <a-row :gutter="16" class="overview-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="团队平均绩效" 
              :value="overallData.avgPerformance" 
              :precision="1"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
            <div class="performance-bar">
              <a-progress :percent="overallData.avgPerformance" size="small" />
            </div>
            <div class="stat-footer">
              <span>目标值: <span class="stat-target">85.0</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="优秀员工占比" 
              :value="overallData.excellentRate" 
              :precision="1"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <TeamOutlined />
              </template>
            </a-statistic>
            <div class="stat-change">
              <span class="change-text">较上期 <span class="increase">+{{ overallData.excellentChange }}%</span></span>
            </div>
            <div class="stat-footer">
              <span>优秀标准: <span class="stat-info">90分以上</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="团队目标达成率" 
              :value="overallData.targetAchievement" 
              :precision="1"
              suffix="%"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <AimOutlined />
              </template>
            </a-statistic>
            <div class="stat-change">
              <span class="change-text">较上期 <span class="increase">+{{ overallData.targetChange }}%</span></span>
            </div>
            <div class="stat-footer">
              <span>超额完成: <span class="stat-success">5.6%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="团队总回收金额" 
              :value="overallData.totalRecovery" 
              :precision="0"
              prefix="¥"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <DollarCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-change">
              <span class="change-text">较上期 <span class="increase">+{{ overallData.recoveryChange }}%</span></span>
            </div>
            <div class="stat-footer">
              <span>人均回收: <span class="stat-avg">¥{{ Math.round(overallData.totalRecovery / 28).toLocaleString() }}</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- AI智能分析卡片 -->
      <a-row :gutter="16" class="ai-analysis-section">
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <RobotOutlined class="ai-icon" />
              <span class="ai-title">AI绩效预测</span>
            </div>
            <div class="ai-content">
              <div class="prediction-score">
                <span class="score-label">下月预测绩效</span>
                <span class="score-value">{{ aiPrediction.nextMonthScore }}</span>
              </div>
              <div class="prediction-trend">
                <RiseOutlined v-if="aiPrediction.trend === 'up'" class="trend-up" />
                <FallOutlined v-else class="trend-down" />
                <span>{{ aiPrediction.trendText }}</span>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <BulbOutlined class="ai-icon" />
              <span class="ai-title">智能建议</span>
            </div>
            <div class="ai-content">
              <div class="suggestion-item">
                <ExclamationCircleOutlined class="suggestion-icon" />
                <span>{{ aiSuggestion.primary }}</span>
              </div>
              <div class="suggestion-count">
                <span>共有 {{ aiSuggestion.count }} 条优化建议</span>
                <a-button type="link" size="small" @click="showAISuggestions">查看详情</a-button>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <AlertOutlined class="ai-icon" />
              <span class="ai-title">风险预警</span>
            </div>
            <div class="ai-content">
              <div class="risk-alert">
                <span class="risk-level" :class="aiRiskAlert.level">{{ aiRiskAlert.levelText }}</span>
                <span class="risk-count">{{ aiRiskAlert.count }}人需关注</span>
              </div>
              <div class="risk-action">
                <a-button type="link" size="small" @click="showRiskDetail">查看详情</a-button>
                <a-button type="link" size="small" @click="generateAction">生成行动计划</a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表分析 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="个人绩效分布" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-radio-group v-model:value="distributionType" size="small">
                <a-radio-button value="grade">等级分布</a-radio-button>
                <a-radio-button value="department">部门分布</a-radio-button>
                <a-radio-button value="position">职位分布</a-radio-button>
              </a-radio-group>
            </div>
            <div ref="performanceDistributionChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="月度绩效趋势" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-checkbox-group v-model:value="trendDepartments" size="small">
                <a-checkbox value="dept1">催收一部</a-checkbox>
                <a-checkbox value="dept2">催收二部</a-checkbox>
                <a-checkbox value="dept3">法务部</a-checkbox>
              </a-checkbox-group>
            </div>
            <div ref="performanceTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="16">
          <a-card title="KPI指标雷达图" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-select v-model:value="selectedEmployee" placeholder="选择员工" style="width: 200px;" size="small">
                <a-select-option value="all">团队平均</a-select-option>
                <a-select-option value="1">李催收</a-select-option>
                <a-select-option value="2">王催收</a-select-option>
                <a-select-option value="3">张催收</a-select-option>
              </a-select>
              <a-button type="link" size="small" @click="compareKPI">对比分析</a-button>
            </div>
            <div ref="kpiRadarChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="部门绩效对比" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-radio-group v-model:value="compareMetric" size="small">
                <a-radio-button value="performance">综合绩效</a-radio-button>
                <a-radio-button value="recovery">回收金额</a-radio-button>
                <a-radio-button value="success">成功率</a-radio-button>
              </a-radio-group>
            </div>
            <div ref="departmentCompareChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 新增图表区域 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="绩效改进趋势" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>时间跨度:</span>
                <a-select v-model:value="improvementPeriod" size="small" style="width: 120px;">
                  <a-select-option value="3m">近3个月</a-select-option>
                  <a-select-option value="6m">近6个月</a-select-option>
                  <a-select-option value="1y">近1年</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="improvementChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="绩效相关性分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>分析维度:</span>
                <a-select v-model:value="correlationDimension" size="small" style="width: 150px;">
                  <a-select-option value="experience">工作经验</a-select-option>
                  <a-select-option value="training">培训时长</a-select-option>
                  <a-select-option value="workload">工作负荷</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="correlationChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 绩效预测图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="AI绩效预测与建议" :body-style="{ padding: '20px' }">
            <a-tabs v-model:activeKey="predictionTab">
              <a-tab-pane key="prediction" tab="绩效预测">
                <div ref="predictionChart" style="height: 400px"></div>
              </a-tab-pane>
              <a-tab-pane key="risk" tab="风险预警">
                <div ref="riskChart" style="height: 400px"></div>
              </a-tab-pane>
              <a-tab-pane key="suggestion" tab="优化建议">
                <div class="suggestion-content">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <h4>个人优化建议</h4>
                      <a-list :data-source="personalSuggestions" size="small">
                        <template #renderItem="{ item }">
                          <a-list-item>
                            <a-list-item-meta>
                              <template #avatar>
                                <a-avatar :style="{ backgroundColor: item.color }">
                                  {{ item.employee.charAt(0) }}
                                </a-avatar>
                              </template>
                              <template #title>{{ item.employee }}</template>
                              <template #description>
                                <div class="suggestion-detail">
                                  <p>{{ item.suggestion }}</p>
                                  <a-tag :color="item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'blue'">
                                    {{ item.priority === 'high' ? '高优先级' : item.priority === 'medium' ? '中优先级' : '低优先级' }}
                                  </a-tag>
                                </div>
                              </template>
                            </a-list-item-meta>
                          </a-list-item>
                        </template>
                      </a-list>
                    </a-col>
                    <a-col :span="12">
                      <h4>团队优化建议</h4>
                      <a-timeline>
                        <a-timeline-item v-for="item in teamSuggestions" :key="item.id" :color="item.color">
                          <p><strong>{{ item.title }}</strong></p>
                          <p>{{ item.description }}</p>
                          <p class="suggestion-impact">预期效果: {{ item.impact }}</p>
                        </a-timeline-item>
                      </a-timeline>
                    </a-col>
                  </a-row>
                </div>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </a-col>
      </a-row>

      <!-- 绩效排行榜 -->
      <a-row :gutter="16" class="ranking-section">
        <a-col :span="8">
          <a-card title="回收金额Top10" class="ranking-card">
            <a-list :data-source="topRecoveryList" size="small">
              <template #renderItem="{ item, index }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: getRankColor(index) }">{{ index + 1 }}</a-avatar>
                    </template>
                    <template #title>
                      <span class="name">{{ item.name }}</span>
                      <span class="department">{{ item.department }}</span>
                    </template>
                    <template #description>
                      ¥{{ item.amount.toLocaleString() }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="成功率Top10" class="ranking-card">
            <a-list :data-source="topSuccessRateList" size="small">
              <template #renderItem="{ item, index }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: getRankColor(index) }">{{ index + 1 }}</a-avatar>
                    </template>
                    <template #title>
                      <span class="name">{{ item.name }}</span>
                      <span class="department">{{ item.department }}</span>
                    </template>
                    <template #description>
                      {{ item.rate }}%
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="综合绩效Top10" class="ranking-card">
            <a-list :data-source="topPerformanceList" size="small">
              <template #renderItem="{ item, index }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: getRankColor(index) }">{{ index + 1 }}</a-avatar>
                    </template>
                    <template #title>
                      <span class="name">{{ item.name }}</span>
                      <span class="department">{{ item.department }}</span>
                    </template>
                    <template #description>
                      <a-rate :value="item.score / 20" disabled />
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细绩效表格 -->
      <a-card title="员工绩效详情">
        <a-table 
          :columns="columns" 
          :data-source="performanceData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar :src="record.avatar">{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <template v-if="column.key === 'performance'">
              <div class="performance-cell">
                <a-progress :percent="record.performance" size="small" />
                <span class="performance-text">{{ record.performance }}/100</span>
              </div>
            </template>
            <template v-if="column.key === 'grade'">
              <a-tag :color="getGradeColor(record.grade)">
                {{ getGradeText(record.grade) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'recoveryAmount'">
              ¥{{ record.recoveryAmount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'kpiCompletion'">
              <a-progress 
                :percent="record.kpiCompletion" 
                size="small" 
                :stroke-color="getKpiColor(record.kpiCompletion)"
              />
            </template>
            <template v-if="column.key === 'trend'">
              <span :class="{ 'increase': record.trend > 0, 'decrease': record.trend < 0 }">
                {{ record.trend > 0 ? '↗' : record.trend < 0 ? '↘' : '→' }}
                {{ Math.abs(record.trend) }}%
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="performanceAnalysis(record)">分析</a-button>
                <a-button type="link" size="small" @click="setTarget(record)">设定目标</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 绩效分析建议 -->
      <a-card title="绩效分析与建议" class="analysis-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>团队优势分析</h4>
            <a-list size="small" :data-source="strengthAnalysis">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-icon component="CheckCircleOutlined" style="color: #52c41a;" />
                    </template>
                    <template #title>{{ item.title }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
          <a-col :span="12">
            <h4>改进建议</h4>
            <a-list size="small" :data-source="improvementSuggestions">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-icon component="ExclamationCircleOutlined" style="color: #faad14;" />
                    </template>
                    <template #title>{{ item.title }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  DownloadOutlined, 
  TrophyOutlined, 
  SettingOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  SearchOutlined, 
  ReloadOutlined,
  TeamOutlined,
  AimOutlined,
  DollarCircleOutlined,
  RobotOutlined,
  BulbOutlined,
  AlertOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  period: 'monthly',
  dateRange: [dayjs().startOf('month'), dayjs().endOf('month')],
  department: 'all',
  grade: 'all'
})

// 日期预设
const datePresets = {
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '上月': [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
  '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 整体数据
const overallData = reactive({
  avgPerformance: 78.5,
  excellentRate: 32.8,
  excellentChange: 5.2,
  targetAchievement: 85.6,
  targetChange: 8.1,
  totalRecovery: 8456000,
  recoveryChange: 12.3
})

// AI预测数据
const aiPrediction = reactive({
  nextMonthScore: 82.1,
  trend: 'up',
  trendText: '预测上升3.6分',
  confidence: 85
})

// AI建议数据
const aiSuggestion = reactive({
  primary: '建议加强新员工培训，提升整体绩效',
  count: 8
})

// AI风险预警
const aiRiskAlert = reactive({
  level: 'medium',
  levelText: '中等风险',
  count: 3
})

// 图表控制变量
const distributionType = ref('grade')
const trendDepartments = ref(['dept1', 'dept2', 'dept3'])
const selectedEmployee = ref('all')
const compareMetric = ref('performance')
const improvementPeriod = ref('6m')
const correlationDimension = ref('experience')
const predictionTab = ref('prediction')

// 个人优化建议
const personalSuggestions = ref([
  {
    employee: '张催收',
    suggestion: '建议加强沟通技巧培训，提高客户满意度',
    priority: 'high',
    color: '#ff4d4f'
  },
  {
    employee: '李新人',
    suggestion: '需要增加实战经验，建议安排资深员工指导',
    priority: 'high',
    color: '#fa8c16'
  },
  {
    employee: '王催收',
    suggestion: '表现优秀，可考虑承担更多挑战性任务',
    priority: 'medium',
    color: '#52c41a'
  },
  {
    employee: '刘催收',
    suggestion: '建议参加法律知识进修，提升专业能力',
    priority: 'medium',
    color: '#1890ff'
  }
])

// 团队优化建议
const teamSuggestions = ref([
  {
    id: 1,
    title: '建立导师制度',
    description: '为新员工配备资深导师，加速成长',
    impact: '预计新员工绩效提升25%',
    color: 'green'
  },
  {
    id: 2,
    title: '优化激励机制',
    description: '完善绩效奖励体系，提高员工积极性',
    impact: '预计整体绩效提升15%',
    color: 'blue'
  },
  {
    id: 3,
    title: '引入AI工具',
    description: '使用智能分析工具辅助催收决策',
    impact: '预计成功率提升10%',
    color: 'purple'
  },
  {
    id: 4,
    title: '加强团队协作',
    description: '建立部门间沟通机制，提高协作效率',
    impact: '预计流程效率提升20%',
    color: 'orange'
  }
])

// 排行榜数据
const topRecoveryList = ref([
  { name: '李催收', department: '催收一部', amount: 890000 },
  { name: '王催收', department: '催收二部', amount: 756000 },
  { name: '张催收', department: '催收一部', amount: 698000 },
  { name: '刘催收', department: '法务部', amount: 645000 },
  { name: '陈催收', department: '催收二部', amount: 589000 }
])

const topSuccessRateList = ref([
  { name: '王催收', department: '催收二部', rate: 89.5 },
  { name: '李催收', department: '催收一部', rate: 87.2 },
  { name: '周催收', department: '催收一部', rate: 85.8 },
  { name: '吴催收', department: '法务部', rate: 83.6 },
  { name: '郑催收', department: '催收二部', rate: 82.1 }
])

const topPerformanceList = ref([
  { name: '李催收', department: '催收一部', score: 95 },
  { name: '王催收', department: '催收二部', score: 92 },
  { name: '张催收', department: '催收一部', score: 89 },
  { name: '刘催收', department: '法务部', score: 87 },
  { name: '陈催收', department: '催收二部', score: 85 }
])

// 分析数据
const strengthAnalysis = ref([
  {
    title: '回收能力强',
    description: '团队整体回收金额超出目标15%，回收能力突出'
  },
  {
    title: '专业技能优秀',
    description: '多数员工在沟通技巧和法律知识方面表现优异'
  },
  {
    title: '团队协作良好',
    description: '部门间协作效率高，案件流转顺畅'
  }
])

const improvementSuggestions = ref([
  {
    title: '加强新员工培训',
    description: '新入职员工绩效偏低，建议加强专业技能培训'
  },
  {
    title: '优化激励机制',
    description: '建议完善绩效奖励体系，提高员工积极性'
  },
  {
    title: '引入智能工具',
    description: '使用AI辅助工具提高催收效率和成功率'
  }
])

// 表格配置
const columns = [
  {
    title: '头像',
    key: 'avatar',
    width: 60
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    fixed: 'left'
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '职位',
    dataIndex: 'position',
    key: 'position',
    width: 100
  },
  {
    title: '综合绩效',
    key: 'performance',
    width: 150
  },
  {
    title: '绩效等级',
    key: 'grade',
    width: 100
  },
  {
    title: '回收金额',
    key: 'recoveryAmount',
    width: 120
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate',
    width: 80
  },
  {
    title: 'KPI完成度',
    key: 'kpiCompletion',
    width: 120
  },
  {
    title: '趋势',
    key: 'trend',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 表格数据
const performanceData = ref([
  {
    id: 1,
    name: '李催收',
    department: '催收一部',
    position: '高级催收员',
    performance: 95,
    grade: 'excellent',
    recoveryAmount: 890000,
    successRate: '87.2%',
    kpiCompletion: 112,
    trend: 8.5,
    avatar: ''
  },
  {
    id: 2,
    name: '王催收',
    department: '催收二部',
    position: '催收专员',
    performance: 92,
    grade: 'excellent',
    recoveryAmount: 756000,
    successRate: '89.5%',
    kpiCompletion: 108,
    trend: 5.2,
    avatar: ''
  },
  {
    id: 3,
    name: '张催收',
    department: '催收一部',
    position: '催收专员',
    performance: 78,
    grade: 'good',
    recoveryAmount: 698000,
    successRate: '75.8%',
    kpiCompletion: 95,
    trend: -2.1,
    avatar: ''
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: performanceData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const performanceDistributionChart = ref()
const performanceTrendChart = ref()
const kpiRadarChart = ref()
const departmentCompareChart = ref()
const improvementChart = ref()
const correlationChart = ref()
const predictionChart = ref()
const riskChart = ref()

// 状态相关方法
const getGradeColor = (grade) => {
  const colors = {
    excellent: 'green',
    good: 'blue',
    average: 'orange',
    poor: 'red'
  }
  return colors[grade] || 'default'
}

const getGradeText = (grade) => {
  const texts = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return texts[grade] || '未知'
}

const getKpiColor = (completion) => {
  if (completion >= 100) return '#52c41a'
  if (completion >= 80) return '#faad14'
  return '#ff4d4f'
}

const getRankColor = (index) => {
  if (index === 0) return '#ffd700'
  if (index === 1) return '#c0c0c0'
  if (index === 2) return '#cd7f32'
  return '#1890ff'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('绩效数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.period = 'monthly'
  searchForm.dateRange = [dayjs().startOf('month'), dayjs().endOf('month')]
  searchForm.department = 'all'
  searchForm.grade = 'all'
  handleSearch()
}

const exportReport = () => {
  message.success('绩效报表导出成功')
}

const generateRanking = () => {
  message.success('绩效排名生成成功')
}

const kpiSettings = () => {
  message.info('KPI设置功能')
}

const viewDetail = (record) => {
  message.info(`查看 ${record.name} 的绩效详情`)
}

const performanceAnalysis = (record) => {
  message.info(`分析 ${record.name} 的绩效表现`)
}

const setTarget = (record) => {
  message.info(`为 ${record.name} 设定目标`)
}

// AI相关方法
const showAISuggestions = () => {
  message.info('显示AI智能建议')
}

const showRiskDetail = () => {
  message.info('显示风险详情')
}

const generateAction = () => {
  message.info('生成行动计划')
}

const compareKPI = () => {
  message.info('KPI对比分析')
}

// 图表交互方法
const updateDistributionChart = () => {
  // 根据distributionType更新图表
  initCharts()
}

const updateTrendChart = () => {
  // 根据trendDepartments更新图表
  initCharts()
}

// 图表初始化
const initCharts = () => {
  // 绩效分布图
  const distributionChart = echarts.init(performanceDistributionChart.value)
  const distributionOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: 0
    },
    series: [
      {
        name: '绩效分布',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '优秀(90-100)' },
          { value: 42, name: '良好(80-89)' },
          { value: 18, name: '一般(70-79)' },
          { value: 5, name: '较差(60-69)' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  distributionChart.setOption(distributionOption)

  // 绩效趋势图
  const trendChart = echarts.init(performanceTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '平均绩效分'
    },
    series: [
      {
        name: '催收一部',
        type: 'line',
        data: [75, 78, 82, 85, 83, 87],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '催收二部',
        type: 'line',
        data: [72, 76, 79, 81, 84, 85],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '法务部',
        type: 'line',
        data: [78, 80, 83, 86, 88, 90],
        smooth: true,
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // KPI雷达图
  const radarChart = echarts.init(kpiRadarChart.value)
  const radarOption = {
    title: {
      text: '团队KPI指标',
      left: 'center'
    },
    tooltip: {},
    legend: {
      bottom: 0,
      data: ['目标值', '实际值']
    },
    radar: {
      indicator: [
        { name: '回收金额', max: 100 },
        { name: '成功率', max: 100 },
        { name: '客户满意度', max: 100 },
        { name: '工作效率', max: 100 },
        { name: '团队协作', max: 100 },
        { name: '专业技能', max: 100 }
      ]
    },
    series: [
      {
        name: 'KPI指标',
        type: 'radar',
        data: [
          {
            value: [85, 90, 88, 92, 85, 90],
            name: '目标值',
            itemStyle: { color: '#1890ff' }
          },
          {
            value: [92, 87, 85, 89, 88, 86],
            name: '实际值',
            itemStyle: { color: '#52c41a' }
          }
        ]
      }
    ]
  }
  radarChart.setOption(radarOption)

  // 部门对比图
  const compareChart = echarts.init(departmentCompareChart.value)
  const compareOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['催收一部', '催收二部', '法务部']
    },
    yAxis: {
      type: 'value',
      name: '平均分'
    },
    series: [
      {
        name: '综合绩效',
        type: 'bar',
        data: [87, 85, 90],
        itemStyle: { color: '#1890ff' }
      }
    ]
  }
  compareChart.setOption(compareOption)

  // 绩效改进趋势图
  setTimeout(() => {
    const improvementEl = improvementChart.value
    if (improvementEl) {
      const improvementChartInstance = echarts.init(improvementEl)
      const improvementOption = {
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value', name: '改进幅度(%)' },
        series: [
          {
            name: '绩效改进',
            type: 'line',
            data: [0, 5.2, 8.6, 12.1, 15.8, 18.5],
            smooth: true,
            areaStyle: { opacity: 0.3 },
            itemStyle: { color: '#52c41a' }
          }
        ]
      }
      improvementChartInstance.setOption(improvementOption)
    }
  }, 400)

  // 相关性分析图
  setTimeout(() => {
    const correlationEl = correlationChart.value
    if (correlationEl) {
      const correlationChartInstance = echarts.init(correlationEl)
      const correlationOption = {
        tooltip: { trigger: 'item' },
        xAxis: { type: 'value', name: '工作经验(年)' },
        yAxis: { type: 'value', name: '绩效评分' },
        series: [{
          name: '经验-绩效相关性',
          type: 'scatter',
          data: [
            [1, 65], [1.5, 68], [2, 72], [2.5, 75], [3, 78],
            [3.5, 82], [4, 85], [4.5, 88], [5, 90], [6, 92],
            [7, 95], [8, 93], [9, 89], [10, 87]
          ],
          symbolSize: 8,
          itemStyle: { color: '#1890ff' }
        }]
      }
      correlationChartInstance.setOption(correlationOption)
    }
  }, 450)

  // AI预测图表
  setTimeout(() => {
    const predictionEl = predictionChart.value
    if (predictionEl) {
      const predictionChartInstance = echarts.init(predictionEl)
      const predictionOption = {
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月(预测)', '8月(预测)', '9月(预测)']
        },
        yAxis: { type: 'value', name: '绩效分数' },
        series: [
          {
            name: '实际绩效',
            type: 'line',
            data: [75, 78, 82, 85, 83, 87, null, null, null],
            smooth: true,
            itemStyle: { color: '#1890ff' }
          },
          {
            name: 'AI预测',
            type: 'line',
            data: [null, null, null, null, null, 87, 89.5, 92, 94],
            smooth: true,
            lineStyle: { type: 'dashed' },
            itemStyle: { color: '#52c41a' }
          }
        ]
      }
      predictionChartInstance.setOption(predictionOption)
    }
  }, 500)

  // 风险预警图表
  setTimeout(() => {
    const riskEl = riskChart.value
    if (riskEl) {
      const riskChartInstance = echarts.init(riskEl)
      const riskOption = {
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        radar: {
          indicator: [
            { name: '绩效下滑风险', max: 100 },
            { name: '离职风险', max: 100 },
            { name: '客户投诉风险', max: 100 },
            { name: '合规风险', max: 100 },
            { name: '培训需求', max: 100 }
          ]
        },
        series: [{
          type: 'radar',
          data: [
            {
              value: [25, 15, 8, 12, 35],
              name: '当前风险水平',
              itemStyle: { color: '#ff4d4f' }
            },
            {
              value: [20, 20, 15, 15, 20],
              name: '行业平均',
              itemStyle: { color: '#faad14' }
            }
          ]
        }]
      }
      riskChartInstance.setOption(riskOption)
    }
  }, 550)

  // 响应式处理
  window.addEventListener('resize', () => {
    distributionChart.resize()
    trendChart.resize()
    radarChart.resize()
    compareChart.resize()
    
    // 新增图表的响应式处理
    const improvementEl = improvementChart.value
    const correlationEl = correlationChart.value
    const predictionEl = predictionChart.value
    const riskEl = riskChart.value
    
    if (improvementEl) echarts.getInstanceByDom(improvementEl)?.resize()
    if (correlationEl) echarts.getInstanceByDom(correlationEl)?.resize()
    if (predictionEl) echarts.getInstanceByDom(predictionEl)?.resize()
    if (riskEl) echarts.getInstanceByDom(riskEl)?.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.overview-cards {
  margin-bottom: 16px;
}

.chart-section {
  margin-bottom: 16px;
}

.ranking-section {
  margin-bottom: 16px;
}

.ranking-card {
  height: 450px;
}

.ranking-card .ant-list {
  max-height: 380px;
  overflow-y: auto;
}

.analysis-card {
  margin-top: 16px;
}

.analysis-card h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.performance-bar {
  margin-top: 8px;
}

.performance-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.performance-text {
  font-size: 12px;
  color: #666;
}

.stat-change {
  margin-top: 8px;
  font-size: 12px;
}

.change-text {
  color: #666;
}

.increase {
  color: #52c41a;
}

.decrease {
  color: #ff4d4f;
}

.name {
  font-weight: 500;
}

.department {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 新增样式 */
.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-target,
.stat-info,
.stat-success,
.stat-avg {
  color: #1890ff;
  font-weight: 500;
}

.ai-analysis-section {
  margin-bottom: 16px;
}

.ai-card {
  height: 120px;
  position: relative;
}

.ai-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.ai-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #1890ff;
}

.ai-title {
  font-weight: 500;
  color: #262626;
}

.ai-content {
  flex: 1;
}

.prediction-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.score-label {
  font-size: 12px;
  color: #666;
}

.score-value {
  font-size: 20px;
  font-weight: bold;
  color: #52c41a;
}

.prediction-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.trend-up {
  color: #52c41a;
  margin-right: 4px;
}

.trend-down {
  color: #ff4d4f;
  margin-right: 4px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.suggestion-icon {
  color: #faad14;
  margin-right: 8px;
  margin-top: 2px;
}

.suggestion-count {
  font-size: 12px;
  color: #666;
}

.risk-alert {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.risk-level {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.risk-level.low {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.risk-level.medium {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.risk-level.high {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.risk-count {
  font-size: 12px;
  color: #666;
}

.risk-action {
  font-size: 12px;
}

.chart-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-content {
  padding: 16px 0;
}

.suggestion-content h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.suggestion-detail p {
  margin: 0 0 8px 0;
}

.suggestion-impact {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
  margin: 0;
}
</style>
