<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>催收效果报表</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="催收方式">
                <a-select v-model:value="searchForm.collectionMethod" placeholder="请选择催收方式" allow-clear>
                  <a-select-option value="all">全部方式</a-select-option>
                  <a-select-option value="phone">电话催收</a-select-option>
                  <a-select-option value="sms">短信催收</a-select-option>
                  <a-select-option value="visit">外访催收</a-select-option>
                  <a-select-option value="legal">法务催收</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间维度">
                <a-select v-model:value="searchForm.timeDimension" placeholder="请选择时间维度" allow-clear>
                  <a-select-option value="daily">按日</a-select-option>
                  <a-select-option value="weekly">按周</a-select-option>
                  <a-select-option value="monthly">按月</a-select-option>
                  <a-select-option value="quarterly">按季度</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="催收员">
                <a-select v-model:value="searchForm.collector" placeholder="请选择催收员" allow-clear>
                  <a-select-option value="all">全部催收员</a-select-option>
                  <a-select-option value="user1">李催收</a-select-option>
                  <a-select-option value="user2">王催收</a-select-option>
                  <a-select-option value="user3">张催收</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportReport">
                      <download-outlined />
                      导出报表
                    </a-button>
                    <a-button @click="generateInsight">
                      <bulb-outlined />
                      智能分析
                    </a-button>
                    <a-button @click="compareReport">
                      <bar-chart-outlined />
                      对比分析
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 效果概览 -->
      <a-row :gutter="16" class="overview-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总催收次数" 
              :value="effectData.totalAttempts" 
              :value-style="{ color: '#1890ff' }"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="{ 'increase': effectData.attemptsChange > 0, 'decrease': effectData.attemptsChange < 0 }">{{ effectData.attemptsChange > 0 ? '+' : '' }}{{ effectData.attemptsChange }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="成功催收案件" 
              :value="effectData.successfulCases" 
              :value-style="{ color: '#52c41a' }"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="{ 'increase': effectData.successChange > 0, 'decrease': effectData.successChange < 0 }">{{ effectData.successChange > 0 ? '+' : '' }}{{ effectData.successChange }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="催收成功率" 
              :value="effectData.successRate" 
              :precision="2" 
              suffix="%"
              :value-style="{ color: '#faad14' }"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="{ 'increase': effectData.rateChange > 0, 'decrease': effectData.rateChange < 0 }">{{ effectData.rateChange > 0 ? '+' : '' }}{{ effectData.rateChange }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均联系时间" 
              :value="effectData.avgContactTime" 
              suffix="分钟"
              :value-style="{ color: '#722ed1' }"
            />
            <div class="stat-change">
              <span class="change-text">较上期 <span :class="{ 'increase': effectData.timeChange < 0, 'decrease': effectData.timeChange > 0 }">{{ effectData.timeChange > 0 ? '+' : '' }}{{ effectData.timeChange }}%</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表分析 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="催收方式效果对比" :body-style="{ padding: '20px' }">
            <div ref="methodEffectChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="催收成功率趋势" :body-style="{ padding: '20px' }">
            <div ref="successTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="16">
          <a-card title="时段催收效果分析" :body-style="{ padding: '20px' }">
            <div ref="timeSlotChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="催收结果分布" :body-style="{ padding: '20px' }">
            <div ref="resultDistributionChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细数据表格 -->
      <a-card title="催收效果明细">
        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'method'">
              <a-tag :color="getMethodColor(record.method)">
                {{ getMethodText(record.method) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'successRate'">
              <a-progress 
                :percent="record.successRate" 
                size="small" 
                :stroke-color="getProgressColor(record.successRate)"
              />
            </template>
            <template v-if="column.key === 'improvement'">
              <span :class="{ 'increase': record.improvement > 0, 'decrease': record.improvement < 0 }">
                {{ record.improvement > 0 ? '+' : '' }}{{ record.improvement }}%
              </span>
            </template>
            <template v-if="column.key === 'efficiency'">
              <a-rate :value="record.efficiency" disabled />
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 智能分析建议 -->
      <a-card title="智能分析建议" class="insight-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>效果优化建议</h4>
            <a-list size="small" :data-source="optimizationSuggestions">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: item.color }">{{ item.type }}</a-avatar>
                    </template>
                    <template #title>
                      {{ item.title }}
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
          <a-col :span="12">
            <h4>关键指标趋势</h4>
            <a-descriptions size="small" bordered>
              <a-descriptions-item label="最佳催收时段" span="2">
                {{ insights.bestTimeSlot }}
              </a-descriptions-item>
              <a-descriptions-item label="最有效方式" span="2">
                {{ insights.mostEffectiveMethod }}
              </a-descriptions-item>
              <a-descriptions-item label="平均联系次数" span="2">
                {{ insights.avgContactAttempts }}次
              </a-descriptions-item>
              <a-descriptions-item label="预期提升空间" span="2">
                <span class="increase">+{{ insights.improvementPotential }}%</span>
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined, BulbOutlined, BarChartOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  collectionMethod: 'all',
  timeDimension: 'monthly',
  dateRange: [dayjs().subtract(3, 'month'), dayjs()],
  collector: 'all'
})

// 日期预设
const datePresets = {
  '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '最近3个月': [dayjs().subtract(3, 'month'), dayjs()],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 效果数据
const effectData = reactive({
  totalAttempts: 15642,
  attemptsChange: 8.5,
  successfulCases: 4892,
  successChange: 12.3,
  successRate: 31.28,
  rateChange: 2.1,
  avgContactTime: 12.5,
  timeChange: -5.2
})

// 洞察数据
const insights = reactive({
  bestTimeSlot: '10:00-11:00, 14:00-16:00',
  mostEffectiveMethod: '电话+短信组合',
  avgContactAttempts: 3.2,
  improvementPotential: 15.8
})

// 优化建议
const optimizationSuggestions = ref([
  {
    type: '时',
    color: '#1890ff',
    title: '优化催收时段',
    description: '建议在10-11点和14-16点进行电话催收，成功率可提升20%'
  },
  {
    type: '策',
    color: '#52c41a',
    title: '调整催收策略',
    description: '对高风险客户建议采用电话+短信+外访的组合方式'
  },
  {
    type: '人',
    color: '#faad14',
    title: '人员培训优化',
    description: '部分催收员成功率偏低，建议加强沟通技巧培训'
  },
  {
    type: '技',
    color: '#722ed1',
    title: '技术辅助提升',
    description: '建议使用AI智能外呼系统，可提升30%的联系成功率'
  }
])

// 表格配置
const columns = [
  {
    title: '催收方式',
    key: 'method',
    width: 120
  },
  {
    title: '尝试次数',
    dataIndex: 'attempts',
    key: 'attempts',
    width: 100
  },
  {
    title: '成功次数',
    dataIndex: 'successes',
    key: 'successes',
    width: 100
  },
  {
    title: '成功率',
    key: 'successRate',
    width: 120
  },
  {
    title: '平均时长',
    dataIndex: 'avgDuration',
    key: 'avgDuration',
    width: 100
  },
  {
    title: '回收金额',
    dataIndex: 'recoveredAmount',
    key: 'recoveredAmount',
    width: 120
  },
  {
    title: '效率提升',
    key: 'improvement',
    width: 100
  },
  {
    title: '综合评级',
    key: 'efficiency',
    width: 120
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    method: 'phone',
    attempts: 8523,
    successes: 2890,
    successRate: 33.9,
    avgDuration: '12分钟',
    recoveredAmount: '¥2,340,000',
    improvement: 5.2,
    efficiency: 4
  },
  {
    id: 2,
    method: 'sms',
    attempts: 12456,
    successes: 1456,
    successRate: 11.7,
    avgDuration: '即时',
    recoveredAmount: '¥890,000',
    improvement: 8.1,
    efficiency: 3
  },
  {
    id: 3,
    method: 'visit',
    attempts: 234,
    successes: 189,
    successRate: 80.8,
    avgDuration: '45分钟',
    recoveredAmount: '¥1,200,000',
    improvement: -2.3,
    efficiency: 5
  },
  {
    id: 4,
    method: 'legal',
    attempts: 89,
    successes: 67,
    successRate: 75.3,
    avgDuration: '30天',
    recoveredAmount: '¥3,500,000',
    improvement: 12.5,
    efficiency: 4
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const methodEffectChart = ref()
const successTrendChart = ref()
const timeSlotChart = ref()
const resultDistributionChart = ref()

// 状态相关方法
const getMethodColor = (method) => {
  const colors = {
    phone: 'blue',
    sms: 'green',
    visit: 'orange',
    legal: 'red'
  }
  return colors[method] || 'default'
}

const getMethodText = (method) => {
  const texts = {
    phone: '电话催收',
    sms: '短信催收',
    visit: '外访催收',
    legal: '法务催收'
  }
  return texts[method] || '未知'
}

const getProgressColor = (rate) => {
  if (rate >= 70) return '#52c41a'
  if (rate >= 50) return '#faad14'
  if (rate >= 30) return '#1890ff'
  return '#ff4d4f'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('报表数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.collectionMethod = 'all'
  searchForm.timeDimension = 'monthly'
  searchForm.dateRange = [dayjs().subtract(3, 'month'), dayjs()]
  searchForm.collector = 'all'
  handleSearch()
}

const exportReport = () => {
  message.success('催收效果报表导出成功')
}

const generateInsight = () => {
  message.success('智能分析报告生成成功')
}

const compareReport = () => {
  message.info('对比分析功能')
}

// 图表初始化
const initCharts = () => {
  // 催收方式效果对比图
  const methodChart = echarts.init(methodEffectChart.value)
  const methodOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['电话催收', '短信催收', '外访催收', '法务催收']
    },
    yAxis: [
      {
        type: 'value',
        name: '成功率(%)',
        position: 'left'
      },
      {
        type: 'value',
        name: '回收金额(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '成功率',
        type: 'bar',
        data: [33.9, 11.7, 80.8, 75.3],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '回收金额',
        type: 'line',
        yAxisIndex: 1,
        data: [234, 89, 120, 350],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  methodChart.setOption(methodOption)

  // 催收成功率趋势图
  const trendChart = echarts.init(successTrendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '成功率(%)'
    },
    series: [
      {
        name: '电话催收',
        type: 'line',
        data: [32.1, 33.5, 31.8, 34.2, 33.9, 35.1],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '短信催收',
        type: 'line',
        data: [10.2, 11.8, 10.5, 12.3, 11.7, 12.9],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '外访催收',
        type: 'line',
        data: [78.5, 79.2, 81.1, 80.8, 82.3, 81.5],
        smooth: true,
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 时段催收效果分析
  const timeChart = echarts.init(timeSlotChart.value)
  const timeOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['9-10点', '10-11点', '11-12点', '14-15点', '15-16点', '16-17点', '19-20点', '20-21点']
    },
    yAxis: {
      type: 'value',
      name: '成功率(%)'
    },
    series: [
      {
        name: '联系成功率',
        type: 'bar',
        data: [25.3, 42.8, 28.9, 38.7, 41.2, 32.1, 29.5, 22.8],
        itemStyle: { 
          color: function(params) {
            const colors = ['#ff7875', '#87d068', '#ff7875', '#87d068', '#87d068', '#ff7875', '#ff7875', '#ff7875']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  timeChart.setOption(timeOption)

  // 催收结果分布图
  const resultChart = echarts.init(resultDistributionChart.value)
  const resultOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center'
    },
    series: [
      {
        name: '催收结果',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 31.2, name: '承诺还款' },
          { value: 25.8, name: '部分还款' },
          { value: 18.5, name: '拒绝还款' },
          { value: 15.3, name: '无法联系' },
          { value: 9.2, name: '其他结果' }
        ]
      }
    ]
  }
  resultChart.setOption(resultOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    methodChart.resize()
    trendChart.resize()
    timeChart.resize()
    resultChart.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.overview-cards {
  margin-bottom: 16px;
}

.chart-section {
  margin-bottom: 16px;
}

.insight-card {
  margin-top: 16px;
}

.insight-card h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.stat-change {
  margin-top: 8px;
  font-size: 12px;
}

.change-text {
  color: #666;
}

.increase {
  color: #52c41a;
}

.decrease {
  color: #ff4d4f;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
