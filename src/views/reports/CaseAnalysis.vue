<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>案件分析报表</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="案件状态">
                <a-select v-model:value="searchForm.caseStatus" placeholder="请选择案件状态" allow-clear>
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="active">处理中</a-select-option>
                  <a-select-option value="closed">已结案</a-select-option>
                  <a-select-option value="suspended">暂停</a-select-option>
                  <a-select-option value="transferred">已转移</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="案件类型">
                <a-select v-model:value="searchForm.caseType" placeholder="请选择案件类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="personal">个人案件</a-select-option>
                  <a-select-option value="corporate">企业案件</a-select-option>
                  <a-select-option value="secured">抵押案件</a-select-option>
                  <a-select-option value="unsecured">信用案件</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="逾期阶段">
                <a-select v-model:value="searchForm.overdueStage" placeholder="请选择逾期阶段" allow-clear>
                  <a-select-option value="all">全部阶段</a-select-option>
                  <a-select-option value="m1">M1(1-30天)</a-select-option>
                  <a-select-option value="m2">M2(31-60天)</a-select-option>
                  <a-select-option value="m3">M3(61-90天)</a-select-option>
                  <a-select-option value="m4plus">M4+(90天以上)</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportReport">
                      <DownloadOutlined />
                      导出报表
                    </a-button>
                    <a-button @click="generateAIInsight">
                      <RobotOutlined />
                      AI分析
                    </a-button>
                    <a-button @click="showPredictiveAnalysis">
                      <LineChartOutlined />
                      预测分析
                    </a-button>
                    <a-button @click="compareCases">
                      <BarChartOutlined />
                      对比分析
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 增强概览统计 -->
      <a-row :gutter="16" class="overview-cards enhanced-overview">
        <a-col :span="6">
          <a-card class="overview-card gradient-blue" hoverable>
            <div class="card-content">
              <div class="stat-icon">
                <FileTextOutlined />
              </div>
              <div class="stat-info">
                <a-statistic 
                  title="案件总数" 
                  :value="overviewData.totalCases"
                  :value-style="{ color: '#1890ff', fontSize: '28px', fontWeight: 'bold' }"
                >
                  <template #suffix>
                    <span class="stat-unit">件</span>
                  </template>
                </a-statistic>
                <div class="stat-trend">
                  <span class="trend-indicator up">
                    <CaretUpOutlined />
                    +12.5%
                  </span>
                  <span class="trend-period">较上月</span>
                </div>
              </div>
            </div>
            <div class="card-progress">
              <a-progress 
                :percent="75" 
                :show-info="false" 
                stroke-color="#1890ff"
                trail-color="#e6f7ff"
              />
              <span class="progress-text">目标完成率 75%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card gradient-orange" hoverable>
            <div class="card-content">
              <div class="stat-icon">
                <FileTextOutlined />
              </div>
              <div class="stat-info">
                <a-statistic
                  title="新增案件"
                  :value="overviewData.newCases"
                  :value-style="{ color: '#1890ff' }"
                >
                  <template #prefix>
                    <FileTextOutlined />
                  </template>
                </a-statistic>
                <div class="stat-footer">
                  <span>较上期 <span class="stat-change" :class="{ 'up': overviewData.casesChange > 0, 'down': overviewData.casesChange < 0 }">
                    {{ overviewData.casesChange > 0 ? '+' : '' }}{{ overviewData.casesChange }}%
                  </span></span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card gradient-green" hoverable>
            <div class="card-content">
              <div class="stat-icon">
                <DollarCircleOutlined />
              </div>
              <div class="stat-info">
                <a-statistic 
                  title="平均案件金额" 
                  :value="overviewData.avgAmount" 
                  :precision="0"
                  prefix="¥"
                  :value-style="{ color: '#52c41a', fontSize: '28px', fontWeight: 'bold' }"
                />
                <div class="stat-trend">
                  <span class="trend-indicator up">
                    <CaretUpOutlined />
                    +8.3%
                  </span>
                  <span class="trend-period">较上月</span>
                </div>
              </div>
            </div>
            <div class="card-progress">
              <a-progress 
                :percent="68" 
                :show-info="false" 
                stroke-color="#52c41a"
                trail-color="#f6ffed"
              />
              <span class="progress-text">中位数: ¥{{ overviewData.medianAmount.toLocaleString() }}</span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="6">
          <a-card class="overview-card gradient-orange" hoverable>
            <div class="card-content">
              <div class="stat-icon">
                <ClockCircleOutlined />
              </div>
              <div class="stat-info">
                <a-statistic 
                  title="平均处理周期" 
                  :value="overviewData.avgCycle" 
                  :precision="1"
                  suffix="天"
                  :value-style="{ color: '#faad14', fontSize: '28px', fontWeight: 'bold' }"
                />
                <div class="stat-trend">
                  <span class="trend-indicator down">
                    <CaretDownOutlined />
                    -5.2%
                  </span>
                  <span class="trend-period">较上月</span>
                </div>
              </div>
            </div>
            <div class="card-progress">
              <a-progress 
                :percent="85" 
                :show-info="false" 
                stroke-color="#faad14"
                trail-color="#fff7e6"
              />
              <span class="progress-text">目标周期: ≤30天</span>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="6">
          <a-card class="overview-card gradient-purple" hoverable>
            <div class="card-content">
              <div class="stat-icon">
                <CheckCircleOutlined />
              </div>
              <div class="stat-info">
                <a-statistic 
                  title="结案率" 
                  :value="overviewData.closureRate" 
                  :precision="2"
                  suffix="%"
                  :value-style="{ color: '#722ed1', fontSize: '28px', fontWeight: 'bold' }"
                />
                <div class="stat-trend">
                  <span class="trend-indicator up">
                    <CaretUpOutlined />
                    +3.1%
                  </span>
                  <span class="trend-period">较上月</span>
                </div>
              </div>
            </div>
            <div class="card-progress">
              <a-progress 
                :percent="Math.round(overviewData.closureRate)" 
                :show-info="false" 
                stroke-color="#722ed1"
                trail-color="#f9f0ff"
              />
              <span class="progress-text">目标结案率: ≥85%</span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- AI智能分析卡片 -->
      <a-row :gutter="16" class="ai-analysis-section">
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <RobotOutlined class="ai-icon" />
              <span class="ai-title">案件质量评分</span>
            </div>
            <div class="ai-content">
              <div class="quality-score">
                <div class="score-circle">
                  <a-progress 
                    type="circle" 
                    :percent="aiAnalysis.qualityScore" 
                    :stroke-color="getScoreColor(aiAnalysis.qualityScore)"
                    :width="80"
                  />
                </div>
                <div class="score-desc">
                  <div class="score-label">综合质量</div>
                  <div class="score-trend">{{ aiAnalysis.qualityTrend }}</div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <ThunderboltOutlined class="ai-icon" />
              <span class="ai-title">处理效率分析</span>
            </div>
            <div class="ai-content">
              <div class="efficiency-metrics">
                <div class="metric-item">
                  <span class="metric-label">处理速度</span>
                  <span class="metric-value fast">{{ aiAnalysis.processingSpeed }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">资源利用率</span>
                  <span class="metric-value">{{ aiAnalysis.resourceUtilization }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">瓶颈识别</span>
                  <span class="metric-value warning">{{ aiAnalysis.bottleneck }}</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <AlertOutlined class="ai-icon" />
              <span class="ai-title">风险预警</span>
            </div>
            <div class="ai-content">
              <div class="risk-alerts">
                <div class="alert-item" :class="aiAnalysis.riskLevel">
                  <div class="alert-level">{{ aiAnalysis.riskLevelText }}</div>
                  <div class="alert-count">{{ aiAnalysis.highRiskCases }}个高风险案件</div>
                  <div class="alert-action">
                    <a-button type="link" size="small" @click="showRiskDetails">查看详情</a-button>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表分析区域 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="案件状态分布" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-radio-group v-model:value="statusChartType" size="small">
                <a-radio-button value="pie">饼图</a-radio-button>
                <a-radio-button value="bar">柱状图</a-radio-button>
              </a-radio-group>
            </div>
            <div ref="caseStatusChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="案件金额分布" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-select v-model:value="amountRangeType" size="small" style="width: 120px;">
                <a-select-option value="standard">标准分布</a-select-option>
                <a-select-option value="logarithmic">对数分布</a-select-option>
              </a-select>
            </div>
            <div ref="caseAmountChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="16">
          <a-card title="案件趋势分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <a-checkbox-group v-model:value="trendMetrics" size="small">
                  <a-checkbox value="new">新增案件</a-checkbox>
                  <a-checkbox value="closed">结案案件</a-checkbox>
                  <a-checkbox value="transferred">转移案件</a-checkbox>
                </a-checkbox-group>
                <a-divider type="vertical" />
                <a-select v-model:value="trendPeriod" size="small" style="width: 100px;">
                  <a-select-option value="daily">日</a-select-option>
                  <a-select-option value="weekly">周</a-select-option>
                  <a-select-option value="monthly">月</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="caseTrendChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="逾期阶段分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-radio-group v-model:value="overdueViewType" size="small">
                <a-radio-button value="count">数量</a-radio-button>
                <a-radio-button value="amount">金额</a-radio-button>
              </a-radio-group>
            </div>
            <div ref="overdueStageChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 高级分析图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="案件复杂度分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>分析维度:</span>
                <a-select v-model:value="complexityDimension" size="small" style="width: 140px;">
                  <a-select-option value="duration">处理时长</a-select-option>
                  <a-select-option value="interactions">交互次数</a-select-option>
                  <a-select-option value="transfers">转移次数</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="complexityChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="案件成功率分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>分组方式:</span>
                <a-select v-model:value="successGroupBy" size="small" style="width: 120px;">
                  <a-select-option value="amount">金额区间</a-select-option>
                  <a-select-option value="age">账龄区间</a-select-option>
                  <a-select-option value="type">案件类型</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="successRateChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- AI预测分析 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="AI预测分析" :body-style="{ padding: '20px' }">
            <a-tabs v-model:activeKey="predictionTab">
              <a-tab-pane key="trend" tab="趋势预测">
                <div ref="trendPredictionChart" style="height: 400px"></div>
              </a-tab-pane>
              <a-tab-pane key="risk" tab="风险预测">
                <div ref="riskPredictionChart" style="height: 400px"></div>
              </a-tab-pane>
              <a-tab-pane key="resource" tab="资源预测">
                <div ref="resourcePredictionChart" style="height: 400px"></div>
              </a-tab-pane>
              <a-tab-pane key="insight" tab="洞察建议">
                <div class="insight-content">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <h4>关键发现</h4>
                      <a-list :data-source="keyFindings" size="small">
                        <template #renderItem="{ item }">
                          <a-list-item>
                            <a-list-item-meta>
                              <template #avatar>
                                <a-avatar :style="{ backgroundColor: item.color }" size="small">
                                  <component :is="item.icon" />
                                </a-avatar>
                              </template>
                              <template #title>{{ item.title }}</template>
                              <template #description>{{ item.description }}</template>
                            </a-list-item-meta>
                          </a-list-item>
                        </template>
                      </a-list>
                    </a-col>
                    <a-col :span="12">
                      <h4>优化建议</h4>
                      <a-timeline>
                        <a-timeline-item v-for="suggestion in optimizationSuggestions" :key="suggestion.id" :color="suggestion.color">
                          <p><strong>{{ suggestion.title }}</strong></p>
                          <p>{{ suggestion.description }}</p>
                          <p class="suggestion-impact">预期效果: {{ suggestion.impact }}</p>
                        </a-timeline-item>
                      </a-timeline>
                    </a-col>
                  </a-row>
                </div>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细数据表格 -->
      <a-card title="案件分析明细">
        <div class="table-toolbar">
          <a-space>
            <a-button @click="refreshData" :loading="loading">
              <ReloadOutlined />
              刷新数据
            </a-button>
            <a-button @click="exportTableData">
              <DownloadOutlined />
              导出明细
            </a-button>
            <a-button @click="showColumnsConfig">
              <SettingOutlined />
              列设置
            </a-button>
          </a-space>
        </div>
        <a-table 
          :columns="tableColumns" 
          :data-source="caseAnalysisData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1600 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'caseStatus'">
              <a-tag :color="getCaseStatusColor(record.caseStatus)">
                {{ getCaseStatusText(record.caseStatus) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'complexity'">
              <a-rate :value="record.complexity" disabled />
            </template>
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'efficiency'">
              <a-progress 
                :percent="record.efficiency" 
                size="small" 
                :stroke-color="getEfficiencyColor(record.efficiency)"
              />
            </template>
            <template v-if="column.key === 'amount'">
              ¥{{ record.amount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'prediction'">
              <span :class="{ 'success-prediction': record.prediction > 70, 'warning-prediction': record.prediction < 40 }">
                {{ record.prediction }}%
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewCaseDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="analyzeCaseTrend(record)">趋势</a-button>
                <a-button type="link" size="small" @click="predictCaseOutcome(record)">预测</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  RobotOutlined,
  LineChartOutlined,
  BarChartOutlined,
  FileTextOutlined,
  DollarCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined,
  AlertOutlined,
  SettingOutlined,
  BulbOutlined,
  RiseOutlined,
  WarningOutlined,
  SafetyOutlined,
  CaretUpOutlined,
  CaretDownOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  caseStatus: 'all',
  caseType: 'all',
  dateRange: [dayjs().subtract(3, 'month'), dayjs()],
  overdueStage: 'all'
})

// 日期预设
const datePresets = {
  '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '最近3个月': [dayjs().subtract(3, 'month'), dayjs()],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 概览数据
const overviewData = reactive({
  totalCases: 15634,
  casesChange: 8.5,
  avgAmount: 42650,
  medianAmount: 28500,
  avgCycle: 28.5,
  closureRate: 76.8
})

// AI分析数据
const aiAnalysis = reactive({
  qualityScore: 78,
  qualityTrend: '较上月提升5.2%',
  processingSpeed: '优秀',
  resourceUtilization: 85,
  bottleneck: '审批环节',
  riskLevel: 'medium',
  riskLevelText: '中等风险',
  highRiskCases: 23
})

// 关键发现
const keyFindings = ref([
  {
    title: '案件处理效率提升',
    description: '相比上月，平均处理时间缩短了12%',
    icon: 'RiseOutlined',
    color: '#52c41a'
  },
  {
    title: '高金额案件集中',
    description: '50万以上案件占比上升至15%，需重点关注',
    icon: 'WarningOutlined',
    color: '#faad14'
  },
  {
    title: '新兴风险模式',
    description: '发现3种新的欺诈模式，建议更新风控规则',
    icon: 'SafetyOutlined',
    color: '#ff4d4f'
  },
  {
    title: '季节性波动',
    description: '案件量呈现明显季节性特征，建议调整资源配置',
    icon: 'BulbOutlined',
    color: '#1890ff'
  }
])

// 优化建议
const optimizationSuggestions = ref([
  {
    id: 1,
    title: '优化案件分配算法',
    description: '基于AI分析结果，调整案件自动分配规则',
    impact: '预计提升处理效率20%',
    color: 'green'
  },
  {
    id: 2,
    title: '加强高风险案件监控',
    description: '建立实时监控机制，及时识别和处理风险案件',
    impact: '预计降低损失率15%',
    color: 'blue'
  },
  {
    id: 3,
    title: '完善培训体系',
    description: '针对复杂案件处理，加强人员专业技能培训',
    impact: '预计提升成功率10%',
    color: 'purple'
  },
  {
    id: 4,
    title: '引入预测模型',
    description: '使用机器学习预测案件处理结果，提前制定策略',
    impact: '预计提升决策准确率25%',
    color: 'orange'
  }
])

// 图表控制变量
const statusChartType = ref('pie')
const amountRangeType = ref('standard')
const trendMetrics = ref(['new', 'closed'])
const trendPeriod = ref('monthly')
const overdueViewType = ref('count')
const complexityDimension = ref('duration')
const successGroupBy = ref('amount')
const predictionTab = ref('trend')

// 表格列配置
const tableColumns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    fixed: 'left'
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '案件状态',
    key: 'caseStatus',
    width: 100
  },
  {
    title: '案件金额',
    key: 'amount',
    width: 120
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDays',
    key: 'overdueDays',
    width: 100
  },
  {
    title: '复杂度',
    key: 'complexity',
    width: 120
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '处理效率',
    key: 'efficiency',
    width: 120
  },
  {
    title: '成功预测',
    key: 'prediction',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 表格数据
const caseAnalysisData = ref([
  {
    id: 1,
    caseNumber: 'CS2024001',
    customerName: '张三',
    caseStatus: 'active',
    amount: 125000,
    overdueDays: 45,
    complexity: 3,
    riskLevel: 'medium',
    efficiency: 78,
    prediction: 75,
    createTime: '2024-01-15'
  },
  {
    id: 2,
    caseNumber: 'CS2024002',
    customerName: '李四',
    caseStatus: 'closed',
    amount: 85000,
    overdueDays: 30,
    complexity: 2,
    riskLevel: 'low',
    efficiency: 92,
    prediction: 88,
    createTime: '2024-01-18'
  },
  {
    id: 3,
    caseNumber: 'CS2024003',
    customerName: '王五',
    caseStatus: 'transferred',
    amount: 320000,
    overdueDays: 90,
    complexity: 5,
    riskLevel: 'high',
    efficiency: 45,
    prediction: 35,
    createTime: '2024-01-20'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: caseAnalysisData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const caseStatusChart = ref()
const caseAmountChart = ref()
const caseTrendChart = ref()
const overdueStageChart = ref()
const complexityChart = ref()
const successRateChart = ref()
const trendPredictionChart = ref()
const riskPredictionChart = ref()
const resourcePredictionChart = ref()

// 状态相关方法
const getCaseStatusColor = (status) => {
  const colors = {
    active: 'blue',
    closed: 'green',
    suspended: 'orange',
    transferred: 'purple'
  }
  return colors[status] || 'default'
}

const getCaseStatusText = (status) => {
  const texts = {
    active: '处理中',
    closed: '已结案',
    suspended: '暂停',
    transferred: '已转移'
  }
  return texts[status] || '未知'
}

const getRiskLevelColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[level] || '未知'
}

const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 80) return '#52c41a'
  if (efficiency >= 60) return '#faad14'
  return '#ff4d4f'
}

const getScoreColor = (score) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  return '#ff4d4f'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('案件分析数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.caseStatus = 'all'
  searchForm.caseType = 'all'
  searchForm.dateRange = [dayjs().subtract(3, 'month'), dayjs()]
  searchForm.overdueStage = 'all'
  handleSearch()
}

const exportReport = () => {
  message.success('案件分析报表导出成功')
}

const generateAIInsight = () => {
  message.success('AI智能分析报告生成成功')
}

const showPredictiveAnalysis = () => {
  predictionTab.value = 'trend'
  message.info('显示预测分析')
}

const compareCases = () => {
  message.info('案件对比分析功能')
}

const showRiskDetails = () => {
  message.info('显示风险详情')
}

const refreshData = () => {
  handleSearch()
}

const exportTableData = () => {
  message.success('明细数据导出成功')
}

const showColumnsConfig = () => {
  message.info('列配置功能')
}

const viewCaseDetail = (record) => {
  message.info(`查看案件 ${record.caseNumber} 详情`)
}

const analyzeCaseTrend = (record) => {
  message.info(`分析案件 ${record.caseNumber} 趋势`)
}

const predictCaseOutcome = (record) => {
  message.info(`预测案件 ${record.caseNumber} 结果`)
}

// 图表初始化
const initCharts = () => {
  // 案件状态分布图
  setTimeout(() => {
    const statusEl = caseStatusChart.value
    if (statusEl) {
      const statusChart = echarts.init(statusEl)
      updateStatusChart(statusChart)
    }
  }, 100)

  // 案件金额分布图
  setTimeout(() => {
    const amountEl = caseAmountChart.value
    if (amountEl) {
      const amountChart = echarts.init(amountEl)
      updateAmountChart(amountChart)
    }
  }, 150)

  // 案件趋势图
  setTimeout(() => {
    const trendEl = caseTrendChart.value
    if (trendEl) {
      const trendChart = echarts.init(trendEl)
      updateTrendChart(trendChart)
    }
  }, 200)

  // 逾期阶段图
  setTimeout(() => {
    const overdueEl = overdueStageChart.value
    if (overdueEl) {
      const overdueChart = echarts.init(overdueEl)
      updateOverdueChart(overdueChart)
    }
  }, 250)

  // 复杂度分析图
  setTimeout(() => {
    const complexityEl = complexityChart.value
    if (complexityEl) {
      const complexityChartInstance = echarts.init(complexityEl)
      updateComplexityChart(complexityChartInstance)
    }
  }, 300)

  // 成功率分析图
  setTimeout(() => {
    const successEl = successRateChart.value
    if (successEl) {
      const successChart = echarts.init(successEl)
      updateSuccessRateChart(successChart)
    }
  }, 350)

  // 预测分析图表
  setTimeout(() => {
    initPredictionCharts()
  }, 400)
}

// 更新状态图表
const updateStatusChart = (chart) => {
  const option = statusChartType.value === 'pie' ? {
    tooltip: { trigger: 'item' },
    legend: { bottom: '5%' },
    series: [{
      name: '案件状态',
      type: 'pie',
      radius: ['40%', '70%'],
      data: [
        { value: 8520, name: '处理中' },
        { value: 4680, name: '已结案' },
        { value: 1890, name: '暂停' },
        { value: 544, name: '已转移' }
      ]
    }]
  } : {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['处理中', '已结案', '暂停', '已转移']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '案件数量',
      type: 'bar',
      data: [8520, 4680, 1890, 544],
      itemStyle: { color: '#1890ff' }
    }]
  }
  chart.setOption(option)
}

// 更新金额分布图
const updateAmountChart = (chart) => {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['<1万', '1-5万', '5-10万', '10-30万', '30-50万', '>50万']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '案件数量',
      type: 'bar',
      data: [2145, 5680, 3920, 2890, 980, 385],
      itemStyle: {
        color: (params) => {
          const colors = ['#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452']
          return colors[params.dataIndex]
        }
      }
    }]
  }
  chart.setOption(option)
}

// 更新趋势图
const updateTrendChart = (chart) => {
  const option = {
    tooltip: { trigger: 'axis' },
    legend: { bottom: '5%' },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: { type: 'value' },
    series: [
      ...(trendMetrics.value.includes('new') ? [{
        name: '新增案件',
        type: 'line',
        data: [1200, 1350, 1180, 1420, 1380, 1500],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      }] : []),
      ...(trendMetrics.value.includes('closed') ? [{
        name: '结案案件',
        type: 'line',
        data: [980, 1120, 1050, 1180, 1200, 1350],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      }] : []),
      ...(trendMetrics.value.includes('transferred') ? [{
        name: '转移案件',
        type: 'line',
        data: [85, 95, 78, 102, 88, 115],
        smooth: true,
        itemStyle: { color: '#faad14' }
      }] : [])
    ]
  }
  chart.setOption(option)
}

// 更新逾期阶段图
const updateOverdueChart = (chart) => {
  const data = overdueViewType.value === 'count' 
    ? [3200, 2800, 2100, 1850]
    : [1250, 2800, 4200, 8900]
  
  const option = {
    tooltip: { trigger: 'item' },
    series: [{
      name: overdueViewType.value === 'count' ? '案件数量' : '案件金额(万元)',
      type: 'pie',
      radius: '65%',
      data: [
        { value: data[0], name: 'M1(1-30天)' },
        { value: data[1], name: 'M2(31-60天)' },
        { value: data[2], name: 'M3(61-90天)' },
        { value: data[3], name: 'M4+(90天以上)' }
      ]
    }]
  }
  chart.setOption(option)
}

// 更新复杂度图
const updateComplexityChart = (chart) => {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['简单', '一般', '复杂', '非常复杂', '极度复杂']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '案件数量',
      type: 'bar',
      data: [4200, 6800, 3200, 1100, 300],
      itemStyle: {
        color: (params) => {
          const colors = ['#52c41a', '#1890ff', '#faad14', '#ff7a45', '#ff4d4f']
          return colors[params.dataIndex]
        }
      }
    }]
  }
  chart.setOption(option)
}

// 更新成功率图
const updateSuccessRateChart = (chart) => {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['<1万', '1-5万', '5-10万', '10-30万', '30-50万', '>50万']
    },
    yAxis: { type: 'value', max: 100 },
    series: [{
      name: '成功率(%)',
      type: 'line',
      data: [85, 78, 72, 65, 58, 45],
      markLine: {
        data: [{ type: 'average', name: '平均成功率' }]
      },
      areaStyle: { opacity: 0.3 }
    }]
  }
  chart.setOption(option)
}

// 初始化预测图表
const initPredictionCharts = () => {
  // 趋势预测
  const trendPredictionEl = trendPredictionChart.value
  if (trendPredictionEl) {
    const chart = echarts.init(trendPredictionEl)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { bottom: '5%' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月(预测)', '8月(预测)', '9月(预测)']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '实际案件量',
          type: 'line',
          data: [1200, 1350, 1180, 1420, 1380, 1500, null, null, null],
          itemStyle: { color: '#1890ff' }
        },
        {
          name: 'AI预测',
          type: 'line',
          data: [null, null, null, null, null, 1500, 1580, 1620, 1700],
          lineStyle: { type: 'dashed' },
          itemStyle: { color: '#52c41a' }
        }
      ]
    })
  }

  // 风险预测
  const riskPredictionEl = riskPredictionChart.value
  if (riskPredictionEl) {
    const chart = echarts.init(riskPredictionEl)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      radar: {
        indicator: [
          { name: '逾期风险', max: 100 },
          { name: '欺诈风险', max: 100 },
          { name: '流动性风险', max: 100 },
          { name: '操作风险', max: 100 },
          { name: '合规风险', max: 100 }
        ]
      },
      series: [{
        type: 'radar',
        data: [
          {
            value: [65, 25, 45, 35, 20],
            name: '当前风险水平'
          },
          {
            value: [70, 30, 50, 40, 25],
            name: '预测风险水平'
          }
        ]
      }]
    })
  }

  // 资源预测
  const resourcePredictionEl = resourcePredictionChart.value
  if (resourcePredictionEl) {
    const chart = echarts.init(resourcePredictionEl)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { bottom: '5%' },
      xAxis: {
        type: 'category',
        data: ['下月', '下季度', '下半年']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '需求人员',
          type: 'bar',
          data: [28, 32, 35]
        },
        {
          name: '当前人员',
          type: 'bar',
          data: [25, 25, 25]
        }
      ]
    })
  }
}

// 监听图表控制变量变化
watch(statusChartType, () => {
  const chart = echarts.getInstanceByDom(caseStatusChart.value)
  if (chart) updateStatusChart(chart)
})

watch(overdueViewType, () => {
  const chart = echarts.getInstanceByDom(overdueStageChart.value)
  if (chart) updateOverdueChart(chart)
})

watch(trendMetrics, () => {
  const chart = echarts.getInstanceByDom(caseTrendChart.value)
  if (chart) updateTrendChart(chart)
}, { deep: true })

// 响应式处理
window.addEventListener('resize', () => {
  const charts = [
    caseStatusChart, caseAmountChart, caseTrendChart, overdueStageChart,
    complexityChart, successRateChart, trendPredictionChart, riskPredictionChart, resourcePredictionChart
  ]
  charts.forEach(chartRef => {
    if (chartRef.value) {
      const chart = echarts.getInstanceByDom(chartRef.value)
      chart?.resize()
    }
  })
})

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.overview-cards {
  margin-bottom: 16px;
  
  &.enhanced-overview {
    .overview-card {
      border: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
      
      &.gradient-blue {
        background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
        
        .stat-icon {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }
      }
      
      &.gradient-green {
        background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
        
        .stat-icon {
          color: #52c41a;
          background: rgba(82, 196, 26, 0.1);
        }
      }
      
      &.gradient-orange {
        background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
        
        .stat-icon {
          color: #faad14;
          background: rgba(250, 173, 20, 0.1);
        }
      }
      
      &.gradient-purple {
        background: linear-gradient(135deg, #f9f0ff 0%, #d3adf7 100%);
        
        .stat-icon {
          color: #722ed1;
          background: rgba(114, 46, 209, 0.1);
        }
      }
    }
    
    .card-content {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 16px;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        flex-shrink: 0;
      }
      
      .stat-info {
        flex: 1;
        
        :deep(.ant-statistic-title) {
          color: #666;
          font-size: 14px;
          margin-bottom: 8px;
        }
      }
    }
    
    .stat-trend {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      font-size: 12px;
      
      .trend-indicator {
        display: flex;
        align-items: center;
        gap: 2px;
        font-weight: 500;
        
        &.up {
          color: #52c41a;
        }
        
        &.down {
          color: #ff4d4f;
        }
      }
      
      .trend-period {
        color: #999;
      }
    }
    
    .card-progress {
      .progress-text {
        font-size: 12px;
        color: #666;
        margin-top: 8px;
        display: block;
      }
    }
    
    .stat-unit {
      font-size: 16px;
      font-weight: normal;
      color: #999;
    }
  }
}

/* 移动端响应式优化 */
@media (max-width: 992px) {
  .overview-cards.enhanced-overview {
    .card-content {
      .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }
    }
    
    :deep(.ant-statistic-content-value) {
      font-size: 24px !important;
    }
  }
}

@media (max-width: 768px) {
  .search-card {
    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }
    
    .search-actions {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
      
      .action-buttons {
        width: 100%;
        
        :deep(.ant-space) {
          width: 100%;
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }
  }
  
  .overview-cards.enhanced-overview {
    .card-content {
      flex-direction: column;
      text-align: center;
      gap: 12px;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
        align-self: center;
      }
      
      .stat-info {
        :deep(.ant-statistic-title) {
          text-align: center;
        }
      }
    }
    
    .stat-trend {
      justify-content: center;
    }
    
    .card-progress {
      text-align: center;
    }
  }
}

@media (max-width: 576px) {
  .content-wrapper {
    padding: 16px;
  }
  
  .overview-cards.enhanced-overview {
    .card-content {
      .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }
    }
    
    :deep(.ant-statistic-content-value) {
      font-size: 20px !important;
    }
    
    .stat-trend {
      font-size: 11px;
    }
    
    .card-progress {
      .progress-text {
        font-size: 11px;
      }
    }
  }
}

.ai-analysis-section {
  margin-bottom: 16px;
}

.chart-section {
  margin-bottom: 16px;
}

.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-change {
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-change.down {
  color: #ff4d4f;
}

.stat-median,
.stat-target {
  color: #1890ff;
  font-weight: 500;
}

.ai-card {
  height: 140px;
}

.ai-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.ai-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #1890ff;
}

.ai-title {
  font-weight: 500;
  color: #262626;
}

.ai-content {
  flex: 1;
}

.quality-score {
  display: flex;
  align-items: center;
  gap: 16px;
}

.score-desc {
  flex: 1;
}

.score-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.score-trend {
  font-size: 12px;
  color: #52c41a;
}

.efficiency-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
}

.metric-value.fast {
  color: #52c41a;
}

.metric-value.warning {
  color: #faad14;
}

.risk-alerts {
  text-align: center;
}

.alert-item.medium {
  border: 1px solid #faad14;
  border-radius: 4px;
  padding: 8px;
  background-color: #fff7e6;
}

.alert-level {
  font-size: 14px;
  font-weight: 500;
  color: #faad14;
  margin-bottom: 4px;
}

.alert-count {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.chart-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.insight-content {
  padding: 16px 0;
}

.insight-content h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.suggestion-impact {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
  margin: 0;
}

.success-prediction {
  color: #52c41a;
  font-weight: 500;
}

.warning-prediction {
  color: #ff4d4f;
  font-weight: 500;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>