<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>回款报表</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="统计维度">
                <a-select v-model:value="searchForm.dimension" placeholder="请选择统计维度" allow-clear>
                  <a-select-option value="time">时间维度</a-select-option>
                  <a-select-option value="department">部门维度</a-select-option>
                  <a-select-option value="collector">催收员维度</a-select-option>
                  <a-select-option value="channel">渠道维度</a-select-option>
                  <a-select-option value="product">产品维度</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="支付方式">
                <a-select v-model:value="searchForm.paymentMethod" placeholder="请选择支付方式" allow-clear>
                  <a-select-option value="all">全部方式</a-select-option>
                  <a-select-option value="bank_transfer">银行转账</a-select-option>
                  <a-select-option value="alipay">支付宝</a-select-option>
                  <a-select-option value="wechat">微信支付</a-select-option>
                  <a-select-option value="cash">现金</a-select-option>
                  <a-select-option value="other">其他</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="回款类型">
                <a-select v-model:value="searchForm.paymentType" placeholder="请选择回款类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="full">全额回款</a-select-option>
                  <a-select-option value="partial">部分回款</a-select-option>
                  <a-select-option value="installment">分期回款</a-select-option>
                  <a-select-option value="settlement">和解回款</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportReport">
                      <DownloadOutlined />
                      导出报表
                    </a-button>
                    <a-button @click="showAIAnalysis">
                      <RobotOutlined />
                      AI分析
                    </a-button>
                    <a-button @click="showForecast">
                      <LineChartOutlined />
                      回款预测
                    </a-button>
                    <a-button @click="scheduleReport">
                      <ScheduleOutlined />
                      定时推送
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 回款概览统计 -->
      <a-row :gutter="16" class="overview-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="本期回款总额" 
              :value="overviewData.totalPayment" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <DollarCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>较上期 <span class="stat-change" :class="{ 'up': overviewData.paymentChange > 0, 'down': overviewData.paymentChange < 0 }">
                {{ overviewData.paymentChange > 0 ? '+' : '' }}{{ overviewData.paymentChange }}%
              </span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="回款案件数" 
              :value="overviewData.paymentCases" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>平均回款: <span class="stat-avg">¥{{ (overviewData.totalPayment / overviewData.paymentCases).toFixed(0).toLocaleString() }}</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="回款率" 
              :value="overviewData.paymentRate" 
              :precision="2" 
              suffix="%"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <PercentageOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>目标: <span class="stat-target">≥85%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="当日回款" 
              :value="overviewData.todayPayment" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>实时更新 <span class="stat-time">{{ currentTime }}</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- AI智能分析卡片 -->
      <a-row :gutter="16" class="ai-analysis-section">
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <RiseOutlined class="ai-icon" />
              <span class="ai-title">回款趋势分析</span>
            </div>
            <div class="ai-content">
              <div class="trend-info">
                <div class="trend-main">
                  <span class="trend-label">预测本月回款</span>
                  <span class="trend-value">¥{{ aiAnalysis.predictedAmount.toLocaleString() }}</span>
                </div>
                <div class="trend-sub">
                  <span class="trend-accuracy">准确率: {{ aiAnalysis.accuracy }}%</span>
                  <a-button type="link" size="small" @click="showTrendDetail">详情</a-button>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <BulbOutlined class="ai-icon" />
              <span class="ai-title">回款优化建议</span>
            </div>
            <div class="ai-content">
              <div class="optimization-info">
                <div class="opt-item">
                  <ExclamationCircleOutlined class="opt-icon" />
                  <span>{{ aiAnalysis.primarySuggestion }}</span>
                </div>
                <div class="opt-action">
                  <span class="opt-potential">潜在提升: {{ aiAnalysis.potential }}%</span>
                  <a-button type="link" size="small" @click="viewOptimization">查看方案</a-button>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="ai-card">
            <div class="ai-card-header">
              <WarningOutlined class="ai-icon" />
              <span class="ai-title">回款预警</span>
            </div>
            <div class="ai-content">
              <div class="warning-info">
                <div class="warning-stat">
                  <span class="warning-level" :class="aiAnalysis.warningLevel">{{ aiAnalysis.warningLevelText }}</span>
                  <span class="warning-count">{{ aiAnalysis.warningCount }}个预警事项</span>
                </div>
                <div class="warning-action">
                  <a-button type="link" size="small" @click="showWarningDetail">立即处理</a-button>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表分析区域 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="回款趋势分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-radio-group v-model:value="trendPeriod" size="small">
                <a-radio-button value="daily">日</a-radio-button>
                <a-radio-button value="weekly">周</a-radio-button>
                <a-radio-button value="monthly">月</a-radio-button>
              </a-radio-group>
            </div>
            <div ref="paymentTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="支付方式分布" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-checkbox-group v-model:value="selectedMethods" size="small">
                <a-checkbox value="bank">银行</a-checkbox>
                <a-checkbox value="alipay">支付宝</a-checkbox>
                <a-checkbox value="wechat">微信</a-checkbox>
                <a-checkbox value="other">其他</a-checkbox>
              </a-checkbox-group>
            </div>
            <div ref="paymentMethodChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="16">
          <a-card title="部门回款对比" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>对比指标:</span>
                <a-select v-model:value="compareMetric" size="small" style="width: 120px;">
                  <a-select-option value="amount">回款金额</a-select-option>
                  <a-select-option value="count">回款数量</a-select-option>
                  <a-select-option value="rate">回款率</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="departmentCompareChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="回款时段分布" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-radio-group v-model:value="timeSlotType" size="small">
                <a-radio-button value="hour">小时</a-radio-button>
                <a-radio-button value="day">天</a-radio-button>
              </a-radio-group>
            </div>
            <div ref="timeSlotChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 高级分析图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="回款周期分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>分析维度:</span>
                <a-select v-model:value="cycleDimension" size="small" style="width: 120px;">
                  <a-select-option value="amount">金额区间</a-select-option>
                  <a-select-option value="age">账龄区间</a-select-option>
                  <a-select-option value="type">客户类型</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div ref="paymentCycleChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="回款质量分析" :body-style="{ padding: '20px' }">
            <div class="chart-header">
              <a-space>
                <span>评估指标:</span>
                <a-checkbox-group v-model:value="qualityMetrics" size="small">
                  <a-checkbox value="timeliness">及时性</a-checkbox>
                  <a-checkbox value="completeness">完整性</a-checkbox>
                  <a-checkbox value="stability">稳定性</a-checkbox>
                </a-checkbox-group>
              </a-space>
            </div>
            <div ref="paymentQualityChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- AI预测与分析 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="AI回款预测与分析" :body-style="{ padding: '20px' }">
            <a-tabs v-model:activeKey="analysisTab">
              <a-tab-pane key="forecast" tab="回款预测">
                <div class="forecast-content">
                  <div class="forecast-header">
                    <a-space>
                      <span>预测周期:</span>
                      <a-radio-group v-model:value="forecastPeriod" size="small">
                        <a-radio-button value="week">未来一周</a-radio-button>
                        <a-radio-button value="month">未来一月</a-radio-button>
                        <a-radio-button value="quarter">未来一季</a-radio-button>
                      </a-radio-group>
                    </a-space>
                  </div>
                  <div ref="forecastChart" style="height: 400px"></div>
                </div>
              </a-tab-pane>
              <a-tab-pane key="pattern" tab="回款模式">
                <div class="pattern-content">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <div ref="patternChart" style="height: 400px"></div>
                    </a-col>
                    <a-col :span="12">
                      <h4>识别的回款模式</h4>
                      <a-list :data-source="paymentPatterns" size="small">
                        <template #renderItem="{ item }">
                          <a-list-item>
                            <a-list-item-meta>
                              <template #avatar>
                                <a-avatar :style="{ backgroundColor: item.color }" size="small">
                                  {{ item.type }}
                                </a-avatar>
                              </template>
                              <template #title>{{ item.name }}</template>
                              <template #description>
                                <div>{{ item.description }}</div>
                                <div class="pattern-stats">
                                  <span>占比: {{ item.percentage }}%</span>
                                  <span>平均金额: ¥{{ item.avgAmount.toLocaleString() }}</span>
                                </div>
                              </template>
                            </a-list-item-meta>
                          </a-list-item>
                        </template>
                      </a-list>
                    </a-col>
                  </a-row>
                </div>
              </a-tab-pane>
              <a-tab-pane key="optimization" tab="优化建议">
                <div class="optimization-content">
                  <a-row :gutter="16">
                    <a-col :span="8">
                      <h4>催收策略优化</h4>
                      <a-card v-for="strategy in optimizationStrategies" :key="strategy.id" class="strategy-card">
                        <div class="strategy-header">
                          <component :is="strategy.icon" class="strategy-icon" />
                          <span class="strategy-title">{{ strategy.title }}</span>
                        </div>
                        <div class="strategy-content">
                          <p>{{ strategy.description }}</p>
                          <div class="strategy-metrics">
                            <span class="metric-item">
                              <ArrowUpOutlined />
                              预计提升{{ strategy.improvement }}%
                            </span>
                            <span class="metric-item">
                              <TeamOutlined />
                              影响{{ strategy.affectedCases }}个案件
                            </span>
                          </div>
                        </div>
                        <a-button type="primary" size="small" block @click="applyStrategy(strategy)">
                          应用策略
                        </a-button>
                      </a-card>
                    </a-col>
                    <a-col :span="16">
                      <h4>回款提升路径</h4>
                      <div ref="improvementPathChart" style="height: 350px"></div>
                    </a-col>
                  </a-row>
                </div>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细数据表格 -->
      <a-card title="回款明细数据">
        <div class="table-toolbar">
          <a-space>
            <a-button @click="refreshData" :loading="loading">
              <ReloadOutlined />
              刷新数据
            </a-button>
            <a-button @click="exportTableData">
              <DownloadOutlined />
              导出明细
            </a-button>
            <a-button @click="showAdvancedFilter">
              <FilterOutlined />
              高级筛选
            </a-button>
          </a-space>
          <a-space>
            <span>汇总: 回款总额 <strong style="color: #1890ff;">¥{{ tableSummary.totalAmount.toLocaleString() }}</strong></span>
            <a-divider type="vertical" />
            <span>案件数 <strong>{{ tableSummary.totalCount }}</strong></span>
          </a-space>
        </div>
        <a-table 
          :columns="tableColumns" 
          :data-source="paymentData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1800 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'paymentAmount'">
              <span class="payment-amount">¥{{ record.paymentAmount?.toLocaleString() }}</span>
            </template>
            <template v-if="column.key === 'paymentMethod'">
              <a-tag :color="getMethodColor(record.paymentMethod)">
                {{ getMethodText(record.paymentMethod) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'paymentType'">
              <a-tag :color="getTypeColor(record.paymentType)">
                {{ getTypeText(record.paymentType) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'paymentRate'">
              <a-progress 
                :percent="record.paymentRate" 
                size="small" 
                :stroke-color="getProgressColor(record.paymentRate)"
              />
            </template>
            <template v-if="column.key === 'status'">
              <a-badge :status="getStatusBadge(record.status)" :text="getStatusText(record.status)" />
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewPaymentDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="printReceipt(record)">打印</a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="sendNotification(record)">发送通知</a-menu-item>
                      <a-menu-item @click="generateReport(record)">生成报告</a-menu-item>
                      <a-menu-item @click="viewHistory(record)">历史记录</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  RobotOutlined,
  LineChartOutlined,
  ScheduleOutlined,
  DollarCircleOutlined,
  FileTextOutlined,
  PercentageOutlined,
  CalendarOutlined,
  RiseOutlined,
  BulbOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  FilterOutlined,
  DownOutlined,
  ArrowUpOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const currentTime = ref(dayjs().format('HH:mm:ss'))

// 搜索表单
const searchForm = reactive({
  dimension: 'time',
  dateRange: [dayjs().subtract(30, 'day'), dayjs()],
  paymentMethod: 'all',
  paymentType: 'all'
})

// 日期预设
const datePresets = {
  '今日': [dayjs().startOf('day'), dayjs().endOf('day')],
  '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '最近30天': [dayjs().subtract(30, 'day'), dayjs()],
  '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 概览数据
const overviewData = reactive({
  totalPayment: 8956234.56,
  paymentChange: 12.5,
  paymentCases: 1234,
  paymentRate: 78.6,
  todayPayment: 125600.00
})

// AI分析数据
const aiAnalysis = reactive({
  predictedAmount: 2150000,
  accuracy: 92,
  primarySuggestion: '建议在上午10-11点加强催收，成功率最高',
  potential: 15,
  warningLevel: 'medium',
  warningLevelText: '中等风险',
  warningCount: 5
})

// 图表控制变量
const trendPeriod = ref('daily')
const selectedMethods = ref(['bank', 'alipay', 'wechat'])
const compareMetric = ref('amount')
const timeSlotType = ref('hour')
const cycleDimension = ref('amount')
const qualityMetrics = ref(['timeliness', 'completeness'])
const analysisTab = ref('forecast')
const forecastPeriod = ref('month')

// 回款模式数据
const paymentPatterns = ref([
  {
    type: 'A',
    name: '主动回款型',
    description: '客户主动联系并全额回款，通常在月初',
    percentage: 35,
    avgAmount: 45000,
    color: '#52c41a'
  },
  {
    type: 'B',
    name: '催收响应型',
    description: '接到催收后1-3天内回款，金额中等',
    percentage: 45,
    avgAmount: 25000,
    color: '#1890ff'
  },
  {
    type: 'C',
    name: '分期回款型',
    description: '申请分期并按期回款，周期较长',
    percentage: 15,
    avgAmount: 15000,
    color: '#faad14'
  },
  {
    type: 'D',
    name: '困难回款型',
    description: '需要多次催收和协商，回款率低',
    percentage: 5,
    avgAmount: 8000,
    color: '#ff4d4f'
  }
])

// 优化策略
const optimizationStrategies = ref([
  {
    id: 1,
    title: '智能时段催收',
    description: '根据AI分析，在最佳时段进行催收',
    icon: 'ClockCircleOutlined',
    improvement: 18,
    affectedCases: 450
  },
  {
    id: 2,
    title: '个性化还款方案',
    description: '基于客户画像提供定制化方案',
    icon: 'ThunderboltOutlined',
    improvement: 25,
    affectedCases: 320
  },
  {
    id: 3,
    title: '激励机制优化',
    description: '提供阶梯式减免激励，促进回款',
    icon: 'SafetyCertificateOutlined',
    improvement: 15,
    affectedCases: 680
  }
])

// 表格汇总
const tableSummary = reactive({
  totalAmount: 8956234.56,
  totalCount: 1234
})

// 表格列配置
const tableColumns = [
  {
    title: '回款编号',
    dataIndex: 'paymentNo',
    key: 'paymentNo',
    width: 120,
    fixed: 'left'
  },
  {
    title: '案件编号',
    dataIndex: 'caseNo',
    key: 'caseNo',
    width: 120
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '回款金额',
    key: 'paymentAmount',
    width: 120
  },
  {
    title: '原始债务',
    dataIndex: 'originalAmount',
    key: 'originalAmount',
    width: 120
  },
  {
    title: '回款率',
    key: 'paymentRate',
    width: 120
  },
  {
    title: '支付方式',
    key: 'paymentMethod',
    width: 100
  },
  {
    title: '回款类型',
    key: 'paymentType',
    width: 100
  },
  {
    title: '催收员',
    dataIndex: 'collector',
    key: 'collector',
    width: 100
  },
  {
    title: '回款时间',
    dataIndex: 'paymentTime',
    key: 'paymentTime',
    width: 160
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 表格数据
const paymentData = ref([
  {
    id: 1,
    paymentNo: 'PAY2024001',
    caseNo: 'CS2024001',
    customerName: '张三',
    paymentAmount: 45000,
    originalAmount: 50000,
    paymentRate: 90,
    paymentMethod: 'bank_transfer',
    paymentType: 'full',
    collector: '李催收',
    paymentTime: '2024-01-15 10:30:00',
    status: 'completed'
  },
  {
    id: 2,
    paymentNo: 'PAY2024002',
    caseNo: 'CS2024002',
    customerName: '李四',
    paymentAmount: 12000,
    originalAmount: 30000,
    paymentRate: 40,
    paymentMethod: 'alipay',
    paymentType: 'partial',
    collector: '王催收',
    paymentTime: '2024-01-15 14:25:00',
    status: 'processing'
  },
  {
    id: 3,
    paymentNo: 'PAY2024003',
    caseNo: 'CS2024003',
    customerName: '王五',
    paymentAmount: 80000,
    originalAmount: 80000,
    paymentRate: 100,
    paymentMethod: 'wechat',
    paymentType: 'settlement',
    collector: '张催收',
    paymentTime: '2024-01-16 09:15:00',
    status: 'completed'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: paymentData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const paymentTrendChart = ref()
const paymentMethodChart = ref()
const departmentCompareChart = ref()
const timeSlotChart = ref()
const paymentCycleChart = ref()
const paymentQualityChart = ref()
const forecastChart = ref()
const patternChart = ref()
const improvementPathChart = ref()

// 状态相关方法
const getMethodColor = (method) => {
  const colors = {
    bank_transfer: 'blue',
    alipay: 'cyan',
    wechat: 'green',
    cash: 'orange',
    other: 'purple'
  }
  return colors[method] || 'default'
}

const getMethodText = (method) => {
  const texts = {
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat: '微信支付',
    cash: '现金',
    other: '其他'
  }
  return texts[method] || '未知'
}

const getTypeColor = (type) => {
  const colors = {
    full: 'green',
    partial: 'orange',
    installment: 'blue',
    settlement: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    full: '全额回款',
    partial: '部分回款',
    installment: '分期回款',
    settlement: '和解回款'
  }
  return texts[type] || '未知'
}

const getProgressColor = (rate) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  return '#ff4d4f'
}

const getStatusBadge = (status) => {
  const badges = {
    completed: 'success',
    processing: 'processing',
    pending: 'warning',
    failed: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    pending: '待处理',
    failed: '失败'
  }
  return texts[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('回款数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.dimension = 'time'
  searchForm.dateRange = [dayjs().subtract(30, 'day'), dayjs()]
  searchForm.paymentMethod = 'all'
  searchForm.paymentType = 'all'
  handleSearch()
}

const exportReport = () => {
  message.success('回款报表导出成功')
}

const showAIAnalysis = () => {
  message.success('AI分析报告生成成功')
}

const showForecast = () => {
  analysisTab.value = 'forecast'
  message.info('显示回款预测')
}

const scheduleReport = () => {
  message.info('定时推送设置')
}

const showTrendDetail = () => {
  message.info('显示趋势详情')
}

const viewOptimization = () => {
  analysisTab.value = 'optimization'
}

const showWarningDetail = () => {
  message.info('显示预警详情')
}

const refreshData = () => {
  handleSearch()
}

const exportTableData = () => {
  message.success('明细数据导出成功')
}

const showAdvancedFilter = () => {
  message.info('高级筛选功能')
}

const viewPaymentDetail = (record) => {
  message.info(`查看回款 ${record.paymentNo} 详情`)
}

const printReceipt = (record) => {
  message.success(`打印回款单 ${record.paymentNo}`)
}

const sendNotification = (record) => {
  message.success('通知发送成功')
}

const generateReport = (record) => {
  message.success('报告生成成功')
}

const viewHistory = (record) => {
  message.info(`查看 ${record.caseNo} 历史记录`)
}

const applyStrategy = (strategy) => {
  message.success(`策略 "${strategy.title}" 应用成功`)
}

// 图表初始化
const initCharts = () => {
  // 回款趋势图
  setTimeout(() => {
    const trendEl = paymentTrendChart.value
    if (trendEl) {
      const trendChart = echarts.init(trendEl)
      updateTrendChart(trendChart)
    }
  }, 100)

  // 支付方式分布图
  setTimeout(() => {
    const methodEl = paymentMethodChart.value
    if (methodEl) {
      const methodChart = echarts.init(methodEl)
      updateMethodChart(methodChart)
    }
  }, 150)

  // 部门对比图
  setTimeout(() => {
    const compareEl = departmentCompareChart.value
    if (compareEl) {
      const compareChart = echarts.init(compareEl)
      updateCompareChart(compareChart)
    }
  }, 200)

  // 时段分布图
  setTimeout(() => {
    const timeSlotEl = timeSlotChart.value
    if (timeSlotEl) {
      const slotChart = echarts.init(timeSlotEl)
      updateTimeSlotChart(slotChart)
    }
  }, 250)

  // 回款周期图
  setTimeout(() => {
    const cycleEl = paymentCycleChart.value
    if (cycleEl) {
      const cycleChart = echarts.init(cycleEl)
      updateCycleChart(cycleChart)
    }
  }, 300)

  // 回款质量图
  setTimeout(() => {
    const qualityEl = paymentQualityChart.value
    if (qualityEl) {
      const qualityChart = echarts.init(qualityEl)
      updateQualityChart(qualityChart)
    }
  }, 350)

  // 预测图表
  setTimeout(() => {
    initPredictionCharts()
  }, 400)
}

// 更新趋势图
const updateTrendChart = (chart) => {
  const data = trendPeriod.value === 'daily' 
    ? ['1日', '2日', '3日', '4日', '5日', '6日', '7日']
    : trendPeriod.value === 'weekly'
    ? ['第1周', '第2周', '第3周', '第4周']
    : ['1月', '2月', '3月', '4月', '5月', '6月']
    
  const option = {
    tooltip: { trigger: 'axis' },
    legend: { bottom: '5%' },
    xAxis: {
      type: 'category',
      data: data
    },
    yAxis: { type: 'value', name: '回款金额(万元)' },
    series: [
      {
        name: '回款金额',
        type: 'line',
        data: trendPeriod.value === 'daily' 
          ? [120, 135, 128, 142, 138, 155, 148]
          : trendPeriod.value === 'weekly'
          ? [520, 580, 490, 620]
          : [2100, 2350, 2080, 2420, 2580, 2690],
        smooth: true,
        areaStyle: { opacity: 0.3 }
      },
      {
        name: '目标金额',
        type: 'line',
        data: trendPeriod.value === 'daily' 
          ? [130, 130, 130, 130, 130, 130, 130]
          : trendPeriod.value === 'weekly'
          ? [550, 550, 550, 550]
          : [2200, 2200, 2200, 2200, 2200, 2200],
        lineStyle: { type: 'dashed' }
      }
    ]
  }
  chart.setOption(option)
}

// 更新支付方式图
const updateMethodChart = (chart) => {
  const data = []
  if (selectedMethods.value.includes('bank')) data.push({ value: 4520, name: '银行转账' })
  if (selectedMethods.value.includes('alipay')) data.push({ value: 2380, name: '支付宝' })
  if (selectedMethods.value.includes('wechat')) data.push({ value: 1890, name: '微信支付' })
  if (selectedMethods.value.includes('other')) data.push({ value: 166, name: '其他' })
  
  const option = {
    tooltip: { trigger: 'item' },
    legend: { bottom: '5%' },
    series: [{
      name: '支付方式',
      type: 'pie',
      radius: ['40%', '70%'],
      data: data
    }]
  }
  chart.setOption(option)
}

// 更新部门对比图
const updateCompareChart = (chart) => {
  const yAxisName = compareMetric.value === 'amount' ? '回款金额(万元)' 
    : compareMetric.value === 'count' ? '回款数量' : '回款率(%)'
    
  const data = compareMetric.value === 'amount' ? [345, 428, 289, 156]
    : compareMetric.value === 'count' ? [234, 312, 198, 89]
    : [78.5, 82.3, 75.6, 68.9]
    
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['催收一部', '催收二部', '催收三部', '法务部']
    },
    yAxis: { type: 'value', name: yAxisName },
    series: [{
      name: yAxisName,
      type: 'bar',
      data: data,
      itemStyle: {
        color: (params) => {
          const colors = ['#1890ff', '#52c41a', '#faad14', '#722ed1']
          return colors[params.dataIndex]
        }
      }
    }]
  }
  chart.setOption(option)
}

// 更新时段分布图
const updateTimeSlotChart = (chart) => {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'value',
      max: 100
    },
    yAxis: {
      type: 'category',
      data: timeSlotType.value === 'hour' 
        ? ['9-10时', '10-11时', '11-12时', '14-15时', '15-16时', '16-17时']
        : ['周一', '周二', '周三', '周四', '周五']
    },
    series: [{
      name: '回款占比(%)',
      type: 'bar',
      data: timeSlotType.value === 'hour'
        ? [12, 25, 18, 22, 15, 8]
        : [18, 22, 20, 25, 15],
      label: {
        show: true,
        position: 'right',
        formatter: '{c}%'
      }
    }]
  }
  chart.setOption(option)
}

// 更新回款周期图
const updateCycleChart = (chart) => {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: cycleDimension.value === 'amount'
        ? ['<1万', '1-5万', '5-10万', '10-30万', '>30万']
        : cycleDimension.value === 'age'
        ? ['M1', 'M2', 'M3', 'M4+']
        : ['个人', '企业', 'VIP', '普通']
    },
    yAxis: { type: 'value', name: '平均回款天数' },
    series: [{
      name: '回款周期',
      type: 'line',
      data: cycleDimension.value === 'amount'
        ? [3.5, 5.2, 8.6, 12.3, 18.5]
        : cycleDimension.value === 'age'
        ? [4.2, 7.8, 15.6, 28.9]
        : [5.6, 12.8, 3.2, 8.9],
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }]
  }
  chart.setOption(option)
}

// 更新质量图
const updateQualityChart = (chart) => {
  const indicators = []
  const values = []
  
  if (qualityMetrics.value.includes('timeliness')) {
    indicators.push({ name: '及时性', max: 100 })
    values.push(85)
  }
  if (qualityMetrics.value.includes('completeness')) {
    indicators.push({ name: '完整性', max: 100 })
    values.push(92)
  }
  if (qualityMetrics.value.includes('stability')) {
    indicators.push({ name: '稳定性', max: 100 })
    values.push(78)
  }
  
  const option = {
    tooltip: {},
    radar: {
      indicator: indicators
    },
    series: [{
      type: 'radar',
      data: [{
        value: values,
        name: '回款质量'
      }]
    }]
  }
  chart.setOption(option)
}

// 初始化预测图表
const initPredictionCharts = () => {
  // 预测图
  const forecastEl = forecastChart.value
  if (forecastEl) {
    const chart = echarts.init(forecastEl)
    const periods = forecastPeriod.value === 'week' 
      ? ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      : forecastPeriod.value === 'month'
      ? Array.from({ length: 30 }, (_, i) => `${i + 1}日`)
      : ['1月', '2月', '3月']
      
    chart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { bottom: '5%' },
      xAxis: { type: 'category', data: periods },
      yAxis: { type: 'value', name: '预测回款(万元)' },
      series: [
        {
          name: '预测回款',
          type: 'line',
          data: forecastPeriod.value === 'week'
            ? [125, 138, 142, 156, 148, 98, 85]
            : forecastPeriod.value === 'month'
            ? Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 100)
            : [2800, 3200, 3500],
          smooth: true,
          areaStyle: { opacity: 0.3 }
        },
        {
          name: '置信区间上限',
          type: 'line',
          data: forecastPeriod.value === 'week'
            ? [135, 148, 152, 166, 158, 108, 95]
            : forecastPeriod.value === 'month'
            ? Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 120)
            : [3000, 3400, 3700],
          lineStyle: { type: 'dashed', opacity: 0.5 }
        },
        {
          name: '置信区间下限',
          type: 'line',
          data: forecastPeriod.value === 'week'
            ? [115, 128, 132, 146, 138, 88, 75]
            : forecastPeriod.value === 'month'
            ? Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 80)
            : [2600, 3000, 3300],
          lineStyle: { type: 'dashed', opacity: 0.5 }
        }
      ]
    })
  }

  // 模式图
  const patternEl = patternChart.value
  if (patternEl) {
    const chart = echarts.init(patternEl)
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'sunburst',
        data: [{
          name: '回款模式',
          children: paymentPatterns.value.map(p => ({
            name: p.name,
            value: p.percentage,
            children: [
              { name: `占比: ${p.percentage}%`, value: p.percentage / 2 },
              { name: `均额: ¥${p.avgAmount.toLocaleString()}`, value: p.percentage / 2 }
            ]
          }))
        }],
        radius: [0, '90%'],
        label: { rotate: 'radial' }
      }]
    })
  }

  // 提升路径图
  const pathEl = improvementPathChart.value
  if (pathEl) {
    const chart = echarts.init(pathEl)
    chart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['当前', '第1阶段', '第2阶段', '第3阶段', '目标']
      },
      yAxis: { type: 'value', name: '回款率(%)' },
      series: [{
        name: '回款率提升路径',
        type: 'line',
        data: [78.6, 82.5, 85.8, 88.2, 90.0],
        markPoint: {
          data: [
            { type: 'max', name: '目标' },
            { type: 'min', name: '当前' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均' }]
        }
      }]
    })
  }
}

// 监听图表控制变量
watch([trendPeriod, selectedMethods, compareMetric, timeSlotType, cycleDimension, qualityMetrics, forecastPeriod], () => {
  nextTick(() => {
    initCharts()
  })
}, { deep: true })

// 更新当前时间
setInterval(() => {
  currentTime.value = dayjs().format('HH:mm:ss')
}, 1000)

// 响应式处理
window.addEventListener('resize', () => {
  const charts = [
    paymentTrendChart, paymentMethodChart, departmentCompareChart,
    timeSlotChart, paymentCycleChart, paymentQualityChart,
    forecastChart, patternChart, improvementPathChart
  ]
  charts.forEach(chartRef => {
    if (chartRef.value) {
      const chart = echarts.getInstanceByDom(chartRef.value)
      chart?.resize()
    }
  })
})

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.overview-cards {
  margin-bottom: 16px;
}

.ai-analysis-section {
  margin-bottom: 16px;
}

.chart-section {
  margin-bottom: 16px;
}

.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-change {
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-change.down {
  color: #ff4d4f;
}

.stat-avg,
.stat-target,
.stat-time {
  color: #1890ff;
  font-weight: 500;
}

.ai-card {
  height: 120px;
}

.ai-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.ai-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #1890ff;
}

.ai-title {
  font-weight: 500;
  color: #262626;
}

.ai-content {
  flex: 1;
}

.trend-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trend-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-label {
  font-size: 12px;
  color: #666;
}

.trend-value {
  font-size: 18px;
  font-weight: bold;
  color: #52c41a;
}

.trend-sub {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-accuracy {
  font-size: 12px;
  color: #666;
}

.optimization-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.opt-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.opt-icon {
  color: #faad14;
  margin-top: 2px;
}

.opt-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.opt-potential {
  color: #1890ff;
  font-weight: 500;
}

.warning-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.warning-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warning-level {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.warning-level.low {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.warning-level.medium {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.warning-level.high {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.warning-count {
  font-size: 12px;
  color: #666;
}

.chart-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-amount {
  color: #1890ff;
  font-weight: 500;
}

.forecast-content,
.pattern-content,
.optimization-content {
  padding: 16px 0;
}

.forecast-header {
  margin-bottom: 16px;
}

.pattern-content h4,
.optimization-content h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.pattern-stats {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 16px;
}

.strategy-card {
  margin-bottom: 16px;
}

.strategy-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.strategy-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #1890ff;
}

.strategy-title {
  font-weight: 500;
}

.strategy-content p {
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
}

.strategy-metrics {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #1890ff;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>