<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>合规报表</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合规维度">
                <a-select v-model:value="searchForm.dimension" placeholder="请选择维度" allow-clear>
                  <a-select-option value="all">全部维度</a-select-option>
                  <a-select-option value="call">通话合规</a-select-option>
                  <a-select-option value="time">时间合规</a-select-option>
                  <a-select-option value="frequency">频次合规</a-select-option>
                  <a-select-option value="content">内容合规</a-select-option>
                  <a-select-option value="process">流程合规</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合规状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="compliant">合规</a-select-option>
                  <a-select-option value="violation">违规</a-select-option>
                  <a-select-option value="warning">预警</a-select-option>
                  <a-select-option value="pending">待审核</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="报表周期">
                <a-range-picker 
                  v-model:value="searchForm.dateRange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="业务部门">
                  <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                    <a-select-option value="all">全部部门</a-select-option>
                    <a-select-option value="dept1">催收一部</a-select-option>
                    <a-select-option value="dept2">催收二部</a-select-option>
                    <a-select-option value="dept3">法务部</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="催收员">
                  <a-select v-model:value="searchForm.collector" placeholder="请选择催收员" allow-clear>
                    <a-select-option value="all">全部催收员</a-select-option>
                    <a-select-option value="user1">李催收</a-select-option>
                    <a-select-option value="user2">王催收</a-select-option>
                    <a-select-option value="user3">张催收</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="违规等级">
                  <a-select v-model:value="searchForm.violationLevel" placeholder="请选择等级" allow-clear>
                    <a-select-option value="high">严重违规</a-select-option>
                    <a-select-option value="medium">一般违规</a-select-option>
                    <a-select-option value="low">轻微违规</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="客户类型">
                  <a-select v-model:value="searchForm.customerType" placeholder="请选择客户类型" allow-clear>
                    <a-select-option value="individual">个人客户</a-select-option>
                    <a-select-option value="enterprise">企业客户</a-select-option>
                    <a-select-option value="special">特殊客户</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic title="合规检查总数" :value="statistics.totalChecks" suffix="项" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="合规率" :value="statistics.complianceRate" suffix="%" :precision="2" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="违规事件" :value="statistics.violations" suffix="起" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="待处理预警" :value="statistics.pendingWarnings" suffix="个" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索和操作区域 -->
      <a-card class="search-card">
        <div class="search-actions">
          <div class="action-buttons">
            <a-button type="primary" @click="showDetailModal = true">
              <PlusOutlined />
              创建合规检查
            </a-button>
            <a-button @click="showBatchAuditModal = true">
              <CheckOutlined />
              批量审核
            </a-button>
            <a-button @click="showAnalysisModal = true">
              <BarChartOutlined />
              合规分析
            </a-button>
            <a-button @click="showPolicyModal = true">
              <FileTextOutlined />
              合规政策
            </a-button>
            <a-button @click="exportReport">
              <DownloadOutlined />
              导出报表
            </a-button>
            <a-button @click="generateAlert">
              <AlertOutlined />
              生成预警
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 合规趋势图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="合规率趋势">
            <div class="chart-placeholder">📈 合规率趋势图</div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="违规类型分布">
            <div class="chart-placeholder">📊 违规类型饼图</div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="部门合规对比">
            <div class="chart-placeholder">📊 部门合规对比柱状图</div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 合规检查记录 -->
      <a-card>
        <a-table 
          :columns="columns" 
          :data-source="complianceData" 
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'dimension'">
              <a-tag :color="getDimensionColor(record.dimension)">
                {{ getDimensionText(record.dimension) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'violationLevel'">
              <a-tag :color="getViolationLevelColor(record.violationLevel)">
                {{ getViolationLevelText(record.violationLevel) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'complianceScore'">
              <a-progress 
                :percent="record.complianceScore" 
                size="small" 
                :status="record.complianceScore < 60 ? 'exception' : record.complianceScore < 80 ? 'active' : 'success'"
              />
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">
                  <EyeOutlined />
                  查看
                </a-button>
                <a-button 
                  v-if="record.status === 'pending'" 
                  type="link" 
                  size="small" 
                  @click="auditRecord(record)"
                >
                  <CheckOutlined />
                  审核
                </a-button>
                <a-button 
                  v-if="record.status === 'violation'" 
                  type="link" 
                  size="small" 
                  @click="handleViolation(record)"
                >
                  <WarningOutlined />
                  处理
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="generateReport(record)">生成报告</a-menu-item>
                      <a-menu-item @click="viewHistory(record)">查看历史</a-menu-item>
                      <a-menu-item @click="addToPolicy(record)">加入政策</a-menu-item>
                      <a-menu-item @click="exportRecord(record)">导出记录</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 合规详情弹窗 -->
      <a-modal
        v-model:open="showDetailModal"
        title="合规检查详情"
        width="800px"
        @ok="handleDetailSubmit"
        @cancel="showDetailModal = false"
      >
        <a-form ref="detailFormRef" :model="detailForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="检查类型" name="checkType" :rules="[{ required: true, message: '请选择检查类型' }]">
            <a-select v-model:value="detailForm.checkType" placeholder="请选择检查类型">
              <a-select-option value="routine">例行检查</a-select-option>
              <a-select-option value="special">专项检查</a-select-option>
              <a-select-option value="complaint">投诉检查</a-select-option>
              <a-select-option value="random">随机抽查</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="检查维度" name="dimension" :rules="[{ required: true, message: '请选择检查维度' }]">
            <a-checkbox-group v-model:value="detailForm.dimensions">
              <a-checkbox value="call">通话合规</a-checkbox>
              <a-checkbox value="time">时间合规</a-checkbox>
              <a-checkbox value="frequency">频次合规</a-checkbox>
              <a-checkbox value="content">内容合规</a-checkbox>
              <a-checkbox value="process">流程合规</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="检查范围" name="scope" :rules="[{ required: true, message: '请输入检查范围' }]">
            <a-textarea v-model:value="detailForm.scope" :rows="3" placeholder="请输入检查范围描述" />
          </a-form-item>
          
          <a-form-item label="检查标准" name="standard" :rules="[{ required: true, message: '请输入检查标准' }]">
            <a-textarea v-model:value="detailForm.standard" :rows="3" placeholder="请输入检查标准" />
          </a-form-item>
          
          <a-form-item label="检查时间" name="checkTime" :rules="[{ required: true, message: '请选择检查时间' }]">
            <a-range-picker 
              v-model:value="detailForm.checkTime" 
              show-time 
              placeholder="选择检查时间范围"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="检查人员" name="checker" :rules="[{ required: true, message: '请选择检查人员' }]">
            <a-select v-model:value="detailForm.checker" placeholder="请选择检查人员">
              <a-select-option value="user1">张合规</a-select-option>
              <a-select-option value="user2">李合规</a-select-option>
              <a-select-option value="user3">王合规</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="备注信息" name="remarks">
            <a-textarea v-model:value="detailForm.remarks" :rows="2" placeholder="请输入备注信息" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量审核弹窗 -->
      <a-modal
        v-model:open="showBatchAuditModal"
        title="批量审核"
        width="600px"
        @ok="handleBatchAuditSubmit"
        @cancel="showBatchAuditModal = false"
      >
        <a-form ref="batchAuditFormRef" :model="batchAuditForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选中数量">
            <span>已选择 {{ selectedRowKeys.length }} 个检查项</span>
          </a-form-item>
          
          <a-form-item label="审核结果" name="result" :rules="[{ required: true, message: '请选择审核结果' }]">
            <a-radio-group v-model:value="batchAuditForm.result">
              <a-radio value="compliant">合规</a-radio>
              <a-radio value="violation">违规</a-radio>
              <a-radio value="warning">预警</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="batchAuditForm.result === 'violation'"
            label="违规等级"
            name="violationLevel"
            :rules="[{ required: true, message: '请选择违规等级' }]"
          >
            <a-select v-model:value="batchAuditForm.violationLevel" placeholder="请选择违规等级">
              <a-select-option value="high">严重违规</a-select-option>
              <a-select-option value="medium">一般违规</a-select-option>
              <a-select-option value="low">轻微违规</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="审核意见" name="comment" :rules="[{ required: true, message: '请输入审核意见' }]">
            <a-textarea v-model:value="batchAuditForm.comment" :rows="4" placeholder="请输入审核意见" />
          </a-form-item>
          
          <a-form-item label="后续处理">
            <a-checkbox-group v-model:value="batchAuditForm.followUp">
              <a-checkbox value="notify">通知相关人员</a-checkbox>
              <a-checkbox value="training">安排培训</a-checkbox>
              <a-checkbox value="monitor">加强监控</a-checkbox>
              <a-checkbox value="report">上报管理层</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 合规分析弹窗 -->
      <a-modal
        v-model:open="showAnalysisModal"
        title="合规分析"
        width="1200px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="trend" tab="趋势分析">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="合规率变化趋势">
                  <div class="chart-placeholder">📈 合规率趋势图</div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="违规事件趋势">
                  <div class="chart-placeholder">📈 违规事件趋势图</div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="department" tab="部门分析">
            <a-table
              :columns="departmentColumns"
              :data-source="departmentAnalysisData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'complianceRate'">
                  <a-progress :percent="record.complianceRate" size="small" />
                </template>
                <template v-if="column.key === 'trend'">
                  <span :class="{ 'increase': record.trend > 0, 'decrease': record.trend < 0 }">
                    {{ record.trend > 0 ? '↑' : '↓' }} {{ Math.abs(record.trend) }}%
                  </span>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="violation" tab="违规分析">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="违规类型分布">
                  <div class="chart-placeholder">📊 违规类型饼图</div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="违规等级分布">
                  <div class="chart-placeholder">📊 违规等级柱状图</div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 合规政策弹窗 -->
      <a-modal
        v-model:open="showPolicyModal"
        title="合规政策管理"
        width="1000px"
        @ok="handlePolicySubmit"
        @cancel="showPolicyModal = false"
      >
        <a-tabs>
          <a-tab-pane key="policy" tab="政策列表">
            <a-table
              :columns="policyColumns"
              :data-source="policyData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 'active' ? 'green' : 'red'">
                    {{ record.status === 'active' ? '生效中' : '已停用' }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="editPolicy(record)">编辑</a-button>
                    <a-button type="link" size="small" @click="viewPolicy(record)">查看</a-button>
                    <a-button type="link" size="small" danger @click="deletePolicy(record)">删除</a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="create" tab="新建政策">
            <a-form :model="policyForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <a-form-item label="政策名称" name="name" :rules="[{ required: true, message: '请输入政策名称' }]">
                <a-input v-model:value="policyForm.name" placeholder="请输入政策名称" />
              </a-form-item>
              
              <a-form-item label="政策类型" name="type" :rules="[{ required: true, message: '请选择政策类型' }]">
                <a-select v-model:value="policyForm.type" placeholder="请选择政策类型">
                  <a-select-option value="call">通话规范</a-select-option>
                  <a-select-option value="time">时间限制</a-select-option>
                  <a-select-option value="frequency">频次控制</a-select-option>
                  <a-select-option value="content">内容审核</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="政策内容" name="content" :rules="[{ required: true, message: '请输入政策内容' }]">
                <a-textarea v-model:value="policyForm.content" :rows="6" placeholder="请输入详细的政策内容" />
              </a-form-item>
              
              <a-form-item label="生效时间" name="effectiveTime" :rules="[{ required: true, message: '请选择生效时间' }]">
                <a-date-picker 
                  v-model:value="policyForm.effectiveTime" 
                  show-time 
                  placeholder="选择生效时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined, 
  PlusOutlined, 
  CheckOutlined, 
  BarChartOutlined,
  FileTextOutlined,
  DownloadOutlined,
  AlertOutlined,
  EyeOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const selectedRowKeys = ref([])

// 弹窗状态
const showDetailModal = ref(false)
const showBatchAuditModal = ref(false)
const showAnalysisModal = ref(false)
const showPolicyModal = ref(false)

// 搜索表单
const searchForm = reactive({
  dimension: undefined,
  status: undefined,
  dateRange: [],
  department: undefined,
  collector: undefined,
  violationLevel: undefined,
  customerType: undefined
})

// 统计数据
const statistics = reactive({
  totalChecks: 2456,
  complianceRate: 94.2,
  violations: 142,
  pendingWarnings: 23
})

// 详情表单
const detailForm = reactive({
  checkType: undefined,
  dimensions: [],
  scope: '',
  standard: '',
  checkTime: [],
  checker: undefined,
  remarks: ''
})

// 批量审核表单
const batchAuditForm = reactive({
  result: undefined,
  violationLevel: undefined,
  comment: '',
  followUp: []
})

// 政策表单
const policyForm = reactive({
  name: '',
  type: undefined,
  content: '',
  effectiveTime: undefined
})

// 表格列配置
const columns = [
  {
    title: '检查编号',
    dataIndex: 'checkId',
    key: 'checkId',
    width: 120
  },
  {
    title: '检查维度',
    key: 'dimension',
    width: 100
  },
  {
    title: '检查对象',
    dataIndex: 'target',
    key: 'target',
    width: 120
  },
  {
    title: '合规状态',
    key: 'status',
    width: 100
  },
  {
    title: '合规得分',
    key: 'complianceScore',
    width: 120
  },
  {
    title: '违规等级',
    key: 'violationLevel',
    width: 100
  },
  {
    title: '检查时间',
    dataIndex: 'checkTime',
    key: 'checkTime',
    width: 150
  },
  {
    title: '检查人员',
    dataIndex: 'checker',
    key: 'checker',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 部门分析列配置
const departmentColumns = [
  { title: '部门名称', dataIndex: 'name', key: 'name' },
  { title: '检查次数', dataIndex: 'checkCount', key: 'checkCount' },
  { title: '合规率', key: 'complianceRate' },
  { title: '违规数量', dataIndex: 'violations', key: 'violations' },
  { title: '趋势', key: 'trend' }
]

// 政策列配置
const policyColumns = [
  { title: '政策名称', dataIndex: 'name', key: 'name' },
  { title: '政策类型', dataIndex: 'type', key: 'type' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '状态', key: 'status' },
  { title: '操作', key: 'action', width: 150 }
]

// 模拟数据
const complianceData = ref([
  {
    id: 1,
    checkId: 'CC2024001',
    dimension: 'call',
    target: '李催收',
    status: 'compliant',
    complianceScore: 95,
    violationLevel: null,
    checkTime: '2024-01-15 09:30',
    checker: '张合规'
  },
  {
    id: 2,
    checkId: 'CC2024002',
    dimension: 'time',
    target: '王催收',
    status: 'violation',
    complianceScore: 45,
    violationLevel: 'medium',
    checkTime: '2024-01-15 14:20',
    checker: '李合规'
  },
  {
    id: 3,
    checkId: 'CC2024003',
    dimension: 'frequency',
    target: '张催收',
    status: 'warning',
    complianceScore: 72,
    violationLevel: 'low',
    checkTime: '2024-01-14 16:45',
    checker: '王合规'
  }
])

const departmentAnalysisData = ref([
  { name: '催收一部', checkCount: 156, complianceRate: 95, violations: 8, trend: 2.1 },
  { name: '催收二部', checkCount: 142, complianceRate: 92, violations: 11, trend: -1.2 },
  { name: '法务部', checkCount: 89, complianceRate: 98, violations: 2, trend: 1.5 }
])

const policyData = ref([
  {
    id: 1,
    name: '通话时间限制政策',
    type: '时间规范',
    createTime: '2024-01-01',
    status: 'active'
  },
  {
    id: 2,
    name: '催收频次控制政策',
    type: '频次控制',
    createTime: '2024-01-02',
    status: 'active'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: complianceData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 状态相关方法
const getDimensionColor = (dimension) => {
  const colors = {
    call: 'blue',
    time: 'green',
    frequency: 'orange',
    content: 'purple',
    process: 'cyan'
  }
  return colors[dimension] || 'default'
}

const getDimensionText = (dimension) => {
  const texts = {
    call: '通话合规',
    time: '时间合规',
    frequency: '频次合规',
    content: '内容合规',
    process: '流程合规'
  }
  return texts[dimension] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    compliant: 'green',
    violation: 'red',
    warning: 'orange',
    pending: 'blue'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    compliant: '合规',
    violation: '违规',
    warning: '预警',
    pending: '待审核'
  }
  return texts[status] || '未知'
}

const getViolationLevelColor = (level) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'yellow'
  }
  return colors[level] || 'default'
}

const getViolationLevelText = (level) => {
  const texts = {
    high: '严重违规',
    medium: '一般违规',
    low: '轻微违规'
  }
  return texts[level] || '正常'
}

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const viewDetails = (record) => {
  console.log('查看详情:', record)
  message.info('查看合规检查详情')
}

const auditRecord = (record) => {
  console.log('审核记录:', record)
  message.info('审核合规记录')
}

const handleViolation = (record) => {
  console.log('处理违规:', record)
  message.info('处理违规事件')
}

const generateReport = (record) => {
  console.log('生成报告:', record)
  message.success('合规报告生成成功')
}

const viewHistory = (record) => {
  console.log('查看历史:', record)
  message.info('查看历史记录')
}

const addToPolicy = (record) => {
  console.log('加入政策:', record)
  message.success('已加入合规政策')
}

const exportRecord = (record) => {
  console.log('导出记录:', record)
  message.success('记录导出成功')
}

const handleDetailSubmit = () => {
  console.log('提交详情:', detailForm)
  message.success('合规检查创建成功')
  showDetailModal.value = false
}

const handleBatchAuditSubmit = () => {
  console.log('批量审核:', batchAuditForm)
  message.success(`已${batchAuditForm.result === 'compliant' ? '批准' : batchAuditForm.result === 'violation' ? '标记违规' : '标记预警'} ${selectedRowKeys.value.length} 个检查项`)
  showBatchAuditModal.value = false
  selectedRowKeys.value = []
}

const exportReport = () => {
  message.success('合规报表导出成功')
}

const generateAlert = () => {
  message.success('合规预警生成成功')
}

const handlePolicySubmit = () => {
  message.success('合规政策保存成功')
  showPolicyModal.value = false
}

const editPolicy = (record) => {
  console.log('编辑政策:', record)
  message.info('编辑合规政策')
}

const viewPolicy = (record) => {
  console.log('查看政策:', record)
  message.info('查看合规政策')
}

const deletePolicy = (record) => {
  console.log('删除政策:', record)
  message.success('政策删除成功')
}

// 初始化
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.chart-section {
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  color: #999;
  font-size: 16px;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.increase {
  color: #52c41a;
}

.decrease {
  color: #ff4d4f;
}

.ant-table {
  background: #fff;
}

.ant-descriptions-title {
  font-weight: 500;
}
</style>