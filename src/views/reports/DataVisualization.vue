<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>数据可视化</h2>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="数据源">
                <a-select v-model:value="searchForm.dataSource" placeholder="请选择数据源" allow-clear>
                  <a-select-option value="all">全部数据</a-select-option>
                  <a-select-option value="cases">案件数据</a-select-option>
                  <a-select-option value="customers">客户数据</a-select-option>
                  <a-select-option value="collection">催收数据</a-select-option>
                  <a-select-option value="payment">还款数据</a-select-option>
                  <a-select-option value="financial">财务数据</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="图表类型">
                <a-select v-model:value="searchForm.chartType" placeholder="请选择图表类型" allow-clear>
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="line">折线图</a-select-option>
                  <a-select-option value="bar">柱状图</a-select-option>
                  <a-select-option value="pie">饼图</a-select-option>
                  <a-select-option value="scatter">散点图</a-select-option>
                  <a-select-option value="heatmap">热力图</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="数据维度">
                  <a-select v-model:value="searchForm.dimension" placeholder="请选择维度" allow-clear>
                    <a-select-option value="time">时间维度</a-select-option>
                    <a-select-option value="region">地区维度</a-select-option>
                    <a-select-option value="department">部门维度</a-select-option>
                    <a-select-option value="product">产品维度</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="分组方式">
                  <a-select v-model:value="searchForm.groupBy" placeholder="请选择分组方式" allow-clear>
                    <a-select-option value="daily">按日分组</a-select-option>
                    <a-select-option value="weekly">按周分组</a-select-option>
                    <a-select-option value="monthly">按月分组</a-select-option>
                    <a-select-option value="quarterly">按季度分组</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="指标类型">
                  <a-select v-model:value="searchForm.metric" placeholder="请选择指标" allow-clear>
                    <a-select-option value="amount">金额</a-select-option>
                    <a-select-option value="count">数量</a-select-option>
                    <a-select-option value="rate">比率</a-select-option>
                    <a-select-option value="trend">趋势</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="刷新频率">
                  <a-select v-model:value="searchForm.refreshRate" placeholder="请选择刷新频率" allow-clear>
                    <a-select-option value="manual">手动刷新</a-select-option>
                    <a-select-option value="5s">5秒</a-select-option>
                    <a-select-option value="30s">30秒</a-select-option>
                    <a-select-option value="1m">1分钟</a-select-option>
                    <a-select-option value="5m">5分钟</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic title="数据源总数" :value="statistics.dataSources" suffix="个" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="图表总数" :value="statistics.totalCharts" suffix="个" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="在线用户" :value="statistics.onlineUsers" suffix="人" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="刷新频率" :value="statistics.refreshRate" suffix="次/分" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索和操作区域 -->
      <a-card class="search-card">
        <div class="search-actions">
          <div class="action-buttons">
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              创建图表
            </a-button>
            <a-button @click="showDashboardModal = true">
              <DashboardOutlined />
              仪表板设置
            </a-button>
            <a-button @click="showDataSourceModal = true">
              <DatabaseOutlined />
              数据源管理
            </a-button>
            <a-button @click="exportDashboard">
              <DownloadOutlined />
              导出仪表板
            </a-button>
            <a-button @click="shareReport">
              <ShareAltOutlined />
              分享报表
            </a-button>
            <a-button @click="toggleFullscreen">
              <FullscreenOutlined />
              全屏模式
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 快捷选择器 -->
      <a-card class="quick-selector">
        <a-row :gutter="16">
          <a-col :span="8">
            <h4>预设模板</h4>
            <a-space wrap>
              <a-button 
                v-for="template in predefinedTemplates" 
                :key="template.id"
                :type="selectedTemplate === template.id ? 'primary' : 'default'"
                @click="selectTemplate(template.id)"
                size="small"
              >
                {{ template.name }}
              </a-button>
            </a-space>
          </a-col>
          <a-col :span="8">
            <h4>常用图表</h4>
            <a-space wrap>
              <a-button 
                v-for="chart in commonCharts" 
                :key="chart.id"
                @click="addChart(chart)"
                size="small"
              >
                <component :is="chart.icon" />
                {{ chart.name }}
              </a-button>
            </a-space>
          </a-col>
          <a-col :span="8">
            <h4>视图切换</h4>
            <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
              <a-radio-button value="grid">网格视图</a-radio-button>
              <a-radio-button value="list">列表视图</a-radio-button>
              <a-radio-button value="large">大屏视图</a-radio-button>
            </a-radio-group>
          </a-col>
        </a-row>
      </a-card>

      <!-- 图表展示区域 -->
      <div class="chart-container" :class="`view-${viewMode}`">
        <a-row :gutter="[16, 16]" v-if="viewMode === 'grid'">
          <a-col :span="12" v-for="chart in chartList" :key="chart.id">
            <a-card 
              :title="chart.title" 
              :extra="chartExtra(chart)"
              class="chart-card"
            >
              <div :ref="`chart_${chart.id}`" :style="{ height: chart.height || '300px' }">
                <div class="chart-placeholder">
                  <component :is="chart.icon" style="font-size: 24px; margin-bottom: 8px;" />
                  <div>{{ chart.description }}</div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <div v-else-if="viewMode === 'list'" class="list-view">
          <a-list 
            :data-source="chartList" 
            item-layout="vertical"
            :pagination="{ pageSize: 5 }"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button type="link" @click="editChart(item)">编辑</a-button>
                  <a-button type="link" @click="viewChart(item)">查看</a-button>
                  <a-button type="link" @click="deleteChart(item)" danger>删除</a-button>
                </template>
                <a-list-item-meta>
                  <template #title>{{ item.title }}</template>
                  <template #description>{{ item.description }}</template>
                  <template #avatar>
                    <a-avatar>
                      <component :is="item.icon" />
                    </a-avatar>
                  </template>
                </a-list-item-meta>
                <div class="list-chart-content">
                  <div :ref="`chart_list_${item.id}`" style="height: 200px;">
                    <div class="chart-placeholder">
                      {{ item.type }} 图表预览
                    </div>
                  </div>
                </div>
              </a-list-item>
            </template>
          </a-list>
        </div>

        <div v-else-if="viewMode === 'large'" class="large-view">
          <a-row :gutter="[16, 16]">
            <a-col :span="24">
              <a-card title="实时数据大屏" class="large-screen-card">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <div class="large-chart" ref="realTimeChart">
                      <div class="chart-placeholder">📊 实时数据流</div>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div class="large-chart" ref="trendChart">
                      <div class="chart-placeholder">📈 趋势分析</div>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div class="large-chart" ref="distributionChart">
                      <div class="chart-placeholder">📊 分布统计</div>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 创建图表弹窗 -->
      <a-modal
        v-model:open="showCreateModal"
        title="创建图表"
        width="800px"
        @ok="handleCreateSubmit"
        @cancel="showCreateModal = false"
      >
        <a-form ref="createFormRef" :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="图表标题" name="title" :rules="[{ required: true, message: '请输入图表标题' }]">
            <a-input v-model:value="createForm.title" placeholder="请输入图表标题" />
          </a-form-item>
          
          <a-form-item label="图表类型" name="type" :rules="[{ required: true, message: '请选择图表类型' }]">
            <a-radio-group v-model:value="createForm.type">
              <a-radio value="line">折线图</a-radio>
              <a-radio value="bar">柱状图</a-radio>
              <a-radio value="pie">饼图</a-radio>
              <a-radio value="scatter">散点图</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="数据源" name="dataSource" :rules="[{ required: true, message: '请选择数据源' }]">
            <a-select v-model:value="createForm.dataSource" placeholder="请选择数据源">
              <a-select-option value="cases">案件数据</a-select-option>
              <a-select-option value="customers">客户数据</a-select-option>
              <a-select-option value="collection">催收数据</a-select-option>
              <a-select-option value="payment">还款数据</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="X轴字段" name="xField" :rules="[{ required: true, message: '请选择X轴字段' }]">
            <a-select v-model:value="createForm.xField" placeholder="请选择X轴字段">
              <a-select-option value="date">日期</a-select-option>
              <a-select-option value="department">部门</a-select-option>
              <a-select-option value="region">地区</a-select-option>
              <a-select-option value="product">产品</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="Y轴字段" name="yField" :rules="[{ required: true, message: '请选择Y轴字段' }]">
            <a-select v-model:value="createForm.yField" placeholder="请选择Y轴字段">
              <a-select-option value="amount">金额</a-select-option>
              <a-select-option value="count">数量</a-select-option>
              <a-select-option value="rate">比率</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="聚合方式" name="aggregation">
            <a-select v-model:value="createForm.aggregation" placeholder="请选择聚合方式">
              <a-select-option value="sum">求和</a-select-option>
              <a-select-option value="avg">平均值</a-select-option>
              <a-select-option value="count">计数</a-select-option>
              <a-select-option value="max">最大值</a-select-option>
              <a-select-option value="min">最小值</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="图表描述" name="description">
            <a-textarea v-model:value="createForm.description" :rows="3" placeholder="请输入图表描述" />
          </a-form-item>
          
          <a-form-item label="高级设置">
            <a-space direction="vertical" style="width: 100%;">
              <a-checkbox v-model:checked="createForm.showLegend">显示图例</a-checkbox>
              <a-checkbox v-model:checked="createForm.showTooltip">显示提示框</a-checkbox>
              <a-checkbox v-model:checked="createForm.enableZoom">支持缩放</a-checkbox>
              <a-checkbox v-model:checked="createForm.autoRefresh">自动刷新</a-checkbox>
            </a-space>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 仪表板设置弹窗 -->
      <a-modal
        v-model:open="showDashboardModal"
        title="仪表板设置"
        width="600px"
        @ok="handleDashboardSubmit"
        @cancel="showDashboardModal = false"
      >
        <a-form ref="dashboardFormRef" :model="dashboardForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="仪表板名称" name="name" :rules="[{ required: true, message: '请输入仪表板名称' }]">
            <a-input v-model:value="dashboardForm.name" placeholder="请输入仪表板名称" />
          </a-form-item>
          
          <a-form-item label="布局模式" name="layout">
            <a-radio-group v-model:value="dashboardForm.layout">
              <a-radio value="grid">网格布局</a-radio>
              <a-radio value="flow">流式布局</a-radio>
              <a-radio value="fixed">固定布局</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="主题配色" name="theme">
            <a-select v-model:value="dashboardForm.theme" placeholder="请选择主题">
              <a-select-option value="light">浅色主题</a-select-option>
              <a-select-option value="dark">深色主题</a-select-option>
              <a-select-option value="blue">商务蓝</a-select-option>
              <a-select-option value="green">清新绿</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="自动刷新间隔" name="refreshInterval">
            <a-select v-model:value="dashboardForm.refreshInterval" placeholder="请选择刷新间隔">
              <a-select-option value="0">不自动刷新</a-select-option>
              <a-select-option value="30">30秒</a-select-option>
              <a-select-option value="60">1分钟</a-select-option>
              <a-select-option value="300">5分钟</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="权限设置" name="permissions">
            <a-checkbox-group v-model:value="dashboardForm.permissions">
              <a-checkbox value="view">查看权限</a-checkbox>
              <a-checkbox value="edit">编辑权限</a-checkbox>
              <a-checkbox value="share">分享权限</a-checkbox>
              <a-checkbox value="export">导出权限</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 数据源管理弹窗 -->
      <a-modal
        v-model:open="showDataSourceModal"
        title="数据源管理"
        width="1000px"
        @ok="handleDataSourceSubmit"
        @cancel="showDataSourceModal = false"
      >
        <a-table
          :columns="dataSourceColumns"
          :data-source="dataSourceList"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'connected' ? 'green' : 'red'">
                {{ record.status === 'connected' ? '已连接' : '连接失败' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="testConnection(record)">测试连接</a-button>
                <a-button type="link" size="small" @click="editDataSource(record)">编辑</a-button>
                <a-button type="link" size="small" danger @click="deleteDataSource(record)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  DownOutlined, 
  PlusOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  FullscreenOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DotChartOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)
const selectedTemplate = ref('overview')
const viewMode = ref('grid')

// 弹窗状态
const showCreateModal = ref(false)
const showDashboardModal = ref(false)
const showDataSourceModal = ref(false)

// 搜索表单
const searchForm = reactive({
  dataSource: undefined,
  chartType: undefined,
  dateRange: [],
  dimension: undefined,
  groupBy: undefined,
  metric: undefined,
  refreshRate: undefined
})

// 统计数据
const statistics = reactive({
  dataSources: 8,
  totalCharts: 24,
  onlineUsers: 156,
  refreshRate: 12
})

// 创建表单
const createForm = reactive({
  title: '',
  type: undefined,
  dataSource: undefined,
  xField: undefined,
  yField: undefined,
  aggregation: undefined,
  description: '',
  showLegend: true,
  showTooltip: true,
  enableZoom: false,
  autoRefresh: false
})

// 仪表板表单
const dashboardForm = reactive({
  name: '',
  layout: 'grid',
  theme: 'light',
  refreshInterval: '0',
  permissions: ['view']
})

// 预设模板
const predefinedTemplates = ref([
  { id: 'overview', name: '总览仪表板' },
  { id: 'collection', name: '催收分析' },
  { id: 'financial', name: '财务报表' },
  { id: 'performance', name: '绩效监控' },
  { id: 'risk', name: '风险预警' }
])

// 常用图表
const commonCharts = ref([
  { id: 'trend', name: '趋势图', icon: LineChartOutlined, type: 'line' },
  { id: 'compare', name: '对比图', icon: BarChartOutlined, type: 'bar' },
  { id: 'distribution', name: '分布图', icon: PieChartOutlined, type: 'pie' },
  { id: 'correlation', name: '相关图', icon: DotChartOutlined, type: 'scatter' }
])

// 图表列表
const chartList = ref([
  {
    id: 1,
    title: '催收效果趋势',
    type: 'line',
    icon: LineChartOutlined,
    description: '展示近期催收效果变化趋势',
    height: '300px'
  },
  {
    id: 2,
    title: '部门业绩对比',
    type: 'bar',
    icon: BarChartOutlined,
    description: '各部门催收业绩对比分析',
    height: '300px'
  },
  {
    id: 3,
    title: '客户类型分布',
    type: 'pie',
    icon: PieChartOutlined,
    description: '不同类型客户分布情况',
    height: '300px'
  },
  {
    id: 4,
    title: '风险评分分析',
    type: 'scatter',
    icon: DotChartOutlined,
    description: '客户风险评分与回收率关系',
    height: '300px'
  }
])

// 数据源列表
const dataSourceList = ref([
  {
    id: 1,
    name: '案件数据库',
    type: 'MySQL',
    host: '*************',
    status: 'connected',
    lastUpdate: '2024-01-15 10:30'
  },
  {
    id: 2,
    name: '客户数据仓库',
    type: 'PostgreSQL',
    host: '*************',
    status: 'connected',
    lastUpdate: '2024-01-15 10:25'
  },
  {
    id: 3,
    name: '实时数据流',
    type: 'Kafka',
    host: '*************',
    status: 'disconnected',
    lastUpdate: '2024-01-15 09:45'
  }
])

// 数据源表格列
const dataSourceColumns = [
  { title: '数据源名称', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '地址', dataIndex: 'host', key: 'host' },
  { title: '状态', key: 'status' },
  { title: '最后更新', dataIndex: 'lastUpdate', key: 'lastUpdate' },
  { title: '操作', key: 'action', width: 200 }
]

// 图表额外操作
const chartExtra = (chart) => {
  return h('a-space', [
    h('a-button', { 
      type: 'link', 
      size: 'small',
      onClick: () => editChart(chart) 
    }, '编辑'),
    h('a-button', { 
      type: 'link', 
      size: 'small',
      onClick: () => viewChart(chart) 
    }, '查看'),
    h('a-button', { 
      type: 'link', 
      size: 'small', 
      danger: true,
      onClick: () => deleteChart(chart) 
    }, '删除')
  ])
}

// 事件处理方法
const handleSearch = () => {
  loading.value = true
  console.log('搜索参数:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

const selectTemplate = (templateId) => {
  selectedTemplate.value = templateId
  message.success(`已切换到${predefinedTemplates.value.find(t => t.id === templateId)?.name}`)
}

const addChart = (chart) => {
  console.log('添加图表:', chart)
  message.success(`已添加${chart.name}`)
}

const editChart = (chart) => {
  console.log('编辑图表:', chart)
  message.info('编辑图表功能')
}

const viewChart = (chart) => {
  console.log('查看图表:', chart)
  message.info('查看图表详情')
}

const deleteChart = (chart) => {
  console.log('删除图表:', chart)
  message.success('图表删除成功')
}

const handleCreateSubmit = () => {
  console.log('创建图表:', createForm)
  message.success('图表创建成功')
  showCreateModal.value = false
}

const handleDashboardSubmit = () => {
  console.log('仪表板设置:', dashboardForm)
  message.success('仪表板设置保存成功')
  showDashboardModal.value = false
}

const handleDataSourceSubmit = () => {
  message.success('数据源配置保存成功')
  showDataSourceModal.value = false
}

const exportDashboard = () => {
  message.success('仪表板导出成功')
}

const shareReport = () => {
  message.success('报表分享链接已生成')
}

const toggleFullscreen = () => {
  message.info('切换全屏模式')
}

const testConnection = (record) => {
  console.log('测试连接:', record)
  message.success('连接测试成功')
}

const editDataSource = (record) => {
  console.log('编辑数据源:', record)
  message.info('编辑数据源')
}

const deleteDataSource = (record) => {
  console.log('删除数据源:', record)
  message.success('数据源删除成功')
}

// 初始化
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.quick-selector {
  margin-bottom: 16px;
}

.quick-selector h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

.chart-container {
  min-height: 400px;
}

.chart-card {
  height: 100%;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  color: #999;
  font-size: 14px;
}

.list-view {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.list-chart-content {
  margin-top: 16px;
}

.large-view {
  background: white;
  border-radius: 8px;
}

.large-screen-card {
  min-height: 600px;
}

.large-chart {
  height: 280px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.view-grid .ant-col {
  margin-bottom: 16px;
}

.view-list {
  background: white;
  border-radius: 8px;
}

.view-large {
  background: transparent;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.ant-table {
  background: #fff;
}

.ant-descriptions-title {
  font-weight: 500;
}
</style>