<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>财务报表</h2>
      
      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="报表类型">
                <a-select v-model:value="searchForm.reportType" placeholder="请选择报表类型" allow-clear>
                  <a-select-option value="summary">财务汇总</a-select-option>
                  <a-select-option value="recovery">回收分析</a-select-option>
                  <a-select-option value="aging">账龄分析</a-select-option>
                  <a-select-option value="provision">计提分析</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  :presets="datePresets"
                  @change="handleDateChange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="业务部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="all">全部部门</a-select-option>
                  <a-select-option value="dept1">催收一部</a-select-option>
                  <a-select-option value="dept2">催收二部</a-select-option>
                  <a-select-option value="dept3">法务部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="exportReport">
                      <download-outlined />
                      导出报表
                    </a-button>
                    <a-button @click="printReport">
                      <printer-outlined />
                      打印报表
                    </a-button>
                    <a-button @click="scheduleReport">
                      <schedule-outlined />
                      定时报表
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 财务概览卡片 -->
      <a-row :gutter="16" class="overview-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总委案金额" 
              :value="overviewData.totalAmount" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="累计回收金额" 
              :value="overviewData.recoveredAmount" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总体回收率" 
              :value="overviewData.recoveryRate" 
              :precision="2" 
              suffix="%"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="本期收益" 
              :value="overviewData.currentProfit" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表区域 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="月度回收趋势" :body-style="{ padding: '20px' }">
            <div ref="recoveryTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="回收率分布" :body-style="{ padding: '20px' }">
            <div ref="recoveryRateChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="账龄分析" :body-style="{ padding: '20px' }">
            <div ref="agingAnalysisChart" style="height: 400px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细数据表格 -->
      <a-card title="财务明细数据">
        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              ¥{{ record.amount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'recoveredAmount'">
              ¥{{ record.recoveredAmount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'recoveryRate'">
              <a-progress 
                :percent="record.recoveryRate" 
                size="small" 
                :stroke-color="getProgressColor(record.recoveryRate)"
              />
            </template>
            <template v-if="column.key === 'profit'">
              <span :style="{ color: record.profit >= 0 ? '#52c41a' : '#ff4d4f' }">
                ¥{{ record.profit?.toLocaleString() }}
              </span>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 财务分析 -->
      <a-card title="财务分析" class="analysis-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>收益分析</h4>
            <a-descriptions size="small" bordered>
              <a-descriptions-item label="总收入" span="2">
                ¥{{ analysisData.totalRevenue.toLocaleString() }}
              </a-descriptions-item>
              <a-descriptions-item label="总成本" span="2">
                ¥{{ analysisData.totalCost.toLocaleString() }}
              </a-descriptions-item>
              <a-descriptions-item label="净收益" span="2">
                <span :style="{ color: analysisData.netProfit >= 0 ? '#52c41a' : '#ff4d4f' }">
                  ¥{{ analysisData.netProfit.toLocaleString() }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="ROI" span="2">
                {{ analysisData.roi }}%
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="12">
            <h4>风险提示</h4>
            <a-list size="small" :data-source="riskAlerts">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <span :style="{ color: getRiskColor(item.level) }">
                        {{ item.title }}
                      </span>
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined, PrinterOutlined, ScheduleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  reportType: 'summary',
  dateRange: [dayjs().subtract(1, 'month'), dayjs()],
  department: 'all'
})

// 日期预设
const datePresets = {
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '上月': [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
  '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
  '本年': [dayjs().startOf('year'), dayjs().endOf('year')]
}

// 概览数据
const overviewData = reactive({
  totalAmount: 12580000,
  recoveredAmount: 8456000,
  recoveryRate: 67.25,
  currentProfit: 2341000
})

// 分析数据
const analysisData = reactive({
  totalRevenue: 8456000,
  totalCost: 1234000,
  netProfit: 7222000,
  roi: 23.45
})

// 风险提示
const riskAlerts = ref([
  {
    level: 'high',
    title: '高风险案件增长',
    description: '本月新增高风险案件较上月增长15%，需要加强风控措施'
  },
  {
    level: 'medium',
    title: '回收率下降',
    description: '部分区域回收率较上月下降3%，建议调整催收策略'
  },
  {
    level: 'low',
    title: '成本控制良好',
    description: '本月催收成本控制在预算范围内，成本效益比较理想'
  }
])

// 表格配置
const columns = [
  {
    title: '时间',
    dataIndex: 'period',
    key: 'period',
    width: 100,
    fixed: 'left'
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '委案金额',
    key: 'amount',
    width: 120
  },
  {
    title: '回收金额',
    key: 'recoveredAmount',
    width: 120
  },
  {
    title: '回收率',
    key: 'recoveryRate',
    width: 120
  },
  {
    title: '催收成本',
    dataIndex: 'cost',
    key: 'cost',
    width: 120
  },
  {
    title: '净收益',
    key: 'profit',
    width: 120
  },
  {
    title: '案件数量',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    period: '2024-01',
    department: '催收一部',
    amount: 2580000,
    recoveredAmount: 1856000,
    recoveryRate: 71.9,
    cost: 234000,
    profit: 1622000,
    caseCount: 245,
    status: 'completed'
  },
  {
    id: 2,
    period: '2024-01',
    department: '催收二部',
    amount: 3240000,
    recoveredAmount: 2187000,
    recoveryRate: 67.5,
    cost: 298000,
    profit: 1889000,
    caseCount: 312,
    status: 'completed'
  },
  {
    id: 3,
    period: '2024-01',
    department: '法务部',
    amount: 1890000,
    recoveredAmount: 1234000,
    recoveryRate: 65.3,
    cost: 189000,
    profit: 1045000,
    caseCount: 156,
    status: 'in_progress'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const recoveryTrendChart = ref()
const recoveryRateChart = ref()
const agingAnalysisChart = ref()

// 状态相关方法
const getStatusColor = (status) => {
  const colors = {
    completed: 'green',
    in_progress: 'blue',
    pending: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    in_progress: '进行中',
    pending: '待开始'
  }
  return texts[status] || '未知'
}

const getProgressColor = (rate) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  return '#ff4d4f'
}

const getRiskColor = (level) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[level] || '#666'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('报表数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.reportType = 'summary'
  searchForm.dateRange = [dayjs().subtract(1, 'month'), dayjs()]
  searchForm.department = 'all'
  handleSearch()
}

const handleDateChange = (dates) => {
  if (dates) {
    console.log('日期范围变更:', dates)
    // 可以在这里触发数据更新
  }
}

const exportReport = () => {
  message.success('报表导出成功')
}

const printReport = () => {
  message.success('报表打印成功')
}

const scheduleReport = () => {
  message.info('定时报表设置功能')
}

// 图表初始化
const initCharts = () => {
  // 回收趋势图
  const recoveryChart = echarts.init(recoveryTrendChart.value)
  const recoveryOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '金额(万元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '回收率(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '委案金额',
        type: 'bar',
        data: [1200, 1350, 1180, 1420, 1580, 1390],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '回收金额',
        type: 'bar',
        data: [780, 892, 756, 923, 1034, 890],
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '回收率',
        type: 'line',
        yAxisIndex: 1,
        data: [65, 66.1, 64.1, 65.0, 65.4, 64.0],
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  recoveryChart.setOption(recoveryOption)

  // 回收率分布图
  const rateChart = echarts.init(recoveryRateChart.value)
  const rateOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '回收率区间',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '80%以上' },
          { value: 28, name: '60%-80%' },
          { value: 22, name: '40%-60%' },
          { value: 15, name: '40%以下' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  rateChart.setOption(rateOption)

  // 账龄分析图
  const agingChart = echarts.init(agingAnalysisChart.value)
  const agingOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-30天', '31-60天', '61-90天', '91-180天', '181-365天', '365天以上']
    },
    yAxis: [
      {
        type: 'value',
        name: '金额(万元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '案件数量',
        position: 'right'
      }
    ],
    series: [
      {
        name: '委案金额',
        type: 'bar',
        data: [2580, 1890, 1456, 980, 675, 234],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '回收金额',
        type: 'bar',
        data: [1890, 1234, 892, 567, 234, 89],
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '案件数量',
        type: 'line',
        yAxisIndex: 1,
        data: [245, 189, 156, 98, 67, 23],
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  agingChart.setOption(agingOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    recoveryChart.resize()
    rateChart.resize()
    agingChart.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.overview-cards {
  margin-bottom: 16px;
}

.chart-section {
  margin-bottom: 16px;
}

.analysis-card {
  margin-top: 16px;
}

.analysis-card h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}
</style>
