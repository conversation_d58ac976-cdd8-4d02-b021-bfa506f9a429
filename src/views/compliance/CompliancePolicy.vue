<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>合规政策管理</h2>
      
      <!-- 搜索条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="政策类型">
                <a-select v-model:value="searchForm.type" placeholder="请选择政策类型" allow-clear>
                  <a-select-option value="operation">操作合规</a-select-option>
                  <a-select-option value="data">数据保护</a-select-option>
                  <a-select-option value="legal">法律法规</a-select-option>
                  <a-select-option value="financial">财务合规</a-select-option>
                  <a-select-option value="risk">风险管控</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="draft">草稿</a-select-option>
                  <a-select-option value="review">审核中</a-select-option>
                  <a-select-option value="approved">已生效</a-select-option>
                  <a-select-option value="expired">已过期</a-select-option>
                  <a-select-option value="revoked">已撤销</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="生效时间">
                <a-range-picker 
                  v-model:value="searchForm.effectiveDate" 
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="关键词">
                <a-input 
                  v-model:value="searchForm.keyword" 
                  placeholder="请输入政策名称或关键词"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button type="primary" @click="openCreateModal">
                      <plus-outlined />
                      新建政策
                    </a-button>
                    <a-button @click="openTemplateModal">
                      <file-text-outlined />
                      政策模板
                    </a-button>
                    <a-button @click="openVersionModal">
                      <branches-outlined />
                      版本管理
                    </a-button>
                    <a-button @click="exportPolicies">
                      <download-outlined />
                      导出
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总政策数" 
              :value="stats.total" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="生效中" 
              :value="stats.approved" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="审核中" 
              :value="stats.review" 
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="即将过期" 
              :value="stats.expiring" 
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 政策列表 -->
      <a-card title="政策列表">
        <template #extra>
          <a-space>
            <a-button @click="refreshData">
              <reload-outlined />
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleBatchAction">
                  <a-menu-item key="approve">批量审批</a-menu-item>
                  <a-menu-item key="expire">批量过期</a-menu-item>
                  <a-menu-item key="delete">批量删除</a-menu-item>
                </a-menu>
              </template>
              <a-button>
                批量操作 <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>

        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRows, onChange: onSelectChange }"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'effectiveDate'">
              {{ record.effectiveDate }}
              <a-tag v-if="isExpiringSoon(record.expiryDate)" color="red" size="small">
                即将过期
              </a-tag>
            </template>
            <template v-if="column.key === 'version'">
              <a-tooltip :title="`版本历史: ${record.versionHistory?.length || 0} 个版本`">
                <span style="cursor: pointer" @click="showVersionHistory(record)">
                  v{{ record.version }}
                </span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewPolicy(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="editPolicy(record)">
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleAction(key, record)">
                      <a-menu-item key="approve">审批</a-menu-item>
                      <a-menu-item key="version">版本管理</a-menu-item>
                      <a-menu-item key="copy">复制</a-menu-item>
                      <a-menu-item key="revoke">撤销</a-menu-item>
                      <a-menu-item key="delete">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建/编辑政策弹窗 -->
    <a-modal
      v-model:open="policyModalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handlePolicySubmit"
      @cancel="handlePolicyCancel"
    >
      <a-form :model="policyForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="政策名称" required>
          <a-input v-model:value="policyForm.name" placeholder="请输入政策名称" />
        </a-form-item>
        <a-form-item label="政策类型" required>
          <a-select v-model:value="policyForm.type" placeholder="请选择政策类型">
            <a-select-option value="operation">操作合规</a-select-option>
            <a-select-option value="data">数据保护</a-select-option>
            <a-select-option value="legal">法律法规</a-select-option>
            <a-select-option value="financial">财务合规</a-select-option>
            <a-select-option value="risk">风险管控</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="适用范围">
          <a-select v-model:value="policyForm.scope" mode="multiple" placeholder="请选择适用范围">
            <a-select-option value="all">全公司</a-select-option>
            <a-select-option value="collection">催收部门</a-select-option>
            <a-select-option value="legal">法务部门</a-select-option>
            <a-select-option value="finance">财务部门</a-select-option>
            <a-select-option value="risk">风控部门</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级">
          <a-select v-model:value="policyForm.priority" placeholder="请选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="生效日期" required>
          <a-date-picker v-model:value="policyForm.effectiveDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="过期日期">
          <a-date-picker v-model:value="policyForm.expiryDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="政策内容" required>
          <a-textarea 
            v-model:value="policyForm.content" 
            :rows="6" 
            placeholder="请输入政策内容" 
          />
        </a-form-item>
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="policyForm.attachments"
            name="file"
            :before-upload="beforeUpload"
          >
            <a-button>
              <upload-outlined />
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea 
            v-model:value="policyForm.remark" 
            :rows="3" 
            placeholder="请输入备注信息" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 政策详情弹窗 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="政策详情"
      width="800px"
      :footer="null"
    >
      <a-descriptions bordered size="small" v-if="currentPolicy">
        <a-descriptions-item label="政策名称" :span="2">
          {{ currentPolicy.name }}
        </a-descriptions-item>
        <a-descriptions-item label="版本">
          v{{ currentPolicy.version }}
        </a-descriptions-item>
        <a-descriptions-item label="政策类型">
          <a-tag :color="getTypeColor(currentPolicy.type)">
            {{ getTypeText(currentPolicy.type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(currentPolicy.status)">
            {{ getStatusText(currentPolicy.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="getPriorityColor(currentPolicy.priority)">
            {{ getPriorityText(currentPolicy.priority) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="适用范围" :span="3">
          <a-tag v-for="scope in currentPolicy.scope" :key="scope" style="margin-right: 8px">
            {{ getScopeText(scope) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="生效日期">
          {{ currentPolicy.effectiveDate }}
        </a-descriptions-item>
        <a-descriptions-item label="过期日期">
          {{ currentPolicy.expiryDate || '永久有效' }}
        </a-descriptions-item>
        <a-descriptions-item label="创建人">
          {{ currentPolicy.creator }}
        </a-descriptions-item>
        <a-descriptions-item label="政策内容" :span="3">
          <div style="white-space: pre-wrap">{{ currentPolicy.content }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="附件" :span="3" v-if="currentPolicy.attachments?.length">
          <a-space>
            <a-button 
              v-for="file in currentPolicy.attachments" 
              :key="file.uid" 
              type="link" 
              @click="downloadFile(file)"
            >
              {{ file.name }}
            </a-button>
          </a-space>
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="3" v-if="currentPolicy.remark">
          {{ currentPolicy.remark }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 审批弹窗 -->
    <a-modal
      v-model:open="approvalModalVisible"
      title="政策审批"
      @ok="handleApprovalSubmit"
      @cancel="approvalModalVisible = false"
    >
      <a-form :model="approvalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="审批结果" required>
          <a-radio-group v-model:value="approvalForm.result">
            <a-radio value="approved">通过</a-radio>
            <a-radio value="rejected">拒绝</a-radio>
            <a-radio value="pending">待定</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审批意见" required>
          <a-textarea 
            v-model:value="approvalForm.comment" 
            :rows="4" 
            placeholder="请输入审批意见" 
          />
        </a-form-item>
        <a-form-item label="生效日期" v-if="approvalForm.result === 'approved'">
          <a-date-picker v-model:value="approvalForm.effectiveDate" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 版本历史弹窗 -->
    <a-modal
      v-model:open="versionModalVisible"
      title="版本历史"
      width="1000px"
      :footer="null"
    >
      <a-timeline v-if="versionHistory.length">
        <a-timeline-item 
          v-for="version in versionHistory" 
          :key="version.id"
          :color="version.isCurrent ? 'green' : 'blue'"
        >
          <template #dot>
            <crown-outlined v-if="version.isCurrent" style="color: green" />
          </template>
          <div class="version-item">
            <div class="version-header">
              <h4>v{{ version.version }} - {{ version.title }}</h4>
              <a-tag v-if="version.isCurrent" color="green">当前版本</a-tag>
            </div>
            <p><strong>更新时间:</strong> {{ version.updateTime }}</p>
            <p><strong>更新人:</strong> {{ version.updater }}</p>
            <p><strong>更新说明:</strong> {{ version.description }}</p>
            <div v-if="version.changes?.length">
              <strong>主要变更:</strong>
              <ul>
                <li v-for="change in version.changes" :key="change">{{ change }}</li>
              </ul>
            </div>
            <a-space style="margin-top: 8px">
              <a-button type="link" size="small" @click="compareVersions(version)">
                对比差异
              </a-button>
              <a-button type="link" size="small" @click="restoreVersion(version)">
                恢复此版本
              </a-button>
              <a-button type="link" size="small" @click="downloadVersion(version)">
                下载
              </a-button>
            </a-space>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-modal>

    <!-- 政策模板弹窗 -->
    <a-modal
      v-model:open="templateModalVisible"
      title="政策模板管理"
      width="900px"
      :footer="null"
    >
      <div class="template-container">
        <div class="template-actions" style="margin-bottom: 16px">
          <a-button type="primary" @click="createTemplate">
            <plus-outlined />
            新建模板
          </a-button>
        </div>
        <a-row :gutter="16">
          <a-col :span="8" v-for="template in templates" :key="template.id">
            <a-card 
              :title="template.name" 
              size="small"
              class="template-card"
              @click="useTemplate(template)"
            >
              <template #extra>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleTemplateAction(key, template)">
                      <a-menu-item key="edit">编辑</a-menu-item>
                      <a-menu-item key="copy">复制</a-menu-item>
                      <a-menu-item key="delete">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="text" size="small">
                    <ellipsis-outlined />
                  </a-button>
                </a-dropdown>
              </template>
              <p>{{ template.description }}</p>
              <div class="template-meta">
                <a-tag :color="getTypeColor(template.type)">
                  {{ getTypeText(template.type) }}
                </a-tag>
                <span style="color: #999; font-size: 12px">
                  {{ template.usageCount }} 次使用
                </span>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined, 
  DownloadOutlined,
  FileTextOutlined,
  BranchesOutlined,
  DownOutlined,
  UploadOutlined,
  CrownOutlined,
  EllipsisOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  type: undefined,
  status: undefined,
  effectiveDate: undefined,
  keyword: ''
})

// 统计数据
const stats = reactive({
  total: 156,
  approved: 89,
  review: 23,
  expiring: 8
})

// 表格配置
const columns = [
  {
    title: '政策名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '类型',
    key: 'type',
    width: 120
  },
  {
    title: '版本',
    key: 'version',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 100
  },
  {
    title: '生效日期',
    key: 'effectiveDate',
    width: 120
  },
  {
    title: '过期日期',
    dataIndex: 'expiryDate',
    key: 'expiryDate',
    width: 120
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '催收作业合规操作指引',
    type: 'operation',
    version: '2.1',
    status: 'approved',
    priority: 'high',
    effectiveDate: '2024-01-15',
    expiryDate: '2024-12-31',
    creator: '张三',
    createTime: '2024-01-10 10:00:00',
    content: '本指引规范了催收作业的合规操作流程...',
    scope: ['collection', 'legal'],
    versionHistory: [
      { id: 1, version: '2.1', title: '优化流程细节', isCurrent: true },
      { id: 2, version: '2.0', title: '重大版本更新', isCurrent: false }
    ]
  },
  {
    id: 2,
    name: '客户数据保护政策',
    type: 'data',
    version: '1.3',
    status: 'review',
    priority: 'high',
    effectiveDate: '2024-02-01',
    expiryDate: '2025-01-31',
    creator: '李四',
    createTime: '2024-01-20 14:30:00',
    content: '本政策规定了客户数据收集、存储、使用和保护的规范...',
    scope: ['all'],
    versionHistory: [
      { id: 1, version: '1.3', title: '增加GDPR合规要求', isCurrent: true }
    ]
  },
  {
    id: 3,
    name: '金融监管合规管理办法',
    type: 'legal',
    version: '3.0',
    status: 'approved',
    priority: 'high',
    effectiveDate: '2024-01-01',
    expiryDate: '2024-12-31',
    creator: '王五',
    createTime: '2023-12-15 16:45:00',
    content: '根据最新金融监管要求制定的合规管理办法...',
    scope: ['all'],
    versionHistory: [
      { id: 1, version: '3.0', title: '适应新监管要求', isCurrent: true }
    ]
  },
  {
    id: 4,
    name: '财务报告合规标准',
    type: 'financial',
    version: '1.2',
    status: 'expired',
    priority: 'medium',
    effectiveDate: '2023-01-01',
    expiryDate: '2023-12-31',
    creator: '赵六',
    createTime: '2022-12-10 11:20:00',
    content: '财务报告编制和披露的合规标准...',
    scope: ['finance'],
    versionHistory: [
      { id: 1, version: '1.2', title: '更新报告格式', isCurrent: true }
    ]
  },
  {
    id: 5,
    name: '风险控制管理制度',
    type: 'risk',
    version: '2.5',
    status: 'approved',
    priority: 'high',
    effectiveDate: '2024-01-01',
    expiryDate: '2024-06-30',
    creator: '孙七',
    createTime: '2023-12-25 09:15:00',
    content: '全面的风险识别、评估和控制管理制度...',
    scope: ['risk', 'collection'],
    versionHistory: [
      { id: 1, version: '2.5', title: '完善风险评估模型', isCurrent: true }
    ]
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 弹窗控制
const policyModalVisible = ref(false)
const viewModalVisible = ref(false)
const approvalModalVisible = ref(false)
const versionModalVisible = ref(false)
const templateModalVisible = ref(false)

// 表单数据
const policyForm = reactive({
  id: null,
  name: '',
  type: undefined,
  scope: [],
  priority: undefined,
  effectiveDate: undefined,
  expiryDate: undefined,
  content: '',
  attachments: [],
  remark: ''
})

const approvalForm = reactive({
  result: undefined,
  comment: '',
  effectiveDate: undefined
})

// 当前操作的政策
const currentPolicy = ref(null)

// 版本历史
const versionHistory = ref([])

// 模板数据
const templates = ref([
  {
    id: 1,
    name: '数据保护政策模板',
    type: 'data',
    description: '标准的数据保护政策模板，包含基本的数据收集、使用、存储规范',
    usageCount: 23,
    content: '标准模板内容...'
  },
  {
    id: 2,
    name: '操作合规指引模板',
    type: 'operation',
    description: '催收业务操作合规指引模板，规范日常作业流程',
    usageCount: 18,
    content: '标准模板内容...'
  },
  {
    id: 3,
    name: '风险管控制度模板',
    type: 'risk',
    description: '风险识别、评估和控制的标准制度模板',
    usageCount: 15,
    content: '标准模板内容...'
  }
])

// 计算属性
const modalTitle = computed(() => {
  return policyForm.id ? '编辑政策' : '新建政策'
})

// 状态相关方法
const getTypeColor = (type) => {
  const colors = {
    operation: 'blue',
    data: 'green',
    legal: 'purple',
    financial: 'orange',
    risk: 'red'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    operation: '操作合规',
    data: '数据保护',
    legal: '法律法规',
    financial: '财务合规',
    risk: '风险管控'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'default',
    review: 'orange',
    approved: 'green',
    expired: 'red',
    revoked: 'volcano'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    review: '审核中',
    approved: '已生效',
    expired: '已过期',
    revoked: '已撤销'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || '未知'
}

const getScopeText = (scope) => {
  const texts = {
    all: '全公司',
    collection: '催收部门',
    legal: '法务部门',
    finance: '财务部门',
    risk: '风控部门'
  }
  return texts[scope] || scope
}

const isExpiringSoon = (expiryDate) => {
  if (!expiryDate) return false
  const today = dayjs()
  const expiry = dayjs(expiryDate)
  return expiry.diff(today, 'day') <= 30
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    type: undefined,
    status: undefined,
    effectiveDate: undefined,
    keyword: ''
  })
  handleSearch()
}

const refreshData = () => {
  handleSearch()
}

const onSelectChange = (selectedRowKeys) => {
  selectedRows.value = selectedRowKeys
}

const handleBatchAction = ({ key }) => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  console.log('批量操作:', key, selectedRows.value)
  message.success(`批量${key}操作执行完成`)
  selectedRows.value = []
}

// 政策操作
const openCreateModal = () => {
  Object.assign(policyForm, {
    id: null,
    name: '',
    type: undefined,
    scope: [],
    priority: undefined,
    effectiveDate: undefined,
    expiryDate: undefined,
    content: '',
    attachments: [],
    remark: ''
  })
  policyModalVisible.value = true
}

const editPolicy = (record) => {
  Object.assign(policyForm, {
    ...record,
    effectiveDate: record.effectiveDate ? dayjs(record.effectiveDate) : undefined,
    expiryDate: record.expiryDate ? dayjs(record.expiryDate) : undefined
  })
  policyModalVisible.value = true
}

const viewPolicy = (record) => {
  currentPolicy.value = record
  viewModalVisible.value = true
}

const handlePolicySubmit = () => {
  console.log('提交政策:', policyForm)
  // 这里应该调用API保存数据
  message.success(policyForm.id ? '政策更新成功' : '政策创建成功')
  policyModalVisible.value = false
  handleSearch()
}

const handlePolicyCancel = () => {
  policyModalVisible.value = false
}

const handleAction = (action, record) => {
  console.log('执行操作:', action, record)
  
  switch (action) {
    case 'approve':
      currentPolicy.value = record
      approvalModalVisible.value = true
      break
    case 'version':
      showVersionHistory(record)
      break
    case 'copy':
      message.success('政策复制成功')
      break
    case 'revoke':
      message.success('政策撤销成功')
      break
    case 'delete':
      message.success('政策删除成功')
      break
  }
}

// 审批操作
const handleApprovalSubmit = () => {
  console.log('提交审批:', approvalForm)
  message.success('审批结果已提交')
  approvalModalVisible.value = false
  handleSearch()
}

// 版本管理
const showVersionHistory = (record) => {
  currentPolicy.value = record
  versionHistory.value = record.versionHistory || []
  versionModalVisible.value = true
}

const compareVersions = (version) => {
  message.info('版本对比功能')
}

const restoreVersion = (version) => {
  message.success(`已恢复到版本 v${version.version}`)
}

const downloadVersion = (version) => {
  message.success(`版本 v${version.version} 下载成功`)
}

// 模板管理
const openTemplateModal = () => {
  templateModalVisible.value = true
}

const createTemplate = () => {
  message.info('新建模板功能')
}

const useTemplate = (template) => {
  Object.assign(policyForm, {
    id: null,
    name: `${template.name}_${Date.now()}`,
    type: template.type,
    content: template.content,
    scope: [],
    priority: undefined,
    effectiveDate: undefined,
    expiryDate: undefined,
    attachments: [],
    remark: ''
  })
  templateModalVisible.value = false
  policyModalVisible.value = true
  message.success('已应用模板')
}

const handleTemplateAction = (action, template) => {
  console.log('模板操作:', action, template)
  message.success(`模板${action}操作完成`)
}

// 文件处理
const beforeUpload = (file) => {
  const isValidType = ['pdf', 'doc', 'docx', 'txt'].some(type => 
    file.name.toLowerCase().endsWith(type)
  )
  if (!isValidType) {
    message.error('只能上传 PDF、DOC、DOCX、TXT 格式的文件!')
    return false
  }
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    message.error('文件大小不能超过 5MB!')
    return false
  }
  return false // 阻止自动上传
}

const downloadFile = (file) => {
  message.success(`下载文件: ${file.name}`)
}

// 导出功能
const exportPolicies = () => {
  const csvData = tableData.value.map(item => ({
    政策名称: item.name,
    类型: getTypeText(item.type),
    版本: `v${item.version}`,
    状态: getStatusText(item.status),
    优先级: getPriorityText(item.priority),
    生效日期: item.effectiveDate,
    过期日期: item.expiryDate,
    创建人: item.creator,
    创建时间: item.createTime
  }))
  
  console.log('导出数据:', csvData)
  message.success('政策数据导出成功')
}

const openVersionModal = () => {
  message.info('版本管理功能')
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.version-item {
  padding: 12px 0;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.version-header h4 {
  margin: 0;
  color: #1890ff;
}

.template-container {
  min-height: 400px;
}

.template-card {
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-statistic {
  text-align: center;
}
</style>