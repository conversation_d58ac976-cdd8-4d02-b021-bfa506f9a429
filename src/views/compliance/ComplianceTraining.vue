<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>合规培训管理</h2>
      
      <!-- 搜索条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="培训类型">
                <a-select v-model:value="searchForm.type" placeholder="请选择培训类型" allow-clear>
                  <a-select-option value="compliance">合规基础</a-select-option>
                  <a-select-option value="regulation">法规解读</a-select-option>
                  <a-select-option value="operation">操作实务</a-select-option>
                  <a-select-option value="case">案例分析</a-select-option>
                  <a-select-option value="assessment">考核测试</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="培训状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="draft">草稿</a-select-option>
                  <a-select-option value="published">已发布</a-select-option>
                  <a-select-option value="ongoing">进行中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="archived">已归档</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="难度等级">
                <a-select v-model:value="searchForm.difficulty" placeholder="请选择难度" allow-clear>
                  <a-select-option value="beginner">初级</a-select-option>
                  <a-select-option value="intermediate">中级</a-select-option>
                  <a-select-option value="advanced">高级</a-select-option>
                  <a-select-option value="expert">专家</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="创建时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button type="primary" @click="openCreateModal">
                      <plus-outlined />
                      新建培训
                    </a-button>
                    <a-button @click="openCertificationModal">
                      <trophy-outlined />
                      证书管理
                    </a-button>
                    <a-button @click="openReportModal">
                      <bar-chart-outlined />
                      培训报告
                    </a-button>
                    <a-button @click="exportData">
                      <download-outlined />
                      导出数据
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总培训数" 
              :value="stats.total" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中" 
              :value="stats.ongoing" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="完成率" 
              :value="stats.completionRate" 
              suffix="%" 
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均分数" 
              :value="stats.avgScore" 
              suffix="分" 
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 我的培训进度 -->
      <a-card title="我的培训进度" class="progress-card">
        <a-row :gutter="16">
          <a-col :span="8" v-for="progress in myProgress" :key="progress.id">
            <a-card size="small" class="progress-item" @click="viewTraining(progress)">
              <div class="progress-header">
                <h4>{{ progress.title }}</h4>
                <a-tag :color="getStatusColor(progress.status)">
                  {{ getStatusText(progress.status) }}
                </a-tag>
              </div>
              <a-progress 
                :percent="progress.progress" 
                :status="progress.progress === 100 ? 'success' : 'active'"
                size="small" 
              />
              <div class="progress-meta">
                <span>{{ progress.duration }}分钟</span>
                <span>{{ progress.participants }}人参与</span>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>

      <!-- 培训列表 -->
      <a-card title="培训列表">
        <template #extra>
          <a-space>
            <a-button @click="refreshData">
              <reload-outlined />
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleBatchAction">
                  <a-menu-item key="publish">批量发布</a-menu-item>
                  <a-menu-item key="archive">批量归档</a-menu-item>
                  <a-menu-item key="delete">批量删除</a-menu-item>
                </a-menu>
              </template>
              <a-button>
                批量操作 <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>

        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRows, onChange: onSelectChange }"
          :scroll="{ x: 1600 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'difficulty'">
              <a-tag :color="getDifficultyColor(record.difficulty)">
                {{ getDifficultyText(record.difficulty) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'progress'">
              <a-progress :percent="record.completionRate" size="small" />
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewTraining(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="editTraining(record)">
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleAction(key, record)">
                      <a-menu-item key="start">开始培训</a-menu-item>
                      <a-menu-item key="publish">发布</a-menu-item>
                      <a-menu-item key="participants">参与者管理</a-menu-item>
                      <a-menu-item key="statistics">培训统计</a-menu-item>
                      <a-menu-item key="certificate">生成证书</a-menu-item>
                      <a-menu-item key="copy">复制</a-menu-item>
                      <a-menu-item key="archive">归档</a-menu-item>
                      <a-menu-item key="delete">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建/编辑培训弹窗 -->
    <a-modal
      v-model:open="trainingModalVisible"
      :title="modalTitle"
      width="900px"
      @ok="handleTrainingSubmit"
      @cancel="handleTrainingCancel"
    >
      <a-form :model="trainingForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="培训标题" required>
          <a-input v-model:value="trainingForm.title" placeholder="请输入培训标题" />
        </a-form-item>
        <a-form-item label="培训类型" required>
          <a-select v-model:value="trainingForm.type" placeholder="请选择培训类型">
            <a-select-option value="compliance">合规基础</a-select-option>
            <a-select-option value="regulation">法规解读</a-select-option>
            <a-select-option value="operation">操作实务</a-select-option>
            <a-select-option value="case">案例分析</a-select-option>
            <a-select-option value="assessment">考核测试</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="难度等级" required>
          <a-select v-model:value="trainingForm.difficulty" placeholder="请选择难度等级">
            <a-select-option value="beginner">初级</a-select-option>
            <a-select-option value="intermediate">中级</a-select-option>
            <a-select-option value="advanced">高级</a-select-option>
            <a-select-option value="expert">专家</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="培训时长" required>
          <a-input-number 
            v-model:value="trainingForm.duration" 
            :min="1" 
            :max="480" 
            style="width: 100%" 
            placeholder="请输入培训时长（分钟）"
          />
        </a-form-item>
        <a-form-item label="目标人群">
          <a-select v-model:value="trainingForm.targetAudience" mode="multiple" placeholder="请选择目标人群">
            <a-select-option value="all">全员</a-select-option>
            <a-select-option value="new_employee">新员工</a-select-option>
            <a-select-option value="manager">管理层</a-select-option>
            <a-select-option value="collection">催收人员</a-select-option>
            <a-select-option value="legal">法务人员</a-select-option>
            <a-select-option value="risk">风控人员</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="培训讲师">
          <a-input v-model:value="trainingForm.instructor" placeholder="请输入培训讲师" />
        </a-form-item>
        <a-form-item label="开始时间">
          <a-date-picker 
            v-model:value="trainingForm.startDate" 
            show-time 
            format="YYYY-MM-DD HH:mm:ss" 
            style="width: 100%" 
          />
        </a-form-item>
        <a-form-item label="结束时间">
          <a-date-picker 
            v-model:value="trainingForm.endDate" 
            show-time 
            format="YYYY-MM-DD HH:mm:ss" 
            style="width: 100%" 
          />
        </a-form-item>
        <a-form-item label="培训描述" required>
          <a-textarea 
            v-model:value="trainingForm.description" 
            :rows="4" 
            placeholder="请输入培训描述" 
          />
        </a-form-item>
        <a-form-item label="培训内容">
          <a-textarea 
            v-model:value="trainingForm.content" 
            :rows="6" 
            placeholder="请输入详细的培训内容" 
          />
        </a-form-item>
        <a-form-item label="培训材料">
          <a-upload
            v-model:file-list="trainingForm.materials"
            name="file"
            :before-upload="beforeUpload"
            :max-count="10"
          >
            <a-button>
              <upload-outlined />
              上传材料
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item label="考核要求">
          <a-checkbox-group v-model:value="trainingForm.assessmentOptions">
            <a-checkbox value="quiz">在线测试</a-checkbox>
            <a-checkbox value="assignment">作业提交</a-checkbox>
            <a-checkbox value="discussion">讨论参与</a-checkbox>
            <a-checkbox value="attendance">出勤考核</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="及格分数">
          <a-input-number 
            v-model:value="trainingForm.passingScore" 
            :min="0" 
            :max="100" 
            style="width: 100%" 
            placeholder="请输入及格分数"
          />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea 
            v-model:value="trainingForm.remark" 
            :rows="3" 
            placeholder="请输入备注信息" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 培训详情弹窗 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="培训详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="currentTraining">
        <a-row :gutter="16">
          <a-col :span="16">
            <!-- 基本信息 -->
            <a-descriptions bordered size="small" title="培训信息">
              <a-descriptions-item label="培训标题" :span="2">
                {{ currentTraining.title }}
              </a-descriptions-item>
              <a-descriptions-item label="培训编号">
                {{ currentTraining.code }}
              </a-descriptions-item>
              <a-descriptions-item label="培训类型">
                <a-tag :color="getTypeColor(currentTraining.type)">
                  {{ getTypeText(currentTraining.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="难度等级">
                <a-tag :color="getDifficultyColor(currentTraining.difficulty)">
                  {{ getDifficultyText(currentTraining.difficulty) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="培训状态">
                <a-tag :color="getStatusColor(currentTraining.status)">
                  {{ getStatusText(currentTraining.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="培训时长">
                {{ currentTraining.duration }}分钟
              </a-descriptions-item>
              <a-descriptions-item label="培训讲师">
                {{ currentTraining.instructor }}
              </a-descriptions-item>
              <a-descriptions-item label="及格分数">
                {{ currentTraining.passingScore }}分
              </a-descriptions-item>
              <a-descriptions-item label="目标人群" :span="3">
                <a-tag v-for="audience in currentTraining.targetAudience" :key="audience" style="margin-right: 8px">
                  {{ getAudienceText(audience) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="开始时间">
                {{ currentTraining.startDate }}
              </a-descriptions-item>
              <a-descriptions-item label="结束时间">
                {{ currentTraining.endDate }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{ currentTraining.createTime }}
              </a-descriptions-item>
              <a-descriptions-item label="培训描述" :span="3">
                <div style="white-space: pre-wrap">{{ currentTraining.description }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="培训内容" :span="3" v-if="currentTraining.content">
                <div style="white-space: pre-wrap">{{ currentTraining.content }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="考核要求" :span="3">
                <a-tag v-for="option in currentTraining.assessmentOptions" :key="option" style="margin-right: 8px">
                  {{ getAssessmentText(option) }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>

            <!-- 参与统计 -->
            <a-divider>参与统计</a-divider>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="总参与人数" :value="currentTraining.totalParticipants" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="完成人数" :value="currentTraining.completedParticipants" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="完成率" :value="currentTraining.completionRate" suffix="%" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="平均分数" :value="currentTraining.avgScore" suffix="分" />
              </a-col>
            </a-row>

            <!-- 参与者列表 -->
            <a-divider>参与者列表</a-divider>
            <a-table 
              :columns="participantColumns" 
              :data-source="currentTraining.participants || []" 
              :pagination="{ pageSize: 5 }"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'progress'">
                  <a-progress :percent="record.progress" size="small" />
                </template>
                <template v-if="column.key === 'status'">
                  <a-tag :color="getParticipantStatusColor(record.status)">
                    {{ getParticipantStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'score'">
                  <span :style="{ color: record.score >= currentTraining.passingScore ? '#52c41a' : '#ff4d4f' }">
                    {{ record.score || '-' }}
                  </span>
                </template>
              </template>
            </a-table>
          </a-col>
          
          <a-col :span="8">
            <!-- 操作面板 -->
            <a-card title="操作面板" size="small">
              <a-space direction="vertical" style="width: 100%">
                <a-button type="primary" block @click="startTraining(currentTraining)">
                  <play-circle-outlined />
                  开始培训
                </a-button>
                <a-button block @click="manageParticipants(currentTraining)">
                  <team-outlined />
                  参与者管理
                </a-button>
                <a-button block @click="viewStatistics(currentTraining)">
                  <bar-chart-outlined />
                  培训统计
                </a-button>
                <a-button block @click="generateCertificate(currentTraining)">
                  <trophy-outlined />
                  生成证书
                </a-button>
                <a-button block @click="downloadMaterials(currentTraining)">
                  <download-outlined />
                  下载材料
                </a-button>
              </a-space>
            </a-card>

            <!-- 培训材料 -->
            <a-card title="培训材料" size="small" style="margin-top: 16px">
              <a-list size="small" :data-source="currentTraining.materials || []">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.name }}</template>
                      <template #description>{{ item.size }}</template>
                    </a-list-item-meta>
                    <a-button type="link" size="small" @click="downloadFile(item)">下载</a-button>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>

            <!-- 培训进度 -->
            <a-card title="培训进度" size="small" style="margin-top: 16px">
              <div class="progress-chart">
                <a-progress 
                  type="circle" 
                  :percent="currentTraining.completionRate" 
                  :size="120"
                  :stroke-color="{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }"
                />
                <div style="text-align: center; margin-top: 16px">
                  <p>{{ currentTraining.completedParticipants }} / {{ currentTraining.totalParticipants }} 人完成</p>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 证书管理弹窗 -->
    <a-modal
      v-model:open="certificationModalVisible"
      title="证书管理"
      width="1000px"
      :footer="null"
    >
      <div class="certification-container">
        <div class="certification-actions" style="margin-bottom: 16px">
          <a-space>
            <a-button type="primary" @click="createCertificate">
              <plus-outlined />
              新建证书模板
            </a-button>
            <a-button @click="batchGenerateCertificates">
              <trophy-outlined />
              批量生成证书
            </a-button>
          </a-space>
        </div>
        
        <a-table 
          :columns="certificateColumns" 
          :data-source="certificates" 
          :pagination="{ pageSize: 10 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getCertificateStatusColor(record.status)">
                {{ getCertificateStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="previewCertificate(record)">
                  预览
                </a-button>
                <a-button type="link" size="small" @click="downloadCertificate(record)">
                  下载
                </a-button>
                <a-button type="link" size="small" @click="sendCertificate(record)">
                  发送
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 培训报告弹窗 -->
    <a-modal
      v-model:open="reportModalVisible"
      title="培训报告"
      width="1200px"
      :footer="null"
    >
      <div class="report-container">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="培训类型分布" size="small">
              <div ref="typeChart" style="height: 300px"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="完成率分析" size="small">
              <div ref="completionChart" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="24">
            <a-card title="培训趋势" size="small">
              <div ref="trendChart" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 报告数据 -->
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <a-card title="热门培训" size="small">
              <a-list size="small" :data-source="popularTrainings">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>
                        参与人数: {{ item.participants }} | 完成率: {{ item.completionRate }}%
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="培训建议" size="small">
              <a-list size="small" :data-source="trainingRecommendations">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>{{ item.description }}</template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined, 
  DownloadOutlined,
  TrophyOutlined,
  BarChartOutlined,
  DownOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  type: undefined,
  status: undefined,
  difficulty: undefined,
  dateRange: undefined
})

// 统计数据
const stats = reactive({
  total: 45,
  ongoing: 8,
  completionRate: 78.5,
  avgScore: 85.2
})

// 我的培训进度
const myProgress = ref([
  {
    id: 1,
    title: '合规基础培训',
    status: 'completed',
    progress: 100,
    duration: 120,
    participants: 25
  },
  {
    id: 2,
    title: '最新法规解读',
    status: 'ongoing',
    progress: 65,
    duration: 90,
    participants: 18
  },
  {
    id: 3,
    title: '案例分析专题',
    status: 'planned',
    progress: 0,
    duration: 150,
    participants: 0
  }
])

// 表格配置
const columns = [
  {
    title: '培训编号',
    dataIndex: 'code',
    key: 'code',
    width: 120,
    fixed: 'left'
  },
  {
    title: '培训标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '培训类型',
    key: 'type',
    width: 120
  },
  {
    title: '难度等级',
    key: 'difficulty',
    width: 100
  },
  {
    title: '培训状态',
    key: 'status',
    width: 100
  },
  {
    title: '培训时长',
    dataIndex: 'duration',
    key: 'duration',
    width: 100,
    customRender: ({ text }) => `${text}分钟`
  },
  {
    title: '参与人数',
    dataIndex: 'totalParticipants',
    key: 'totalParticipants',
    width: 100
  },
  {
    title: '完成率',
    key: 'progress',
    width: 120
  },
  {
    title: '平均分数',
    dataIndex: 'avgScore',
    key: 'avgScore',
    width: 100,
    customRender: ({ text }) => text ? `${text}分` : '-'
  },
  {
    title: '培训讲师',
    dataIndex: 'instructor',
    key: 'instructor',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 参与者表格配置
const participantColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department'
  },
  {
    title: '进度',
    key: 'progress'
  },
  {
    title: '状态',
    key: 'status'
  },
  {
    title: '分数',
    key: 'score'
  },
  {
    title: '完成时间',
    dataIndex: 'completedAt',
    key: 'completedAt'
  }
]

// 证书表格配置
const certificateColumns = [
  {
    title: '证书编号',
    dataIndex: 'code',
    key: 'code'
  },
  {
    title: '培训名称',
    dataIndex: 'trainingTitle',
    key: 'trainingTitle'
  },
  {
    title: '获得者',
    dataIndex: 'recipient',
    key: 'recipient'
  },
  {
    title: '颁发日期',
    dataIndex: 'issueDate',
    key: 'issueDate'
  },
  {
    title: '状态',
    key: 'status'
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    code: 'TRN-2024-001',
    title: '合规基础培训',
    type: 'compliance',
    difficulty: 'beginner',
    status: 'published',
    duration: 120,
    totalParticipants: 25,
    completedParticipants: 23,
    completionRate: 92,
    avgScore: 87.5,
    instructor: '张三',
    createTime: '2024-01-15 10:00:00',
    description: '针对新员工的合规基础知识培训',
    content: '包括公司合规政策、行业法规要求等基础内容',
    targetAudience: ['new_employee', 'all'],
    startDate: '2024-01-20 09:00:00',
    endDate: '2024-01-20 12:00:00',
    passingScore: 80,
    assessmentOptions: ['quiz', 'attendance'],
    participants: [
      {
        id: 1,
        name: '李四',
        department: '催收部',
        progress: 100,
        status: 'completed',
        score: 92,
        completedAt: '2024-01-20 11:45:00'
      },
      {
        id: 2,
        name: '王五',
        department: '法务部',
        progress: 80,
        status: 'in_progress',
        score: null,
        completedAt: null
      }
    ],
    materials: [
      { name: '合规培训手册.pdf', size: '2.5MB' },
      { name: '案例分析.pptx', size: '1.8MB' }
    ]
  },
  {
    id: 2,
    code: 'TRN-2024-002',
    title: '最新法规解读培训',
    type: 'regulation',
    difficulty: 'intermediate',
    status: 'ongoing',
    duration: 90,
    totalParticipants: 18,
    completedParticipants: 12,
    completionRate: 67,
    avgScore: 83.2,
    instructor: '李四',
    createTime: '2024-01-20 14:30:00',
    description: '解读最新发布的金融监管法规',
    content: '重点讲解新法规的主要变化及对业务的影响',
    targetAudience: ['manager', 'legal'],
    startDate: '2024-01-25 14:00:00',
    endDate: '2024-01-25 15:30:00',
    passingScore: 85,
    assessmentOptions: ['quiz', 'discussion'],
    participants: [],
    materials: []
  },
  {
    id: 3,
    code: 'TRN-2024-003',
    title: '催收操作实务培训',
    type: 'operation',
    difficulty: 'advanced',
    status: 'draft',
    duration: 180,
    totalParticipants: 0,
    completedParticipants: 0,
    completionRate: 0,
    avgScore: 0,
    instructor: '王五',
    createTime: '2024-01-25 09:15:00',
    description: '针对催收人员的专业技能培训',
    content: '包括催收话术、法律边界、客户沟通技巧等',
    targetAudience: ['collection'],
    startDate: null,
    endDate: null,
    passingScore: 80,
    assessmentOptions: ['quiz', 'assignment'],
    participants: [],
    materials: []
  }
])

// 证书数据
const certificates = ref([
  {
    id: 1,
    code: 'CERT-2024-001',
    trainingTitle: '合规基础培训',
    recipient: '李四',
    issueDate: '2024-01-20',
    status: 'issued'
  },
  {
    id: 2,
    code: 'CERT-2024-002',
    trainingTitle: '合规基础培训',
    recipient: '王五',
    issueDate: '2024-01-20',
    status: 'pending'
  }
])

// 报告数据
const popularTrainings = ref([
  { title: '合规基础培训', participants: 25, completionRate: 92 },
  { title: '最新法规解读培训', participants: 18, completionRate: 67 },
  { title: '催收操作实务培训', participants: 12, completionRate: 85 }
])

const trainingRecommendations = ref([
  { title: '增加互动环节', description: '在培训中加入更多互动讨论，提高参与度' },
  { title: '优化培训时长', description: '将长时间培训拆分为多个短期培训模块' },
  { title: '强化实践操作', description: '增加实际案例操作练习，提升培训效果' },
  { title: '建立进阶课程', description: '为不同层级员工设计对应的进阶培训课程' }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 弹窗控制
const trainingModalVisible = ref(false)
const viewModalVisible = ref(false)
const certificationModalVisible = ref(false)
const reportModalVisible = ref(false)

// 表单数据
const trainingForm = reactive({
  id: null,
  title: '',
  type: undefined,
  difficulty: undefined,
  duration: undefined,
  targetAudience: [],
  instructor: '',
  startDate: undefined,
  endDate: undefined,
  description: '',
  content: '',
  materials: [],
  assessmentOptions: [],
  passingScore: 80,
  remark: ''
})

// 当前操作的培训
const currentTraining = ref(null)

// 图表引用
const typeChart = ref()
const completionChart = ref()
const trendChart = ref()

// 计算属性
const modalTitle = computed(() => {
  return trainingForm.id ? '编辑培训' : '新建培训'
})

// 状态相关方法
const getTypeColor = (type) => {
  const colors = {
    compliance: 'blue',
    regulation: 'orange',
    operation: 'green',
    case: 'purple',
    assessment: 'red'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    compliance: '合规基础',
    regulation: '法规解读',
    operation: '操作实务',
    case: '案例分析',
    assessment: '考核测试'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'default',
    published: 'blue',
    ongoing: 'orange',
    completed: 'green',
    archived: 'gray',
    planned: 'cyan'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    ongoing: '进行中',
    completed: '已完成',
    archived: '已归档',
    planned: '计划中'
  }
  return texts[status] || '未知'
}

const getDifficultyColor = (difficulty) => {
  const colors = {
    beginner: 'green',
    intermediate: 'blue',
    advanced: 'orange',
    expert: 'red'
  }
  return colors[difficulty] || 'default'
}

const getDifficultyText = (difficulty) => {
  const texts = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  }
  return texts[difficulty] || '未知'
}

const getAudienceText = (audience) => {
  const texts = {
    all: '全员',
    new_employee: '新员工',
    manager: '管理层',
    collection: '催收人员',
    legal: '法务人员',
    risk: '风控人员'
  }
  return texts[audience] || audience
}

const getAssessmentText = (assessment) => {
  const texts = {
    quiz: '在线测试',
    assignment: '作业提交',
    discussion: '讨论参与',
    attendance: '出勤考核'
  }
  return texts[assessment] || assessment
}

const getParticipantStatusColor = (status) => {
  const colors = {
    not_started: 'default',
    in_progress: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getParticipantStatusText = (status) => {
  const texts = {
    not_started: '未开始',
    in_progress: '进行中',
    completed: '已完成',
    failed: '未通过'
  }
  return texts[status] || '未知'
}

const getCertificateStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    issued: 'green',
    revoked: 'red'
  }
  return colors[status] || 'default'
}

const getCertificateStatusText = (status) => {
  const texts = {
    pending: '待颁发',
    issued: '已颁发',
    revoked: '已撤销'
  }
  return texts[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    type: undefined,
    status: undefined,
    difficulty: undefined,
    dateRange: undefined
  })
  handleSearch()
}

const refreshData = () => {
  handleSearch()
}

const onSelectChange = (selectedRowKeys) => {
  selectedRows.value = selectedRowKeys
}

const handleBatchAction = ({ key }) => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  console.log('批量操作:', key, selectedRows.value)
  message.success(`批量${key}操作执行完成`)
  selectedRows.value = []
}

// 培训操作
const openCreateModal = () => {
  Object.assign(trainingForm, {
    id: null,
    title: '',
    type: undefined,
    difficulty: undefined,
    duration: undefined,
    targetAudience: [],
    instructor: '',
    startDate: undefined,
    endDate: undefined,
    description: '',
    content: '',
    materials: [],
    assessmentOptions: [],
    passingScore: 80,
    remark: ''
  })
  trainingModalVisible.value = true
}

const editTraining = (record) => {
  Object.assign(trainingForm, {
    ...record,
    startDate: record.startDate ? dayjs(record.startDate) : undefined,
    endDate: record.endDate ? dayjs(record.endDate) : undefined
  })
  trainingModalVisible.value = true
}

const viewTraining = (record) => {
  currentTraining.value = record
  viewModalVisible.value = true
}

const handleTrainingSubmit = () => {
  console.log('提交培训:', trainingForm)
  message.success(trainingForm.id ? '培训更新成功' : '培训创建成功')
  trainingModalVisible.value = false
  handleSearch()
}

const handleTrainingCancel = () => {
  trainingModalVisible.value = false
}

const handleAction = (action, record) => {
  console.log('执行操作:', action, record)
  
  switch (action) {
    case 'start':
      startTraining(record)
      break
    case 'publish':
      publishTraining(record)
      break
    case 'participants':
      manageParticipants(record)
      break
    case 'statistics':
      viewStatistics(record)
      break
    case 'certificate':
      generateCertificate(record)
      break
    case 'copy':
      copyTraining(record)
      break
    case 'archive':
      archiveTraining(record)
      break
    case 'delete':
      deleteTraining(record)
      break
  }
}

// 具体操作方法
const startTraining = (record) => {
  message.success(`培训 ${record.title} 已开始`)
}

const publishTraining = (record) => {
  message.success(`培训 ${record.title} 已发布`)
}

const manageParticipants = (record) => {
  message.info(`管理培训 ${record.title} 的参与者`)
}

const viewStatistics = (record) => {
  message.info(`查看培训 ${record.title} 的统计数据`)
}

const generateCertificate = (record) => {
  message.success(`为培训 ${record.title} 生成证书`)
}

const copyTraining = (record) => {
  message.success(`培训 ${record.title} 已复制`)
}

const archiveTraining = (record) => {
  message.success(`培训 ${record.title} 已归档`)
}

const deleteTraining = (record) => {
  message.success(`培训 ${record.title} 已删除`)
}

const downloadMaterials = (training) => {
  message.success(`下载培训材料: ${training.title}`)
}

const downloadFile = (file) => {
  message.success(`下载文件: ${file.name}`)
}

// 证书管理
const openCertificationModal = () => {
  certificationModalVisible.value = true
}

const createCertificate = () => {
  message.info('新建证书模板功能')
}

const batchGenerateCertificates = () => {
  message.success('批量生成证书成功')
}

const previewCertificate = (certificate) => {
  message.info(`预览证书: ${certificate.code}`)
}

const downloadCertificate = (certificate) => {
  message.success(`下载证书: ${certificate.code}`)
}

const sendCertificate = (certificate) => {
  message.success(`发送证书: ${certificate.code}`)
}

// 培训报告
const openReportModal = () => {
  reportModalVisible.value = true
  nextTick(() => {
    initCharts()
  })
}

const initCharts = () => {
  // 培训类型分布图
  const typeChartInstance = echarts.init(typeChart.value)
  const typeOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '培训类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 15, name: '合规基础' },
          { value: 12, name: '法规解读' },
          { value: 8, name: '操作实务' },
          { value: 6, name: '案例分析' },
          { value: 4, name: '考核测试' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  typeChartInstance.setOption(typeOption)

  // 完成率分析图
  const completionChartInstance = echarts.init(completionChart.value)
  const completionOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['初级', '中级', '高级', '专家']
    },
    yAxis: {
      type: 'value',
      name: '完成率(%)'
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        data: [92, 78, 65, 45],
        itemStyle: {
          color: function(params) {
            const colors = ['#52c41a', '#1890ff', '#faad14', '#ff4d4f']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  completionChartInstance.setOption(completionOption)

  // 培训趋势图
  const trendChartInstance = echarts.init(trendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['培训数量', '参与人数']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '培训数量'
      },
      {
        type: 'value',
        name: '参与人数'
      }
    ],
    series: [
      {
        name: '培训数量',
        type: 'bar',
        data: [8, 12, 15, 10, 13, 9],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '参与人数',
        type: 'line',
        yAxisIndex: 1,
        data: [156, 234, 298, 189, 267, 195],
        smooth: true
      }
    ]
  }
  trendChartInstance.setOption(trendOption)
}

// 文件上传
const beforeUpload = (file) => {
  const isValidType = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'mp4', 'avi'].some(type => 
    file.name.toLowerCase().endsWith(type)
  )
  if (!isValidType) {
    message.error('只能上传文档、演示文稿或视频格式的文件!')
    return false
  }
  const isLt100M = file.size / 1024 / 1024 < 100
  if (!isLt100M) {
    message.error('文件大小不能超过 100MB!')
    return false
  }
  return false // 阻止自动上传
}

// 导出功能
const exportData = () => {
  const csvData = tableData.value.map(item => ({
    培训编号: item.code,
    培训标题: item.title,
    培训类型: getTypeText(item.type),
    难度等级: getDifficultyText(item.difficulty),
    培训状态: getStatusText(item.status),
    培训时长: `${item.duration}分钟`,
    参与人数: item.totalParticipants,
    完成率: `${item.completionRate}%`,
    平均分数: item.avgScore ? `${item.avgScore}分` : '-',
    培训讲师: item.instructor,
    创建时间: item.createTime
  }))
  
  console.log('导出数据:', csvData)
  message.success('培训数据导出成功')
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.progress-card {
  margin-bottom: 16px;
}

.progress-item {
  cursor: pointer;
  transition: all 0.3s;
}

.progress-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-header h4 {
  margin: 0;
  font-size: 14px;
}

.progress-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.progress-chart {
  text-align: center;
  padding: 16px;
}

.certification-container {
  min-height: 400px;
}

.report-container {
  min-height: 600px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-statistic {
  text-align: center;
}
</style>