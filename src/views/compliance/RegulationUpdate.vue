<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>法规更新管理</h2>
      
      <!-- 搜索条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="法规类型">
                <a-select v-model:value="searchForm.type" placeholder="请选择法规类型" allow-clear>
                  <a-select-option value="financial">金融监管</a-select-option>
                  <a-select-option value="data">数据保护</a-select-option>
                  <a-select-option value="collection">催收相关</a-select-option>
                  <a-select-option value="privacy">隐私保护</a-select-option>
                  <a-select-option value="consumer">消费者权益</a-select-option>
                  <a-select-option value="other">其他法规</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="更新状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="pending">待处理</a-select-option>
                  <a-select-option value="analyzing">分析中</a-select-option>
                  <a-select-option value="reviewed">已审阅</a-select-option>
                  <a-select-option value="implemented">已实施</a-select-option>
                  <a-select-option value="ignored">已忽略</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="影响等级">
                <a-select v-model:value="searchForm.impact" placeholder="请选择影响等级" allow-clear>
                  <a-select-option value="critical">关键</a-select-option>
                  <a-select-option value="high">高</a-select-option>
                  <a-select-option value="medium">中</a-select-option>
                  <a-select-option value="low">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="发布时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button type="primary" @click="openCreateModal">
                      <plus-outlined />
                      新增法规
                    </a-button>
                    <a-button @click="openSubscriptionModal">
                      <bell-outlined />
                      订阅管理
                    </a-button>
                    <a-button @click="openAnalysisModal">
                      <bar-chart-outlined />
                      影响分析
                    </a-button>
                    <a-button @click="exportData">
                      <download-outlined />
                      导出报告
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总法规数" 
              :value="stats.total" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="待处理" 
              :value="stats.pending" 
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="高影响法规" 
              :value="stats.highImpact" 
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="本月新增" 
              :value="stats.thisMonth" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 最新法规快讯 -->
      <a-card title="最新法规快讯" class="news-card">
        <template #extra>
          <a-space>
            <a-switch v-model:checked="autoRefresh" size="small" />
            <span style="font-size: 12px">自动刷新</span>
            <a-button @click="refreshNews" size="small">
              <reload-outlined />
            </a-button>
          </a-space>
        </template>
        
        <a-carousel autoplay :autoplay-speed="5000" dots-class="custom-dots">
          <div v-for="news in latestNews" :key="news.id" class="news-slide">
            <div class="news-content">
              <h4>{{ news.title }}</h4>
              <p>{{ news.summary }}</p>
              <div class="news-meta">
                <a-tag :color="getTypeColor(news.type)">{{ getTypeText(news.type) }}</a-tag>
                <span>{{ news.publishDate }}</span>
                <a-button type="link" size="small" @click="viewNews(news)">查看详情</a-button>
              </div>
            </div>
          </div>
        </a-carousel>
      </a-card>

      <!-- 法规列表 -->
      <a-card title="法规列表">
        <template #extra>
          <a-space>
            <a-button @click="refreshData">
              <reload-outlined />
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleBatchAction">
                  <a-menu-item key="analyze">批量分析</a-menu-item>
                  <a-menu-item key="review">批量审阅</a-menu-item>
                  <a-menu-item key="implement">批量实施</a-menu-item>
                </a-menu>
              </template>
              <a-button>
                批量操作 <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>

        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRows, onChange: onSelectChange }"
          :scroll="{ x: 1600 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'impact'">
              <a-tag :color="getImpactColor(record.impact)">
                {{ getImpactText(record.impact) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'effectiveDate'">
              <span :style="{ color: getEffectiveDateColor(record.effectiveDate) }">
                {{ record.effectiveDate }}
              </span>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewRegulation(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="analyzeRegulation(record)">
                  分析
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleAction(key, record)">
                      <a-menu-item key="edit">编辑</a-menu-item>
                      <a-menu-item key="analyze">影响分析</a-menu-item>
                      <a-menu-item key="review">标记审阅</a-menu-item>
                      <a-menu-item key="implement">标记实施</a-menu-item>
                      <a-menu-item key="ignore">标记忽略</a-menu-item>
                      <a-menu-item key="notify">发送通知</a-menu-item>
                      <a-menu-item key="track">跟踪变更</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建/编辑法规弹窗 -->
    <a-modal
      v-model:open="regulationModalVisible"
      :title="modalTitle"
      width="900px"
      @ok="handleRegulationSubmit"
      @cancel="handleRegulationCancel"
    >
      <a-form :model="regulationForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="法规编号" v-if="regulationForm.id">
          <a-input v-model:value="regulationForm.code" disabled />
        </a-form-item>
        <a-form-item label="法规标题" required>
          <a-input v-model:value="regulationForm.title" placeholder="请输入法规标题" />
        </a-form-item>
        <a-form-item label="法规类型" required>
          <a-select v-model:value="regulationForm.type" placeholder="请选择法规类型">
            <a-select-option value="financial">金融监管</a-select-option>
            <a-select-option value="data">数据保护</a-select-option>
            <a-select-option value="collection">催收相关</a-select-option>
            <a-select-option value="privacy">隐私保护</a-select-option>
            <a-select-option value="consumer">消费者权益</a-select-option>
            <a-select-option value="other">其他法规</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="发布机构">
          <a-input v-model:value="regulationForm.publisher" placeholder="请输入发布机构" />
        </a-form-item>
        <a-form-item label="发布日期" required>
          <a-date-picker v-model:value="regulationForm.publishDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="生效日期" required>
          <a-date-picker v-model:value="regulationForm.effectiveDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="影响等级" required>
          <a-select v-model:value="regulationForm.impact" placeholder="请选择影响等级">
            <a-select-option value="critical">关键</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="适用范围">
          <a-select v-model:value="regulationForm.scope" mode="multiple" placeholder="请选择适用范围">
            <a-select-option value="all">全公司</a-select-option>
            <a-select-option value="collection">催收部门</a-select-option>
            <a-select-option value="legal">法务部门</a-select-option>
            <a-select-option value="finance">财务部门</a-select-option>
            <a-select-option value="risk">风控部门</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="法规摘要" required>
          <a-textarea 
            v-model:value="regulationForm.summary" 
            :rows="4" 
            placeholder="请输入法规摘要" 
          />
        </a-form-item>
        <a-form-item label="主要变化">
          <a-textarea 
            v-model:value="regulationForm.changes" 
            :rows="4" 
            placeholder="请描述主要变化内容" 
          />
        </a-form-item>
        <a-form-item label="影响评估">
          <a-textarea 
            v-model:value="regulationForm.impactAssessment" 
            :rows="4" 
            placeholder="请输入对公司业务的影响评估" 
          />
        </a-form-item>
        <a-form-item label="相关文档">
          <a-upload
            v-model:file-list="regulationForm.attachments"
            name="file"
            :before-upload="beforeUpload"
          >
            <a-button>
              <upload-outlined />
              上传文档
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea 
            v-model:value="regulationForm.remark" 
            :rows="3" 
            placeholder="请输入备注信息" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 法规详情弹窗 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="法规详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="currentRegulation">
        <a-row :gutter="16">
          <a-col :span="16">
            <!-- 基本信息 -->
            <a-descriptions bordered size="small" title="法规信息">
              <a-descriptions-item label="法规编号" :span="2">
                {{ currentRegulation.code }}
              </a-descriptions-item>
              <a-descriptions-item label="法规类型">
                <a-tag :color="getTypeColor(currentRegulation.type)">
                  {{ getTypeText(currentRegulation.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="法规标题" :span="3">
                {{ currentRegulation.title }}
              </a-descriptions-item>
              <a-descriptions-item label="发布机构">
                {{ currentRegulation.publisher }}
              </a-descriptions-item>
              <a-descriptions-item label="发布日期">
                {{ currentRegulation.publishDate }}
              </a-descriptions-item>
              <a-descriptions-item label="生效日期">
                <span :style="{ color: getEffectiveDateColor(currentRegulation.effectiveDate) }">
                  {{ currentRegulation.effectiveDate }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="影响等级">
                <a-tag :color="getImpactColor(currentRegulation.impact)">
                  {{ getImpactText(currentRegulation.impact) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="当前状态">
                <a-tag :color="getStatusColor(currentRegulation.status)">
                  {{ getStatusText(currentRegulation.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="适用范围" :span="3">
                <a-tag v-for="scope in currentRegulation.scope" :key="scope" style="margin-right: 8px">
                  {{ getScopeText(scope) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="法规摘要" :span="3">
                <div style="white-space: pre-wrap">{{ currentRegulation.summary }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="主要变化" :span="3" v-if="currentRegulation.changes">
                <div style="white-space: pre-wrap">{{ currentRegulation.changes }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="影响评估" :span="3" v-if="currentRegulation.impactAssessment">
                <div style="white-space: pre-wrap">{{ currentRegulation.impactAssessment }}</div>
              </a-descriptions-item>
            </a-descriptions>

            <!-- 跟踪记录 -->
            <a-divider>跟踪记录</a-divider>
            <a-timeline>
              <a-timeline-item 
                v-for="record in currentRegulation.trackingRecords || []" 
                :key="record.id"
                :color="getTrackingColor(record.type)"
              >
                <template #dot>
                  <plus-outlined v-if="record.type === 'create'" />
                  <eye-outlined v-else-if="record.type === 'analyze'" />
                  <check-circle-outlined v-else-if="record.type === 'review'" />
                  <rocket-outlined v-else-if="record.type === 'implement'" />
                </template>
                <div class="timeline-item">
                  <div class="timeline-header">
                    <strong>{{ getTrackingTypeText(record.type) }}</strong>
                    <span class="timeline-time">{{ record.time }}</span>
                  </div>
                  <p><strong>操作人:</strong> {{ record.operator }}</p>
                  <div v-if="record.content">
                    <strong>详细内容:</strong>
                    <div style="white-space: pre-wrap; margin-top: 8px">{{ record.content }}</div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-col>
          
          <a-col :span="8">
            <!-- 操作面板 -->
            <a-card title="操作面板" size="small">
              <a-space direction="vertical" style="width: 100%">
                <a-button type="primary" block @click="analyzeRegulation(currentRegulation)">
                  <bar-chart-outlined />
                  影响分析
                </a-button>
                <a-button block @click="reviewRegulation(currentRegulation)">
                  <check-circle-outlined />
                  标记审阅
                </a-button>
                <a-button block @click="implementRegulation(currentRegulation)">
                  <rocket-outlined />
                  标记实施
                </a-button>
                <a-button block @click="notifyStakeholders(currentRegulation)">
                  <bell-outlined />
                  发送通知
                </a-button>
                <a-button block @click="trackChanges(currentRegulation)">
                  <eye-outlined />
                  跟踪变更
                </a-button>
              </a-space>
            </a-card>

            <!-- 相关信息 -->
            <a-card title="相关信息" size="small" style="margin-top: 16px">
              <a-list size="small">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>相关法规</template>
                    <template #description>发现5条相关法规</template>
                  </a-list-item-meta>
                  <a-button type="link" size="small">查看</a-button>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>影响的政策</template>
                    <template #description>3条公司政策需要更新</template>
                  </a-list-item-meta>
                  <a-button type="link" size="small">查看</a-button>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>实施建议</template>
                    <template #description>AI智能推荐实施方案</template>
                  </a-list-item-meta>
                  <a-button type="link" size="small">查看</a-button>
                </a-list-item>
              </a-list>
            </a-card>

            <!-- 订阅信息 -->
            <a-card title="订阅信息" size="small" style="margin-top: 16px">
              <a-descriptions size="small">
                <a-descriptions-item label="订阅状态" :span="2">
                  <a-tag color="green">已订阅</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="通知方式" :span="2">
                  邮件 + 系统通知
                </a-descriptions-item>
                <a-descriptions-item label="关注人数" :span="2">
                  {{ currentRegulation.subscribers || 0 }}人
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 订阅管理弹窗 -->
    <a-modal
      v-model:open="subscriptionModalVisible"
      title="订阅管理"
      width="800px"
      :footer="null"
    >
      <div class="subscription-container">
        <a-tabs>
          <a-tab-pane key="categories" tab="分类订阅">
            <a-list :data-source="subscriptionCategories">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                  <a-switch v-model:checked="item.subscribed" @change="updateSubscription(item)" />
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
          
          <a-tab-pane key="sources" tab="来源订阅">
            <a-list :data-source="subscriptionSources">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.url }}</template>
                  </a-list-item-meta>
                  <a-space>
                    <a-switch v-model:checked="item.enabled" size="small" />
                    <a-button type="link" size="small" @click="testSource(item)">测试</a-button>
                  </a-space>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
          
          <a-tab-pane key="notifications" tab="通知设置">
            <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-item label="邮件通知">
                <a-switch v-model:checked="notificationSettings.email" />
              </a-form-item>
              <a-form-item label="系统通知">
                <a-switch v-model:checked="notificationSettings.system" />
              </a-form-item>
              <a-form-item label="短信通知">
                <a-switch v-model:checked="notificationSettings.sms" />
              </a-form-item>
              <a-form-item label="微信通知">
                <a-switch v-model:checked="notificationSettings.wechat" />
              </a-form-item>
              <a-form-item label="通知频率">
                <a-select v-model:value="notificationSettings.frequency">
                  <a-select-option value="immediate">立即</a-select-option>
                  <a-select-option value="daily">每日汇总</a-select-option>
                  <a-select-option value="weekly">每周汇总</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 影响分析弹窗 -->
    <a-modal
      v-model:open="analysisModalVisible"
      title="影响分析报告"
      width="1200px"
      :footer="null"
    >
      <div class="analysis-container">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="法规类型分布" size="small">
              <div ref="typeChart" style="height: 300px"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="影响等级分析" size="small">
              <div ref="impactChart" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="24">
            <a-card title="法规发布趋势" size="small">
              <div ref="trendChart" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 分析数据 -->
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <a-card title="重点关注法规" size="small">
              <a-list size="small" :data-source="focusRegulations">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>
                        影响等级: {{ getImpactText(item.impact) }} | 生效日期: {{ item.effectiveDate }}
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="应对建议" size="small">
              <a-list size="small" :data-source="recommendations">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>{{ item.description }}</template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined, 
  DownloadOutlined,
  BellOutlined,
  BarChartOutlined,
  DownOutlined,
  UploadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const selectedRows = ref([])
const autoRefresh = ref(true)

// 搜索表单
const searchForm = reactive({
  type: undefined,
  status: undefined,
  impact: undefined,
  dateRange: undefined
})

// 统计数据
const stats = reactive({
  total: 156,
  pending: 23,
  highImpact: 15,
  thisMonth: 8
})

// 最新法规快讯
const latestNews = ref([
  {
    id: 1,
    title: '央行发布《征信业务管理办法》修订版',
    summary: '新修订的办法将于2024年3月1日正式实施，对个人征信信息采集和使用提出更严格要求',
    type: 'financial',
    publishDate: '2024-01-15'
  },
  {
    id: 2,
    title: '《个人信息保护法》实施细则更新',
    summary: '针对金融行业个人信息处理活动的具体规范，强化了数据安全保护要求',
    type: 'privacy',
    publishDate: '2024-01-20'
  },
  {
    id: 3,
    title: '银保监会发布催收业务合规指引',
    summary: '规范催收机构业务行为，明确催收流程和操作标准，保护消费者合法权益',
    type: 'collection',
    publishDate: '2024-01-25'
  }
])

// 表格配置
const columns = [
  {
    title: '法规编号',
    dataIndex: 'code',
    key: 'code',
    width: 140,
    fixed: 'left'
  },
  {
    title: '法规标题',
    dataIndex: 'title',
    key: 'title',
    width: 250,
    ellipsis: true
  },
  {
    title: '法规类型',
    key: 'type',
    width: 120
  },
  {
    title: '发布机构',
    dataIndex: 'publisher',
    key: 'publisher',
    width: 150
  },
  {
    title: '发布日期',
    dataIndex: 'publishDate',
    key: 'publishDate',
    width: 120
  },
  {
    title: '生效日期',
    key: 'effectiveDate',
    width: 120
  },
  {
    title: '影响等级',
    key: 'impact',
    width: 100
  },
  {
    title: '当前状态',
    key: 'status',
    width: 100
  },
  {
    title: '关注人数',
    dataIndex: 'subscribers',
    key: 'subscribers',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    code: 'REG-2024-001',
    title: '征信业务管理办法（修订版）',
    type: 'financial',
    publisher: '中国人民银行',
    publishDate: '2024-01-15',
    effectiveDate: '2024-03-01',
    impact: 'critical',
    status: 'analyzing',
    subscribers: 25,
    summary: '对征信机构的业务活动、信息安全、数据质量等方面提出了更严格的要求',
    changes: '1. 强化个人征信信息保护\n2. 规范征信数据采集范围\n3. 完善信息主体权益保护机制',
    impactAssessment: '需要调整现有征信数据处理流程，加强数据安全保护措施',
    scope: ['all', 'risk'],
    trackingRecords: [
      {
        id: 1,
        type: 'create',
        operator: '系统管理员',
        time: '2024-01-15 10:00:00',
        content: '法规信息录入系统'
      },
      {
        id: 2,
        type: 'analyze',
        operator: '合规专员',
        time: '2024-01-16 14:30:00',
        content: '正在分析对现有业务流程的影响'
      }
    ]
  },
  {
    id: 2,
    code: 'REG-2024-002',
    title: '个人信息保护法实施细则',
    type: 'privacy',
    publisher: '国家网信办',
    publishDate: '2024-01-20',
    effectiveDate: '2024-02-01',
    impact: 'high',
    status: 'reviewed',
    subscribers: 18,
    summary: '细化了个人信息处理活动的具体规范，明确了违法行为的认定标准',
    changes: '1. 明确个人信息处理的合法性基础\n2. 细化告知同意规则\n3. 强化个人信息跨境传输管理',
    impactAssessment: '需要更新隐私政策和用户协议，完善个人信息处理记录',
    scope: ['all'],
    trackingRecords: []
  },
  {
    id: 3,
    code: 'REG-2024-003',
    title: '催收业务合规指引',
    type: 'collection',
    publisher: '银保监会',
    publishDate: '2024-01-25',
    effectiveDate: '2024-04-01',
    impact: 'high',
    status: 'pending',
    subscribers: 32,
    summary: '规范催收机构业务行为，保护消费者合法权益',
    changes: '1. 明确催收行为边界\n2. 规范催收时间和频率\n3. 强化催收人员培训要求',
    impactAssessment: '需要全面审查现有催收流程，调整催收策略和操作规范',
    scope: ['collection', 'legal'],
    trackingRecords: []
  }
])

// 订阅分类
const subscriptionCategories = ref([
  {
    id: 1,
    name: '金融监管法规',
    description: '银行、保险、证券等金融监管相关法规',
    subscribed: true
  },
  {
    id: 2,
    name: '数据保护法规',
    description: '个人信息保护、数据安全相关法规',
    subscribed: true
  },
  {
    id: 3,
    name: '催收相关法规',
    description: '债权催收、消费者权益保护相关法规',
    subscribed: true
  },
  {
    id: 4,
    name: '隐私保护法规',
    description: '隐私权保护、信息披露相关法规',
    subscribed: false
  }
])

// 订阅来源
const subscriptionSources = ref([
  {
    id: 1,
    name: '中国人民银行',
    url: 'http://www.pbc.gov.cn',
    enabled: true
  },
  {
    id: 2,
    name: '银保监会',
    url: 'http://www.cbirc.gov.cn',
    enabled: true
  },
  {
    id: 3,
    name: '国家网信办',
    url: 'http://www.cac.gov.cn',
    enabled: true
  }
])

// 通知设置
const notificationSettings = reactive({
  email: true,
  system: true,
  sms: false,
  wechat: true,
  frequency: 'immediate'
})

// 分析数据
const focusRegulations = ref([
  { title: '征信业务管理办法（修订版）', impact: 'critical', effectiveDate: '2024-03-01' },
  { title: '催收业务合规指引', impact: 'high', effectiveDate: '2024-04-01' },
  { title: '个人信息保护法实施细则', impact: 'high', effectiveDate: '2024-02-01' }
])

const recommendations = ref([
  { title: '建立法规影响评估机制', description: '定期评估新法规对业务的影响，制定应对策略' },
  { title: '加强合规培训', description: '针对重点法规开展专题培训，提升员工合规意识' },
  { title: '完善内控制度', description: '根据法规要求及时更新相关内控制度和操作流程' },
  { title: '建立监控预警', description: '建立法规变化监控预警机制，及时发现合规风险' }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 弹窗控制
const regulationModalVisible = ref(false)
const viewModalVisible = ref(false)
const subscriptionModalVisible = ref(false)
const analysisModalVisible = ref(false)

// 表单数据
const regulationForm = reactive({
  id: null,
  code: '',
  title: '',
  type: undefined,
  publisher: '',
  publishDate: undefined,
  effectiveDate: undefined,
  impact: undefined,
  scope: [],
  summary: '',
  changes: '',
  impactAssessment: '',
  attachments: [],
  remark: ''
})

// 当前操作的法规
const currentRegulation = ref(null)

// 图表引用
const typeChart = ref()
const impactChart = ref()
const trendChart = ref()

// 计算属性
const modalTitle = computed(() => {
  return regulationForm.id ? '编辑法规' : '新增法规'
})

// 状态相关方法
const getTypeColor = (type) => {
  const colors = {
    financial: 'blue',
    data: 'green',
    collection: 'orange',
    privacy: 'purple',
    consumer: 'cyan',
    other: 'gray'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    financial: '金融监管',
    data: '数据保护',
    collection: '催收相关',
    privacy: '隐私保护',
    consumer: '消费者权益',
    other: '其他法规'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'red',
    analyzing: 'orange',
    reviewed: 'blue',
    implemented: 'green',
    ignored: 'gray'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    analyzing: '分析中',
    reviewed: '已审阅',
    implemented: '已实施',
    ignored: '已忽略'
  }
  return texts[status] || '未知'
}

const getImpactColor = (impact) => {
  const colors = {
    critical: 'red',
    high: 'orange',
    medium: 'blue',
    low: 'green'
  }
  return colors[impact] || 'default'
}

const getImpactText = (impact) => {
  const texts = {
    critical: '关键',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[impact] || '未知'
}

const getScopeText = (scope) => {
  const texts = {
    all: '全公司',
    collection: '催收部门',
    legal: '法务部门',
    finance: '财务部门',
    risk: '风控部门'
  }
  return texts[scope] || scope
}

const getEffectiveDateColor = (effectiveDate) => {
  const today = dayjs()
  const effective = dayjs(effectiveDate)
  const diffDays = effective.diff(today, 'day')
  
  if (diffDays < 0) return '#999' // 已生效
  if (diffDays <= 30) return '#ff4d4f' // 即将生效
  if (diffDays <= 90) return '#faad14' // 近期生效
  return '#52c41a' // 远期生效
}

const getTrackingColor = (type) => {
  const colors = {
    create: 'blue',
    analyze: 'orange',
    review: 'green',
    implement: 'purple',
    ignore: 'gray'
  }
  return colors[type] || 'blue'
}

const getTrackingTypeText = (type) => {
  const texts = {
    create: '创建法规',
    analyze: '影响分析',
    review: '审阅完成',
    implement: '实施完成',
    ignore: '标记忽略'
  }
  return texts[type] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    type: undefined,
    status: undefined,
    impact: undefined,
    dateRange: undefined
  })
  handleSearch()
}

const refreshData = () => {
  handleSearch()
}

const refreshNews = () => {
  message.success('法规快讯已刷新')
}

const onSelectChange = (selectedRowKeys) => {
  selectedRows.value = selectedRowKeys
}

const handleBatchAction = ({ key }) => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  console.log('批量操作:', key, selectedRows.value)
  message.success(`批量${key}操作执行完成`)
  selectedRows.value = []
}

// 法规操作
const openCreateModal = () => {
  Object.assign(regulationForm, {
    id: null,
    code: '',
    title: '',
    type: undefined,
    publisher: '',
    publishDate: undefined,
    effectiveDate: undefined,
    impact: undefined,
    scope: [],
    summary: '',
    changes: '',
    impactAssessment: '',
    attachments: [],
    remark: ''
  })
  regulationModalVisible.value = true
}

const viewRegulation = (record) => {
  currentRegulation.value = record
  viewModalVisible.value = true
}

const viewNews = (news) => {
  message.info(`查看法规快讯: ${news.title}`)
}

const analyzeRegulation = (record) => {
  currentRegulation.value = record
  message.info(`正在分析法规: ${record.title}`)
}

const handleRegulationSubmit = () => {
  console.log('提交法规:', regulationForm)
  message.success(regulationForm.id ? '法规更新成功' : '法规创建成功')
  regulationModalVisible.value = false
  handleSearch()
}

const handleRegulationCancel = () => {
  regulationModalVisible.value = false
}

const handleAction = (action, record) => {
  console.log('执行操作:', action, record)
  
  switch (action) {
    case 'edit':
      editRegulation(record)
      break
    case 'analyze':
      analyzeRegulation(record)
      break
    case 'review':
      reviewRegulation(record)
      break
    case 'implement':
      implementRegulation(record)
      break
    case 'ignore':
      ignoreRegulation(record)
      break
    case 'notify':
      notifyStakeholders(record)
      break
    case 'track':
      trackChanges(record)
      break
  }
}

// 具体操作方法
const editRegulation = (record) => {
  Object.assign(regulationForm, {
    ...record,
    publishDate: record.publishDate ? dayjs(record.publishDate) : undefined,
    effectiveDate: record.effectiveDate ? dayjs(record.effectiveDate) : undefined
  })
  regulationModalVisible.value = true
}

const reviewRegulation = (record) => {
  message.success(`法规 ${record.title} 已标记为已审阅`)
}

const implementRegulation = (record) => {
  message.success(`法规 ${record.title} 已标记为已实施`)
}

const ignoreRegulation = (record) => {
  message.success(`法规 ${record.title} 已标记为已忽略`)
}

const notifyStakeholders = (record) => {
  message.success(`已发送通知: ${record.title}`)
}

const trackChanges = (record) => {
  message.info(`开始跟踪法规变更: ${record.title}`)
}

// 订阅管理
const openSubscriptionModal = () => {
  subscriptionModalVisible.value = true
}

const updateSubscription = (item) => {
  message.success(`${item.name} 订阅状态已更新`)
}

const testSource = (source) => {
  message.info(`正在测试数据源: ${source.name}`)
}

// 分析功能
const openAnalysisModal = () => {
  analysisModalVisible.value = true
  nextTick(() => {
    initCharts()
  })
}

const initCharts = () => {
  // 法规类型分布图
  const typeChartInstance = echarts.init(typeChart.value)
  const typeOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '法规类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 45, name: '金融监管' },
          { value: 28, name: '数据保护' },
          { value: 32, name: '催收相关' },
          { value: 18, name: '隐私保护' },
          { value: 15, name: '消费者权益' },
          { value: 18, name: '其他法规' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  typeChartInstance.setOption(typeOption)

  // 影响等级分析图
  const impactChartInstance = echarts.init(impactChart.value)
  const impactOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['关键', '高', '中', '低']
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [
      {
        name: '法规数量',
        type: 'bar',
        data: [15, 38, 62, 41],
        itemStyle: {
          color: function(params) {
            const colors = ['#ff4d4f', '#faad14', '#1890ff', '#52c41a']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  impactChartInstance.setOption(impactOption)

  // 法规发布趋势图
  const trendChartInstance = echarts.init(trendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增法规', '更新法规']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增法规',
        type: 'line',
        data: [12, 15, 8, 11, 13, 9],
        smooth: true
      },
      {
        name: '更新法规',
        type: 'line',
        data: [8, 12, 15, 9, 11, 7],
        smooth: true
      }
    ]
  }
  trendChartInstance.setOption(trendOption)
}

// 文件上传
const beforeUpload = (file) => {
  const isValidType = ['pdf', 'doc', 'docx', 'txt'].some(type => 
    file.name.toLowerCase().endsWith(type)
  )
  if (!isValidType) {
    message.error('只能上传 PDF、DOC、DOCX、TXT 格式的文件!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 导出功能
const exportData = () => {
  const csvData = tableData.value.map(item => ({
    法规编号: item.code,
    法规标题: item.title,
    法规类型: getTypeText(item.type),
    发布机构: item.publisher,
    发布日期: item.publishDate,
    生效日期: item.effectiveDate,
    影响等级: getImpactText(item.impact),
    当前状态: getStatusText(item.status),
    关注人数: item.subscribers
  }))
  
  console.log('导出数据:', csvData)
  message.success('法规数据导出成功')
}

// 生命周期
onMounted(() => {
  handleSearch()
  
  // 设置自动刷新
  if (autoRefresh.value) {
    setInterval(() => {
      if (autoRefresh.value) {
        // 这里可以调用API获取最新法规快讯
        console.log('自动刷新法规快讯')
      }
    }, 30000) // 30秒刷新一次
  }
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.news-card {
  margin-bottom: 16px;
}

.news-slide {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.news-content h4 {
  color: white;
  margin-bottom: 8px;
}

.news-content p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-meta span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.timeline-item {
  padding: 8px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-time {
  color: #999;
  font-size: 12px;
}

.subscription-container {
  min-height: 400px;
}

.analysis-container {
  min-height: 600px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-statistic {
  text-align: center;
}

:deep(.custom-dots) {
  bottom: 10px;
}

:deep(.custom-dots li button) {
  background: rgba(255, 255, 255, 0.5);
  border: none;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

:deep(.custom-dots li.slick-active button) {
  background: white;
}
</style>