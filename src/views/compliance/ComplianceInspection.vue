<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>合规检查管理</h2>
      
      <!-- 搜索条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="检查类型">
                <a-select v-model:value="searchForm.type" placeholder="请选择检查类型" allow-clear>
                  <a-select-option value="regular">常规检查</a-select-option>
                  <a-select-option value="special">专项检查</a-select-option>
                  <a-select-option value="random">随机抽查</a-select-option>
                  <a-select-option value="follow_up">跟踪检查</a-select-option>
                  <a-select-option value="external">外部检查</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="检查状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="planned">计划中</a-select-option>
                  <a-select-option value="ongoing">进行中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="suspended">已暂停</a-select-option>
                  <a-select-option value="cancelled">已取消</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="检查时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="检查部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="all">全部部门</a-select-option>
                  <a-select-option value="collection">催收部门</a-select-option>
                  <a-select-option value="legal">法务部门</a-select-option>
                  <a-select-option value="finance">财务部门</a-select-option>
                  <a-select-option value="risk">风控部门</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button type="primary" @click="openCreateModal">
                      <plus-outlined />
                      新建检查
                    </a-button>
                    <a-button @click="openPlanModal">
                      <calendar-outlined />
                      检查计划
                    </a-button>
                    <a-button @click="openTemplateModal">
                      <file-text-outlined />
                      检查模板
                    </a-button>
                    <a-button @click="exportData">
                      <download-outlined />
                      导出报告
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总检查次数" 
              :value="stats.total" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中" 
              :value="stats.ongoing" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="问题发现率" 
              :value="stats.issueRate" 
              suffix="%" 
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="整改完成率" 
              :value="stats.rectificationRate" 
              suffix="%" 
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 检查列表 -->
      <a-card title="检查列表">
        <template #extra>
          <a-space>
            <a-button @click="refreshData">
              <reload-outlined />
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleBatchAction">
                  <a-menu-item key="start">批量启动</a-menu-item>
                  <a-menu-item key="complete">批量完成</a-menu-item>
                  <a-menu-item key="cancel">批量取消</a-menu-item>
                </a-menu>
              </template>
              <a-button>
                批量操作 <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>

        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRows, onChange: onSelectChange }"
          :scroll="{ x: 1600 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'progress'">
              <a-progress :percent="record.progress" size="small" />
            </template>
            <template v-if="column.key === 'severity'">
              <a-tag :color="getSeverityColor(record.severity)">
                {{ getSeverityText(record.severity) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewInspection(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="editInspection(record)">
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleAction(key, record)">
                      <a-menu-item key="start">开始检查</a-menu-item>
                      <a-menu-item key="complete">完成检查</a-menu-item>
                      <a-menu-item key="report">生成报告</a-menu-item>
                      <a-menu-item key="issues">问题管理</a-menu-item>
                      <a-menu-item key="rectify">整改跟踪</a-menu-item>
                      <a-menu-item key="copy">复制</a-menu-item>
                      <a-menu-item key="delete">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建/编辑检查弹窗 -->
    <a-modal
      v-model:open="inspectionModalVisible"
      :title="modalTitle"
      width="900px"
      @ok="handleInspectionSubmit"
      @cancel="handleInspectionCancel"
    >
      <a-form :model="inspectionForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="检查名称" required>
          <a-input v-model:value="inspectionForm.name" placeholder="请输入检查名称" />
        </a-form-item>
        <a-form-item label="检查类型" required>
          <a-select v-model:value="inspectionForm.type" placeholder="请选择检查类型">
            <a-select-option value="regular">常规检查</a-select-option>
            <a-select-option value="special">专项检查</a-select-option>
            <a-select-option value="random">随机抽查</a-select-option>
            <a-select-option value="follow_up">跟踪检查</a-select-option>
            <a-select-option value="external">外部检查</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检查范围">
          <a-select v-model:value="inspectionForm.scope" mode="multiple" placeholder="请选择检查范围">
            <a-select-option value="all">全公司</a-select-option>
            <a-select-option value="collection">催收部门</a-select-option>
            <a-select-option value="legal">法务部门</a-select-option>
            <a-select-option value="finance">财务部门</a-select-option>
            <a-select-option value="risk">风控部门</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检查内容">
          <a-select v-model:value="inspectionForm.content" mode="multiple" placeholder="请选择检查内容">
            <a-select-option value="operation">操作合规</a-select-option>
            <a-select-option value="data">数据安全</a-select-option>
            <a-select-option value="financial">财务合规</a-select-option>
            <a-select-option value="legal">法律合规</a-select-option>
            <a-select-option value="risk">风险控制</a-select-option>
            <a-select-option value="internal">内控制度</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检查负责人" required>
          <a-select v-model:value="inspectionForm.inspector" placeholder="请选择检查负责人">
            <a-select-option value="inspector1">张三</a-select-option>
            <a-select-option value="inspector2">李四</a-select-option>
            <a-select-option value="inspector3">王五</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检查团队">
          <a-select v-model:value="inspectionForm.team" mode="multiple" placeholder="请选择检查团队成员">
            <a-select-option value="member1">赵六</a-select-option>
            <a-select-option value="member2">孙七</a-select-option>
            <a-select-option value="member3">周八</a-select-option>
            <a-select-option value="member4">吴九</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="计划开始时间" required>
          <a-date-picker v-model:value="inspectionForm.startDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="计划结束时间" required>
          <a-date-picker v-model:value="inspectionForm.endDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="检查重点">
          <a-textarea 
            v-model:value="inspectionForm.focus" 
            :rows="4" 
            placeholder="请输入检查重点和要求" 
          />
        </a-form-item>
        <a-form-item label="检查方法">
          <a-checkbox-group v-model:value="inspectionForm.methods">
            <a-checkbox value="document">文档审查</a-checkbox>
            <a-checkbox value="interview">人员访谈</a-checkbox>
            <a-checkbox value="observation">现场观察</a-checkbox>
            <a-checkbox value="test">测试验证</a-checkbox>
            <a-checkbox value="sample">抽样检查</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea 
            v-model:value="inspectionForm.remark" 
            :rows="3" 
            placeholder="请输入备注信息" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 检查详情弹窗 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="检查详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentInspection">
        <a-descriptions bordered size="small">
          <a-descriptions-item label="检查名称" :span="2">
            {{ currentInspection.name }}
          </a-descriptions-item>
          <a-descriptions-item label="检查编号">
            {{ currentInspection.code }}
          </a-descriptions-item>
          <a-descriptions-item label="检查类型">
            <a-tag :color="getTypeColor(currentInspection.type)">
              {{ getTypeText(currentInspection.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="检查状态">
            <a-tag :color="getStatusColor(currentInspection.status)">
              {{ getStatusText(currentInspection.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="检查进度">
            <a-progress :percent="currentInspection.progress" size="small" />
          </a-descriptions-item>
          <a-descriptions-item label="检查范围" :span="3">
            <a-tag v-for="scope in currentInspection.scope" :key="scope" style="margin-right: 8px">
              {{ getScopeText(scope) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="检查内容" :span="3">
            <a-tag v-for="content in currentInspection.content" :key="content" style="margin-right: 8px">
              {{ getContentText(content) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="检查负责人">
            {{ currentInspection.inspector }}
          </a-descriptions-item>
          <a-descriptions-item label="检查团队" :span="2">
            {{ currentInspection.team?.join('、') }}
          </a-descriptions-item>
          <a-descriptions-item label="计划开始时间">
            {{ currentInspection.startDate }}
          </a-descriptions-item>
          <a-descriptions-item label="计划结束时间">
            {{ currentInspection.endDate }}
          </a-descriptions-item>
          <a-descriptions-item label="实际完成时间">
            {{ currentInspection.actualEndDate || '未完成' }}
          </a-descriptions-item>
          <a-descriptions-item label="检查重点" :span="3">
            <div style="white-space: pre-wrap">{{ currentInspection.focus }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="检查方法" :span="3">
            <a-tag v-for="method in currentInspection.methods" :key="method" style="margin-right: 8px">
              {{ getMethodText(method) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 检查结果 -->
        <a-divider>检查结果</a-divider>
        <a-table 
          :columns="issueColumns" 
          :data-source="currentInspection.issues || []" 
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'severity'">
              <a-tag :color="getSeverityColor(record.severity)">
                {{ getSeverityText(record.severity) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getIssueStatusColor(record.status)">
                {{ getIssueStatusText(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 检查计划弹窗 -->
    <a-modal
      v-model:open="planModalVisible"
      title="检查计划管理"
      width="1200px"
      :footer="null"
    >
      <div class="plan-container">
        <div class="plan-actions" style="margin-bottom: 16px">
          <a-space>
            <a-button type="primary" @click="createPlan">
              <plus-outlined />
              新建计划
            </a-button>
            <a-button @click="importPlan">
              <import-outlined />
              导入计划
            </a-button>
            <a-button @click="exportPlan">
              <export-outlined />
              导出计划
            </a-button>
          </a-space>
        </div>
        
        <!-- 年度计划视图 -->
        <a-calendar v-model:value="planCalendarValue" @select="onPlanDateSelect">
          <template #dateCellRender="{ current }">
            <div v-if="getPlansByDate(current).length > 0" class="plan-indicator">
              <a-badge :count="getPlansByDate(current).length" />
            </div>
          </template>
        </a-calendar>
      </div>
    </a-modal>

    <!-- 检查模板弹窗 -->
    <a-modal
      v-model:open="templateModalVisible"
      title="检查模板管理"
      width="1000px"
      :footer="null"
    >
      <div class="template-container">
        <div class="template-actions" style="margin-bottom: 16px">
          <a-button type="primary" @click="createTemplate">
            <plus-outlined />
            新建模板
          </a-button>
        </div>
        
        <a-table 
          :columns="templateColumns" 
          :data-source="templates" 
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="useTemplate(record)">
                  使用
                </a-button>
                <a-button type="link" size="small" @click="editTemplate(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="deleteTemplate(record)">
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 问题管理弹窗 -->
    <a-modal
      v-model:open="issueModalVisible"
      title="问题管理"
      width="1200px"
      :footer="null"
    >
      <div class="issue-container">
        <div class="issue-stats" style="margin-bottom: 16px">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总问题数" :value="issueStats.total" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="高风险" :value="issueStats.high" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="待整改" :value="issueStats.pending" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="已整改" :value="issueStats.resolved" />
            </a-col>
          </a-row>
        </div>
        
        <a-table 
          :columns="issueColumns" 
          :data-source="issueList" 
          :pagination="{ pageSize: 10 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'severity'">
              <a-tag :color="getSeverityColor(record.severity)">
                {{ getSeverityText(record.severity) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getIssueStatusColor(record.status)">
                {{ getIssueStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="rectifyIssue(record)">
                  整改
                </a-button>
                <a-button type="link" size="small" @click="followUpIssue(record)">
                  跟踪
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined, 
  DownloadOutlined,
  CalendarOutlined,
  FileTextOutlined,
  DownOutlined,
  ImportOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  type: undefined,
  status: undefined,
  dateRange: undefined,
  department: undefined
})

// 统计数据
const stats = reactive({
  total: 234,
  ongoing: 12,
  issueRate: 23.5,
  rectificationRate: 89.2
})

// 表格配置
const columns = [
  {
    title: '检查名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '检查编号',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '检查类型',
    key: 'type',
    width: 120
  },
  {
    title: '检查状态',
    key: 'status',
    width: 100
  },
  {
    title: '检查进度',
    key: 'progress',
    width: 120
  },
  {
    title: '检查负责人',
    dataIndex: 'inspector',
    key: 'inspector',
    width: 120
  },
  {
    title: '计划开始时间',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 120
  },
  {
    title: '计划结束时间',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 120
  },
  {
    title: '问题数量',
    dataIndex: 'issueCount',
    key: 'issueCount',
    width: 100
  },
  {
    title: '问题严重度',
    key: 'severity',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    fixed: 'right'
  }
]

// 问题表格配置
const issueColumns = [
  {
    title: '问题描述',
    dataIndex: 'description',
    key: 'description',
    width: 200
  },
  {
    title: '问题类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '严重程度',
    key: 'severity',
    width: 100
  },
  {
    title: '责任部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '整改状态',
    key: 'status',
    width: 100
  },
  {
    title: '整改期限',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 模板表格配置
const templateColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '模板类型',
    key: 'type'
  },
  {
    title: '适用范围',
    dataIndex: 'scope',
    key: 'scope'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '2024年第一季度催收业务合规检查',
    code: 'COMP-2024-Q1-001',
    type: 'regular',
    status: 'completed',
    progress: 100,
    inspector: '张三',
    team: ['李四', '王五'],
    startDate: '2024-01-15',
    endDate: '2024-01-25',
    actualEndDate: '2024-01-24',
    issueCount: 5,
    severity: 'medium',
    createTime: '2024-01-10 10:00:00',
    scope: ['collection'],
    content: ['operation', 'data'],
    focus: '重点检查催收业务操作合规性，包括催收流程、数据使用等方面',
    methods: ['document', 'interview', 'observation'],
    issues: [
      {
        id: 1,
        description: '部分催收员未按规定记录通话内容',
        type: '操作合规',
        severity: 'medium',
        department: '催收一部',
        status: 'resolved',
        deadline: '2024-02-15'
      }
    ]
  },
  {
    id: 2,
    name: '数据安全专项检查',
    code: 'COMP-2024-SP-002',
    type: 'special',
    status: 'ongoing',
    progress: 65,
    inspector: '李四',
    team: ['张三', '赵六'],
    startDate: '2024-02-01',
    endDate: '2024-02-15',
    actualEndDate: null,
    issueCount: 3,
    severity: 'high',
    createTime: '2024-01-25 14:30:00',
    scope: ['all'],
    content: ['data', 'risk'],
    focus: '检查客户数据保护措施，评估数据安全风险',
    methods: ['document', 'test'],
    issues: [
      {
        id: 2,
        description: '客户数据访问权限设置不当',
        type: '数据安全',
        severity: 'high',
        department: '技术部',
        status: 'pending',
        deadline: '2024-03-01'
      }
    ]
  },
  {
    id: 3,
    name: '风控部门内控制度执行检查',
    code: 'COMP-2024-IC-003',
    type: 'follow_up',
    status: 'planned',
    progress: 0,
    inspector: '王五',
    team: ['孙七', '周八'],
    startDate: '2024-03-01',
    endDate: '2024-03-10',
    actualEndDate: null,
    issueCount: 0,
    severity: 'low',
    createTime: '2024-02-15 09:45:00',
    scope: ['risk'],
    content: ['internal', 'risk'],
    focus: '跟踪检查上次检查发现问题的整改情况',
    methods: ['document', 'interview'],
    issues: []
  }
])

// 计划数据
const planCalendarValue = ref(dayjs())
const planData = ref([
  {
    date: '2024-03-15',
    inspections: [
      { name: '季度合规检查', type: 'regular' },
      { name: '数据安全检查', type: 'special' }
    ]
  }
])

// 模板数据
const templates = ref([
  {
    id: 1,
    name: '常规合规检查模板',
    type: 'regular',
    scope: '全部门',
    usageCount: 25,
    createTime: '2024-01-01'
  },
  {
    id: 2,
    name: '数据安全检查模板',
    type: 'special',
    scope: '技术部门',
    usageCount: 12,
    createTime: '2024-01-15'
  }
])

// 问题列表
const issueList = ref([
  {
    id: 1,
    description: '催收流程不规范',
    type: '操作合规',
    severity: 'high',
    department: '催收部',
    status: 'pending',
    deadline: '2024-03-15'
  },
  {
    id: 2,
    description: '数据访问权限过度',
    type: '数据安全',
    severity: 'medium',
    department: '技术部',
    status: 'resolved',
    deadline: '2024-02-28'
  }
])

// 问题统计
const issueStats = reactive({
  total: 23,
  high: 8,
  pending: 15,
  resolved: 8
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 弹窗控制
const inspectionModalVisible = ref(false)
const viewModalVisible = ref(false)
const planModalVisible = ref(false)
const templateModalVisible = ref(false)
const issueModalVisible = ref(false)

// 表单数据
const inspectionForm = reactive({
  id: null,
  name: '',
  type: undefined,
  scope: [],
  content: [],
  inspector: undefined,
  team: [],
  startDate: undefined,
  endDate: undefined,
  focus: '',
  methods: [],
  remark: ''
})

// 当前操作的检查
const currentInspection = ref(null)

// 计算属性
const modalTitle = computed(() => {
  return inspectionForm.id ? '编辑检查' : '新建检查'
})

// 状态相关方法
const getTypeColor = (type) => {
  const colors = {
    regular: 'blue',
    special: 'purple',
    random: 'green',
    follow_up: 'orange',
    external: 'red'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    regular: '常规检查',
    special: '专项检查',
    random: '随机抽查',
    follow_up: '跟踪检查',
    external: '外部检查'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    planned: 'default',
    ongoing: 'blue',
    completed: 'green',
    suspended: 'orange',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    planned: '计划中',
    ongoing: '进行中',
    completed: '已完成',
    suspended: '已暂停',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getSeverityColor = (severity) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[severity] || 'default'
}

const getSeverityText = (severity) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || '未知'
}

const getIssueStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    processing: 'blue',
    resolved: 'green',
    rejected: 'red'
  }
  return colors[status] || 'default'
}

const getIssueStatusText = (status) => {
  const texts = {
    pending: '待整改',
    processing: '整改中',
    resolved: '已整改',
    rejected: '已拒绝'
  }
  return texts[status] || '未知'
}

const getScopeText = (scope) => {
  const texts = {
    all: '全公司',
    collection: '催收部门',
    legal: '法务部门',
    finance: '财务部门',
    risk: '风控部门'
  }
  return texts[scope] || scope
}

const getContentText = (content) => {
  const texts = {
    operation: '操作合规',
    data: '数据安全',
    financial: '财务合规',
    legal: '法律合规',
    risk: '风险控制',
    internal: '内控制度'
  }
  return texts[content] || content
}

const getMethodText = (method) => {
  const texts = {
    document: '文档审查',
    interview: '人员访谈',
    observation: '现场观察',
    test: '测试验证',
    sample: '抽样检查'
  }
  return texts[method] || method
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    type: undefined,
    status: undefined,
    dateRange: undefined,
    department: undefined
  })
  handleSearch()
}

const refreshData = () => {
  handleSearch()
}

const onSelectChange = (selectedRowKeys) => {
  selectedRows.value = selectedRowKeys
}

const handleBatchAction = ({ key }) => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  console.log('批量操作:', key, selectedRows.value)
  message.success(`批量${key}操作执行完成`)
  selectedRows.value = []
}

// 检查操作
const openCreateModal = () => {
  Object.assign(inspectionForm, {
    id: null,
    name: '',
    type: undefined,
    scope: [],
    content: [],
    inspector: undefined,
    team: [],
    startDate: undefined,
    endDate: undefined,
    focus: '',
    methods: [],
    remark: ''
  })
  inspectionModalVisible.value = true
}

const editInspection = (record) => {
  Object.assign(inspectionForm, {
    ...record,
    startDate: record.startDate ? dayjs(record.startDate) : undefined,
    endDate: record.endDate ? dayjs(record.endDate) : undefined
  })
  inspectionModalVisible.value = true
}

const viewInspection = (record) => {
  currentInspection.value = record
  viewModalVisible.value = true
}

const handleInspectionSubmit = () => {
  console.log('提交检查:', inspectionForm)
  message.success(inspectionForm.id ? '检查更新成功' : '检查创建成功')
  inspectionModalVisible.value = false
  handleSearch()
}

const handleInspectionCancel = () => {
  inspectionModalVisible.value = false
}

const handleAction = (action, record) => {
  console.log('执行操作:', action, record)
  
  switch (action) {
    case 'start':
      message.success('检查已开始')
      break
    case 'complete':
      message.success('检查已完成')
      break
    case 'report':
      message.success('报告生成成功')
      break
    case 'issues':
      currentInspection.value = record
      issueModalVisible.value = true
      break
    case 'rectify':
      message.info('整改跟踪功能')
      break
    case 'copy':
      message.success('检查复制成功')
      break
    case 'delete':
      message.success('检查删除成功')
      break
  }
}

// 计划管理
const openPlanModal = () => {
  planModalVisible.value = true
}

const createPlan = () => {
  message.info('新建计划功能')
}

const importPlan = () => {
  message.info('导入计划功能')
}

const exportPlan = () => {
  message.success('计划导出成功')
}

const onPlanDateSelect = (date) => {
  console.log('选择日期:', date.format('YYYY-MM-DD'))
}

const getPlansByDate = (date) => {
  const dateStr = date.format('YYYY-MM-DD')
  const plan = planData.value.find(p => p.date === dateStr)
  return plan ? plan.inspections : []
}

// 模板管理
const openTemplateModal = () => {
  templateModalVisible.value = true
}

const createTemplate = () => {
  message.info('新建模板功能')
}

const useTemplate = (template) => {
  Object.assign(inspectionForm, {
    id: null,
    name: `${template.name}_${Date.now()}`,
    type: template.type,
    scope: [],
    content: [],
    inspector: undefined,
    team: [],
    startDate: undefined,
    endDate: undefined,
    focus: '',
    methods: [],
    remark: ''
  })
  templateModalVisible.value = false
  inspectionModalVisible.value = true
  message.success('已应用模板')
}

const editTemplate = (template) => {
  message.info(`编辑模板: ${template.name}`)
}

const deleteTemplate = (template) => {
  message.success(`删除模板: ${template.name}`)
}

// 问题管理
const rectifyIssue = (issue) => {
  message.info(`开始整改: ${issue.description}`)
}

const followUpIssue = (issue) => {
  message.info(`跟踪问题: ${issue.description}`)
}

// 导出功能
const exportData = () => {
  const csvData = tableData.value.map(item => ({
    检查名称: item.name,
    检查编号: item.code,
    检查类型: getTypeText(item.type),
    检查状态: getStatusText(item.status),
    检查进度: `${item.progress}%`,
    检查负责人: item.inspector,
    计划开始时间: item.startDate,
    计划结束时间: item.endDate,
    问题数量: item.issueCount,
    问题严重度: getSeverityText(item.severity),
    创建时间: item.createTime
  }))
  
  console.log('导出数据:', csvData)
  message.success('检查报告导出成功')
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.plan-container {
  min-height: 600px;
}

.plan-indicator {
  position: relative;
}

.template-container {
  min-height: 400px;
}

.issue-container {
  min-height: 500px;
}

.issue-stats {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-statistic {
  text-align: center;
}
</style>