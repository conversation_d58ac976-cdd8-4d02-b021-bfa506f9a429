<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>投诉处理管理</h2>
      
      <!-- 搜索条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="投诉类型">
                <a-select v-model:value="searchForm.type" placeholder="请选择投诉类型" allow-clear>
                  <a-select-option value="operation">操作投诉</a-select-option>
                  <a-select-option value="service">服务投诉</a-select-option>
                  <a-select-option value="fee">费用投诉</a-select-option>
                  <a-select-option value="privacy">隐私投诉</a-select-option>
                  <a-select-option value="other">其他投诉</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="处理状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="pending">待处理</a-select-option>
                  <a-select-option value="processing">处理中</a-select-option>
                  <a-select-option value="resolved">已解决</a-select-option>
                  <a-select-option value="closed">已关闭</a-select-option>
                  <a-select-option value="escalated">已升级</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="紧急程度">
                <a-select v-model:value="searchForm.priority" placeholder="请选择紧急程度" allow-clear>
                  <a-select-option value="urgent">紧急</a-select-option>
                  <a-select-option value="high">高</a-select-option>
                  <a-select-option value="medium">中</a-select-option>
                  <a-select-option value="low">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="投诉时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button type="primary" @click="openCreateModal">
                      <plus-outlined />
                      新建投诉
                    </a-button>
                    <a-button @click="openTemplateModal">
                      <file-text-outlined />
                      回复模板
                    </a-button>
                    <a-button @click="openAnalysisModal">
                      <bar-chart-outlined />
                      投诉分析
                    </a-button>
                    <a-button @click="exportData">
                      <download-outlined />
                      导出报告
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总投诉数" 
              :value="stats.total" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="待处理" 
              :value="stats.pending" 
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="处理率" 
              :value="stats.resolveRate" 
              suffix="%" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均响应时间" 
              :value="stats.avgResponseTime" 
              suffix="小时" 
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 投诉列表 -->
      <a-card title="投诉列表">
        <template #extra>
          <a-space>
            <a-button @click="refreshData">
              <reload-outlined />
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleBatchAction">
                  <a-menu-item key="assign">批量分配</a-menu-item>
                  <a-menu-item key="resolve">批量解决</a-menu-item>
                  <a-menu-item key="close">批量关闭</a-menu-item>
                </a-menu>
              </template>
              <a-button>
                批量操作 <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>

        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRows, onChange: onSelectChange }"
          :scroll="{ x: 1800 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'satisfaction'">
              <a-rate :value="record.satisfaction" disabled allow-half />
            </template>
            <template v-if="column.key === 'responseTime'">
              <span :style="{ color: getResponseTimeColor(record.responseTime) }">
                {{ record.responseTime }}小时
              </span>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewComplaint(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleComplaint(record)">
                  处理
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleAction(key, record)">
                      <a-menu-item key="assign">分配</a-menu-item>
                      <a-menu-item key="reply">回复</a-menu-item>
                      <a-menu-item key="escalate">升级</a-menu-item>
                      <a-menu-item key="resolve">解决</a-menu-item>
                      <a-menu-item key="close">关闭</a-menu-item>
                      <a-menu-item key="followup">跟进</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建/编辑投诉弹窗 -->
    <a-modal
      v-model:open="complaintModalVisible"
      :title="modalTitle"
      width="900px"
      @ok="handleComplaintSubmit"
      @cancel="handleComplaintCancel"
    >
      <a-form :model="complaintForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="投诉编号" v-if="complaintForm.id">
          <a-input v-model:value="complaintForm.code" disabled />
        </a-form-item>
        <a-form-item label="客户姓名" required>
          <a-input v-model:value="complaintForm.customerName" placeholder="请输入客户姓名" />
        </a-form-item>
        <a-form-item label="联系方式" required>
          <a-input v-model:value="complaintForm.phone" placeholder="请输入联系方式" />
        </a-form-item>
        <a-form-item label="投诉类型" required>
          <a-select v-model:value="complaintForm.type" placeholder="请选择投诉类型">
            <a-select-option value="operation">操作投诉</a-select-option>
            <a-select-option value="service">服务投诉</a-select-option>
            <a-select-option value="fee">费用投诉</a-select-option>
            <a-select-option value="privacy">隐私投诉</a-select-option>
            <a-select-option value="other">其他投诉</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="紧急程度" required>
          <a-select v-model:value="complaintForm.priority" placeholder="请选择紧急程度">
            <a-select-option value="urgent">紧急</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="投诉渠道">
          <a-select v-model:value="complaintForm.channel" placeholder="请选择投诉渠道">
            <a-select-option value="phone">电话</a-select-option>
            <a-select-option value="online">在线客服</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="letter">信函</a-select-option>
            <a-select-option value="visit">上门</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="相关案件">
          <a-input v-model:value="complaintForm.caseId" placeholder="请输入相关案件编号" />
        </a-form-item>
        <a-form-item label="投诉内容" required>
          <a-textarea 
            v-model:value="complaintForm.content" 
            :rows="6" 
            placeholder="请详细描述投诉内容" 
          />
        </a-form-item>
        <a-form-item label="期望结果">
          <a-textarea 
            v-model:value="complaintForm.expectation" 
            :rows="3" 
            placeholder="请输入客户期望的解决结果" 
          />
        </a-form-item>
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="complaintForm.attachments"
            name="file"
            :before-upload="beforeUpload"
            :max-count="5"
          >
            <a-button>
              <upload-outlined />
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea 
            v-model:value="complaintForm.remark" 
            :rows="3" 
            placeholder="请输入备注信息" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 投诉详情弹窗 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="投诉详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="currentComplaint">
        <a-row :gutter="16">
          <a-col :span="16">
            <!-- 基本信息 -->
            <a-descriptions bordered size="small" title="投诉信息">
              <a-descriptions-item label="投诉编号" :span="2">
                {{ currentComplaint.code }}
              </a-descriptions-item>
              <a-descriptions-item label="投诉时间">
                {{ currentComplaint.createTime }}
              </a-descriptions-item>
              <a-descriptions-item label="客户姓名">
                {{ currentComplaint.customerName }}
              </a-descriptions-item>
              <a-descriptions-item label="联系方式">
                {{ currentComplaint.phone }}
              </a-descriptions-item>
              <a-descriptions-item label="相关案件">
                {{ currentComplaint.caseId || '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="投诉类型">
                <a-tag :color="getTypeColor(currentComplaint.type)">
                  {{ getTypeText(currentComplaint.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="紧急程度">
                <a-tag :color="getPriorityColor(currentComplaint.priority)">
                  {{ getPriorityText(currentComplaint.priority) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="投诉渠道">
                {{ getChannelText(currentComplaint.channel) }}
              </a-descriptions-item>
              <a-descriptions-item label="投诉内容" :span="3">
                <div style="white-space: pre-wrap">{{ currentComplaint.content }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="期望结果" :span="3" v-if="currentComplaint.expectation">
                <div style="white-space: pre-wrap">{{ currentComplaint.expectation }}</div>
              </a-descriptions-item>
            </a-descriptions>

            <!-- 处理记录 -->
            <a-divider>处理记录</a-divider>
            <a-timeline>
              <a-timeline-item 
                v-for="record in currentComplaint.handleRecords || []" 
                :key="record.id"
                :color="getTimelineColor(record.type)"
              >
                <template #dot>
                  <user-outlined v-if="record.type === 'assign'" />
                  <message-outlined v-else-if="record.type === 'reply'" />
                  <check-circle-outlined v-else-if="record.type === 'resolve'" />
                  <close-circle-outlined v-else-if="record.type === 'close'" />
                </template>
                <div class="timeline-item">
                  <div class="timeline-header">
                    <strong>{{ getRecordTypeText(record.type) }}</strong>
                    <span class="timeline-time">{{ record.time }}</span>
                  </div>
                  <p><strong>处理人:</strong> {{ record.handler }}</p>
                  <div v-if="record.content">
                    <strong>处理内容:</strong>
                    <div style="white-space: pre-wrap; margin-top: 8px">{{ record.content }}</div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-col>
          
          <a-col :span="8">
            <!-- 处理状态 -->
            <a-card title="处理状态" size="small">
              <a-descriptions size="small">
                <a-descriptions-item label="当前状态" :span="2">
                  <a-tag :color="getStatusColor(currentComplaint.status)">
                    {{ getStatusText(currentComplaint.status) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="处理人" :span="2">
                  {{ currentComplaint.handler || '未分配' }}
                </a-descriptions-item>
                <a-descriptions-item label="响应时间" :span="2">
                  {{ currentComplaint.responseTime || 0 }}小时
                </a-descriptions-item>
                <a-descriptions-item label="处理时长" :span="2">
                  {{ currentComplaint.handleDuration || 0 }}小时
                </a-descriptions-item>
                <a-descriptions-item label="满意度" :span="2">
                  <a-rate :value="currentComplaint.satisfaction" disabled allow-half />
                </a-descriptions-item>
              </a-descriptions>
              
              <a-divider />
              
              <!-- 快速操作 -->
              <a-space direction="vertical" style="width: 100%">
                <a-button type="primary" block @click="openReplyModal(currentComplaint)">
                  <message-outlined />
                  回复客户
                </a-button>
                <a-button block @click="assignComplaint(currentComplaint)">
                  <user-outlined />
                  分配处理人
                </a-button>
                <a-button block @click="escalateComplaint(currentComplaint)">
                  <exclamation-circle-outlined />
                  升级处理
                </a-button>
                <a-button type="success" block @click="resolveComplaint(currentComplaint)">
                  <check-circle-outlined />
                  标记解决
                </a-button>
              </a-space>
            </a-card>

            <!-- 相关信息 -->
            <a-card title="相关信息" size="small" style="margin-top: 16px">
              <a-list size="small">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>相似投诉</template>
                    <template #description>发现3条相似投诉记录</template>
                  </a-list-item-meta>
                  <a-button type="link" size="small">查看</a-button>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>客户历史</template>
                    <template #description>该客户共有2次投诉记录</template>
                  </a-list-item-meta>
                  <a-button type="link" size="small">查看</a-button>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>处理建议</template>
                    <template #description>AI智能推荐处理方案</template>
                  </a-list-item-meta>
                  <a-button type="link" size="small">查看</a-button>
                </a-list-item>
              </a-list>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 处理投诉弹窗 -->
    <a-modal
      v-model:open="handleModalVisible"
      title="处理投诉"
      width="800px"
      @ok="handleHandleSubmit"
      @cancel="handleModalVisible = false"
    >
      <a-form :model="handleForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="处理类型" required>
          <a-select v-model:value="handleForm.type" placeholder="请选择处理类型">
            <a-select-option value="assign">分配处理人</a-select-option>
            <a-select-option value="reply">回复客户</a-select-option>
            <a-select-option value="escalate">升级处理</a-select-option>
            <a-select-option value="resolve">标记解决</a-select-option>
            <a-select-option value="close">关闭投诉</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="处理人" v-if="handleForm.type === 'assign'" required>
          <a-select v-model:value="handleForm.handler" placeholder="请选择处理人">
            <a-select-option value="handler1">张三</a-select-option>
            <a-select-option value="handler2">李四</a-select-option>
            <a-select-option value="handler3">王五</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="回复内容" v-if="handleForm.type === 'reply'" required>
          <a-textarea 
            v-model:value="handleForm.replyContent" 
            :rows="6" 
            placeholder="请输入回复内容" 
          />
        </a-form-item>
        <a-form-item label="升级原因" v-if="handleForm.type === 'escalate'" required>
          <a-textarea 
            v-model:value="handleForm.escalateReason" 
            :rows="4" 
            placeholder="请输入升级原因" 
          />
        </a-form-item>
        <a-form-item label="解决方案" v-if="handleForm.type === 'resolve'" required>
          <a-textarea 
            v-model:value="handleForm.solution" 
            :rows="6" 
            placeholder="请输入解决方案" 
          />
        </a-form-item>
        <a-form-item label="关闭原因" v-if="handleForm.type === 'close'" required>
          <a-textarea 
            v-model:value="handleForm.closeReason" 
            :rows="4" 
            placeholder="请输入关闭原因" 
          />
        </a-form-item>
        <a-form-item label="处理说明" required>
          <a-textarea 
            v-model:value="handleForm.remark" 
            :rows="3" 
            placeholder="请输入处理说明" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 回复模板弹窗 -->
    <a-modal
      v-model:open="templateModalVisible"
      title="回复模板管理"
      width="1000px"
      :footer="null"
    >
      <div class="template-container">
        <div class="template-actions" style="margin-bottom: 16px">
          <a-button type="primary" @click="createTemplate">
            <plus-outlined />
            新建模板
          </a-button>
        </div>
        
        <a-table 
          :columns="templateColumns" 
          :data-source="templates" 
          :pagination="{ pageSize: 10 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="useTemplate(record)">
                  使用
                </a-button>
                <a-button type="link" size="small" @click="editTemplate(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="deleteTemplate(record)">
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 投诉分析弹窗 -->
    <a-modal
      v-model:open="analysisModalVisible"
      title="投诉分析报告"
      width="1200px"
      :footer="null"
    >
      <div class="analysis-container">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="投诉类型分布" size="small">
              <div ref="typeChart" style="height: 300px"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="处理时效分析" size="small">
              <div ref="timeChart" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="24">
            <a-card title="投诉趋势" size="small">
              <div ref="trendChart" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 分析数据 -->
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <a-card title="热点问题分析" size="small">
              <a-list size="small" :data-source="hotIssues">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.issue }}</template>
                      <template #description>
                        投诉次数: {{ item.count }} | 占比: {{ item.percentage }}%
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="改进建议" size="small">
              <a-list size="small" :data-source="suggestions">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>{{ item.description }}</template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined, 
  DownloadOutlined,
  FileTextOutlined,
  BarChartOutlined,
  DownOutlined,
  UploadOutlined,
  UserOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  type: undefined,
  status: undefined,
  priority: undefined,
  dateRange: undefined
})

// 统计数据
const stats = reactive({
  total: 289,
  pending: 23,
  resolveRate: 87.5,
  avgResponseTime: 2.5
})

// 表格配置
const columns = [
  {
    title: '投诉编号',
    dataIndex: 'code',
    key: 'code',
    width: 140,
    fixed: 'left'
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120
  },
  {
    title: '投诉类型',
    key: 'type',
    width: 120
  },
  {
    title: '处理状态',
    key: 'status',
    width: 100
  },
  {
    title: '紧急程度',
    key: 'priority',
    width: 100
  },
  {
    title: '投诉渠道',
    dataIndex: 'channel',
    key: 'channel',
    width: 100
  },
  {
    title: '处理人',
    dataIndex: 'handler',
    key: 'handler',
    width: 100
  },
  {
    title: '响应时间',
    key: 'responseTime',
    width: 100
  },
  {
    title: '投诉时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '满意度',
    key: 'satisfaction',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 模板表格配置
const templateColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '适用类型',
    key: 'type'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    code: 'COMP-2024-001',
    customerName: '张三',
    phone: '13800138000',
    type: 'operation',
    status: 'processing',
    priority: 'high',
    channel: 'phone',
    handler: '李四',
    responseTime: 2,
    handleDuration: 8,
    createTime: '2024-01-15 10:30:00',
    satisfaction: 4,
    content: '催收员在通话过程中态度恶劣，存在威胁性语言',
    expectation: '希望更换催收员，并对相关人员进行处罚',
    caseId: 'CASE-2024-0001',
    handleRecords: [
      {
        id: 1,
        type: 'assign',
        handler: '客服主管',
        time: '2024-01-15 11:00:00',
        content: '已分配给李四处理'
      },
      {
        id: 2,
        type: 'reply',
        handler: '李四',
        time: '2024-01-15 14:30:00',
        content: '已联系客户了解具体情况，正在核实相关通话记录'
      }
    ]
  },
  {
    id: 2,
    code: 'COMP-2024-002',
    customerName: '王五',
    phone: '13900139000',
    type: 'fee',
    status: 'resolved',
    priority: 'medium',
    channel: 'online',
    handler: '赵六',
    responseTime: 1,
    handleDuration: 24,
    createTime: '2024-01-20 09:15:00',
    satisfaction: 5,
    content: '对收取的逾期费用计算方式有异议，认为收费过高',
    expectation: '希望重新核算费用，并退还多收部分',
    caseId: 'CASE-2024-0002',
    handleRecords: [
      {
        id: 1,
        type: 'assign',
        handler: '客服主管',
        time: '2024-01-20 09:30:00',
        content: '已分配给赵六处理'
      },
      {
        id: 2,
        type: 'reply',
        handler: '赵六',
        time: '2024-01-20 10:00:00',
        content: '已核实费用计算方式，确实存在计算错误'
      },
      {
        id: 3,
        type: 'resolve',
        handler: '赵六',
        time: '2024-01-21 09:15:00',
        content: '已重新核算费用并退还多收的320元，客户表示满意'
      }
    ]
  },
  {
    id: 3,
    code: 'COMP-2024-003',
    customerName: '孙七',
    phone: '13700137000',
    type: 'privacy',
    status: 'pending',
    priority: 'urgent',
    channel: 'email',
    handler: null,
    responseTime: 0,
    handleDuration: 0,
    createTime: '2024-02-01 16:45:00',
    satisfaction: 0,
    content: '个人信息被泄露给第三方，要求说明情况并承担责任',
    expectation: '要求彻查信息泄露原因，并给予赔偿',
    caseId: null,
    handleRecords: []
  }
])

// 模板数据
const templates = ref([
  {
    id: 1,
    name: '操作投诉回复模板',
    type: 'operation',
    content: '尊敬的客户，感谢您的反馈...',
    usageCount: 25,
    createTime: '2024-01-01'
  },
  {
    id: 2,
    name: '费用投诉回复模板',
    type: 'fee',
    content: '尊敬的客户，关于您提到的费用问题...',
    usageCount: 18,
    createTime: '2024-01-15'
  }
])

// 分析数据
const hotIssues = ref([
  { issue: '催收员态度问题', count: 45, percentage: 32.1 },
  { issue: '费用计算争议', count: 38, percentage: 27.1 },
  { issue: '隐私保护问题', count: 28, percentage: 20.0 },
  { issue: '服务响应延迟', count: 18, percentage: 12.9 },
  { issue: '其他问题', count: 11, percentage: 7.9 }
])

const suggestions = ref([
  { title: '加强催收员培训', description: '定期开展服务态度和沟通技巧培训' },
  { title: '优化费用计算系统', description: '完善费用计算逻辑，减少计算错误' },
  { title: '强化隐私保护', description: '建立更严格的数据保护制度和流程' },
  { title: '提升响应速度', description: '优化客服流程，缩短响应时间' }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 弹窗控制
const complaintModalVisible = ref(false)
const viewModalVisible = ref(false)
const handleModalVisible = ref(false)
const templateModalVisible = ref(false)
const analysisModalVisible = ref(false)

// 表单数据
const complaintForm = reactive({
  id: null,
  code: '',
  customerName: '',
  phone: '',
  type: undefined,
  priority: undefined,
  channel: undefined,
  caseId: '',
  content: '',
  expectation: '',
  attachments: [],
  remark: ''
})

const handleForm = reactive({
  type: undefined,
  handler: undefined,
  replyContent: '',
  escalateReason: '',
  solution: '',
  closeReason: '',
  remark: ''
})

// 当前操作的投诉
const currentComplaint = ref(null)

// 图表引用
const typeChart = ref()
const timeChart = ref()
const trendChart = ref()

// 计算属性
const modalTitle = computed(() => {
  return complaintForm.id ? '编辑投诉' : '新建投诉'
})

// 状态相关方法
const getTypeColor = (type) => {
  const colors = {
    operation: 'red',
    service: 'orange',
    fee: 'blue',
    privacy: 'purple',
    other: 'green'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    operation: '操作投诉',
    service: '服务投诉',
    fee: '费用投诉',
    privacy: '隐私投诉',
    other: '其他投诉'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'red',
    processing: 'orange',
    resolved: 'green',
    closed: 'default',
    escalated: 'purple'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭',
    escalated: '已升级'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'red',
    high: 'orange',
    medium: 'blue',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || '未知'
}

const getChannelText = (channel) => {
  const texts = {
    phone: '电话',
    online: '在线客服',
    email: '邮件',
    letter: '信函',
    visit: '上门'
  }
  return texts[channel] || '未知'
}

const getResponseTimeColor = (time) => {
  if (time <= 2) return '#52c41a'
  if (time <= 8) return '#faad14'
  return '#ff4d4f'
}

const getTimelineColor = (type) => {
  const colors = {
    assign: 'blue',
    reply: 'green',
    escalate: 'orange',
    resolve: 'green',
    close: 'gray'
  }
  return colors[type] || 'blue'
}

const getRecordTypeText = (type) => {
  const texts = {
    assign: '分配处理',
    reply: '回复客户',
    escalate: '升级处理',
    resolve: '标记解决',
    close: '关闭投诉'
  }
  return texts[type] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  console.log('查询参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询完成')
  }, 1000)
}

const resetSearch = () => {
  Object.assign(searchForm, {
    type: undefined,
    status: undefined,
    priority: undefined,
    dateRange: undefined
  })
  handleSearch()
}

const refreshData = () => {
  handleSearch()
}

const onSelectChange = (selectedRowKeys) => {
  selectedRows.value = selectedRowKeys
}

const handleBatchAction = ({ key }) => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  console.log('批量操作:', key, selectedRows.value)
  message.success(`批量${key}操作执行完成`)
  selectedRows.value = []
}

// 投诉操作
const openCreateModal = () => {
  Object.assign(complaintForm, {
    id: null,
    code: '',
    customerName: '',
    phone: '',
    type: undefined,
    priority: undefined,
    channel: undefined,
    caseId: '',
    content: '',
    expectation: '',
    attachments: [],
    remark: ''
  })
  complaintModalVisible.value = true
}

const viewComplaint = (record) => {
  currentComplaint.value = record
  viewModalVisible.value = true
}

const handleComplaint = (record) => {
  currentComplaint.value = record
  Object.assign(handleForm, {
    type: undefined,
    handler: undefined,
    replyContent: '',
    escalateReason: '',
    solution: '',
    closeReason: '',
    remark: ''
  })
  handleModalVisible.value = true
}

const handleComplaintSubmit = () => {
  console.log('提交投诉:', complaintForm)
  message.success(complaintForm.id ? '投诉更新成功' : '投诉创建成功')
  complaintModalVisible.value = false
  handleSearch()
}

const handleComplaintCancel = () => {
  complaintModalVisible.value = false
}

const handleHandleSubmit = () => {
  console.log('提交处理:', handleForm)
  message.success('投诉处理成功')
  handleModalVisible.value = false
  handleSearch()
}

const handleAction = (action, record) => {
  console.log('执行操作:', action, record)
  
  switch (action) {
    case 'assign':
      assignComplaint(record)
      break
    case 'reply':
      openReplyModal(record)
      break
    case 'escalate':
      escalateComplaint(record)
      break
    case 'resolve':
      resolveComplaint(record)
      break
    case 'close':
      closeComplaint(record)
      break
    case 'followup':
      message.info('跟进功能')
      break
  }
}

// 快速操作
const assignComplaint = (record) => {
  currentComplaint.value = record
  handleForm.type = 'assign'
  handleModalVisible.value = true
}

const openReplyModal = (record) => {
  currentComplaint.value = record
  handleForm.type = 'reply'
  handleModalVisible.value = true
}

const escalateComplaint = (record) => {
  currentComplaint.value = record
  handleForm.type = 'escalate'
  handleModalVisible.value = true
}

const resolveComplaint = (record) => {
  currentComplaint.value = record
  handleForm.type = 'resolve'
  handleModalVisible.value = true
}

const closeComplaint = (record) => {
  currentComplaint.value = record
  handleForm.type = 'close'
  handleModalVisible.value = true
}

// 模板管理
const openTemplateModal = () => {
  templateModalVisible.value = true
}

const createTemplate = () => {
  message.info('新建模板功能')
}

const useTemplate = (template) => {
  message.success(`已应用模板: ${template.name}`)
}

const editTemplate = (template) => {
  message.info(`编辑模板: ${template.name}`)
}

const deleteTemplate = (template) => {
  message.success(`删除模板: ${template.name}`)
}

// 分析功能
const openAnalysisModal = () => {
  analysisModalVisible.value = true
  nextTick(() => {
    initCharts()
  })
}

const initCharts = () => {
  // 投诉类型分布图
  const typeChartInstance = echarts.init(typeChart.value)
  const typeOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '投诉类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 45, name: '操作投诉' },
          { value: 38, name: '费用投诉' },
          { value: 28, name: '隐私投诉' },
          { value: 18, name: '服务投诉' },
          { value: 11, name: '其他投诉' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  typeChartInstance.setOption(typeOption)

  // 处理时效分析图
  const timeChartInstance = echarts.init(timeChart.value)
  const timeOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['响应时间', '处理时间']
    },
    xAxis: {
      type: 'category',
      data: ['紧急', '高', '中', '低']
    },
    yAxis: {
      type: 'value',
      name: '小时'
    },
    series: [
      {
        name: '响应时间',
        type: 'bar',
        data: [1.2, 2.5, 4.8, 8.5],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '处理时间',
        type: 'bar',
        data: [8.5, 15.2, 24.8, 48.5],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  timeChartInstance.setOption(timeOption)

  // 投诉趋势图
  const trendChartInstance = echarts.init(trendChart.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['投诉数量', '解决数量']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '投诉数量',
        type: 'line',
        data: [45, 52, 38, 41, 35, 29],
        smooth: true
      },
      {
        name: '解决数量',
        type: 'line',
        data: [40, 48, 35, 39, 33, 27],
        smooth: true
      }
    ]
  }
  trendChartInstance.setOption(trendOption)
}

// 文件上传
const beforeUpload = (file) => {
  const isValidType = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'].some(type => 
    file.name.toLowerCase().endsWith(type)
  )
  if (!isValidType) {
    message.error('只能上传图片、PDF、Word格式的文件!')
    return false
  }
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    message.error('文件大小不能超过 5MB!')
    return false
  }
  return false // 阻止自动上传
}

// 导出功能
const exportData = () => {
  const csvData = tableData.value.map(item => ({
    投诉编号: item.code,
    客户姓名: item.customerName,
    投诉类型: getTypeText(item.type),
    处理状态: getStatusText(item.status),
    紧急程度: getPriorityText(item.priority),
    投诉渠道: getChannelText(item.channel),
    处理人: item.handler || '未分配',
    响应时间: `${item.responseTime}小时`,
    投诉时间: item.createTime,
    满意度: item.satisfaction
  }))
  
  console.log('导出数据:', csvData)
  message.success('投诉数据导出成功')
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.timeline-item {
  padding: 8px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-time {
  color: #999;
  font-size: 12px;
}

.template-container {
  min-height: 400px;
}

.analysis-container {
  min-height: 600px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-statistic {
  text-align: center;
}
</style>