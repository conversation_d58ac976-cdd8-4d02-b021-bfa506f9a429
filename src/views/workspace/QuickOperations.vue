<template>
  <div class="quick-operations">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="operations-info">
          <h2>快捷操作</h2>
          <p class="operations-desc">快速访问常用功能，提升工作效率</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshOperations">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="showCustomModal = true">
            <template #icon><PlusOutlined /></template>
            自定义操作
          </a-button>
          <a-button type="primary" @click="showSettings = true">
            <template #icon><SettingOutlined /></template>
            操作设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 操作统计 -->
    <div class="operation-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日操作" 
              :value="operationStats.today" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ThunderboltOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="常用功能" 
              :value="operationStats.frequent" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><StarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="自定义操作" 
              :value="operationStats.custom" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><ToolOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="节省时间" 
              :value="operationStats.timeSaved" 
              :value-style="{ color: '#faad14' }"
              suffix="分钟"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧操作面板 -->
      <a-col :span="18">
        <!-- 常用操作 -->
        <a-card title="常用操作" class="operations-card">
          <template #extra>
            <a-radio-group v-model:value="viewMode" size="small">
              <a-radio-button value="grid">网格视图</a-radio-button>
              <a-radio-button value="list">列表视图</a-radio-button>
            </a-radio-group>
          </template>
          
          <div v-if="viewMode === 'grid'" class="operations-grid">
            <div 
              v-for="operation in frequentOperations" 
              :key="operation.id"
              class="operation-card"
              @click="executeOperation(operation)"
            >
              <div class="operation-icon">
                <component :is="operation.icon" :style="{ color: operation.color }" />
              </div>
              <div class="operation-content">
                <div class="operation-title">{{ operation.title }}</div>
                <div class="operation-desc">{{ operation.description }}</div>
                <div class="operation-stats">
                  <span class="usage-count">今日使用 {{ operation.todayUsage }}次</span>
                </div>
              </div>
              <div class="operation-actions">
                <a-tooltip title="添加到收藏">
                  <a-button type="link" size="small" @click.stop="toggleFavorite(operation)">
                    <StarOutlined :class="{ 'favorited': operation.favorited }" />
                  </a-button>
                </a-tooltip>
              </div>
            </div>
          </div>

          <div v-else class="operations-list">
            <a-table 
              :columns="operationColumns" 
              :data-source="frequentOperations"
              :pagination="false"
              size="middle"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'operation'">
                  <div class="operation-info">
                    <component :is="record.icon" :style="{ color: record.color }" />
                    <div class="operation-details">
                      <div class="operation-name">{{ record.title }}</div>
                      <div class="operation-description">{{ record.description }}</div>
                    </div>
                  </div>
                </template>
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button type="link" size="small" @click="executeOperation(record)">
                      执行
                    </a-button>
                    <a-button type="link" size="small" @click="toggleFavorite(record)">
                      {{ record.favorited ? '取消收藏' : '收藏' }}
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-card>

        <!-- 自定义操作 -->
        <a-card title="自定义操作" class="operations-card">
          <template #extra>
            <a @click="showCustomModal = true">添加操作</a>
          </template>
          
          <div class="custom-operations">
            <div 
              v-for="operation in customOperations" 
              :key="operation.id"
              class="custom-operation-item"
            >
              <div class="custom-operation-header">
                <div class="custom-operation-info">
                  <component :is="operation.icon" :style="{ color: operation.color }" />
                  <span class="custom-operation-name">{{ operation.name }}</span>
                </div>
                <div class="custom-operation-actions">
                  <a-button type="link" size="small" @click="executeCustomOperation(operation)">
                    执行
                  </a-button>
                  <a-button type="link" size="small" @click="editCustomOperation(operation)">
                    编辑
                  </a-button>
                  <a-button type="link" size="small" danger @click="deleteCustomOperation(operation)">
                    删除
                  </a-button>
                </div>
              </div>
              <div class="custom-operation-content">
                <div class="custom-operation-desc">{{ operation.description }}</div>
                <div class="custom-operation-config">
                  <a-tag v-for="step in operation.steps.slice(0, 3)" :key="step.id" size="small">
                    {{ step.name }}
                  </a-tag>
                  <span v-if="operation.steps.length > 3" class="more-steps">
                    +{{ operation.steps.length - 3 }}个步骤
                  </span>
                </div>
              </div>
            </div>
            <a-empty v-if="customOperations.length === 0" description="暂无自定义操作" />
          </div>
        </a-card>

        <!-- 批量工具 -->
        <a-card title="批量工具" class="operations-card">
          <div class="batch-tools">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="batch-tool-item" @click="showBatchModal('import')">
                  <div class="batch-tool-icon">
                    <ImportOutlined />
                  </div>
                  <div class="batch-tool-content">
                    <div class="batch-tool-title">批量导入</div>
                    <div class="batch-tool-desc">Excel批量导入案件</div>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="batch-tool-item" @click="showBatchModal('export')">
                  <div class="batch-tool-icon">
                    <ExportOutlined />
                  </div>
                  <div class="batch-tool-content">
                    <div class="batch-tool-title">批量导出</div>
                    <div class="batch-tool-desc">导出筛选结果</div>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="batch-tool-item" @click="showBatchModal('update')">
                  <div class="batch-tool-icon">
                    <SyncOutlined />
                  </div>
                  <div class="batch-tool-content">
                    <div class="batch-tool-title">批量更新</div>
                    <div class="batch-tool-desc">批量更新案件状态</div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧工具栏 -->
      <a-col :span="6">
        <!-- 效率工具 -->
        <a-card title="效率工具" class="operations-card">
          <div class="efficiency-tools">
            <div 
              v-for="tool in efficiencyTools" 
              :key="tool.id"
              class="efficiency-tool-item"
              @click="useTool(tool)"
            >
              <div class="tool-icon">
                <component :is="tool.icon" :style="{ color: tool.color }" />
              </div>
              <div class="tool-content">
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-desc">{{ tool.description }}</div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 操作历史 -->
        <a-card title="操作历史" class="operations-card">
          <div class="operation-history">
            <div 
              v-for="history in operationHistory" 
              :key="history.id"
              class="history-item"
            >
              <div class="history-icon">
                <component :is="history.icon" :style="{ color: history.color }" />
              </div>
              <div class="history-content">
                <div class="history-title">{{ history.operation }}</div>
                <div class="history-time">{{ history.time }}</div>
              </div>
              <div class="history-actions">
                <a-button type="link" size="small" @click="repeatOperation(history)">
                  重复
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 快捷键提示 -->
        <a-card title="快捷键" class="operations-card">
          <div class="shortcuts">
            <div 
              v-for="shortcut in shortcuts" 
              :key="shortcut.id"
              class="shortcut-item"
            >
              <div class="shortcut-keys">
                <a-tag v-for="key in shortcut.keys" :key="key" size="small">{{ key }}</a-tag>
              </div>
              <div class="shortcut-desc">{{ shortcut.description }}</div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 自定义操作模态框 -->
    <a-modal
      v-model:open="showCustomModal"
      title="创建自定义操作"
      width="600px"
      @ok="saveCustomOperation"
    >
      <a-form :model="customOperationForm" layout="vertical">
        <a-form-item label="操作名称" required>
          <a-input v-model:value="customOperationForm.name" placeholder="请输入操作名称" />
        </a-form-item>
        <a-form-item label="操作描述">
          <a-textarea v-model:value="customOperationForm.description" placeholder="描述操作功能" :rows="3" />
        </a-form-item>
        <a-form-item label="图标">
          <a-select v-model:value="customOperationForm.icon" placeholder="选择图标">
            <a-select-option value="ToolOutlined">工具</a-select-option>
            <a-select-option value="ThunderboltOutlined">闪电</a-select-option>
            <a-select-option value="RocketOutlined">火箭</a-select-option>
            <a-select-option value="BulbOutlined">灯泡</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="操作步骤">
          <div class="operation-steps">
            <div 
              v-for="(step, index) in customOperationForm.steps" 
              :key="index"
              class="step-item"
            >
              <a-input 
                v-model:value="step.name" 
                placeholder="步骤名称" 
                style="flex: 1"
              />
              <a-select 
                v-model:value="step.type" 
                style="width: 120px; margin-left: 8px"
                placeholder="步骤类型"
              >
                <a-select-option value="navigate">导航</a-select-option>
                <a-select-option value="action">操作</a-select-option>
                <a-select-option value="input">输入</a-select-option>
              </a-select>
              <a-button 
                type="link" 
                danger 
                @click="removeStep(index)"
                style="margin-left: 8px"
              >
                删除
              </a-button>
            </div>
            <a-button type="dashed" block @click="addStep">
              <PlusOutlined /> 添加步骤
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 操作设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="操作设置"
      @ok="saveOperationSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="显示模式">
          <a-radio-group v-model:value="operationSettings.defaultView">
            <a-radio value="grid">网格视图</a-radio>
            <a-radio value="list">列表视图</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="显示内容">
          <a-checkbox-group v-model:value="operationSettings.visibleSections">
            <a-checkbox value="frequent">常用操作</a-checkbox>
            <a-checkbox value="custom">自定义操作</a-checkbox>
            <a-checkbox value="batch">批量工具</a-checkbox>
            <a-checkbox value="efficiency">效率工具</a-checkbox>
            <a-checkbox value="history">操作历史</a-checkbox>
            <a-checkbox value="shortcuts">快捷键</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="确认模式">
          <a-switch v-model:checked="operationSettings.confirmBeforeExecute" />
          <span style="margin-left: 8px">执行操作前需要确认</span>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  StarOutlined,
  ToolOutlined,
  ClockCircleOutlined,
  ImportOutlined,
  ExportOutlined,
  SyncOutlined,
  FileTextOutlined,
  PhoneOutlined,
  MessageOutlined,
  UserOutlined,
  SearchOutlined,
  BarChartOutlined,
  MoneyCollectOutlined,
  RocketOutlined,
  BulbOutlined,
  CalculatorOutlined,
  CalendarOutlined,
  CameraOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showCustomModal = ref(false)
const showSettings = ref(false)
const viewMode = ref('grid')

// 操作统计
const operationStats = reactive({
  today: 24,
  frequent: 12,
  custom: 5,
  timeSaved: 156
})

// 常用操作
const frequentOperations = ref([
  {
    id: 1,
    title: '创建案件',
    description: '快速创建新的催收案件',
    icon: 'PlusOutlined',
    color: '#1890ff',
    todayUsage: 8,
    favorited: true
  },
  {
    id: 2,
    title: '拨打电话',
    description: '一键拨打客户电话',
    icon: 'PhoneOutlined',
    color: '#52c41a',
    todayUsage: 15,
    favorited: false
  },
  {
    id: 3,
    title: '发送短信',
    description: '批量发送催收短信',
    icon: 'MessageOutlined',
    color: '#faad14',
    todayUsage: 6,
    favorited: true
  },
  {
    id: 4,
    title: '客户查询',
    description: '快速查询客户信息',
    icon: 'SearchOutlined',
    color: '#722ed1',
    todayUsage: 12,
    favorited: false
  },
  {
    id: 5,
    title: '还款登记',
    description: '登记客户还款记录',
    icon: 'MoneyCollectOutlined',
    color: '#13c2c2',
    todayUsage: 4,
    favorited: true
  },
  {
    id: 6,
    title: '生成报表',
    description: '生成工作统计报表',
    icon: 'BarChartOutlined',
    color: '#eb2f96',
    todayUsage: 2,
    favorited: false
  }
])

// 表格列定义
const operationColumns = [
  {
    title: '操作',
    key: 'operation',
    width: '60%'
  },
  {
    title: '今日使用',
    dataIndex: 'todayUsage',
    key: 'todayUsage',
    width: '20%'
  },
  {
    title: '操作',
    key: 'actions',
    width: '20%'
  }
]

// 自定义操作
const customOperations = ref([
  {
    id: 1,
    name: '案件批量分配',
    description: '根据规则自动分配案件给催收员',
    icon: 'ThunderboltOutlined',
    color: '#1890ff',
    steps: [
      { id: 1, name: '选择案件', type: 'action' },
      { id: 2, name: '设置分配规则', type: 'input' },
      { id: 3, name: '执行分配', type: 'action' }
    ]
  },
  {
    id: 2,
    name: '客户信息同步',
    description: '从第三方系统同步客户最新信息',
    icon: 'SyncOutlined',
    color: '#52c41a',
    steps: [
      { id: 1, name: '连接外部系统', type: 'action' },
      { id: 2, name: '获取客户数据', type: 'action' },
      { id: 3, name: '更新本地数据', type: 'action' }
    ]
  }
])

// 自定义操作表单
const customOperationForm = reactive({
  name: '',
  description: '',
  icon: 'ToolOutlined',
  steps: [
    { name: '', type: 'action' }
  ]
})

// 效率工具
const efficiencyTools = ref([
  {
    id: 1,
    name: '计算器',
    description: '快速计算',
    icon: 'CalculatorOutlined',
    color: '#1890ff'
  },
  {
    id: 2,
    name: '日历',
    description: '查看日程',
    icon: 'CalendarOutlined',
    color: '#52c41a'
  },
  {
    id: 3,
    name: '截图',
    description: '截图工具',
    icon: 'CameraOutlined',
    color: '#faad14'
  },
  {
    id: 4,
    name: '记事本',
    description: '快速记录',
    icon: 'FileTextOutlined',
    color: '#722ed1'
  }
])

// 操作历史
const operationHistory = ref([
  {
    id: 1,
    operation: '创建案件',
    time: '10分钟前',
    icon: 'PlusOutlined',
    color: '#1890ff'
  },
  {
    id: 2,
    operation: '拨打电话',
    time: '25分钟前',
    icon: 'PhoneOutlined',
    color: '#52c41a'
  },
  {
    id: 3,
    operation: '发送短信',
    time: '1小时前',
    icon: 'MessageOutlined',
    color: '#faad14'
  },
  {
    id: 4,
    operation: '还款登记',
    time: '2小时前',
    icon: 'MoneyCollectOutlined',
    color: '#13c2c2'
  }
])

// 快捷键
const shortcuts = ref([
  {
    id: 1,
    keys: ['Ctrl', 'N'],
    description: '创建新案件'
  },
  {
    id: 2,
    keys: ['Ctrl', 'F'],
    description: '查询客户'
  },
  {
    id: 3,
    keys: ['Ctrl', 'S'],
    description: '保存记录'
  },
  {
    id: 4,
    keys: ['Ctrl', 'P'],
    description: '拨打电话'
  }
])

// 操作设置
const operationSettings = reactive({
  defaultView: 'grid',
  visibleSections: ['frequent', 'custom', 'batch', 'efficiency', 'history', 'shortcuts'],
  confirmBeforeExecute: false
})

// 方法定义
const refreshOperations = () => {
  console.log('刷新操作列表')
}

const executeOperation = (operation) => {
  console.log('执行操作:', operation.title)
}

const toggleFavorite = (operation) => {
  operation.favorited = !operation.favorited
  console.log('切换收藏状态:', operation.title, operation.favorited)
}

const executeCustomOperation = (operation) => {
  console.log('执行自定义操作:', operation.name)
}

const editCustomOperation = (operation) => {
  console.log('编辑自定义操作:', operation.name)
}

const deleteCustomOperation = (operation) => {
  const index = customOperations.value.findIndex(op => op.id === operation.id)
  if (index > -1) {
    customOperations.value.splice(index, 1)
  }
  console.log('删除自定义操作:', operation.name)
}

const showBatchModal = (type) => {
  console.log('显示批量操作模态框:', type)
}

const useTool = (tool) => {
  console.log('使用效率工具:', tool.name)
}

const repeatOperation = (history) => {
  console.log('重复操作:', history.operation)
}

const addStep = () => {
  customOperationForm.steps.push({ name: '', type: 'action' })
}

const removeStep = (index) => {
  if (customOperationForm.steps.length > 1) {
    customOperationForm.steps.splice(index, 1)
  }
}

const saveCustomOperation = () => {
  const newOperation = {
    id: Date.now(),
    name: customOperationForm.name,
    description: customOperationForm.description,
    icon: customOperationForm.icon,
    color: '#1890ff',
    steps: [...customOperationForm.steps]
  }
  
  customOperations.value.push(newOperation)
  
  // 重置表单
  Object.assign(customOperationForm, {
    name: '',
    description: '',
    icon: 'ToolOutlined',
    steps: [{ name: '', type: 'action' }]
  })
  
  showCustomModal.value = false
  console.log('保存自定义操作:', newOperation)
}

const saveOperationSettings = () => {
  console.log('保存操作设置:', operationSettings)
  showSettings.value = false
}

// 生命周期
onMounted(() => {
  refreshOperations()
})
</script>

<style scoped>
.quick-operations {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operations-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.operations-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.operation-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.operations-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.operation-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.operation-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.operation-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f6f8ff;
  border-radius: 8px;
  margin-bottom: 12px;
  font-size: 20px;
}

.operation-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.operation-desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.operation-stats {
  font-size: 12px;
  color: #999;
}

.operation-actions {
  position: absolute;
  top: 12px;
  right: 12px;
}

.favorited {
  color: #faad14;
}

.operation-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.operation-details {
  flex: 1;
}

.operation-name {
  font-weight: 500;
  color: #262626;
}

.operation-description {
  font-size: 12px;
  color: #999;
}

.custom-operations {
  max-height: 400px;
  overflow-y: auto;
}

.custom-operation-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.custom-operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.custom-operation-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-operation-name {
  font-weight: 500;
  color: #262626;
}

.custom-operation-actions {
  display: flex;
  gap: 4px;
}

.custom-operation-desc {
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.custom-operation-config {
  display: flex;
  align-items: center;
  gap: 4px;
}

.more-steps {
  font-size: 12px;
  color: #999;
}

.batch-tools {
  padding: 8px 0;
}

.batch-tool-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  height: 80px;
}

.batch-tool-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.batch-tool-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f6f8ff;
  border-radius: 8px;
  color: #1890ff;
  font-size: 18px;
}

.batch-tool-content {
  flex: 1;
}

.batch-tool-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.batch-tool-desc {
  font-size: 12px;
  color: #999;
}

.efficiency-tools {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.efficiency-tool-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.efficiency-tool-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.tool-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
  margin-bottom: 2px;
}

.tool-desc {
  font-size: 11px;
  color: #999;
}

.operation-history {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-content {
  flex: 1;
}

.history-title {
  font-size: 13px;
  color: #262626;
  margin-bottom: 2px;
}

.history-time {
  font-size: 11px;
  color: #999;
}

.shortcuts {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.shortcut-keys {
  display: flex;
  gap: 2px;
  min-width: 80px;
}

.shortcut-desc {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.operation-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: center;
}
</style>