<template>
  <div class="message-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="message-info">
          <h2>消息中心</h2>
          <p class="message-desc">统一管理系统通知、消息提醒和公告信息</p>
        </div>
        <div class="header-actions">
          <a-button @click="markAllAsRead">
            <template #icon><CheckOutlined /></template>
            全部已读
          </a-button>
          <a-button @click="refreshMessages">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showSettings = true">
            <template #icon><SettingOutlined /></template>
            消息设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 消息统计 -->
    <div class="message-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="未读消息" 
              :value="messageStats.unread" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><MailOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日消息" 
              :value="messageStats.today" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><BellOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="系统通知" 
              :value="messageStats.system" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><NotificationOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="工作提醒" 
              :value="messageStats.work" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧消息列表 -->
      <a-col :span="16">
        <a-card title="消息列表" class="message-card">
          <template #extra>
            <a-space>
              <a-select v-model:value="messageFilter" style="width: 120px" @change="filterMessages">
                <a-select-option value="all">全部消息</a-select-option>
                <a-select-option value="unread">未读消息</a-select-option>
                <a-select-option value="system">系统通知</a-select-option>
                <a-select-option value="work">工作提醒</a-select-option>
                <a-select-option value="announcement">公告消息</a-select-option>
              </a-select>
              <a-input-search 
                v-model:value="searchKeyword" 
                placeholder="搜索消息" 
                style="width: 200px"
                @search="searchMessages"
              />
            </a-space>
          </template>
          
          <div class="message-list">
            <div 
              v-for="message in filteredMessages" 
              :key="message.id"
              class="message-item"
              :class="{ 'message-unread': !message.read, 'message-selected': selectedMessage?.id === message.id }"
              @click="selectMessage(message)"
            >
              <div class="message-icon">
                <a-badge :dot="!message.read">
                  <component :is="getMessageIcon(message.type)" :style="{ color: getMessageColor(message.type) }" />
                </a-badge>
              </div>
              <div class="message-content">
                <div class="message-title">{{ message.title }}</div>
                <div class="message-preview">{{ message.content.substring(0, 50) }}...</div>
                <div class="message-meta">
                  <a-tag :color="getMessageTypeColor(message.type)" size="small">
                    {{ getMessageTypeText(message.type) }}
                  </a-tag>
                  <span class="message-time">{{ message.time }}</span>
                </div>
              </div>
              <div class="message-actions">
                <a-button type="link" size="small" @click.stop="markAsRead(message)">
                  <CheckOutlined />
                </a-button>
                <a-button type="link" size="small" @click.stop="deleteMessage(message)">
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>
            <a-empty v-if="filteredMessages.length === 0" description="暂无消息" />
          </div>
          
          <div class="message-pagination">
            <a-pagination 
              v-model:current="pagination.current"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              show-size-changer
              show-quick-jumper
              @change="handlePageChange"
            />
          </div>
        </a-card>
      </a-col>

      <!-- 右侧消息详情和快捷操作 -->
      <a-col :span="8">
        <!-- 消息详情 -->
        <a-card v-if="selectedMessage" title="消息详情" class="message-card">
          <div class="message-detail">
            <div class="detail-header">
              <h3>{{ selectedMessage.title }}</h3>
              <div class="detail-meta">
                <a-tag :color="getMessageTypeColor(selectedMessage.type)">
                  {{ getMessageTypeText(selectedMessage.type) }}
                </a-tag>
                <span class="detail-time">{{ selectedMessage.time }}</span>
              </div>
            </div>
            <div class="detail-content" v-html="selectedMessage.content"></div>
            <div class="detail-actions">
              <a-button v-if="selectedMessage.actionRequired" type="primary" @click="handleMessageAction(selectedMessage)">
                {{ selectedMessage.actionText || '立即处理' }}
              </a-button>
              <a-button @click="markAsRead(selectedMessage)">标记已读</a-button>
              <a-button danger @click="deleteMessage(selectedMessage)">删除</a-button>
            </div>
          </div>
        </a-card>

        <!-- 快捷操作 -->
        <a-card title="快捷操作" class="message-card">
          <div class="quick-operations">
            <a-button 
              v-for="operation in quickOperations" 
              :key="operation.id"
              block 
              class="operation-btn"
              @click="executeOperation(operation)"
            >
              <component :is="operation.icon" />
              {{ operation.name }}
            </a-button>
          </div>
        </a-card>

        <!-- 消息分类 -->
        <a-card title="消息分类" class="message-card">
          <div class="message-categories">
            <div 
              v-for="category in messageCategories" 
              :key="category.type"
              class="category-item"
              @click="filterByCategory(category.type)"
            >
              <div class="category-icon">
                <component :is="category.icon" :style="{ color: category.color }" />
              </div>
              <div class="category-content">
                <div class="category-name">{{ category.name }}</div>
                <div class="category-count">{{ category.count }}条</div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 消息设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="消息设置"
      @ok="saveMessageSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="消息提醒">
          <a-checkbox-group v-model:value="messageSettings.notifications">
            <a-checkbox value="system">系统通知</a-checkbox>
            <a-checkbox value="work">工作提醒</a-checkbox>
            <a-checkbox value="announcement">公告消息</a-checkbox>
            <a-checkbox value="email">邮件提醒</a-checkbox>
            <a-checkbox value="sms">短信提醒</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="提醒方式">
          <a-radio-group v-model:value="messageSettings.alertType">
            <a-radio value="popup">弹窗提醒</a-radio>
            <a-radio value="sound">声音提醒</a-radio>
            <a-radio value="both">弹窗+声音</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="免打扰时间">
          <a-time-range-picker 
            v-model:value="messageSettings.quietHours" 
            format="HH:mm"
            placeholder="设置免打扰时间段"
          />
        </a-form-item>
        <a-form-item label="消息保留时间">
          <a-select v-model:value="messageSettings.retentionDays">
            <a-select-option :value="7">7天</a-select-option>
            <a-select-option :value="30">30天</a-select-option>
            <a-select-option :value="90">90天</a-select-option>
            <a-select-option :value="365">1年</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import {
  CheckOutlined,
  ReloadOutlined,
  SettingOutlined,
  MailOutlined,
  BellOutlined,
  NotificationOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  SoundOutlined,
  MessageOutlined,
  FileTextOutlined,
  UserOutlined,
  SendOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showSettings = ref(false)
const messageFilter = ref('all')
const searchKeyword = ref('')
const selectedMessage = ref(null)

// 消息统计
const messageStats = reactive({
  unread: 15,
  today: 28,
  system: 12,
  work: 16
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156
})

// 消息列表
const messages = ref([
  {
    id: 1,
    title: '新案件分配通知',
    content: '您有3个新的催收案件已分配，请及时处理。案件编号：CASE20240115001、CASE20240115002、CASE20240115003',
    type: 'work',
    time: '10分钟前',
    read: false,
    actionRequired: true,
    actionText: '查看案件'
  },
  {
    id: 2,
    title: '系统升级通知',
    content: '系统将于今晚22:00-24:00进行升级维护，期间可能影响正常使用，请提前安排工作。',
    type: 'system',
    time: '2小时前',
    read: false,
    actionRequired: false
  },
  {
    id: 3,
    title: '客户还款到账提醒',
    content: '客户张三（手机：138****1234）已成功还款5000元，案件CASE20240110001状态已更新。',
    type: 'work',
    time: '3小时前',
    read: true,
    actionRequired: true,
    actionText: '查看详情'
  },
  {
    id: 4,
    title: '月度工作总结提醒',
    content: '本月工作总结报告截止日期为1月20日，请及时提交您的工作总结和下月计划。',
    type: 'announcement',
    time: '1天前',
    read: false,
    actionRequired: true,
    actionText: '立即提交'
  },
  {
    id: 5,
    title: '催收技巧培训通知',
    content: '部门将于1月25日下午14:00举办催收技巧提升培训，地点在会议室A，请准时参加。',
    type: 'announcement',
    time: '2天前',
    read: true,
    actionRequired: false
  },
  {
    id: 6,
    title: '逾期案件预警',
    content: '您负责的案件CASE20240105001已逾期5天未处理，请尽快联系客户或更新案件状态。',
    type: 'work',
    time: '3天前',
    read: false,
    actionRequired: true,
    actionText: '立即处理'
  },
  {
    id: 7,
    title: '密码安全提醒',
    content: '您的账户密码已超过90天未更新，为保证账户安全，建议您及时修改密码。',
    type: 'system',
    time: '5天前',
    read: true,
    actionRequired: true,
    actionText: '修改密码'
  },
  {
    id: 8,
    title: '绩效考核通知',
    content: '1月份绩效考核已开始，请在本月底前完成自评，并提交相关证明材料。',
    type: 'announcement',
    time: '1周前',
    read: true,
    actionRequired: false
  }
])

// 快捷操作
const quickOperations = ref([
  {
    id: 1,
    name: '发送通知',
    icon: 'SendOutlined'
  },
  {
    id: 2,
    name: '消息模板',
    icon: 'FileTextOutlined'
  },
  {
    id: 3,
    name: '群发消息',
    icon: 'SoundOutlined'
  },
  {
    id: 4,
    name: '联系管理员',
    icon: 'UserOutlined'
  }
])

// 消息分类
const messageCategories = ref([
  {
    type: 'system',
    name: '系统通知',
    icon: 'NotificationOutlined',
    color: '#52c41a',
    count: 12
  },
  {
    type: 'work',
    name: '工作提醒',
    icon: 'ClockCircleOutlined',
    color: '#722ed1',
    count: 16
  },
  {
    type: 'announcement',
    name: '公告消息',
    icon: 'SoundOutlined',
    color: '#1890ff',
    count: 8
  }
])

// 消息设置
const messageSettings = reactive({
  notifications: ['system', 'work', 'announcement'],
  alertType: 'popup',
  quietHours: null,
  retentionDays: 30
})

// 计算属性
const filteredMessages = computed(() => {
  let result = messages.value

  // 按类型筛选
  if (messageFilter.value !== 'all') {
    if (messageFilter.value === 'unread') {
      result = result.filter(msg => !msg.read)
    } else {
      result = result.filter(msg => msg.type === messageFilter.value)
    }
  }

  // 关键词搜索
  if (searchKeyword.value) {
    result = result.filter(msg => 
      msg.title.includes(searchKeyword.value) || 
      msg.content.includes(searchKeyword.value)
    )
  }

  return result
})

// 方法定义
const getMessageIcon = (type) => {
  const icons = {
    system: 'NotificationOutlined',
    work: 'ClockCircleOutlined',
    announcement: 'SoundOutlined',
    warning: 'WarningOutlined'
  }
  return icons[type] || 'InfoCircleOutlined'
}

const getMessageColor = (type) => {
  const colors = {
    system: '#52c41a',
    work: '#722ed1',
    announcement: '#1890ff',
    warning: '#faad14'
  }
  return colors[type] || '#8c8c8c'
}

const getMessageTypeColor = (type) => {
  const colors = {
    system: 'green',
    work: 'purple',
    announcement: 'blue',
    warning: 'orange'
  }
  return colors[type] || 'default'
}

const getMessageTypeText = (type) => {
  const texts = {
    system: '系统通知',
    work: '工作提醒',
    announcement: '公告消息',
    warning: '警告消息'
  }
  return texts[type] || type
}

const refreshMessages = () => {
  console.log('刷新消息列表')
}

const markAllAsRead = () => {
  messages.value.forEach(msg => {
    msg.read = true
  })
  console.log('所有消息已标记为已读')
}

const markAsRead = (message) => {
  message.read = true
  console.log('消息已标记为已读:', message.title)
}

const deleteMessage = (message) => {
  const index = messages.value.findIndex(msg => msg.id === message.id)
  if (index > -1) {
    messages.value.splice(index, 1)
    if (selectedMessage.value?.id === message.id) {
      selectedMessage.value = null
    }
  }
  console.log('消息已删除:', message.title)
}

const selectMessage = (message) => {
  selectedMessage.value = message
  if (!message.read) {
    markAsRead(message)
  }
}

const filterMessages = () => {
  console.log('筛选消息:', messageFilter.value)
}

const searchMessages = () => {
  console.log('搜索消息:', searchKeyword.value)
}

const filterByCategory = (type) => {
  messageFilter.value = type
  filterMessages()
}

const handlePageChange = () => {
  console.log('分页变化:', pagination)
}

const handleMessageAction = (message) => {
  console.log('处理消息操作:', message)
}

const executeOperation = (operation) => {
  console.log('执行快捷操作:', operation)
}

const saveMessageSettings = () => {
  console.log('保存消息设置:', messageSettings)
  showSettings.value = false
}

// 生命周期
onMounted(() => {
  refreshMessages()
})
</script>

<style scoped>
.message-center {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.message-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.message-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-list {
  max-height: 600px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:hover {
  background-color: #f6f8ff;
}

.message-item.message-unread {
  background-color: #fff7e6;
  border-left: 4px solid #faad14;
}

.message-item.message-selected {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
}

.message-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f6f8ff;
}

.message-content {
  flex: 1;
}

.message-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.message-preview {
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-actions {
  display: flex;
  gap: 4px;
}

.message-pagination {
  margin-top: 16px;
  text-align: center;
}

.message-detail {
  padding: 8px 0;
}

.detail-header {
  margin-bottom: 16px;
}

.detail-header h3 {
  margin: 0 0 8px 0;
  color: #262626;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-time {
  font-size: 12px;
  color: #999;
}

.detail-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.quick-operations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-start;
  height: 40px;
}

.message-categories {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.category-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.category-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-content {
  flex: 1;
}

.category-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.category-count {
  font-size: 12px;
  color: #999;
}
</style>