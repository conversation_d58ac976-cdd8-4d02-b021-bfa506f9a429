<template>
  <div class="management-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="dashboard-info">
          <h2>管理驾驶舱</h2>
          <p class="dashboard-desc">实时掌控业务运营状况，支持科学决策</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportReport">
            <template #icon><DownloadOutlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="showSettings = true">
            <template #icon><SettingOutlined /></template>
            仪表板设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 核心指标 -->
    <div class="core-metrics">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic 
              title="总案件数" 
              :value="coreMetrics.totalCases" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><FileOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="coreMetrics.casesTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="coreMetrics.casesTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(coreMetrics.casesTrend) }}%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic 
              title="回收金额" 
              :value="coreMetrics.recoveredAmount" 
              :value-style="{ color: '#52c41a' }"
              suffix="万元"
            >
              <template #prefix><MoneyCollectOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="coreMetrics.amountTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="coreMetrics.amountTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(coreMetrics.amountTrend) }}%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic 
              title="回收率" 
              :value="coreMetrics.recoveryRate" 
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><PercentageOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="coreMetrics.rateTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="coreMetrics.rateTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(coreMetrics.rateTrend) }}%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic 
              title="客户满意度" 
              :value="coreMetrics.satisfaction" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><StarOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="coreMetrics.satisfactionTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="coreMetrics.satisfactionTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(coreMetrics.satisfactionTrend) }}%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧图表区 -->
      <a-col :span="16">
        <!-- 业务趋势分析 -->
        <a-card title="业务趋势分析" class="dashboard-card">
          <template #extra>
            <a-radio-group v-model:value="trendPeriod" size="small">
              <a-radio-button value="7d">7天</a-radio-button>
              <a-radio-button value="30d">30天</a-radio-button>
              <a-radio-button value="90d">90天</a-radio-button>
            </a-radio-group>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </a-card>

        <!-- 部门绩效对比 -->
        <a-card title="部门绩效对比" class="dashboard-card">
          <div class="department-comparison">
            <div 
              v-for="dept in departmentPerformance" 
              :key="dept.id"
              class="dept-item"
            >
              <div class="dept-header">
                <span class="dept-name">{{ dept.name }}</span>
                <span class="dept-score" :class="getScoreClass(dept.score)">{{ dept.score }}分</span>
              </div>
              <div class="dept-metrics">
                <div class="metric-row">
                  <span class="metric-label">案件完成率</span>
                  <a-progress :percent="dept.caseCompletionRate" size="small" />
                  <span class="metric-value">{{ dept.caseCompletionRate }}%</span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">回收达成率</span>
                  <a-progress :percent="dept.recoveryAchievementRate" size="small" stroke-color="#52c41a" />
                  <span class="metric-value">{{ dept.recoveryAchievementRate }}%</span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">客户满意度</span>
                  <a-progress :percent="dept.customerSatisfaction" size="small" stroke-color="#722ed1" />
                  <span class="metric-value">{{ dept.customerSatisfaction }}%</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 案件分布分析 -->
        <a-card title="案件分布分析" class="dashboard-card">
          <a-row :gutter="16">
            <a-col :span="12">
              <div ref="caseStatusChart" class="chart-container-small"></div>
            </a-col>
            <a-col :span="12">
              <div ref="caseTypeChart" class="chart-container-small"></div>
            </a-col>
          </a-row>
        </a-card>
      </a-col>

      <!-- 右侧监控区 -->
      <a-col :span="8">
        <!-- 实时监控 -->
        <a-card title="实时监控" class="dashboard-card">
          <div class="real-time-monitor">
            <div class="monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">在线催收员</span>
                <span class="monitor-status online">{{ realTimeData.onlineCollectors }}人</span>
              </div>
              <div class="monitor-desc">当前在线工作人员数量</div>
            </div>
            <a-divider />
            <div class="monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">今日新增案件</span>
                <span class="monitor-status">{{ realTimeData.todayNewCases }}件</span>
              </div>
              <div class="monitor-desc">今日系统新增案件数量</div>
            </div>
            <a-divider />
            <div class="monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">今日回收金额</span>
                <span class="monitor-status success">{{ realTimeData.todayRecoveredAmount }}万元</span>
              </div>
              <div class="monitor-desc">今日累计回收金额</div>
            </div>
            <a-divider />
            <div class="monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">系统响应时间</span>
                <span class="monitor-status" :class="getResponseTimeClass(realTimeData.responseTime)">
                  {{ realTimeData.responseTime }}ms
                </span>
              </div>
              <div class="monitor-desc">系统平均响应时间</div>
            </div>
          </div>
        </a-card>

        <!-- 风险预警 -->
        <a-card title="风险预警" class="dashboard-card">
          <template #extra>
            <a-badge :count="riskWarnings.filter(r => r.level === 'high').length" />
          </template>
          <div class="risk-warnings">
            <div 
              v-for="warning in riskWarnings" 
              :key="warning.id"
              class="warning-item"
              :class="`warning-${warning.level}`"
            >
              <div class="warning-header">
                <component :is="getWarningIcon(warning.level)" class="warning-icon" />
                <span class="warning-title">{{ warning.title }}</span>
                <a-tag :color="getWarningColor(warning.level)" size="small">
                  {{ getWarningLevelText(warning.level) }}
                </a-tag>
              </div>
              <div class="warning-content">{{ warning.content }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </a-card>

        <!-- 决策建议 -->
        <a-card title="AI决策建议" class="dashboard-card">
          <div class="decision-suggestions">
            <div 
              v-for="suggestion in decisionSuggestions" 
              :key="suggestion.id"
              class="suggestion-item"
            >
              <div class="suggestion-header">
                <BulbOutlined class="suggestion-icon" />
                <span class="suggestion-title">{{ suggestion.title }}</span>
              </div>
              <div class="suggestion-content">{{ suggestion.content }}</div>
              <div class="suggestion-actions">
                <a-button size="small" type="link" @click="viewSuggestionDetail(suggestion)">
                  查看详情
                </a-button>
                <a-button size="small" type="link" @click="applySuggestion(suggestion)">
                  采纳建议
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 快速操作 -->
        <a-card title="快速操作" class="dashboard-card">
          <div class="quick-operations">
            <a-button 
              v-for="operation in quickOperations" 
              :key="operation.id"
              block 
              class="operation-btn"
              @click="executeOperation(operation)"
            >
              <component :is="operation.icon" />
              {{ operation.name }}
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 仪表板设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="仪表板设置"
      @ok="saveDashboardSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="显示组件">
          <a-checkbox-group v-model:value="dashboardSettings.widgets">
            <a-checkbox value="metrics">核心指标</a-checkbox>
            <a-checkbox value="trends">业务趋势</a-checkbox>
            <a-checkbox value="departments">部门对比</a-checkbox>
            <a-checkbox value="cases">案件分布</a-checkbox>
            <a-checkbox value="monitor">实时监控</a-checkbox>
            <a-checkbox value="warnings">风险预警</a-checkbox>
            <a-checkbox value="suggestions">决策建议</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="刷新频率">
          <a-select v-model:value="dashboardSettings.refreshInterval">
            <a-select-option :value="30000">30秒</a-select-option>
            <a-select-option :value="60000">1分钟</a-select-option>
            <a-select-option :value="300000">5分钟</a-select-option>
            <a-select-option :value="0">手动刷新</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="默认时间范围">
          <a-select v-model:value="dashboardSettings.defaultPeriod">
            <a-select-option value="7d">最近7天</a-select-option>
            <a-select-option value="30d">最近30天</a-select-option>
            <a-select-option value="90d">最近90天</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  FileOutlined,
  MoneyCollectOutlined,
  PercentageOutlined,
  StarOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  BulbOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  AlertOutlined,
  PlusOutlined,
  SearchOutlined,
  BarChartOutlined,
  UserOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showSettings = ref(false)
const trendPeriod = ref('30d')

// 图表引用
const trendChart = ref(null)
const caseStatusChart = ref(null)
const caseTypeChart = ref(null)

// 核心指标
const coreMetrics = reactive({
  totalCases: 1245,
  casesTrend: 12.5,
  recoveredAmount: 892.6,
  amountTrend: 18.3,
  recoveryRate: 68.4,
  rateTrend: 3.2,
  satisfaction: 87.9,
  satisfactionTrend: 5.1
})

// 部门绩效
const departmentPerformance = ref([
  {
    id: 1,
    name: '催收一部',
    score: 92,
    caseCompletionRate: 94,
    recoveryAchievementRate: 89,
    customerSatisfaction: 91
  },
  {
    id: 2,
    name: '催收二部',
    score: 87,
    caseCompletionRate: 88,
    recoveryAchievementRate: 85,
    customerSatisfaction: 89
  },
  {
    id: 3,
    name: '催收三部',
    score: 89,
    caseCompletionRate: 91,
    recoveryAchievementRate: 87,
    customerSatisfaction: 90
  }
])

// 实时数据
const realTimeData = reactive({
  onlineCollectors: 24,
  todayNewCases: 87,
  todayRecoveredAmount: 156.8,
  responseTime: 245
})

// 风险预警
const riskWarnings = ref([
  {
    id: 1,
    title: '逾期案件激增',
    content: '本周逾期案件数量较上周增长35%，需要重点关注',
    level: 'high',
    time: '10分钟前'
  },
  {
    id: 2,
    title: '回收率下降',
    content: '催收二部本月回收率较上月下降8%',
    level: 'medium',
    time: '2小时前'
  },
  {
    id: 3,
    title: '客户投诉增加',
    content: '本周客户投诉量较平时增加20%',
    level: 'medium',
    time: '1天前'
  },
  {
    id: 4,
    title: '系统性能告警',
    content: '数据库响应时间超过阈值',
    level: 'low',
    time: '2天前'
  }
])

// 决策建议
const decisionSuggestions = ref([
  {
    id: 1,
    title: '优化案件分配策略',
    content: '建议根据催收员历史绩效和案件特征，调整自动分配算法'
  },
  {
    id: 2,
    title: '加强员工培训',
    content: '针对回收率较低的部门，建议开展专项技能培训'
  },
  {
    id: 3,
    title: '改进客户沟通方式',
    content: '分析投诉原因，优化催收话术和沟通流程'
  }
])

// 快速操作
const quickOperations = ref([
  {
    id: 1,
    name: '创建案件',
    icon: 'PlusOutlined'
  },
  {
    id: 2,
    name: '数据查询',
    icon: 'SearchOutlined'
  },
  {
    id: 3,
    name: '生成报表',
    icon: 'BarChartOutlined'
  },
  {
    id: 4,
    name: '用户管理',
    icon: 'UserOutlined'
  }
])

// 仪表板设置
const dashboardSettings = reactive({
  widgets: ['metrics', 'trends', 'departments', 'cases', 'monitor', 'warnings', 'suggestions'],
  refreshInterval: 60000,
  defaultPeriod: '30d'
})

// 方法定义
const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 70) return 'score-average'
  return 'score-poor'
}

const getResponseTimeClass = (time) => {
  if (time <= 200) return 'response-good'
  if (time <= 500) return 'response-average'
  return 'response-poor'
}

const getWarningIcon = (level) => {
  const icons = {
    high: 'AlertOutlined',
    medium: 'WarningOutlined',
    low: 'ExclamationCircleOutlined'
  }
  return icons[level] || 'ExclamationCircleOutlined'
}

const getWarningColor = (level) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[level] || 'default'
}

const getWarningLevelText = (level) => {
  const texts = {
    high: '严重',
    medium: '警告',
    low: '提醒'
  }
  return texts[level] || level
}

const refreshData = () => {
  console.log('刷新仪表板数据')
  initCharts()
}

const exportReport = () => {
  console.log('导出管理报告')
}

const viewSuggestionDetail = (suggestion) => {
  console.log('查看建议详情:', suggestion)
}

const applySuggestion = (suggestion) => {
  console.log('采纳建议:', suggestion)
}

const executeOperation = (operation) => {
  console.log('执行快速操作:', operation)
}

const saveDashboardSettings = () => {
  console.log('保存仪表板设置:', dashboardSettings)
  showSettings.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 业务趋势图
    if (trendChart.value) {
      const chart1 = echarts.init(trendChart.value)
      chart1.setOption({
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: [
          {
            type: 'value',
            name: '案件数',
            position: 'left'
          },
          {
            type: 'value',
            name: '金额(万元)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '新增案件',
            type: 'bar',
            data: [180, 192, 201, 234, 290, 330],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '完成案件',
            type: 'bar',
            data: [165, 175, 188, 210, 265, 298],
            itemStyle: { color: '#52c41a' }
          },
          {
            name: '回收金额',
            type: 'line',
            yAxisIndex: 1,
            data: [120, 132, 145, 168, 198, 225],
            lineStyle: { color: '#faad14' }
          }
        ]
      })
    }

    // 案件状态分布图
    if (caseStatusChart.value) {
      const chart2 = echarts.init(caseStatusChart.value)
      chart2.setOption({
        title: { text: '案件状态分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 485, name: '处理中' },
            { value: 325, name: '已完成' },
            { value: 234, name: '暂停' },
            { value: 156, name: '已归档' }
          ]
        }]
      })
    }

    // 案件类型分布图
    if (caseTypeChart.value) {
      const chart3 = echarts.init(caseTypeChart.value)
      chart3.setOption({
        title: { text: '案件类型分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 520, name: '信用卡' },
            { value: 380, name: '个人贷款' },
            { value: 245, name: '房贷' },
            { value: 180, name: '车贷' }
          ]
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.management-dashboard {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.dashboard-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.core-metrics {
  margin-bottom: 16px;
}

.metric-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-trend {
  margin-top: 8px;
  font-size: 12px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-text {
  color: #8c8c8c;
  margin-left: 4px;
}

.dashboard-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container {
  height: 350px;
  width: 100%;
}

.chart-container-small {
  height: 250px;
  width: 100%;
}

.department-comparison {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dept-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
}

.dept-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dept-name {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.dept-score {
  font-size: 18px;
  font-weight: bold;
}

.score-excellent {
  color: #52c41a;
}

.score-good {
  color: #1890ff;
}

.score-average {
  color: #faad14;
}

.score-poor {
  color: #ff4d4f;
}

.dept-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-row {
  display: grid;
  grid-template-columns: 80px 1fr 50px;
  gap: 12px;
  align-items: center;
}

.metric-label {
  font-size: 13px;
  color: #666;
}

.metric-value {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.real-time-monitor {
  padding: 8px 0;
}

.monitor-item {
  padding: 8px 0;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.monitor-label {
  font-weight: 500;
  color: #262626;
}

.monitor-status {
  font-weight: bold;
  color: #1890ff;
}

.monitor-status.online {
  color: #52c41a;
}

.monitor-status.success {
  color: #52c41a;
}

.response-good {
  color: #52c41a;
}

.response-average {
  color: #faad14;
}

.response-poor {
  color: #ff4d4f;
}

.monitor-desc {
  font-size: 12px;
  color: #999;
}

.risk-warnings {
  max-height: 300px;
  overflow-y: auto;
}

.warning-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid #d9d9d9;
}

.warning-high {
  background-color: #fff2f0;
  border-left-color: #ff4d4f;
}

.warning-medium {
  background-color: #fff7e6;
  border-left-color: #faad14;
}

.warning-low {
  background-color: #f6f8ff;
  border-left-color: #1890ff;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.warning-icon {
  font-size: 14px;
}

.warning-title {
  font-weight: 500;
  color: #262626;
  flex: 1;
}

.warning-content {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.warning-time {
  font-size: 12px;
  color: #999;
}

.decision-suggestions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.suggestion-icon {
  color: #faad14;
}

.suggestion-title {
  font-weight: 500;
  color: #262626;
}

.suggestion-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.quick-operations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-start;
  height: 40px;
}
</style>