<template>
  <div class="personal-workspace">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="welcome-section">
          <h2>你好，{{ userInfo.name }}</h2>
          <p class="welcome-text">今天是 {{ currentDate }}，{{ getGreeting() }}！</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showSettings = true">
            <template #icon><SettingOutlined /></template>
            个性化设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 快捷统计 -->
    <div class="quick-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日待办" 
              :value="stats.todayTodos" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
            <div class="stat-action">
              <a @click="goToTodos">查看详情</a>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="本月案件" 
              :value="stats.monthlyCases" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><FileTextOutlined /></template>
            </a-statistic>
            <div class="stat-action">
              <a @click="goToCases">处理案件</a>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="回收金额" 
              :value="stats.recoveredAmount" 
              :value-style="{ color: '#52c41a' }"
              suffix="元"
            >
              <template #prefix><MoneyCollectOutlined /></template>
            </a-statistic>
            <div class="stat-action">
              <a @click="goToPayments">回款记录</a>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="完成率" 
              :value="stats.completionRate" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
            <div class="stat-action">
              <a @click="goToReports">绩效分析</a>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 待办事项 -->
        <a-card title="今日待办" class="workspace-card">
          <template #extra>
            <a @click="showAddTodo = true">添加待办</a>
          </template>
          <div class="todo-list">
            <div 
              v-for="todo in todoList" 
              :key="todo.id" 
              class="todo-item"
              :class="{ 'todo-completed': todo.completed }"
            >
              <a-checkbox 
                v-model:checked="todo.completed" 
                @change="updateTodoStatus(todo)"
              />
              <div class="todo-content">
                <div class="todo-title">{{ todo.title }}</div>
                <div class="todo-meta">
                  <a-tag :color="getPriorityColor(todo.priority)" size="small">
                    {{ getPriorityText(todo.priority) }}
                  </a-tag>
                  <span class="todo-time">{{ todo.deadline }}</span>
                </div>
              </div>
              <div class="todo-actions">
                <a-button type="link" size="small" @click="editTodo(todo)">
                  <EditOutlined />
                </a-button>
                <a-button type="link" size="small" danger @click="deleteTodo(todo)">
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>
            <a-empty v-if="todoList.length === 0" description="暂无待办事项" />
          </div>
        </a-card>

        <!-- 工作进度 -->
        <a-card title="本月工作进度" class="workspace-card">
          <div class="progress-section">
            <div class="progress-item">
              <div class="progress-header">
                <span>案件处理进度</span>
                <span>{{ workProgress.cases.completed }}/{{ workProgress.cases.total }}</span>
              </div>
              <a-progress 
                :percent="Math.round(workProgress.cases.completed / workProgress.cases.total * 100)" 
                stroke-color="#1890ff"
              />
            </div>
            <div class="progress-item">
              <div class="progress-header">
                <span>回款目标达成</span>
                <span>{{ workProgress.payment.achieved }}万/{{ workProgress.payment.target }}万</span>
              </div>
              <a-progress 
                :percent="Math.round(workProgress.payment.achieved / workProgress.payment.target * 100)" 
                stroke-color="#52c41a"
              />
            </div>
            <div class="progress-item">
              <div class="progress-header">
                <span>客户联系率</span>
                <span>{{ workProgress.contact.rate }}%</span>
              </div>
              <a-progress 
                :percent="workProgress.contact.rate" 
                stroke-color="#722ed1"
              />
            </div>
          </div>
        </a-card>

        <!-- 最近活动 -->
        <a-card title="最近活动" class="workspace-card">
          <a-timeline>
            <a-timeline-item 
              v-for="activity in recentActivities" 
              :key="activity.id"
              :color="activity.color"
            >
              <template #dot>
                <component :is="activity.icon" :style="{ color: activity.color }" />
              </template>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 快捷操作 -->
        <a-card title="快捷操作" class="workspace-card">
          <div class="quick-actions">
            <div 
              v-for="action in quickActions" 
              :key="action.id"
              class="action-item"
              @click="handleQuickAction(action)"
            >
              <div class="action-icon">
                <component :is="action.icon" />
              </div>
              <div class="action-content">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 工作日历 -->
        <a-card title="工作日历" class="workspace-card">
          <a-calendar 
            v-model:value="selectedDate" 
            :fullscreen="false"
            @select="onDateSelect"
          >
            <template #dateCellRender="{ current }">
              <div class="calendar-cell">
                <div 
                  v-for="event in getDateEvents(current)" 
                  :key="event.id"
                  class="calendar-event"
                  :class="event.type"
                >
                  {{ event.title }}
                </div>
              </div>
            </template>
          </a-calendar>
        </a-card>

        <!-- 消息通知 -->
        <a-card title="消息通知" class="workspace-card">
          <template #extra>
            <a-badge :count="unreadCount">
              <BellOutlined />
            </a-badge>
          </template>
          <div class="notification-list">
            <div 
              v-for="notification in notifications" 
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.read }"
              @click="markAsRead(notification)"
            >
              <div class="notification-icon">
                <component :is="notification.icon" :style="{ color: notification.color }" />
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-time">{{ notification.time }}</div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 添加待办模态框 -->
    <a-modal
      v-model:open="showAddTodo"
      title="添加待办事项"
      @ok="handleAddTodo"
    >
      <a-form :model="todoForm" layout="vertical">
        <a-form-item label="待办标题" required>
          <a-input v-model:value="todoForm.title" placeholder="请输入待办标题" />
        </a-form-item>
        <a-form-item label="优先级">
          <a-select v-model:value="todoForm.priority" placeholder="选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止时间">
          <a-date-picker 
            v-model:value="todoForm.deadline" 
            show-time 
            style="width: 100%"
            placeholder="选择截止时间"
          />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:value="todoForm.description" placeholder="待办描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 个性化设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="个性化设置"
      @ok="saveSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="工作台布局">
          <a-radio-group v-model:value="settings.layout">
            <a-radio value="default">默认布局</a-radio>
            <a-radio value="compact">紧凑布局</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="显示组件">
          <a-checkbox-group v-model:value="settings.widgets">
            <a-checkbox value="stats">快捷统计</a-checkbox>
            <a-checkbox value="todos">待办事项</a-checkbox>
            <a-checkbox value="progress">工作进度</a-checkbox>
            <a-checkbox value="activities">最近活动</a-checkbox>
            <a-checkbox value="calendar">工作日历</a-checkbox>
            <a-checkbox value="notifications">消息通知</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="刷新频率">
          <a-select v-model:value="settings.refreshInterval">
            <a-select-option :value="30000">30秒</a-select-option>
            <a-select-option :value="60000">1分钟</a-select-option>
            <a-select-option :value="300000">5分钟</a-select-option>
            <a-select-option :value="0">手动刷新</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  MoneyCollectOutlined,
  TrophyOutlined,
  EditOutlined,
  DeleteOutlined,
  BellOutlined,
  PlusOutlined,
  PhoneOutlined,
  MessageOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showAddTodo = ref(false)
const showSettings = ref(false)
const selectedDate = ref(dayjs())

// 用户信息
const userInfo = reactive({
  name: '张催收',
  department: '催收一部',
  position: '催收专员'
})

// 当前日期
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日')
})

// 统计数据
const stats = reactive({
  todayTodos: 8,
  monthlyCases: 45,
  recoveredAmount: 125000,
  completionRate: 87.5
})

// 工作进度
const workProgress = reactive({
  cases: {
    completed: 32,
    total: 45
  },
  payment: {
    achieved: 12.5,
    target: 15
  },
  contact: {
    rate: 85
  }
})

// 待办事项
const todoList = ref([
  {
    id: 1,
    title: '联系客户张三讨论还款计划',
    priority: 'high',
    deadline: '2024-01-15 18:00',
    description: '客户表示愿意协商，需要制定合理的还款计划',
    completed: false
  },
  {
    id: 2,
    title: '完成本周工作报告',
    priority: 'medium',
    deadline: '2024-01-17 17:00',
    description: '整理本周案件处理情况和回款统计',
    completed: false
  },
  {
    id: 3,
    title: '参加团队例会',
    priority: 'medium',
    deadline: '2024-01-16 14:00',
    description: '周二下午部门例会，汇报工作进展',
    completed: true
  },
  {
    id: 4,
    title: '更新案件CASE001状态',
    priority: 'low',
    deadline: '2024-01-16 16:00',
    description: '客户已部分还款，需要更新案件状态',
    completed: false
  }
])

// 待办表单
const todoForm = reactive({
  title: '',
  priority: 'medium',
  deadline: null,
  description: ''
})

// 快捷操作
const quickActions = ref([
  {
    id: 1,
    title: '新建案件',
    description: '创建新的催收案件',
    icon: 'PlusOutlined',
    action: 'create-case'
  },
  {
    id: 2,
    title: '拨打电话',
    description: '联系客户进行催收',
    icon: 'PhoneOutlined',
    action: 'make-call'
  },
  {
    id: 3,
    title: '发送短信',
    description: '发送催收短信',
    icon: 'MessageOutlined',
    action: 'send-sms'
  },
  {
    id: 4,
    title: '客户档案',
    description: '查看客户详细信息',
    icon: 'UserOutlined',
    action: 'view-customer'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '完成案件CASE003催收',
    description: '客户已全额还款，案件结案',
    time: '2小时前',
    icon: 'CheckCircleOutlined',
    color: '#52c41a'
  },
  {
    id: 2,
    title: '收到客户投诉',
    description: '客户对催收方式提出异议',
    time: '4小时前',
    icon: 'ExclamationCircleOutlined',
    color: '#faad14'
  },
  {
    id: 3,
    title: '更新还款计划',
    description: '为客户李四制定新的分期还款计划',
    time: '1天前',
    icon: 'FileTextOutlined',
    color: '#1890ff'
  },
  {
    id: 4,
    title: '参加培训',
    description: '完成催收技巧提升培训',
    time: '2天前',
    icon: 'TrophyOutlined',
    color: '#722ed1'
  }
])

// 消息通知
const notifications = ref([
  {
    id: 1,
    title: '新的案件分配',
    time: '10分钟前',
    read: false,
    icon: 'FileTextOutlined',
    color: '#1890ff'
  },
  {
    id: 2,
    title: '客户还款到账',
    time: '1小时前',
    read: false,
    icon: 'MoneyCollectOutlined',
    color: '#52c41a'
  },
  {
    id: 3,
    title: '系统升级通知',
    time: '3小时前',
    read: true,
    icon: 'BellOutlined',
    color: '#faad14'
  }
])

// 未读消息数量
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

// 个性化设置
const settings = reactive({
  layout: 'default',
  widgets: ['stats', 'todos', 'progress', 'activities', 'calendar', 'notifications'],
  refreshInterval: 60000
})

// 日历事件
const calendarEvents = ref([
  {
    id: 1,
    date: '2024-01-15',
    title: '客户拜访',
    type: 'meeting'
  },
  {
    id: 2,
    date: '2024-01-16',
    title: '团队例会',
    type: 'meeting'
  },
  {
    id: 3,
    date: '2024-01-17',
    title: '工作汇报',
    type: 'task'
  }
])

// 方法定义
const getGreeting = () => {
  const hour = dayjs().hour()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const refreshData = () => {
  console.log('刷新数据')
}

const goToTodos = () => {
  console.log('跳转到待办')
}

const goToCases = () => {
  console.log('跳转到案件')
}

const goToPayments = () => {
  console.log('跳转到回款')
}

const goToReports = () => {
  console.log('跳转到报表')
}

const updateTodoStatus = (todo) => {
  console.log('更新待办状态:', todo)
}

const editTodo = (todo) => {
  console.log('编辑待办:', todo)
}

const deleteTodo = (todo) => {
  const index = todoList.value.findIndex(t => t.id === todo.id)
  if (index > -1) {
    todoList.value.splice(index, 1)
  }
}

const handleAddTodo = () => {
  const newTodo = {
    id: Date.now(),
    title: todoForm.title,
    priority: todoForm.priority,
    deadline: todoForm.deadline ? todoForm.deadline.format('YYYY-MM-DD HH:mm') : '',
    description: todoForm.description,
    completed: false
  }
  todoList.value.unshift(newTodo)
  
  // 重置表单
  Object.assign(todoForm, {
    title: '',
    priority: 'medium',
    deadline: null,
    description: ''
  })
  
  showAddTodo.value = false
}

const handleQuickAction = (action) => {
  console.log('执行快捷操作:', action)
}

const onDateSelect = (date) => {
  console.log('选择日期:', date.format('YYYY-MM-DD'))
}

const getDateEvents = (date) => {
  const dateStr = date.format('YYYY-MM-DD')
  return calendarEvents.value.filter(event => event.date === dateStr)
}

const markAsRead = (notification) => {
  notification.read = true
}

const saveSettings = () => {
  console.log('保存设置:', settings)
  showSettings.value = false
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.personal-workspace {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-section h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.welcome-text {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.quick-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-action {
  margin-top: 12px;
  text-align: center;
}

.stat-action a {
  color: #1890ff;
}

.workspace-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.todo-list {
  max-height: 400px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.todo-completed {
  opacity: 0.6;
}

.todo-item.todo-completed .todo-title {
  text-decoration: line-through;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: #262626;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.todo-time {
  font-size: 12px;
  color: #999;
}

.todo-actions {
  display: flex;
  gap: 4px;
}

.progress-section {
  space-y: 24px;
}

.progress-item {
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.activity-content {
  margin-left: 8px;
}

.activity-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.activity-desc {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
}

.activity-time {
  color: #999;
  font-size: 12px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.action-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f6f8ff;
  border-radius: 50%;
  color: #1890ff;
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.action-desc {
  font-size: 12px;
  color: #999;
}

.calendar-cell {
  height: 40px;
  overflow: hidden;
}

.calendar-event {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin: 1px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.calendar-event.meeting {
  background: #e6f7ff;
  color: #1890ff;
}

.calendar-event.task {
  background: #f6ffed;
  color: #52c41a;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f6f8ff;
}

.notification-item.unread {
  background-color: #fff7e6;
}

.notification-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.notification-time {
  font-size: 12px;
  color: #999;
}
</style>