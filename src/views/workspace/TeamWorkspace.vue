<template>
  <div class="team-workspace">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="team-info">
          <h2>{{ teamInfo.name }}</h2>
          <p class="team-desc">{{ teamInfo.description }}</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showTeamSettings = true">
            <template #icon><SettingOutlined /></template>
            团队设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 团队概览 -->
    <div class="team-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="团队成员" 
              :value="teamStats.memberCount" 
              :value-style="{ color: '#1890ff' }"
              suffix="人"
            >
              <template #prefix><TeamOutlined /></template>
            </a-statistic>
            <div class="stat-trend">
              <span class="online-count">在线: {{ teamStats.onlineCount }}人</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="本月案件" 
              :value="teamStats.monthlyCases" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><FileTextOutlined /></template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-up">
                <CaretUpOutlined />
                +12%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="回收金额" 
              :value="teamStats.recoveredAmount" 
              :value-style="{ color: '#faad14' }"
              suffix="万元"
            >
              <template #prefix><MoneyCollectOutlined /></template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-up">
                <CaretUpOutlined />
                +8.5%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="团队效率" 
              :value="teamStats.efficiency" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><ThunderboltOutlined /></template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-up">
                <CaretUpOutlined />
                +3.2%
              </span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 团队成员状态 -->
        <a-card title="团队成员状态" class="workspace-card">
          <template #extra>
            <a-radio-group v-model:value="memberViewMode" size="small">
              <a-radio-button value="list">列表</a-radio-button>
              <a-radio-button value="grid">卡片</a-radio-button>
            </a-radio-group>
          </template>
          
          <!-- 列表视图 -->
          <div v-if="memberViewMode === 'list'" class="member-list">
            <a-table 
              :columns="memberColumns" 
              :data-source="teamMembers" 
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <div class="member-info">
                    <a-badge :status="record.online ? 'success' : 'default'">
                      <a-avatar :src="record.avatar" size="small">
                        {{ record.name.charAt(0) }}
                      </a-avatar>
                    </a-badge>
                    <span class="member-name">{{ record.name }}</span>
                  </div>
                </template>
                <template v-else-if="column.key === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'workload'">
                  <a-progress 
                    :percent="record.workload" 
                    size="small" 
                    :status="record.workload > 80 ? 'exception' : 'active'"
                  />
                </template>
                <template v-else-if="column.key === 'performance'">
                  <span :class="getPerformanceClass(record.performance)">
                    {{ record.performance }}%
                  </span>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 卡片视图 -->
          <div v-else class="member-grid">
            <div 
              v-for="member in teamMembers" 
              :key="member.id"
              class="member-card"
              @click="viewMemberDetail(member)"
            >
              <div class="member-header">
                <a-badge :status="member.online ? 'success' : 'default'">
                  <a-avatar :src="member.avatar">{{ member.name.charAt(0) }}</a-avatar>
                </a-badge>
                <div class="member-basic">
                  <div class="member-name">{{ member.name }}</div>
                  <div class="member-role">{{ member.role }}</div>
                </div>
                <a-tag :color="getStatusColor(member.status)" size="small">
                  {{ getStatusText(member.status) }}
                </a-tag>
              </div>
              <div class="member-stats">
                <div class="stat-item">
                  <span class="stat-label">今日案件</span>
                  <span class="stat-value">{{ member.todayCases }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">工作负荷</span>
                  <span class="stat-value">{{ member.workload }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">绩效</span>
                  <span class="stat-value" :class="getPerformanceClass(member.performance)">
                    {{ member.performance }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 团队动态 -->
        <a-card title="团队动态" class="workspace-card">
          <template #extra>
            <a @click="viewAllActivities">查看全部</a>
          </template>
          <a-timeline>
            <a-timeline-item 
              v-for="activity in teamActivities" 
              :key="activity.id"
              :color="activity.color"
            >
              <template #dot>
                <component :is="activity.icon" :style="{ color: activity.color }" />
              </template>
              <div class="activity-content">
                <div class="activity-header">
                  <span class="activity-user">{{ activity.user }}</span>
                  <span class="activity-action">{{ activity.action }}</span>
                </div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>

        <!-- 协作任务 -->
        <a-card title="协作任务" class="workspace-card">
          <template #extra>
            <a-button type="primary" size="small" @click="showCreateTask = true">
              <template #icon><PlusOutlined /></template>
              创建任务
            </a-button>
          </template>
          <div class="task-list">
            <div 
              v-for="task in collaborativeTasks" 
              :key="task.id"
              class="task-item"
              :class="{ 'task-urgent': task.priority === 'high' }"
            >
              <div class="task-header">
                <div class="task-title">{{ task.title }}</div>
                <a-tag :color="getPriorityColor(task.priority)" size="small">
                  {{ getPriorityText(task.priority) }}
                </a-tag>
              </div>
              <div class="task-content">
                <div class="task-desc">{{ task.description }}</div>
                <div class="task-meta">
                  <div class="task-assignees">
                    <span class="meta-label">参与者:</span>
                    <a-avatar-group size="small" :max-count="3">
                      <a-avatar 
                        v-for="assignee in task.assignees" 
                        :key="assignee.id"
                        :src="assignee.avatar"
                      >
                        {{ assignee.name.charAt(0) }}
                      </a-avatar>
                    </a-avatar-group>
                  </div>
                  <div class="task-deadline">
                    <ClockCircleOutlined />
                    {{ task.deadline }}
                  </div>
                </div>
              </div>
              <div class="task-progress">
                <a-progress :percent="task.progress" size="small" />
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 团队排行榜 -->
        <a-card title="本月排行榜" class="workspace-card">
          <a-tabs v-model:activeKey="rankingTab" size="small">
            <a-tab-pane key="cases" tab="案件处理">
              <div class="ranking-list">
                <div 
                  v-for="(member, index) in caseRanking" 
                  :key="member.id"
                  class="ranking-item"
                >
                  <div class="ranking-position">
                    <a-badge 
                      :count="index + 1" 
                      :number-style="{ backgroundColor: getRankColor(index) }"
                    />
                  </div>
                  <a-avatar :src="member.avatar" size="small">
                    {{ member.name.charAt(0) }}
                  </a-avatar>
                  <div class="ranking-info">
                    <div class="ranking-name">{{ member.name }}</div>
                    <div class="ranking-value">{{ member.casesHandled }}件</div>
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="recovery" tab="回收金额">
              <div class="ranking-list">
                <div 
                  v-for="(member, index) in recoveryRanking" 
                  :key="member.id"
                  class="ranking-item"
                >
                  <div class="ranking-position">
                    <a-badge 
                      :count="index + 1" 
                      :number-style="{ backgroundColor: getRankColor(index) }"
                    />
                  </div>
                  <a-avatar :src="member.avatar" size="small">
                    {{ member.name.charAt(0) }}
                  </a-avatar>
                  <div class="ranking-info">
                    <div class="ranking-name">{{ member.name }}</div>
                    <div class="ranking-value">{{ member.recoveredAmount }}万</div>
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 快捷协作工具 -->
        <a-card title="协作工具" class="workspace-card">
          <div class="collaboration-tools">
            <div 
              v-for="tool in collaborationTools" 
              :key="tool.id"
              class="tool-item"
              @click="openTool(tool)"
            >
              <div class="tool-icon">
                <component :is="tool.icon" />
              </div>
              <div class="tool-content">
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-desc">{{ tool.description }}</div>
              </div>
              <div class="tool-action">
                <RightOutlined />
              </div>
            </div>
          </div>
        </a-card>

        <!-- 团队公告 -->
        <a-card title="团队公告" class="workspace-card">
          <template #extra>
            <a @click="manageAnnouncements">管理</a>
          </template>
          <div class="announcement-list">
            <div 
              v-for="announcement in teamAnnouncements" 
              :key="announcement.id"
              class="announcement-item"
              :class="{ 'announcement-important': announcement.important }"
            >
              <div class="announcement-header">
                <div class="announcement-title">
                  <SoundOutlined v-if="announcement.important" class="important-icon" />
                  {{ announcement.title }}
                </div>
                <div class="announcement-time">{{ announcement.time }}</div>
              </div>
              <div class="announcement-content">{{ announcement.content }}</div>
            </div>
          </div>
        </a-card>

        <!-- 工作日历 -->
        <a-card title="团队日历" class="workspace-card">
          <a-calendar 
            v-model:value="selectedDate" 
            :fullscreen="false"
            @select="onDateSelect"
          >
            <template #dateCellRender="{ current }">
              <div class="calendar-cell">
                <div 
                  v-for="event in getDateEvents(current)" 
                  :key="event.id"
                  class="calendar-event"
                  :class="event.type"
                >
                  {{ event.title }}
                </div>
              </div>
            </template>
          </a-calendar>
        </a-card>
      </a-col>
    </a-row>

    <!-- 创建任务模态框 -->
    <a-modal
      v-model:open="showCreateTask"
      title="创建协作任务"
      @ok="handleCreateTask"
    >
      <a-form :model="taskForm" layout="vertical">
        <a-form-item label="任务标题" required>
          <a-input v-model:value="taskForm.title" placeholder="请输入任务标题" />
        </a-form-item>
        <a-form-item label="任务描述">
          <a-textarea v-model:value="taskForm.description" placeholder="任务描述" :rows="3" />
        </a-form-item>
        <a-form-item label="优先级">
          <a-select v-model:value="taskForm.priority" placeholder="选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="参与成员">
          <a-select 
            v-model:value="taskForm.assignees" 
            mode="multiple" 
            placeholder="选择参与成员"
          >
            <a-select-option 
              v-for="member in teamMembers" 
              :key="member.id" 
              :value="member.id"
            >
              {{ member.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止时间">
          <a-date-picker 
            v-model:value="taskForm.deadline" 
            show-time 
            style="width: 100%"
            placeholder="选择截止时间"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 团队设置模态框 -->
    <a-modal
      v-model:open="showTeamSettings"
      title="团队设置"
      @ok="saveTeamSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="团队名称">
          <a-input v-model:value="teamSettings.name" />
        </a-form-item>
        <a-form-item label="团队描述">
          <a-textarea v-model:value="teamSettings.description" :rows="3" />
        </a-form-item>
        <a-form-item label="工作时间">
          <a-time-range-picker v-model:value="teamSettings.workTime" style="width: 100%" />
        </a-form-item>
        <a-form-item label="团队可见性">
          <a-radio-group v-model:value="teamSettings.visibility">
            <a-radio value="public">公开</a-radio>
            <a-radio value="private">私有</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  SettingOutlined,
  TeamOutlined,
  FileTextOutlined,
  MoneyCollectOutlined,
  ThunderboltOutlined,
  CaretUpOutlined,
  PlusOutlined,
  ClockCircleOutlined,
  RightOutlined,
  SoundOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  MessageOutlined,
  VideoCameraOutlined,
  ShareAltOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const memberViewMode = ref('list')
const rankingTab = ref('cases')
const showCreateTask = ref(false)
const showTeamSettings = ref(false)
const selectedDate = ref(dayjs())

// 团队信息
const teamInfo = reactive({
  name: '催收一部',
  description: '负责个人信贷业务的催收工作，团队成员8人'
})

// 团队统计
const teamStats = reactive({
  memberCount: 8,
  onlineCount: 6,
  monthlyCases: 342,
  recoveredAmount: 285.6,
  efficiency: 92.3
})

// 团队成员
const teamMembers = ref([
  {
    id: 1,
    name: '张催收',
    role: '催收专员',
    avatar: '',
    online: true,
    status: 'working',
    todayCases: 12,
    workload: 85,
    performance: 94.2
  },
  {
    id: 2,
    name: '李主管',
    role: '催收主管',
    avatar: '',
    online: true,
    status: 'meeting',
    todayCases: 8,
    workload: 72,
    performance: 96.8
  },
  {
    id: 3,
    name: '王专员',
    role: '催收专员',
    avatar: '',
    online: true,
    status: 'working',
    todayCases: 15,
    workload: 90,
    performance: 89.5
  },
  {
    id: 4,
    name: '赵催收',
    role: '催收专员',
    avatar: '',
    online: false,
    status: 'offline',
    todayCases: 0,
    workload: 0,
    performance: 87.3
  },
  {
    id: 5,
    name: '刘专员',
    role: '催收专员',
    avatar: '',
    online: true,
    status: 'break',
    todayCases: 9,
    workload: 68,
    performance: 91.7
  }
])

// 成员表格列
const memberColumns = [
  {
    title: '成员',
    key: 'name',
    width: 120
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '今日案件',
    dataIndex: 'todayCases',
    key: 'todayCases',
    width: 80
  },
  {
    title: '工作负荷',
    key: 'workload',
    width: 120
  },
  {
    title: '绩效',
    key: 'performance',
    width: 80
  }
]

// 团队动态
const teamActivities = ref([
  {
    id: 1,
    user: '张催收',
    action: '完成案件处理',
    description: '成功处理案件CASE-2024-001，客户已全额还款',
    time: '10分钟前',
    icon: 'CheckCircleOutlined',
    color: '#52c41a'
  },
  {
    id: 2,
    user: '李主管',
    action: '创建团队任务',
    description: '创建了"本周质检计划"协作任务',
    time: '1小时前',
    icon: 'PlusOutlined',
    color: '#1890ff'
  },
  {
    id: 3,
    user: '王专员',
    action: '分享经验',
    description: '在团队群里分享了催收话术技巧',
    time: '2小时前',
    icon: 'ShareAltOutlined',
    color: '#faad14'
  },
  {
    id: 4,
    user: '刘专员',
    action: '更新进度',
    description: '更新了协作任务"客户回访"的进度至80%',
    time: '3小时前',
    icon: 'ExclamationCircleOutlined',
    color: '#722ed1'
  }
])

// 协作任务
const collaborativeTasks = ref([
  {
    id: 1,
    title: '本周质检计划制定',
    description: '制定下周的质检计划，包括抽检比例和重点关注项',
    priority: 'high',
    progress: 65,
    deadline: '2024-01-18 18:00',
    assignees: [
      { id: 1, name: '张催收', avatar: '' },
      { id: 2, name: '李主管', avatar: '' },
      { id: 3, name: '王专员', avatar: '' }
    ]
  },
  {
    id: 2,
    title: '客户满意度调研',
    description: '对本月处理的客户进行满意度电话回访',
    priority: 'medium',
    progress: 30,
    deadline: '2024-01-20 17:00',
    assignees: [
      { id: 4, name: '赵催收', avatar: '' },
      { id: 5, name: '刘专员', avatar: '' }
    ]
  },
  {
    id: 3,
    title: '新员工培训计划',
    description: '为即将入职的新员工制定培训计划和考核标准',
    priority: 'low',
    progress: 10,
    deadline: '2024-01-25 18:00',
    assignees: [
      { id: 2, name: '李主管', avatar: '' }
    ]
  }
])

// 排行榜数据
const caseRanking = ref([
  { id: 1, name: '张催收', avatar: '', casesHandled: 48 },
  { id: 3, name: '王专员', avatar: '', casesHandled: 45 },
  { id: 5, name: '刘专员', avatar: '', casesHandled: 42 },
  { id: 2, name: '李主管', avatar: '', casesHandled: 38 },
  { id: 4, name: '赵催收', avatar: '', casesHandled: 35 }
])

const recoveryRanking = ref([
  { id: 2, name: '李主管', avatar: '', recoveredAmount: 65.8 },
  { id: 1, name: '张催收', avatar: '', recoveredAmount: 58.2 },
  { id: 3, name: '王专员', avatar: '', recoveredAmount: 52.1 },
  { id: 5, name: '刘专员', avatar: '', recoveredAmount: 48.9 },
  { id: 4, name: '赵催收', avatar: '', recoveredAmount: 42.3 }
])

// 协作工具
const collaborationTools = ref([
  {
    id: 1,
    name: '团队会议',
    description: '发起视频会议',
    icon: 'VideoCameraOutlined'
  },
  {
    id: 2,
    name: '团队群聊',
    description: '即时沟通交流',
    icon: 'MessageOutlined'
  },
  {
    id: 3,
    name: '文件共享',
    description: '共享工作文档',
    icon: 'ShareAltOutlined'
  },
  {
    id: 4,
    name: '团队日程',
    description: '查看团队日程',
    icon: 'CalendarOutlined'
  }
])

// 团队公告
const teamAnnouncements = ref([
  {
    id: 1,
    title: '系统升级通知',
    content: '系统将于本周六凌晨2:00-6:00进行升级维护，请提前做好工作安排。',
    time: '1小时前',
    important: true
  },
  {
    id: 2,
    title: '团建活动安排',
    content: '下周五下午组织团队建设活动，地点待定，请大家留出时间。',
    time: '1天前',
    important: false
  },
  {
    id: 3,
    title: '培训课程通知',
    content: '催收技能提升培训将在下周三举行，请相关人员准时参加。',
    time: '2天前',
    important: false
  }
])

// 任务表单
const taskForm = reactive({
  title: '',
  description: '',
  priority: 'medium',
  assignees: [],
  deadline: null
})

// 团队设置
const teamSettings = reactive({
  name: teamInfo.name,
  description: teamInfo.description,
  workTime: [],
  visibility: 'private'
})

// 日历事件
const calendarEvents = ref([
  {
    id: 1,
    date: '2024-01-16',
    title: '团队例会',
    type: 'meeting'
  },
  {
    id: 2,
    date: '2024-01-18',
    title: '质检计划',
    type: 'task'
  },
  {
    id: 3,
    date: '2024-01-19',
    title: '培训课程',
    type: 'training'
  }
])

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    working: 'green',
    meeting: 'blue',
    break: 'orange',
    offline: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    working: '工作中',
    meeting: '会议中',
    break: '休息中',
    offline: '离线'
  }
  return texts[status] || status
}

const getPerformanceClass = (performance) => {
  if (performance >= 95) return 'performance-excellent'
  if (performance >= 90) return 'performance-good'
  if (performance >= 85) return 'performance-average'
  return 'performance-poor'
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getRankColor = (index) => {
  const colors = ['#f5222d', '#fa8c16', '#fadb14', '#52c41a']
  return colors[index] || '#d9d9d9'
}

const refreshData = () => {
  console.log('刷新团队数据')
}

const viewMemberDetail = (member) => {
  console.log('查看成员详情:', member)
}

const viewAllActivities = () => {
  console.log('查看全部动态')
}

const handleCreateTask = () => {
  console.log('创建任务:', taskForm)
  showCreateTask.value = false
  
  // 重置表单
  Object.assign(taskForm, {
    title: '',
    description: '',
    priority: 'medium',
    assignees: [],
    deadline: null
  })
}

const openTool = (tool) => {
  console.log('打开协作工具:', tool)
}

const manageAnnouncements = () => {
  console.log('管理公告')
}

const onDateSelect = (date) => {
  console.log('选择日期:', date.format('YYYY-MM-DD'))
}

const getDateEvents = (date) => {
  const dateStr = date.format('YYYY-MM-DD')
  return calendarEvents.value.filter(event => event.date === dateStr)
}

const saveTeamSettings = () => {
  console.log('保存团队设置:', teamSettings)
  Object.assign(teamInfo, {
    name: teamSettings.name,
    description: teamSettings.description
  })
  showTeamSettings.value = false
}
</script>

<style scoped>
.team-workspace {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.team-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.team-overview {
  margin-bottom: 16px;
}

.overview-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-trend {
  margin-top: 8px;
  font-size: 12px;
}

.online-count {
  color: #52c41a;
}

.trend-up {
  color: #52c41a;
}

.trend-text {
  color: #8c8c8c;
  margin-left: 4px;
}

.workspace-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-name {
  font-weight: 500;
}

.performance-excellent {
  color: #52c41a;
  font-weight: 500;
}

.performance-good {
  color: #1890ff;
  font-weight: 500;
}

.performance-average {
  color: #faad14;
  font-weight: 500;
}

.performance-poor {
  color: #ff4d4f;
  font-weight: 500;
}

.member-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.member-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.member-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.member-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.member-basic {
  flex: 1;
}

.member-name {
  font-weight: 500;
  color: #262626;
}

.member-role {
  font-size: 12px;
  color: #999;
}

.member-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.stat-value {
  font-weight: 500;
  color: #262626;
}

.activity-content {
  margin-left: 8px;
}

.activity-header {
  margin-bottom: 4px;
}

.activity-user {
  font-weight: 500;
  color: #1890ff;
}

.activity-action {
  color: #262626;
  margin-left: 4px;
}

.activity-desc {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
}

.activity-time {
  color: #999;
  font-size: 12px;
}

.task-list {
  max-height: 400px;
  overflow-y: auto;
}

.task-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.task-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.task-urgent {
  border-color: #ff4d4f;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-title {
  font-weight: 500;
  color: #262626;
}

.task-desc {
  color: #666;
  font-size: 13px;
  margin-bottom: 12px;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-assignees {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-label {
  font-size: 12px;
  color: #999;
}

.task-deadline {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.ranking-list {
  space-y: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.ranking-position {
  width: 24px;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-weight: 500;
  color: #262626;
}

.ranking-value {
  font-size: 12px;
  color: #999;
}

.collaboration-tools {
  space-y: 12px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.tool-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f6f8ff;
  border-radius: 50%;
  color: #1890ff;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.tool-desc {
  font-size: 12px;
  color: #999;
}

.tool-action {
  color: #999;
}

.announcement-list {
  max-height: 300px;
  overflow-y: auto;
}

.announcement-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-important {
  background-color: #fff7e6;
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 4px;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.announcement-title {
  font-weight: 500;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 4px;
}

.important-icon {
  color: #faad14;
}

.announcement-time {
  font-size: 12px;
  color: #999;
}

.announcement-content {
  color: #666;
  font-size: 13px;
  line-height: 1.5;
}

.calendar-cell {
  height: 40px;
  overflow: hidden;
}

.calendar-event {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin: 1px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.calendar-event.meeting {
  background: #e6f7ff;
  color: #1890ff;
}

.calendar-event.task {
  background: #f6ffed;
  color: #52c41a;
}

.calendar-event.training {
  background: #fff1f0;
  color: #ff4d4f;
}
</style>