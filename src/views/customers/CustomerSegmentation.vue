<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>客户分群</h2>
        <a-space>
          <a-button type="primary" @click="showCreateSegmentModal = true">
            <plus-outlined />
            新建分群
          </a-button>
          <a-button @click="refreshSegments">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <!-- 分群列表 -->
      <a-row :gutter="16" class="segment-cards">
        <a-col :xs="24" :sm="12" :lg="8" :xl="6" v-for="segment in segmentList" :key="segment.id">
          <a-card 
            :title="segment.name" 
            class="segment-card"
            :hoverable="true"
            @click="viewSegmentDetail(segment)"
          >
            <template #extra>
              <a-dropdown>
                <a-button type="text" size="small">
                  <more-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleSegmentAction(key, segment)">
                    <a-menu-item key="edit">编辑</a-menu-item>
                    <a-menu-item key="refresh">刷新数据</a-menu-item>
                    <a-menu-item key="export">导出客户</a-menu-item>
                    <a-menu-item key="delete" class="text-danger">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
            
            <div class="segment-info">
              <div class="segment-description">{{ segment.description }}</div>
              
              <div class="segment-stats">
                <a-row :gutter="16">
                  <a-col span="12">
                    <a-statistic 
                      title="客户数量" 
                      :value="segment.customerCount" 
                      :value-style="{ fontSize: '18px', color: '#1890ff' }"
                    />
                  </a-col>
                  <a-col span="12">
                    <a-statistic 
                      title="债务总额" 
                      :value="segment.totalDebt" 
                      suffix="万"
                      :precision="1"
                      :value-style="{ fontSize: '18px', color: '#52c41a' }"
                    />
                  </a-col>
                </a-row>
              </div>

              <div class="segment-meta">
                <div class="segment-tags">
                  <a-tag v-for="tag in segment.tags" :key="tag" :color="getTagColor(tag)">
                    {{ tag }}
                  </a-tag>
                </div>
                
                <div class="segment-footer">
                  <span class="update-time">更新时间: {{ segment.updateTime }}</span>
                  <a-tag :color="segment.status === 'active' ? 'green' : 'orange'">
                    {{ segment.status === 'active' ? '启用' : '停用' }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 新建分群弹窗 -->
      <a-modal
        v-model:open="showCreateSegmentModal"
        title="新建客户分群"
        width="800px"
        @ok="handleCreateSegment"
        @cancel="resetSegmentForm"
      >
        <a-form :model="segmentForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="分群名称" required>
            <a-input v-model:value="segmentForm.name" placeholder="请输入分群名称" />
          </a-form-item>
          
          <a-form-item label="分群描述">
            <a-textarea 
              v-model:value="segmentForm.description" 
              :rows="2"
              placeholder="请输入分群描述"
            />
          </a-form-item>
          
          <a-form-item label="分群类型" required>
            <a-select v-model:value="segmentForm.type" placeholder="请选择分群类型">
              <a-select-option value="risk">风险等级分群</a-select-option>
              <a-select-option value="payment">还款能力分群</a-select-option>
              <a-select-option value="region">地域分群</a-select-option>
              <a-select-option value="age">年龄分群</a-select-option>
              <a-select-option value="occupation">职业分群</a-select-option>
              <a-select-option value="overdue">逾期时长分群</a-select-option>
              <a-select-option value="amount">债务金额分群</a-select-option>
              <a-select-option value="custom">自定义分群</a-select-option>
            </a-select>
          </a-form-item>

          <!-- 分群规则配置 -->
          <a-form-item label="分群规则">
            <div class="rule-builder">
              <div v-for="(rule, index) in segmentForm.rules" :key="index" class="rule-item">
                <a-row :gutter="8" align="middle">
                  <a-col :span="6">
                    <a-select v-model:value="rule.field" placeholder="选择字段">
                      <a-select-option value="age">年龄</a-select-option>
                      <a-select-option value="debt_amount">债务金额</a-select-option>
                      <a-select-option value="overdue_days">逾期天数</a-select-option>
                      <a-select-option value="risk_level">风险等级</a-select-option>
                      <a-select-option value="region">地区</a-select-option>
                      <a-select-option value="occupation">职业</a-select-option>
                      <a-select-option value="income">收入</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="4">
                    <a-select v-model:value="rule.operator" placeholder="条件">
                      <a-select-option value="eq">等于</a-select-option>
                      <a-select-option value="gt">大于</a-select-option>
                      <a-select-option value="lt">小于</a-select-option>
                      <a-select-option value="gte">大于等于</a-select-option>
                      <a-select-option value="lte">小于等于</a-select-option>
                      <a-select-option value="in">包含</a-select-option>
                      <a-select-option value="like">包含文本</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="8">
                    <a-input v-model:value="rule.value" placeholder="请输入值" />
                  </a-col>
                  <a-col :span="4">
                    <a-select v-model:value="rule.logic" v-if="index < segmentForm.rules.length - 1">
                      <a-select-option value="and">且</a-select-option>
                      <a-select-option value="or">或</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="2">
                    <a-button type="text" danger @click="removeRule(index)" v-if="segmentForm.rules.length > 1">
                      <delete-outlined />
                    </a-button>
                  </a-col>
                </a-row>
              </div>
              
              <a-button type="dashed" @click="addRule" block>
                <plus-outlined />
                添加规则
              </a-button>
            </div>
          </a-form-item>

          <a-form-item label="自动更新">
            <a-switch v-model:checked="segmentForm.autoUpdate" />
            <span class="form-item-help">开启后将根据规则自动更新分群客户</span>
          </a-form-item>

          <a-form-item label="更新频率" v-if="segmentForm.autoUpdate">
            <a-select v-model:value="segmentForm.updateFrequency">
              <a-select-option value="daily">每日</a-select-option>
              <a-select-option value="weekly">每周</a-select-option>
              <a-select-option value="monthly">每月</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 分群详情弹窗 -->
      <a-modal
        v-model:open="showSegmentDetailModal"
        :title="currentSegment?.name + ' - 分群详情'"
        width="1200px"
        :footer="null"
      >
        <div v-if="currentSegment" class="segment-detail">
          <!-- 分群基本信息 -->
          <a-descriptions title="基本信息" :column="2" bordered>
            <a-descriptions-item label="分群名称">{{ currentSegment.name }}</a-descriptions-item>
            <a-descriptions-item label="分群类型">{{ getSegmentTypeText(currentSegment.type) }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ currentSegment.createTime }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ currentSegment.updateTime }}</a-descriptions-item>
            <a-descriptions-item label="客户数量">{{ currentSegment.customerCount }}</a-descriptions-item>
            <a-descriptions-item label="债务总额">¥{{ currentSegment.totalDebt }}万</a-descriptions-item>
            <a-descriptions-item label="分群描述" :span="2">{{ currentSegment.description }}</a-descriptions-item>
          </a-descriptions>

          <!-- 分群规则 -->
          <div class="segment-rules" style="margin-top: 24px;">
            <h4>分群规则</h4>
            <div v-for="(rule, index) in currentSegment.rules" :key="index" class="rule-display">
              <a-tag color="blue">{{ getFieldText(rule.field) }}</a-tag>
              <span class="operator">{{ getOperatorText(rule.operator) }}</span>
              <a-tag color="green">{{ rule.value }}</a-tag>
              <span v-if="index < currentSegment.rules.length - 1" class="logic">
                {{ rule.logic === 'and' ? '且' : '或' }}
              </span>
            </div>
          </div>

          <!-- 分群统计 -->
          <div class="segment-statistics" style="margin-top: 24px;">
            <h4>分群统计</h4>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-card>
                  <a-statistic
                    title="平均债务金额"
                    :value="currentSegment.stats.avgDebt"
                    suffix="万"
                    :precision="2"
                  />
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card>
                  <a-statistic
                    title="平均逾期天数"
                    :value="currentSegment.stats.avgOverdueDays"
                    suffix="天"
                  />
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card>
                  <a-statistic
                    title="催收成功率"
                    :value="currentSegment.stats.successRate"
                    suffix="%"
                    :precision="1"
                  />
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card>
                  <a-statistic
                    title="平均年龄"
                    :value="currentSegment.stats.avgAge"
                    suffix="岁"
                  />
                </a-card>
              </a-col>
            </a-row>
          </div>

          <!-- 客户列表 -->
          <div class="segment-customers" style="margin-top: 24px;">
            <div class="customers-header">
              <h4>客户列表</h4>
              <a-space>
                <a-button @click="exportSegmentCustomers">
                  <download-outlined />
                  导出客户
                </a-button>
                <a-button type="primary" @click="batchAssignCases">
                  <user-add-outlined />
                  批量分配案件
                </a-button>
              </a-space>
            </div>
            
            <a-table
              :columns="customerColumns"
              :data-source="segmentCustomers"
              :pagination="{ pageSize: 10 }"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'riskLevel'">
                  <a-tag :color="getRiskLevelColor(record.riskLevel)">
                    {{ getRiskLevelText(record.riskLevel) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'debtAmount'">
                  ¥{{ record.debtAmount.toLocaleString() }}
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewCustomerDetail(record)">
                      详情
                    </a-button>
                    <a-button type="link" size="small" @click="assignCase(record)">
                      分配案件
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </a-modal>

      <!-- 分群预览弹窗 -->
      <a-modal
        v-model:open="showPreviewModal"
        title="分群预览"
        width="800px"
        :footer="null"
      >
        <div class="preview-content">
          <a-alert
            :message="`根据当前规则，预计匹配 ${previewResult.count} 位客户`"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />
          
          <a-table
            :columns="previewColumns"
            :data-source="previewResult.customers"
            :pagination="false"
            size="small"
          />
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  PlusOutlined, 
  ReloadOutlined, 
  MoreOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UserAddOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

// 状态管理
const showCreateSegmentModal = ref(false)
const showSegmentDetailModal = ref(false)
const showPreviewModal = ref(false)
const currentSegment = ref(null)
const loading = ref(false)

// 分群表单
const segmentForm = reactive({
  name: '',
  description: '',
  type: 'risk',
  rules: [
    {
      field: 'risk_level',
      operator: 'eq',
      value: 'high',
      logic: 'and'
    }
  ],
  autoUpdate: true,
  updateFrequency: 'daily'
})

// 分群列表数据
const segmentList = ref([
  {
    id: 1,
    name: '高风险客户',
    description: '风险等级为高的客户群体，需要重点关注',
    type: 'risk',
    customerCount: 1245,
    totalDebt: 2350.8,
    tags: ['高风险', '重点关注'],
    status: 'active',
    createTime: '2024-01-15 10:30',
    updateTime: '2024-01-20 15:20',
    rules: [
      { field: 'risk_level', operator: 'eq', value: 'high' }
    ],
    stats: {
      avgDebt: 18.9,
      avgOverdueDays: 45,
      successRate: 35.2,
      avgAge: 35
    }
  },
  {
    id: 2,
    name: '大额债务客户',
    description: '债务金额超过10万的客户',
    type: 'amount',
    customerCount: 567,
    totalDebt: 1890.5,
    tags: ['大额债务', '重要客户'],
    status: 'active',
    createTime: '2024-01-10 14:20',
    updateTime: '2024-01-20 09:15',
    rules: [
      { field: 'debt_amount', operator: 'gt', value: '100000' }
    ],
    stats: {
      avgDebt: 33.4,
      avgOverdueDays: 28,
      successRate: 58.7,
      avgAge: 42
    }
  },
  {
    id: 3,
    name: '长期逾期客户',
    description: '逾期时间超过90天的客户',
    type: 'overdue',
    customerCount: 823,
    totalDebt: 1234.2,
    tags: ['长期逾期', '难处理'],
    status: 'active',
    createTime: '2024-01-08 16:45',
    updateTime: '2024-01-19 11:30',
    rules: [
      { field: 'overdue_days', operator: 'gt', value: '90' }
    ],
    stats: {
      avgDebt: 15.0,
      avgOverdueDays: 125,
      successRate: 22.1,
      avgAge: 38
    }
  },
  {
    id: 4,
    name: '年轻客户群',
    description: '30岁以下的年轻客户群体',
    type: 'age',
    customerCount: 1156,
    totalDebt: 1567.3,
    tags: ['年轻群体', '潜力客户'],
    status: 'active',
    createTime: '2024-01-12 09:20',
    updateTime: '2024-01-18 14:10',
    rules: [
      { field: 'age', operator: 'lt', value: '30' }
    ],
    stats: {
      avgDebt: 13.6,
      avgOverdueDays: 32,
      successRate: 45.8,
      avgAge: 26
    }
  },
  {
    id: 5,
    name: '北京地区客户',
    description: '北京地区的所有客户',
    type: 'region',
    customerCount: 789,
    totalDebt: 1890.7,
    tags: ['北京', '地域分群'],
    status: 'active',
    createTime: '2024-01-05 11:15',
    updateTime: '2024-01-17 16:45',
    rules: [
      { field: 'region', operator: 'like', value: '北京' }
    ],
    stats: {
      avgDebt: 24.0,
      avgOverdueDays: 38,
      successRate: 52.3,
      avgAge: 34
    }
  },
  {
    id: 6,
    name: '白领客户群',
    description: '职业为白领的客户群体',
    type: 'occupation',
    customerCount: 934,
    totalDebt: 2134.5,
    tags: ['白领', '稳定收入'],
    status: 'active',
    createTime: '2024-01-14 13:30',
    updateTime: '2024-01-19 10:20',
    rules: [
      { field: 'occupation', operator: 'in', value: '白领,管理层,技术人员' }
    ],
    stats: {
      avgDebt: 22.9,
      avgOverdueDays: 25,
      successRate: 62.1,
      avgAge: 32
    }
  }
])

// 分群客户数据
const segmentCustomers = ref([
  {
    id: 1,
    name: '张三',
    phone: '138****5678',
    age: 35,
    riskLevel: 'high',
    debtAmount: 50000,
    overdueDays: 45,
    region: '北京',
    occupation: '销售'
  },
  {
    id: 2,
    name: '李四',
    phone: '139****1234',
    age: 28,
    riskLevel: 'medium',
    debtAmount: 120000,
    overdueDays: 30,
    region: '上海',
    occupation: '工程师'
  },
  {
    id: 3,
    name: '王五',
    phone: '137****9876',
    age: 42,
    riskLevel: 'high',
    debtAmount: 80000,
    overdueDays: 60,
    region: '广州',
    occupation: '经理'
  }
])

// 预览结果
const previewResult = ref({
  count: 0,
  customers: []
})

// 表格列配置
const customerColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name', width: 100 },
  { title: '手机号', dataIndex: 'phone', key: 'phone', width: 120 },
  { title: '年龄', dataIndex: 'age', key: 'age', width: 80 },
  { title: '风险等级', dataIndex: 'riskLevel', key: 'riskLevel', width: 100 },
  { title: '债务金额', dataIndex: 'debtAmount', key: 'debtAmount', width: 120 },
  { title: '逾期天数', dataIndex: 'overdueDays', key: 'overdueDays', width: 100 },
  { title: '地区', dataIndex: 'region', key: 'region', width: 80 },
  { title: '职业', dataIndex: 'occupation', key: 'occupation', width: 100 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' }
]

const previewColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  { title: '风险等级', dataIndex: 'riskLevel', key: 'riskLevel' },
  { title: '债务金额', dataIndex: 'debtAmount', key: 'debtAmount' },
  { title: '逾期天数', dataIndex: 'overdueDays', key: 'overdueDays' }
]

// 方法
const refreshSegments = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('分群数据已刷新')
  }, 1000)
}

const viewSegmentDetail = (segment) => {
  currentSegment.value = segment
  showSegmentDetailModal.value = true
  loadSegmentCustomers(segment.id)
}

const loadSegmentCustomers = (segmentId) => {
  // 模拟加载分群客户数据
  console.log('加载分群客户:', segmentId)
}

const handleSegmentAction = (action, segment) => {
  switch (action) {
    case 'edit':
      editSegment(segment)
      break
    case 'refresh':
      refreshSegment(segment)
      break
    case 'export':
      exportSegment(segment)
      break
    case 'delete':
      deleteSegment(segment)
      break
  }
}

const editSegment = (segment) => {
  Object.assign(segmentForm, segment)
  showCreateSegmentModal.value = true
}

const refreshSegment = (segment) => {
  message.loading('正在刷新分群数据...')
  setTimeout(() => {
    message.success('分群数据刷新成功')
  }, 1500)
}

const exportSegment = (segment) => {
  message.success(`正在导出${segment.name}的客户数据...`)
}

const deleteSegment = (segment) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分群"${segment.name}"吗？`,
    onOk() {
      const index = segmentList.value.findIndex(s => s.id === segment.id)
      if (index > -1) {
        segmentList.value.splice(index, 1)
        message.success('分群删除成功')
      }
    }
  })
}

const addRule = () => {
  segmentForm.rules.push({
    field: '',
    operator: 'eq',
    value: '',
    logic: 'and'
  })
}

const removeRule = (index) => {
  segmentForm.rules.splice(index, 1)
}

const handleCreateSegment = () => {
  if (!segmentForm.name) {
    message.warning('请输入分群名称')
    return
  }
  
  if (!segmentForm.type) {
    message.warning('请选择分群类型')
    return
  }

  const validRules = segmentForm.rules.filter(rule => 
    rule.field && rule.operator && rule.value
  )
  
  if (validRules.length === 0) {
    message.warning('请至少设置一个有效的分群规则')
    return
  }

  message.loading('正在创建分群...')
  setTimeout(() => {
    const newSegment = {
      id: Date.now(),
      ...segmentForm,
      rules: validRules,
      customerCount: Math.floor(Math.random() * 1000) + 100,
      totalDebt: Math.floor(Math.random() * 2000) + 500,
      tags: generateTags(segmentForm.type),
      status: 'active',
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString(),
      stats: {
        avgDebt: (Math.random() * 30 + 10).toFixed(1),
        avgOverdueDays: Math.floor(Math.random() * 60) + 20,
        successRate: (Math.random() * 40 + 30).toFixed(1),
        avgAge: Math.floor(Math.random() * 20) + 25
      }
    }
    
    segmentList.value.unshift(newSegment)
    message.success('分群创建成功')
    showCreateSegmentModal.value = false
    resetSegmentForm()
  }, 1500)
}

const resetSegmentForm = () => {
  Object.assign(segmentForm, {
    name: '',
    description: '',
    type: 'risk',
    rules: [
      {
        field: 'risk_level',
        operator: 'eq',
        value: 'high',
        logic: 'and'
      }
    ],
    autoUpdate: true,
    updateFrequency: 'daily'
  })
}

const exportSegmentCustomers = () => {
  message.success('正在导出客户数据...')
}

const batchAssignCases = () => {
  message.info('正在打开批量分配案件界面...')
}

const viewCustomerDetail = (customer) => {
  message.info(`查看客户 ${customer.name} 的详细信息`)
}

const assignCase = (customer) => {
  message.info(`为客户 ${customer.name} 分配案件`)
}

// 工具方法
const getTagColor = (tag) => {
  const colors = {
    '高风险': 'red',
    '重点关注': 'orange',
    '大额债务': 'purple',
    '重要客户': 'blue',
    '长期逾期': 'volcano',
    '难处理': 'magenta',
    '年轻群体': 'green',
    '潜力客户': 'cyan',
    '地域分群': 'geekblue',
    '稳定收入': 'gold'
  }
  return colors[tag] || 'default'
}

const getSegmentTypeText = (type) => {
  const types = {
    risk: '风险等级分群',
    payment: '还款能力分群',
    region: '地域分群',
    age: '年龄分群',
    occupation: '职业分群',
    overdue: '逾期时长分群',
    amount: '债务金额分群',
    custom: '自定义分群'
  }
  return types[type] || type
}

const getFieldText = (field) => {
  const fields = {
    age: '年龄',
    debt_amount: '债务金额',
    overdue_days: '逾期天数',
    risk_level: '风险等级',
    region: '地区',
    occupation: '职业',
    income: '收入'
  }
  return fields[field] || field
}

const getOperatorText = (operator) => {
  const operators = {
    eq: '等于',
    gt: '大于',
    lt: '小于',
    gte: '大于等于',
    lte: '小于等于',
    in: '包含',
    like: '包含文本'
  }
  return operators[operator] || operator
}

const getRiskLevelColor = (level) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || level
}

const generateTags = (type) => {
  const tagMap = {
    risk: ['风险分群', '智能分析'],
    payment: ['还款能力', '财务分析'],
    region: ['地域分群', '区域管理'],
    age: ['年龄分群', '人群画像'],
    occupation: ['职业分群', '行业分析'],
    overdue: ['逾期分析', '时效管理'],
    amount: ['金额分群', '债务分析'],
    custom: ['自定义', '个性化']
  }
  return tagMap[type] || ['分群']
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h2 {
    margin: 0;
  }
}

.segment-cards {
  margin-bottom: 24px;
  
  .segment-card {
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .segment-info {
      .segment-description {
        color: #666;
        font-size: 14px;
        margin-bottom: 16px;
        min-height: 20px;
      }
      
      .segment-stats {
        margin-bottom: 16px;
      }
      
      .segment-meta {
        .segment-tags {
          margin-bottom: 12px;
          min-height: 24px;
        }
        
        .segment-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .update-time {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}

.rule-builder {
  .rule-item {
    margin-bottom: 12px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fafafa;
  }
}

.segment-detail {
  .segment-rules {
    .rule-display {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      .operator, .logic {
        color: #666;
        font-size: 14px;
      }
    }
  }
  
  .customers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
    }
  }
}

.preview-content {
  .ant-table {
    margin-top: 16px;
  }
}

.form-item-help {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.text-danger {
  color: #ff4d4f !important;
}
</style>
