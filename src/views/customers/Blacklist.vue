<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>黑名单管理</h2>
        <a-space>
          <a-button type="primary" @click="showAddModal = true">
            <plus-outlined />
            添加黑名单
          </a-button>
          <a-button @click="showBatchAddModal = true">
            <upload-outlined />
            批量导入
          </a-button>
          <a-button @click="showConfigModal = true">
            <setting-outlined />
            规则配置
          </a-button>
          <a-button @click="refreshData">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input 
                  v-model:value="searchParams.name" 
                  placeholder="请输入客户姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="身份证号">
                <a-input 
                  v-model:value="searchParams.idCard" 
                  placeholder="请输入身份证号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="黑名单类型">
                <a-select 
                  v-model:value="searchParams.blacklistType"
                  placeholder="请选择黑名单类型"
                  allow-clear
                >
                  <a-select-option value="malicious_debt">恶意逃债</a-select-option>
                  <a-select-option value="false_info">虚假信息</a-select-option>
                  <a-select-option value="violent_resist">暴力抗拒</a-select-option>
                  <a-select-option value="legal_dispute">法律纠纷</a-select-option>
                  <a-select-option value="poor_credit">信用极差</a-select-option>
                  <a-select-option value="industry_shared">行业共享</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select 
                  v-model:value="searchParams.status"
                  placeholder="请选择状态"
                  allow-clear
                >
                  <a-select-option value="active">生效中</a-select-option>
                  <a-select-option value="expired">已过期</a-select-option>
                  <a-select-option value="removed">已移除</a-select-option>
                  <a-select-option value="suspended">暂停</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 高级搜索 -->
          <a-row :gutter="16" v-show="showAdvancedSearch">
            <a-col :span="6">
              <a-form-item label="手机号码">
                <a-input 
                  v-model:value="searchParams.phone" 
                  placeholder="请输入手机号码"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="添加时间">
                <a-range-picker 
                  v-model:value="searchParams.addTimeRange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="操作人">
                <a-input 
                  v-model:value="searchParams.operator" 
                  placeholder="请输入操作人"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="风险等级">
                <a-select 
                  v-model:value="searchParams.riskLevel"
                  placeholder="请选择风险等级"
                  allow-clear
                >
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="handleReset">
                    <reload-outlined />
                    重置
                  </a-button>
                  <a-button type="link" @click="showAdvancedSearch = !showAdvancedSearch">
                    {{ showAdvancedSearch ? '收起' : '展开高级搜索' }}
                    <down-outlined v-if="!showAdvancedSearch" />
                    <up-outlined v-else />
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-dropdown v-if="selectedRowKeys.length > 0">
                      <template #overlay>
                        <a-menu @click="handleBatchAction">
                          <a-menu-item key="remove">
                            <stop-outlined /> 批量移除
                          </a-menu-item>
                          <a-menu-item key="suspend">
                            <warning-outlined /> 批量暂停
                          </a-menu-item>
                          <a-menu-item key="activate">
                            <plus-circle-outlined /> 批量激活
                          </a-menu-item>
                          <a-menu-item key="export">
                            <download-outlined /> 导出选中
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button>
                        批量操作 ({{ selectedRowKeys.length }})
                        <down-outlined />
                      </a-button>
                    </a-dropdown>
                    <a-button @click="exportBlacklist">
                      <download-outlined /> 导出全部
                    </a-button>
                    <a-button @click="refreshData">
                      <reload-outlined /> 刷新
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="黑名单总数"
              :value="stats.totalBlacklist"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <stop-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="生效中"
              :value="stats.activeBlacklist"
              :value-style="{ color: '#f5222d' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="本月新增"
              :value="stats.monthlyAdded"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <plus-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="拦截次数"
              :value="stats.blockCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <safety-certificate-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 黑名单列表 -->
      <a-card class="blacklist-card">
        <template #title>
          <a-space>
            <span>黑名单列表</span>
            <a-tag color="red">共 {{ blacklistData.length }} 条</a-tag>
          </a-space>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="exportData">
              <download-outlined />
              导出
            </a-button>
            <a-dropdown>
              <a-button>
                批量操作
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleBatchOperation">
                  <a-menu-item key="remove">批量移除</a-menu-item>
                  <a-menu-item key="suspend">批量暂停</a-menu-item>
                  <a-menu-item key="activate">批量激活</a-menu-item>
                  <a-menu-item key="extend">批量延期</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>

        <a-table
          :columns="columns"
          :data-source="blacklistData"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
          }"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true
          }"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a @click="viewDetail(record)">{{ record.name }}</a>
            </template>
            <template v-else-if="column.key === 'blacklistType'">
              <a-tag :color="getBlacklistTypeColor(record.blacklistType)">
                {{ getBlacklistTypeText(record.blacklistType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'expireTime'">
              <span :class="{ 'expire-warning': isExpiringSoon(record.expireTime) }">
                {{ record.expireTime }}
              </span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewDetail(record)"
                >
                  详情
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="editBlacklist(record)"
                  v-if="record.status !== 'removed'"
                >
                  编辑
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMoreAction(key, record)">
                      <a-menu-item key="remove" v-if="record.status === 'active'">移除</a-menu-item>
                      <a-menu-item key="suspend" v-if="record.status === 'active'">暂停</a-menu-item>
                      <a-menu-item key="activate" v-if="record.status === 'suspended'">激活</a-menu-item>
                      <a-menu-item key="extend">延期</a-menu-item>
                      <a-menu-item key="history">操作历史</a-menu-item>
                      <a-menu-item key="report">举报记录</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 添加黑名单弹窗 -->
      <a-modal
        v-model:open="showAddModal"
        title="添加黑名单"
        width="800px"
        @ok="handleAddBlacklist"
      >
        <a-form :model="addForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-tabs v-model:activeKey="activeAddTab">
            <a-tab-pane key="basic" tab="基本信息">
              <a-form-item label="客户姓名" required>
                <a-input v-model:value="addForm.name" placeholder="请输入客户姓名" />
              </a-form-item>
              <a-form-item label="身份证号" required>
                <a-input v-model:value="addForm.idCard" placeholder="请输入身份证号" />
              </a-form-item>
              <a-form-item label="手机号码">
                <a-input v-model:value="addForm.phone" placeholder="请输入手机号码" />
              </a-form-item>
              <a-form-item label="黑名单类型" required>
                <a-select v-model:value="addForm.blacklistType" placeholder="请选择类型">
                  <a-select-option value="malicious_debt">恶意逃债黑名单</a-select-option>
                  <a-select-option value="false_info">虚假信息黑名单</a-select-option>
                  <a-select-option value="violent_resist">暴力抗拒黑名单</a-select-option>
                  <a-select-option value="legal_dispute">法律纠纷黑名单</a-select-option>
                  <a-select-option value="poor_credit">信用极差黑名单</a-select-option>
                  <a-select-option value="industry_shared">行业共享黑名单</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="风险等级" required>
                <a-select v-model:value="addForm.riskLevel" placeholder="请选择风险等级">
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-tab-pane>
            
            <a-tab-pane key="details" tab="详细信息">
              <a-form-item label="加入原因" required>
                <a-textarea 
                  v-model:value="addForm.reason" 
                  :rows="4"
                  placeholder="请详细描述加入黑名单的原因..."
                />
              </a-form-item>
              <a-form-item label="有效期设置">
                <a-radio-group v-model:value="addForm.expireType">
                  <a-radio value="permanent">永久有效</a-radio>
                  <a-radio value="temporary">临时有效</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="过期时间" v-if="addForm.expireType === 'temporary'">
                <a-date-picker 
                  v-model:value="addForm.expireTime" 
                  style="width: 100%"
                  placeholder="请选择过期时间"
                />
              </a-form-item>
              <a-form-item label="证据材料">
                <a-upload
                  v-model:file-list="addForm.evidenceFiles"
                  :before-upload="() => false"
                  multiple
                >
                  <a-button>
                    <upload-outlined />
                    上传证据
                  </a-button>
                </a-upload>
                <div style="margin-top: 8px; color: #666;">
                  支持图片、文档等格式，单个文件不超过10MB
                </div>
              </a-form-item>
              <a-form-item label="关联人员">
                <a-input v-model:value="addForm.relatedPersons" placeholder="如担保人、共同债务人等" />
              </a-form-item>
              <a-form-item label="备注信息">
                <a-textarea 
                  v-model:value="addForm.remark" 
                  :rows="3"
                  placeholder="其他需要说明的信息..."
                />
              </a-form-item>
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </a-modal>

      <!-- 黑名单详情弹窗 -->
      <a-modal
        v-model:open="showDetailModal"
        :title="currentRecord?.name + ' - 黑名单详情'"
        width="1000px"
        :footer="null"
      >
        <div v-if="currentRecord" class="blacklist-detail">
          <!-- 基本信息 -->
          <a-descriptions title="基本信息" :column="2" bordered>
            <a-descriptions-item label="客户姓名">{{ currentRecord.name }}</a-descriptions-item>
            <a-descriptions-item label="身份证号">{{ currentRecord.idCard }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ currentRecord.phone }}</a-descriptions-item>
            <a-descriptions-item label="黑名单类型">
              <a-tag :color="getBlacklistTypeColor(currentRecord.blacklistType)">
                {{ getBlacklistTypeText(currentRecord.blacklistType) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="风险等级">
              <a-tag :color="getRiskLevelColor(currentRecord.riskLevel)">
                {{ getRiskLevelText(currentRecord.riskLevel) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="当前状态">
              <a-tag :color="getStatusColor(currentRecord.status)">
                {{ getStatusText(currentRecord.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="添加时间">{{ currentRecord.addTime }}</a-descriptions-item>
            <a-descriptions-item label="过期时间">
              <span :class="{ 'expire-warning': isExpiringSoon(currentRecord.expireTime) }">
                {{ currentRecord.expireTime || '永久有效' }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="操作人">{{ currentRecord.operator }}</a-descriptions-item>
            <a-descriptions-item label="拦截次数">
              <span style="color: #f5222d; font-weight: bold;">{{ currentRecord.blockCount }}</span>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 详细信息 -->
          <div class="detail-section" style="margin-top: 24px;">
            <h4>详细信息</h4>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="加入原因">
                {{ currentRecord.reason }}
              </a-descriptions-item>
              <a-descriptions-item label="关联人员" v-if="currentRecord.relatedPersons">
                {{ currentRecord.relatedPersons }}
              </a-descriptions-item>
              <a-descriptions-item label="备注信息" v-if="currentRecord.remark">
                {{ currentRecord.remark }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 证据材料 -->
          <div class="evidence-section" style="margin-top: 24px;" v-if="currentRecord.evidenceFiles?.length">
            <h4>证据材料</h4>
            <a-list
              :data-source="currentRecord.evidenceFiles"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a @click="previewFile(item)">{{ item.name }}</a>
                    </template>
                    <template #description>
                      文件大小: {{ item.size }} | 上传时间: {{ item.uploadTime }}
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a @click="downloadFile(item)">下载</a>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <!-- 拦截记录 -->
          <div class="block-records" style="margin-top: 24px;">
            <h4>最近拦截记录</h4>
            <a-table
              :columns="blockColumns"
              :data-source="currentRecord.blockRecords || []"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'type'">
                  <a-tag>{{ record.type }}</a-tag>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 操作历史 -->
          <div class="operation-history" style="margin-top: 24px;">
            <h4>操作历史</h4>
            <a-timeline>
              <a-timeline-item
                v-for="(item, index) in currentRecord.operationHistory || []"
                :key="index"
                :color="getOperationColor(item.type)"
              >
                <div class="history-item">
                  <div class="history-header">
                    <span class="history-time">{{ item.time }}</span>
                    <a-tag :color="getOperationColor(item.type)">
                      {{ item.type }}
                    </a-tag>
                    <span class="history-operator">{{ item.operator }}</span>
                  </div>
                  <div class="history-content">
                    {{ item.description }}
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>
      </a-modal>

      <!-- 批量导入弹窗 -->
      <a-modal
        v-model:open="showBatchAddModal"
        title="批量导入黑名单"
        width="600px"
        @ok="handleBatchImport"
      >
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选择文件">
            <a-upload
              v-model:file-list="batchImportFiles"
              :before-upload="() => false"
              accept=".xlsx,.xls,.csv"
            >
              <a-button>
                <upload-outlined />
                选择Excel/CSV文件
              </a-button>
            </a-upload>
            <div style="margin-top: 8px; color: #666;">
              支持Excel(.xlsx, .xls)和CSV格式文件
            </div>
          </a-form-item>
          
          <a-form-item label="下载模板">
            <a-button type="link" @click="downloadTemplate">
              <download-outlined />
              下载导入模板
            </a-button>
          </a-form-item>
          
          <a-form-item label="导入说明">
            <a-alert
              message="导入说明"
              description="请按照模板格式填写数据，必填字段包括：姓名、身份证号、黑名单类型、加入原因。系统将自动验证数据格式和重复性。"
              type="info"
              show-icon
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 规则配置弹窗 -->
      <a-modal
        v-model:open="showConfigModal"
        title="黑名单规则配置"
        width="800px"
        @ok="handleConfigSave"
      >
        <a-tabs v-model:activeKey="activeConfigTab">
          <a-tab-pane key="auto" tab="自动识别规则">
            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item label="逾期超过天数自动加入">
                <a-input-number 
                  v-model:value="config.autoRules.overdueDays" 
                  :min="1"
                  placeholder="天数"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="连续逾期次数">
                <a-input-number 
                  v-model:value="config.autoRules.continuousOverdue" 
                  :min="1"
                  placeholder="次数"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="恶意拒接电话次数">
                <a-input-number 
                  v-model:value="config.autoRules.refuseCallCount" 
                  :min="1"
                  placeholder="次数"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="启用自动识别">
                <a-switch v-model:checked="config.autoRules.enabled" />
              </a-form-item>
            </a-form>
          </a-tab-pane>
          
          <a-tab-pane key="expire" tab="过期策略">
            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item label="默认有效期（天）">
                <a-input-number 
                  v-model:value="config.expirePolicy.defaultDays" 
                  :min="1"
                  placeholder="天数"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="过期前提醒天数">
                <a-input-number 
                  v-model:value="config.expirePolicy.reminderDays" 
                  :min="1"
                  placeholder="天数"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="自动清理过期记录">
                <a-switch v-model:checked="config.expirePolicy.autoClean" />
              </a-form-item>
            </a-form>
          </a-tab-pane>
          
          <a-tab-pane key="sharing" tab="共享设置">
            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item label="启用行业共享">
                <a-switch v-model:checked="config.sharing.enabled" />
              </a-form-item>
              <a-form-item label="共享级别">
                <a-select v-model:value="config.sharing.level" style="width: 100%">
                  <a-select-option value="internal">仅内部</a-select-option>
                  <a-select-option value="group">集团内部</a-select-option>
                  <a-select-option value="industry">行业共享</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="自动同步">
                <a-switch v-model:checked="config.sharing.autoSync" />
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  PlusOutlined,
  UploadOutlined,
  SettingOutlined,
  ReloadOutlined,
  SearchOutlined,
  ClearOutlined,
  DownOutlined,
  UpOutlined,
  StopOutlined,
  WarningOutlined,
  PlusCircleOutlined,
  SafetyCertificateOutlined,
  DownloadOutlined,
  UserOutlined,
  IdcardOutlined,
  MobileOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'

// 状态管理
const loading = ref(false)
const showAdvancedSearch = ref(false)
const showAddModal = ref(false)
const showDetailModal = ref(false)
const showBatchAddModal = ref(false)
const showConfigModal = ref(false)
const currentRecord = ref(null)
const selectedRowKeys = ref([])
const activeAddTab = ref('basic')
const activeConfigTab = ref('auto')

// 搜索参数
const searchParams = reactive({
  name: '',
  idCard: '',
  blacklistType: undefined,
  status: undefined,
  phone: '',
  addTimeRange: [],
  operator: '',
  riskLevel: undefined
})

// 日期预设
const datePresets = {
  '今天': [dayjs(), dayjs()],
  '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
  '最近7天': [dayjs().subtract(7, 'day'), dayjs()],
  '最近30天': [dayjs().subtract(30, 'day'), dayjs()]
}

// 新增状态
const showBatchOperationModal = ref(false)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50
})

// 统计数据
const stats = reactive({
  totalBlacklist: 1256,
  activeBlacklist: 892,
  monthlyAdded: 45,
  blockCount: 2358
})

// 添加黑名单表单
const addForm = reactive({
  name: '',
  idCard: '',
  phone: '',
  blacklistType: undefined,
  riskLevel: undefined,
  reason: '',
  expireType: 'permanent',
  expireTime: null,
  evidenceFiles: [],
  relatedPersons: '',
  remark: ''
})

// 批量导入文件
const batchImportFiles = ref([])

// 配置参数
const config = reactive({
  autoRules: {
    overdueDays: 90,
    continuousOverdue: 3,
    refuseCallCount: 10,
    enabled: true
  },
  expirePolicy: {
    defaultDays: 365,
    reminderDays: 30,
    autoClean: false
  },
  sharing: {
    enabled: true,
    level: 'internal',
    autoSync: false
  }
})

// 黑名单数据
const blacklistData = ref([
  {
    id: 1,
    name: '张恶意',
    idCard: '110101199001011234',
    phone: '138****5678',
    blacklistType: 'malicious_debt',
    riskLevel: 'high',
    status: 'active',
    addTime: '2024-01-15 14:30',
    expireTime: '2025-01-15 14:30',
    operator: '李催收',
    reason: '多次恶意逃债，拒绝还款，提供虚假联系方式',
    blockCount: 25,
    relatedPersons: '担保人：王某某',
    remark: '该客户态度恶劣，建议永久拉黑',
    evidenceFiles: [
      { name: '通话录音.mp3', size: '2.5MB', uploadTime: '2024-01-15 15:00' },
      { name: '催收记录.pdf', size: '1.2MB', uploadTime: '2024-01-15 15:30' }
    ],
    blockRecords: [
      { time: '2024-01-20 10:30', type: '贷款申请', amount: '50000', result: '已拦截' },
      { time: '2024-01-18 16:45', type: '信用卡申请', amount: '20000', result: '已拦截' }
    ],
    operationHistory: [
      { time: '2024-01-15 14:30', type: '添加', operator: '李催收', description: '因恶意逃债加入黑名单' },
      { time: '2024-01-16 09:00', type: '拦截', operator: '系统', description: '拦截贷款申请，金额5万元' }
    ]
  },
  {
    id: 2,
    name: '王虚假',
    idCard: '110101199002021234',
    phone: '139****1234',
    blacklistType: 'false_info',
    riskLevel: 'medium',
    status: 'active',
    addTime: '2024-01-18 16:20',
    expireTime: '2024-07-18 16:20',
    operator: '张审核',
    reason: '提供虚假身份信息和收入证明',
    blockCount: 8,
    relatedPersons: '',
    remark: '发现使用虚假身份证办理业务',
    evidenceFiles: [
      { name: '身份证对比.jpg', size: '856KB', uploadTime: '2024-01-18 16:25' }
    ],
    blockRecords: [
      { time: '2024-01-19 14:20', type: '信用卡申请', amount: '30000', result: '已拦截' }
    ],
    operationHistory: [
      { time: '2024-01-18 16:20', type: '添加', operator: '张审核', description: '因提供虚假信息加入黑名单' }
    ]
  }
])

// 表格列配置
const columns = [
  { title: '客户姓名', dataIndex: 'name', key: 'name', width: 100 },
  { title: '身份证号', dataIndex: 'idCard', key: 'idCard', width: 150 },
  { title: '手机号码', dataIndex: 'phone', key: 'phone', width: 120 },
  { title: '黑名单类型', dataIndex: 'blacklistType', key: 'blacklistType', width: 120 },
  { title: '风险等级', dataIndex: 'riskLevel', key: 'riskLevel', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '拦截次数', dataIndex: 'blockCount', key: 'blockCount', width: 100 },
  { title: '添加时间', dataIndex: 'addTime', key: 'addTime', width: 150 },
  { title: '过期时间', dataIndex: 'expireTime', key: 'expireTime', width: 150 },
  { title: '操作人', dataIndex: 'operator', key: 'operator', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const blockColumns = [
  { title: '拦截时间', dataIndex: 'time', key: 'time' },
  { title: '业务类型', dataIndex: 'type', key: 'type' },
  { title: '涉及金额', dataIndex: 'amount', key: 'amount' },
  { title: '拦截结果', dataIndex: 'result', key: 'result' }
]

// 方法
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const handleSearch = () => {
  console.log('搜索参数:', searchParams)
  message.success('查询成功')
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = undefined
    }
  })
  message.info('筛选条件已重置')
}

// 导出黑名单数据
const exportBlacklist = () => {
  console.log('导出黑名单数据')
  message.success('导出功能开发中...')
}

// 批量操作处理
const handleBatchAction = ({ key }) => {
  switch (key) {
    case 'remove':
      Modal.confirm({
        title: '确认批量移除',
        content: `确定要移除选中的 ${selectedRowKeys.value.length} 条黑名单记录吗？`,
        onOk() {
          message.success(`成功移除 ${selectedRowKeys.value.length} 条记录`)
          selectedRowKeys.value = []
        }
      })
      break
    case 'suspend':
      Modal.confirm({
        title: '确认批量暂停',
        content: `确定要暂停选中的 ${selectedRowKeys.value.length} 条黑名单记录吗？`,
        onOk() {
          message.success(`成功暂停 ${selectedRowKeys.value.length} 条记录`)
          selectedRowKeys.value = []
        }
      })
      break
    case 'activate':
      Modal.confirm({
        title: '确认批量激活',
        content: `确定要激活选中的 ${selectedRowKeys.value.length} 条黑名单记录吗？`,
        onOk() {
          message.success(`成功激活 ${selectedRowKeys.value.length} 条记录`)
          selectedRowKeys.value = []
        }
      })
      break
    case 'export':
      message.success(`正在导出选中的 ${selectedRowKeys.value.length} 条记录...`)
      break
  }
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const viewDetail = (record) => {
  currentRecord.value = record
  showDetailModal.value = true
}

const editBlacklist = (record) => {
  Object.assign(addForm, record)
  showAddModal.value = true
}

const handleMoreAction = (action, record) => {
  switch (action) {
    case 'remove':
      Modal.confirm({
        title: '确认移除',
        content: `确定要移除 ${record.name} 的黑名单记录吗？`,
        onOk() {
          message.success('移除成功')
        }
      })
      break
    case 'suspend':
      message.info(`暂停 ${record.name} 的黑名单`)
      break
    case 'activate':
      message.info(`激活 ${record.name} 的黑名单`)
      break
    case 'extend':
      message.info(`延期 ${record.name} 的黑名单`)
      break
    case 'history':
      message.info(`查看 ${record.name} 操作历史`)
      break
    case 'report':
      message.info(`查看 ${record.name} 举报记录`)
      break
  }
}

const handleBatchOperation = ({ key }) => {
  const count = selectedRowKeys.value.length
  if (count === 0) {
    message.warning('请先选择要操作的记录')
    return
  }
  
  switch (key) {
    case 'remove':
      Modal.confirm({
        title: '批量移除',
        content: `确定要移除选中的 ${count} 条黑名单记录吗？`,
        onOk() {
          message.success('批量移除成功')
        }
      })
      break
    case 'suspend':
      message.info(`批量暂停 ${count} 条记录`)
      break
    case 'activate':
      message.info(`批量激活 ${count} 条记录`)
      break
    case 'extend':
      message.info(`批量延期 ${count} 条记录`)
      break
  }
}

const handleAddBlacklist = () => {
  message.success('添加黑名单成功')
  showAddModal.value = false
  // 重置表单
  Object.keys(addForm).forEach(key => {
    if (Array.isArray(addForm[key])) {
      addForm[key] = []
    } else {
      addForm[key] = ''
    }
  })
}

const handleBatchImport = () => {
  if (batchImportFiles.value.length === 0) {
    message.warning('请先选择要导入的文件')
    return
  }
  
  message.loading('正在导入...')
  setTimeout(() => {
    message.success('批量导入完成')
    showBatchAddModal.value = false
  }, 2000)
}

const downloadTemplate = () => {
  message.success('正在下载模板文件...')
}

const handleConfigSave = () => {
  message.success('规则配置保存成功')
  showConfigModal.value = false
}

const exportData = () => {
  message.success('正在导出黑名单数据...')
}

const previewFile = (file) => {
  message.info(`预览文件: ${file.name}`)
}

const downloadFile = (file) => {
  message.success(`下载文件: ${file.name}`)
}

// 工具方法
const getBlacklistTypeColor = (type) => {
  const colors = {
    malicious_debt: '#f5222d',
    false_info: '#fa8c16',
    violent_resist: '#a8071a',
    legal_dispute: '#722ed1',
    poor_credit: '#faad14',
    industry_shared: '#1890ff'
  }
  return colors[type] || 'default'
}

const getBlacklistTypeText = (type) => {
  const texts = {
    malicious_debt: '恶意逃债',
    false_info: '虚假信息',
    violent_resist: '暴力抗拒',
    legal_dispute: '法律纠纷',
    poor_credit: '信用极差',
    industry_shared: '行业共享'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    active: 'red',
    expired: 'default',
    removed: 'green',
    suspended: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '生效中',
    expired: '已过期',
    removed: '已移除',
    suspended: '暂停'
  }
  return texts[status] || status
}

const getRiskLevelColor = (level) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || level
}

const isExpiringSoon = (expireTime) => {
  if (!expireTime) return false
  const expire = new Date(expireTime)
  const now = new Date()
  const diffDays = (expire - now) / (1000 * 60 * 60 * 24)
  return diffDays <= 30 && diffDays > 0
}

const getOperationColor = (type) => {
  const colors = {
    '添加': 'red',
    '移除': 'green',
    '暂停': 'orange',
    '激活': 'blue',
    '拦截': 'purple'
  }
  return colors[type] || 'default'
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.stats-cards {
  margin-bottom: 16px;
  
  .ant-card {
    height: 100%;
  }
}

.blacklist-card {
  .expire-warning {
    color: #fa8c16;
    font-weight: 500;
  }
}

.blacklist-detail {
  .detail-section,
  .evidence-section,
  .block-records,
  .operation-history {
    h4 {
      margin-bottom: 16px;
      font-weight: 500;
    }
  }
  
  .history-item {
    .history-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
      
      .history-time {
        font-weight: 500;
      }
      
      .history-operator {
        color: #666;
      }
    }
    
    .history-content {
      color: #666;
    }
  }
}
</style>
