<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>信用评估</h2>
        <a-space>
          <a-button type="primary" @click="showBatchAssessModal = true">
            <thunderbolt-outlined />
            批量评估
          </a-button>
          <a-button @click="showModelConfigModal = true">
            <setting-outlined />
            模型配置
          </a-button>
          <a-button @click="refreshData">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input 
                  v-model:value="searchParams.name" 
                  placeholder="请输入客户姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="身份证号">
                <a-input 
                  v-model:value="searchParams.idCard" 
                  placeholder="请输入身份证号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="信用等级">
                <a-select 
                  v-model:value="searchParams.creditLevel"
                  placeholder="请选择信用等级"
                  allow-clear
                >
                  <a-select-option value="AAA">AAA (优秀)</a-select-option>
                  <a-select-option value="AA">AA (良好)</a-select-option>
                  <a-select-option value="A">A (一般)</a-select-option>
                  <a-select-option value="BBB">BBB (较差)</a-select-option>
                  <a-select-option value="BB">BB (差)</a-select-option>
                  <a-select-option value="B">B (极差)</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评估状态">
                <a-select 
                  v-model:value="searchParams.assessStatus"
                  placeholder="请选择评估状态"
                  allow-clear
                >
                  <a-select-option value="pending">待评估</a-select-option>
                  <a-select-option value="processing">评估中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="failed">评估失败</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="评估时间">
                <a-range-picker 
                  v-model:value="searchParams.assessDateRange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="风险等级">
                <a-select 
                  v-model:value="searchParams.riskLevel"
                  placeholder="请选择风险等级"
                  allow-clear
                >
                  <a-select-option value="low">低风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="high">高风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <div class="search-actions">
                <div class="search-buttons">
                  <a-space>
                    <a-button type="primary" html-type="submit">
                      <search-outlined />
                      查询
                    </a-button>
                    <a-button @click="handleReset">
                      <reload-outlined />
                      重置
                    </a-button>
                    <a-button @click="showAdvancedSearch = !showAdvancedSearch">
                    {{ showAdvancedSearch ? '收起' : '展开' }}
                    <down-outlined v-if="!showAdvancedSearch" />
                    <up-outlined v-else />
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="总客户数"
              :value="stats.totalCustomers"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <user-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已评估数"
              :value="stats.assessedCustomers"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="平均信用分"
              :value="stats.avgCreditScore"
              :precision="0"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="高风险比例"
              :value="stats.highRiskRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 客户评估列表 -->
      <a-card class="assessment-list-card">
        <template #title>
          <a-space>
            <span>客户评估列表</span>
            <a-tag color="blue">共 {{ customerList.length }} 条</a-tag>
          </a-space>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="exportData">
              <download-outlined />
              导出
            </a-button>
            <a-dropdown>
              <a-button>
                批量操作
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleBatchOperation">
                  <a-menu-item key="assess">批量评估</a-menu-item>
                  <a-menu-item key="update">批量更新</a-menu-item>
                  <a-menu-item key="export">批量导出</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>

        <a-table
          :columns="columns"
          :data-source="customerList"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
          }"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true
          }"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a @click="viewCustomerDetail(record)">{{ record.name }}</a>
            </template>
            <template v-else-if="column.key === 'creditLevel'">
              <a-tag :color="getCreditLevelColor(record.creditLevel)">
                {{ record.creditLevel }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'creditScore'">
              <div class="credit-score">
                <a-progress
                  :percent="(record.creditScore / 1000) * 100"
                  :show-info="false"
                  :stroke-color="getCreditScoreColor(record.creditScore)"
                  size="small"
                />
                <span class="score-text">{{ record.creditScore }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'assessStatus'">
              <a-tag :color="getAssessStatusColor(record.assessStatus)">
                {{ getAssessStatusText(record.assessStatus) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewAssessmentDetail(record)"
                >
                  详情
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="startAssessment(record)"
                  v-if="record.assessStatus !== 'completed'"
                >
                  评估
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewHistory(record)"
                >
                  历史
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMoreAction(key, record)">
                      <a-menu-item key="reassess">重新评估</a-menu-item>
                      <a-menu-item key="report">生成报告</a-menu-item>
                      <a-menu-item key="adjust">手动调整</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 评估详情弹窗 -->
      <a-modal
        v-model:open="showDetailModal"
        :title="currentCustomer?.name + ' - 信用评估详情'"
        width="1000px"
        :footer="null"
      >
        <div v-if="currentCustomer" class="assessment-detail">
          <!-- 基本信息 -->
          <a-descriptions title="基本信息" :column="2" bordered>
            <a-descriptions-item label="客户姓名">{{ currentCustomer.name }}</a-descriptions-item>
            <a-descriptions-item label="身份证号">{{ currentCustomer.idCard }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ currentCustomer.phone }}</a-descriptions-item>
            <a-descriptions-item label="评估时间">{{ currentCustomer.assessTime }}</a-descriptions-item>
            <a-descriptions-item label="信用等级">
              <a-tag :color="getCreditLevelColor(currentCustomer.creditLevel)">
                {{ currentCustomer.creditLevel }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="信用分数">{{ currentCustomer.creditScore }}</a-descriptions-item>
            <a-descriptions-item label="风险等级">
              <a-tag :color="getRiskLevelColor(currentCustomer.riskLevel)">
                {{ getRiskLevelText(currentCustomer.riskLevel) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="评估状态">
              <a-tag :color="getAssessStatusColor(currentCustomer.assessStatus)">
                {{ getAssessStatusText(currentCustomer.assessStatus) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 评估维度得分 -->
          <div class="assessment-dimensions" style="margin-top: 24px;">
            <h4>评估维度得分</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="还款历史" size="small">
                  <a-progress 
                    :percent="currentCustomer.dimensions?.paymentHistory || 0" 
                    :stroke-color="getProgressColor(currentCustomer.dimensions?.paymentHistory || 0)"
                  />
                  <p style="margin-top: 8px; color: #666;">
                    {{ currentCustomer.dimensionComments?.paymentHistory || '历史还款记录良好，按时还款率高' }}
                  </p>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="收入稳定性" size="small">
                  <a-progress 
                    :percent="currentCustomer.dimensions?.incomeStability || 0"
                    :stroke-color="getProgressColor(currentCustomer.dimensions?.incomeStability || 0)"
                  />
                  <p style="margin-top: 8px; color: #666;">
                    {{ currentCustomer.dimensionComments?.incomeStability || '收入来源稳定，工作单位可靠' }}
                  </p>
                </a-card>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <a-card title="资产负债情况" size="small">
                  <a-progress 
                    :percent="currentCustomer.dimensions?.assetLiability || 0"
                    :stroke-color="getProgressColor(currentCustomer.dimensions?.assetLiability || 0)"
                  />
                  <p style="margin-top: 8px; color: #666;">
                    {{ currentCustomer.dimensionComments?.assetLiability || '资产负债结构合理，负债率适中' }}
                  </p>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="职业稳定性" size="small">
                  <a-progress 
                    :percent="currentCustomer.dimensions?.jobStability || 0"
                    :stroke-color="getProgressColor(currentCustomer.dimensions?.jobStability || 0)"
                  />
                  <p style="margin-top: 8px; color: #666;">
                    {{ currentCustomer.dimensionComments?.jobStability || '工作稳定，在职时间较长' }}
                  </p>
                </a-card>
              </a-col>
            </a-row>
          </div>

          <!-- 外部征信数据 -->
          <div class="external-credit" style="margin-top: 24px;">
            <h4>外部征信数据</h4>
            <a-table
              :columns="creditColumns"
              :data-source="currentCustomer.externalCredit || []"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 'normal' ? 'green' : 'red'">
                    {{ record.status === 'normal' ? '正常' : '异常' }}
                  </a-tag>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 评估建议 -->
          <div class="assessment-suggestions" style="margin-top: 24px;">
            <h4>评估建议</h4>
            <a-alert
              :message="currentCustomer.suggestion?.title || '综合评估建议'"
              :description="currentCustomer.suggestion?.content || '该客户信用状况良好，建议适当提高授信额度。'"
              :type="currentCustomer.suggestion?.type || 'info'"
              show-icon
            />
            <div style="margin-top: 12px;">
              <a-tag v-for="tag in currentCustomer.suggestion?.tags || ['稳定客户', '优质资源']" :key="tag" color="blue">
                {{ tag }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-modal>

      <!-- 批量评估弹窗 -->
      <a-modal
        v-model:open="showBatchAssessModal"
        title="批量信用评估"
        width="600px"
        @ok="handleBatchAssess"
      >
        <a-form :model="batchAssessForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="评估模型">
            <a-select v-model:value="batchAssessForm.model" placeholder="请选择评估模型">
              <a-select-option value="standard">标准模型</a-select-option>
              <a-select-option value="advanced">高级模型</a-select-option>
              <a-select-option value="ai">AI智能模型</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="评估范围">
            <a-radio-group v-model:value="batchAssessForm.scope">
              <a-radio value="selected">选中客户</a-radio>
              <a-radio value="all">全部客户</a-radio>
              <a-radio value="filter">筛选条件</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="优先级">
            <a-select v-model:value="batchAssessForm.priority">
              <a-select-option value="high">高优先级</a-select-option>
              <a-select-option value="normal">普通优先级</a-select-option>
              <a-select-option value="low">低优先级</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="备注">
            <a-textarea 
              v-model:value="batchAssessForm.remark" 
              :rows="3"
              placeholder="请输入批量评估备注..."
            />
          </a-form-item>
        </a-form>
        
        <a-alert
          v-if="selectedRowKeys.length > 0"
          :message="`已选择 ${selectedRowKeys.length} 个客户进行批量评估`"
          type="info"
          show-icon
          style="margin-top: 16px"
        />
      </a-modal>

      <!-- 模型配置弹窗 -->
      <a-modal
        v-model:open="showModelConfigModal"
        title="评估模型配置"
        width="800px"
        @ok="handleModelConfig"
      >
        <a-tabs v-model:activeKey="activeModelTab">
          <a-tab-pane key="weights" tab="权重配置">
            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item label="还款历史权重">
                <a-slider 
                  v-model:value="modelConfig.weights.paymentHistory" 
                  :max="100"
                  :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
                />
              </a-form-item>
              <a-form-item label="收入稳定性权重">
                <a-slider 
                  v-model:value="modelConfig.weights.incomeStability" 
                  :max="100"
                  :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
                />
              </a-form-item>
              <a-form-item label="资产负债权重">
                <a-slider 
                  v-model:value="modelConfig.weights.assetLiability" 
                  :max="100"
                  :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
                />
              </a-form-item>
              <a-form-item label="职业稳定性权重">
                <a-slider 
                  v-model:value="modelConfig.weights.jobStability" 
                  :max="100"
                  :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
                />
              </a-form-item>
            </a-form>
          </a-tab-pane>
          
          <a-tab-pane key="rules" tab="评级规则">
            <a-table
              :columns="ruleColumns"
              :data-source="modelConfig.rules"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'minScore'">
                  <a-input-number 
                    v-model:value="record.minScore" 
                    :min="0" 
                    :max="1000"
                    style="width: 100%"
                  />
                </template>
                <template v-else-if="column.key === 'maxScore'">
                  <a-input-number 
                    v-model:value="record.maxScore" 
                    :min="0" 
                    :max="1000"
                    style="width: 100%"
                  />
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="thresholds" tab="风险阈值">
            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item label="低风险阈值">
                <a-input-number 
                  v-model:value="modelConfig.thresholds.lowRisk" 
                  :min="0" 
                  :max="1000"
                  placeholder="分数大于此值为低风险"
                />
              </a-form-item>
              <a-form-item label="中风险阈值">
                <a-input-number 
                  v-model:value="modelConfig.thresholds.mediumRisk" 
                  :min="0" 
                  :max="1000"
                  placeholder="分数大于此值为中风险"
                />
              </a-form-item>
              <a-form-item label="预警分数">
                <a-input-number 
                  v-model:value="modelConfig.thresholds.warningScore" 
                  :min="0" 
                  :max="1000"
                  placeholder="分数低于此值发出预警"
                />
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 评估历史弹窗 -->
      <a-modal
        v-model:open="showHistoryModal"
        :title="historyCustomer?.name + ' - 评估历史'"
        width="800px"
        :footer="null"
      >
        <a-timeline>
          <a-timeline-item
            v-for="(item, index) in assessmentHistory"
            :key="index"
            :color="getHistoryColor(item.result)"
          >
            <template #dot>
              <component :is="getHistoryIcon(item.result)" />
            </template>
            <div class="history-item">
              <div class="history-header">
                <span class="history-time">{{ item.time }}</span>
                <a-tag :color="getCreditLevelColor(item.creditLevel)">
                  {{ item.creditLevel }}
                </a-tag>
                <span class="history-score">{{ item.score }}分</span>
              </div>
              <div class="history-content">
                <p>{{ item.remark }}</p>
                <div class="history-changes" v-if="item.changes">
                  <a-tag v-for="change in item.changes" :key="change" size="small">
                    {{ change }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  ThunderboltOutlined,
  SettingOutlined,
  ReloadOutlined,
  SearchOutlined,
  ClearOutlined,
  DownOutlined,
  UpOutlined,
  UserOutlined,
  CheckCircleOutlined,
  StarOutlined,
  WarningOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

// 状态管理
const loading = ref(false)
const showAdvancedSearch = ref(false)
const showDetailModal = ref(false)
const showBatchAssessModal = ref(false)
const showModelConfigModal = ref(false)
const showHistoryModal = ref(false)
const currentCustomer = ref(null)
const historyCustomer = ref(null)
const selectedRowKeys = ref([])
const activeModelTab = ref('weights')

// 搜索参数
const searchParams = reactive({
  name: '',
  idCard: '',
  creditLevel: undefined,
  assessStatus: undefined,
  assessDateRange: [],
  riskLevel: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100
})

// 统计数据
const stats = reactive({
  totalCustomers: 2356,
  assessedCustomers: 1892,
  avgCreditScore: 685,
  highRiskRate: 12.5
})

// 批量评估表单
const batchAssessForm = reactive({
  model: 'standard',
  scope: 'selected',
  priority: 'normal',
  remark: ''
})

// 模型配置
const modelConfig = reactive({
  weights: {
    paymentHistory: 35,
    incomeStability: 25,
    assetLiability: 25,
    jobStability: 15
  },
  rules: [
    { level: 'AAA', description: '优秀', minScore: 850, maxScore: 1000, color: '#52c41a' },
    { level: 'AA', description: '良好', minScore: 750, maxScore: 849, color: '#1890ff' },
    { level: 'A', description: '一般', minScore: 650, maxScore: 749, color: '#faad14' },
    { level: 'BBB', description: '较差', minScore: 550, maxScore: 649, color: '#fa8c16' },
    { level: 'BB', description: '差', minScore: 450, maxScore: 549, color: '#f5222d' },
    { level: 'B', description: '极差', minScore: 0, maxScore: 449, color: '#a8071a' }
  ],
  thresholds: {
    lowRisk: 700,
    mediumRisk: 500,
    warningScore: 400
  }
})

// 客户列表数据
const customerList = ref([
  {
    id: 1,
    name: '张三',
    idCard: '110101199001011234',
    phone: '138****5678',
    creditLevel: 'AA',
    creditScore: 765,
    riskLevel: 'low',
    assessStatus: 'completed',
    assessTime: '2024-01-20 14:30',
    assessor: '系统自动',
    dimensions: {
      paymentHistory: 85,
      incomeStability: 75,
      assetLiability: 80,
      jobStability: 70
    },
    dimensionComments: {
      paymentHistory: '历史还款记录良好，按时还款率95%',
      incomeStability: '收入来源稳定，月收入1.2万',
      assetLiability: '资产负债结构合理，负债率45%',
      jobStability: '在职3年，工作稳定'
    },
    externalCredit: [
      { source: '央行征信', score: 780, status: 'normal', updateTime: '2024-01-15' },
      { source: '芝麻信用', score: 750, status: 'normal', updateTime: '2024-01-18' },
      { source: '腾讯征信', score: 720, status: 'normal', updateTime: '2024-01-20' }
    ],
    suggestion: {
      title: '优质客户建议',
      content: '该客户信用状况良好，还款能力强，建议适当提高授信额度。',
      type: 'success',
      tags: ['稳定客户', '优质资源', '可提额']
    }
  },
  {
    id: 2,
    name: '李四',
    idCard: '110101199002021234',
    phone: '139****1234',
    creditLevel: 'BBB',
    creditScore: 580,
    riskLevel: 'medium',
    assessStatus: 'completed',
    assessTime: '2024-01-19 16:45',
    assessor: '王评估师',
    dimensions: {
      paymentHistory: 60,
      incomeStability: 55,
      assetLiability: 50,
      jobStability: 45
    },
    dimensionComments: {
      paymentHistory: '偶有逾期记录，按时还款率70%',
      incomeStability: '收入波动较大，月收入6-8千',
      assetLiability: '负债率偏高，达65%',
      jobStability: '工作不够稳定，频繁换工作'
    },
    externalCredit: [
      { source: '央行征信', score: 590, status: 'normal', updateTime: '2024-01-15' },
      { source: '芝麻信用', score: 580, status: 'normal', updateTime: '2024-01-18' }
    ],
    suggestion: {
      title: '谨慎评估建议',
      content: '该客户信用状况一般，建议加强风险监控，适度控制授信额度。',
      type: 'warning',
      tags: ['风险监控', '谨慎授信']
    }
  },
  {
    id: 3,
    name: '王五',
    idCard: '110101199003031234',
    phone: '137****9876',
    creditLevel: 'B',
    creditScore: 420,
    riskLevel: 'high',
    assessStatus: 'completed',
    assessTime: '2024-01-18 10:20',
    assessor: '系统自动',
    dimensions: {
      paymentHistory: 30,
      incomeStability: 40,
      assetLiability: 35,
      jobStability: 25
    },
    dimensionComments: {
      paymentHistory: '多次逾期记录，按时还款率仅40%',
      incomeStability: '收入不稳定，无固定收入来源',
      assetLiability: '负债率极高，超过80%',
      jobStability: '无固定工作，主要依靠零工'
    },
    externalCredit: [
      { source: '央行征信', score: 450, status: 'abnormal', updateTime: '2024-01-15' },
      { source: '芝麻信用', score: 400, status: 'abnormal', updateTime: '2024-01-18' }
    ],
    suggestion: {
      title: '高风险客户警告',
      content: '该客户信用状况较差，还款风险极高，建议拒绝授信或要求担保。',
      type: 'error',
      tags: ['高风险', '拒绝授信', '需要担保']
    }
  }
])

// 评估历史数据
const assessmentHistory = ref([
  {
    time: '2024-01-20 14:30',
    creditLevel: 'AA',
    score: 765,
    result: 'success',
    remark: '系统自动评估完成，信用等级提升',
    changes: ['等级从A提升到AA', '分数从720提升到765']
  },
  {
    time: '2024-01-15 09:15',
    creditLevel: 'A',
    score: 720,
    result: 'success',
    remark: '定期重评估，维持原等级',
    changes: ['分数从715提升到720']
  },
  {
    time: '2024-01-10 16:20',
    creditLevel: 'A',
    score: 715,
    result: 'warning',
    remark: '发现收入波动，需关注',
    changes: ['收入稳定性下降5分']
  }
])

// 表格列配置
const columns = [
  { title: '客户姓名', dataIndex: 'name', key: 'name', width: 100 },
  { title: '身份证号', dataIndex: 'idCard', key: 'idCard', width: 150 },
  { title: '手机号码', dataIndex: 'phone', key: 'phone', width: 120 },
  { title: '信用等级', dataIndex: 'creditLevel', key: 'creditLevel', width: 100 },
  { title: '信用分数', dataIndex: 'creditScore', key: 'creditScore', width: 150 },
  { title: '风险等级', dataIndex: 'riskLevel', key: 'riskLevel', width: 100 },
  { title: '评估状态', dataIndex: 'assessStatus', key: 'assessStatus', width: 100 },
  { title: '评估时间', dataIndex: 'assessTime', key: 'assessTime', width: 150 },
  { title: '评估人', dataIndex: 'assessor', key: 'assessor', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const creditColumns = [
  { title: '征信机构', dataIndex: 'source', key: 'source' },
  { title: '信用分数', dataIndex: 'score', key: 'score' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' }
]

const ruleColumns = [
  { title: '等级', dataIndex: 'level', key: 'level' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '最低分数', dataIndex: 'minScore', key: 'minScore' },
  { title: '最高分数', dataIndex: 'maxScore', key: 'maxScore' }
]

// 方法
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const handleSearch = () => {
  console.log('搜索参数:', searchParams)
  message.success('查询成功')
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = undefined
    }
  })
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const viewCustomerDetail = (customer) => {
  currentCustomer.value = customer
  showDetailModal.value = true
}

const viewAssessmentDetail = (customer) => {
  currentCustomer.value = customer
  showDetailModal.value = true
}

const startAssessment = (customer) => {
  message.loading('正在启动评估...')
  setTimeout(() => {
    message.success(`${customer.name} 评估已启动`)
  }, 1500)
}

const viewHistory = (customer) => {
  historyCustomer.value = customer
  showHistoryModal.value = true
}

const handleMoreAction = (action, customer) => {
  switch (action) {
    case 'reassess':
      message.info(`重新评估 ${customer.name}`)
      break
    case 'report':
      message.info(`生成 ${customer.name} 评估报告`)
      break
    case 'adjust':
      message.info(`手动调整 ${customer.name} 评估结果`)
      break
  }
}

const handleBatchOperation = ({ key }) => {
  switch (key) {
    case 'assess':
      showBatchAssessModal.value = true
      break
    case 'update':
      message.info('批量更新评估结果')
      break
    case 'export':
      message.info('批量导出评估数据')
      break
  }
}

const handleBatchAssess = () => {
  message.loading('正在启动批量评估...')
  setTimeout(() => {
    message.success('批量评估已启动')
    showBatchAssessModal.value = false
  }, 2000)
}

const handleModelConfig = () => {
  message.success('模型配置已保存')
  showModelConfigModal.value = false
}

const exportData = () => {
  message.success('正在导出评估数据...')
}

// 工具方法
const getCreditLevelColor = (level) => {
  const colors = {
    'AAA': '#52c41a',
    'AA': '#1890ff',
    'A': '#faad14',
    'BBB': '#fa8c16',
    'BB': '#f5222d',
    'B': '#a8071a'
  }
  return colors[level] || 'default'
}

const getCreditScoreColor = (score) => {
  if (score >= 800) return '#52c41a'
  if (score >= 700) return '#1890ff'
  if (score >= 600) return '#faad14'
  if (score >= 500) return '#fa8c16'
  return '#f5222d'
}

const getRiskLevelColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[level] || level
}

const getAssessStatusColor = (status) => {
  const colors = {
    pending: 'default',
    processing: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getAssessStatusText = (status) => {
  const texts = {
    pending: '待评估',
    processing: '评估中',
    completed: '已完成',
    failed: '评估失败'
  }
  return texts[status] || status
}

const getProgressColor = (percent) => {
  if (percent >= 80) return '#52c41a'
  if (percent >= 60) return '#1890ff'
  if (percent >= 40) return '#faad14'
  return '#f5222d'
}

const getHistoryColor = (result) => {
  const colors = {
    success: 'green',
    warning: 'orange',
    error: 'red'
  }
  return colors[result] || 'blue'
}

const getHistoryIcon = (result) => {
  const icons = {
    success: CheckCircleOutlined,
    warning: WarningOutlined,
    error: 'CloseCircleOutlined'
  }
  return icons[result] || 'InfoCircleOutlined'
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  
  .search-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}

.stats-cards {
  margin-bottom: 16px;
  
  .ant-card {
    height: 100%;
  }
}

.assessment-list-card {
  .credit-score {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .ant-progress {
      flex: 1;
      min-width: 60px;
    }
    
    .score-text {
      font-weight: 500;
      min-width: 40px;
    }
  }
}

.assessment-detail {
  .assessment-dimensions {
    .ant-card {
      height: 100%;
    }
  }
  
  .external-credit {
    .ant-table {
      border: 1px solid #f0f0f0;
    }
  }
  
  .assessment-suggestions {
    .ant-tag {
      margin-bottom: 8px;
    }
  }
}

.history-item {
  .history-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    
    .history-time {
      font-weight: 500;
    }
    
    .history-score {
      color: #1890ff;
      font-weight: 500;
    }
  }
  
  .history-content {
    p {
      margin: 0 0 8px 0;
      color: #666;
    }
    
    .history-changes {
      .ant-tag {
        margin-bottom: 4px;
      }
    }
  }
}
</style>
