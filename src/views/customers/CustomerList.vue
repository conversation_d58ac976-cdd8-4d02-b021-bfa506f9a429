<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>客户档案</h2>
        <a-space>
          <a-button @click="showImportModal = true">
            <upload-outlined />
            导入客户
          </a-button>
          <a-button type="primary" @click="() => { isEdit = false; customerModalVisible = true }">
            <plus-outlined />
            新建客户
          </a-button>
        </a-space>
      </div>

      <!-- 搜索区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input 
                  v-model:value="searchParams.name" 
                  placeholder="请输入客户姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="身份证号">
                <a-input 
                  v-model:value="searchParams.idCard" 
                  placeholder="请输入身份证号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="手机号码">
                <a-input 
                  v-model:value="searchParams.phone" 
                  placeholder="请输入手机号码"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="客户等级">
                <a-select 
                  v-model:value="searchParams.level"
                  placeholder="请选择客户等级"
                  allow-clear
                >
                  <a-select-option value="VIP">VIP客户</a-select-option>
                  <a-select-option value="A">A级客户</a-select-option>
                  <a-select-option value="B">B级客户</a-select-option>
                  <a-select-option value="C">C级客户</a-select-option>
                  <a-select-option value="D">D级客户</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="风险等级">
                <a-select 
                  v-model:value="searchParams.riskLevel"
                  placeholder="请选择风险等级"
                  allow-clear
                >
                  <a-select-option value="low">低风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="extreme">极高风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="客户状态">
                <a-select 
                  v-model:value="searchParams.status"
                  placeholder="请选择客户状态"
                  allow-clear
                >
                  <a-select-option value="normal">正常</a-select-option>
                  <a-select-option value="blacklist">黑名单</a-select-option>
                  <a-select-option value="attention">重点关注</a-select-option>
                  <a-select-option value="lost">失联</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="创建时间">
                <a-range-picker 
                  v-model:value="searchParams.createDateRange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <div class="search-actions">
                <div class="search-buttons">
                  <a-space>
                    <a-button type="primary" html-type="submit">
                      <search-outlined />
                      查询
                    </a-button>
                    <a-button @click="handleReset">
                      <reload-outlined />
                      重置
                    </a-button>
                    <a-button @click="toggleAdvanced">
                      {{ showAdvanced ? '收起' : '展开' }}
                      <component :is="showAdvanced ? 'up-outlined' : 'down-outlined'" />
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>

          <!-- 高级搜索选项 -->
          <a-row :gutter="16" v-if="showAdvanced" style="margin-top: 16px">
            <a-col :span="6">
              <a-form-item label="职业类型">
                <a-select 
                  v-model:value="searchParams.occupation"
                  placeholder="请选择职业类型"
                  allow-clear
                >
                  <a-select-option value="employed">在职</a-select-option>
                  <a-select-option value="business">经商</a-select-option>
                  <a-select-option value="freelance">自由职业</a-select-option>
                  <a-select-option value="retired">退休</a-select-option>
                  <a-select-option value="unemployed">无业</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="收入范围">
                <a-select 
                  v-model:value="searchParams.incomeRange"
                  placeholder="请选择收入范围"
                  allow-clear
                >
                  <a-select-option value="0-5000">5000以下</a-select-option>
                  <a-select-option value="5000-10000">5000-10000</a-select-option>
                  <a-select-option value="10000-20000">10000-20000</a-select-option>
                  <a-select-option value="20000+">20000以上</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="地区">
                <a-cascader 
                  v-model:value="searchParams.region"
                  :options="regionOptions"
                  placeholder="请选择地区"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="年龄范围">
                <a-slider 
                  v-model:value="searchParams.ageRange"
                  range
                  :min="18"
                  :max="80"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 操作栏 -->
      <div class="table-operations">
        <a-space>
          <a-button 
            @click="handleBatchUpdate" 
            :disabled="!selectedRowKeys.length"
          >
            批量更新
          </a-button>
          <a-button 
            @click="handleBatchTag" 
            :disabled="!selectedRowKeys.length"
          >
            批量标签
          </a-button>
          <a-button 
            @click="handleBatchExport" 
            :disabled="!selectedRowKeys.length"
          >
            导出选中
          </a-button>
          <a-button @click="handleExportAll">
            导出全部
          </a-button>
        </a-space>
        
        <div>
          <span>共 {{ total }} 条记录</span>
        </div>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange
        }"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true
        }"
        :loading="loading"
        @change="handleTableChange"
        :scroll="{ x: 1800 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a @click="viewCustomerDetail(record)">{{ record.name }}</a>
          </template>
          
          <template v-else-if="column.key === 'level'">
            <a-tag :color="getLevelColor(record.level)">
              {{ record.level }}级客户
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'riskLevel'">
            <a-tag :color="getRiskColor(record.riskLevel)">
              {{ getRiskText(record.riskLevel) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-badge :status="getStatusType(record.status)" :text="getStatusText(record.status)" />
          </template>
          
          <template v-else-if="column.key === 'tags'">
            <a-space size="small">
              <a-tag v-for="tag in record.tags" :key="tag" size="small">{{ tag }}</a-tag>
            </a-space>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewCustomerDetail(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="editCustomer(record)">
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="contact">管理联系人</a-menu-item>
                    <a-menu-item key="assessment">信用评估</a-menu-item>
                    <a-menu-item key="tag">设置标签</a-menu-item>
                    <a-menu-item key="blacklist">加入黑名单</a-menu-item>
                    <a-menu-item key="delete">删除客户</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 新建/编辑客户弹窗 -->
      <a-modal
        v-model:open="customerModalVisible"
        :title="isEdit ? '编辑客户' : '新建客户'"
        width="900px"
        @ok="handleSaveCustomer"
      >
        <a-form
          ref="customerForm"
          :model="customerFormState"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-tabs v-model:activeKey="activeTab">
            <!-- 基本信息 -->
            <a-tab-pane key="basic" tab="基本信息">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="客户姓名" name="name" required>
                    <a-input v-model:value="customerFormState.name" placeholder="请输入客户姓名" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="身份证号" name="idCard" required>
                    <a-input v-model:value="customerFormState.idCard" placeholder="请输入身份证号" />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="性别" name="gender">
                    <a-radio-group v-model:value="customerFormState.gender">
                      <a-radio value="male">男</a-radio>
                      <a-radio value="female">女</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="出生日期" name="birthDate">
                    <a-date-picker 
                      v-model:value="customerFormState.birthDate" 
                      style="width: 100%"
                      placeholder="请选择出生日期"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="手机号码" name="phone" required>
                    <a-input v-model:value="customerFormState.phone" placeholder="请输入手机号码" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="邮箱地址" name="email">
                    <a-input v-model:value="customerFormState.email" placeholder="请输入邮箱地址" />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-form-item label="居住地址" name="address">
                <a-input v-model:value="customerFormState.address" placeholder="请输入居住地址" />
              </a-form-item>
            </a-tab-pane>

            <!-- 职业信息 -->
            <a-tab-pane key="occupation" tab="职业信息">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="职业类型" name="occupation">
                    <a-select v-model:value="customerFormState.occupation" placeholder="请选择职业类型">
                      <a-select-option value="employed">在职</a-select-option>
                      <a-select-option value="business">经商</a-select-option>
                      <a-select-option value="freelance">自由职业</a-select-option>
                      <a-select-option value="retired">退休</a-select-option>
                      <a-select-option value="unemployed">无业</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="工作单位" name="company">
                    <a-input v-model:value="customerFormState.company" placeholder="请输入工作单位" />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="职位" name="position">
                    <a-input v-model:value="customerFormState.position" placeholder="请输入职位" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="月收入" name="income">
                    <a-input-number 
                      v-model:value="customerFormState.income" 
                      :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                      style="width: 100%"
                      placeholder="请输入月收入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-form-item label="工作地址" name="companyAddress">
                <a-input v-model:value="customerFormState.companyAddress" placeholder="请输入工作地址" />
              </a-form-item>
            </a-tab-pane>

            <!-- 财务信息 -->
            <a-tab-pane key="financial" tab="财务信息">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="资产总额" name="totalAssets">
                    <a-input-number 
                      v-model:value="customerFormState.totalAssets" 
                      :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                      style="width: 100%"
                      placeholder="请输入资产总额"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="负债总额" name="totalDebt">
                    <a-input-number 
                      v-model:value="customerFormState.totalDebt" 
                      :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                      style="width: 100%"
                      placeholder="请输入负债总额"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="月收入" name="monthlyIncome">
                    <a-input-number 
                      v-model:value="customerFormState.monthlyIncome" 
                      :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                      style="width: 100%"
                      placeholder="请输入月收入"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="月支出" name="monthlyExpense">
                    <a-input-number 
                      v-model:value="customerFormState.monthlyExpense" 
                      :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                      style="width: 100%"
                      placeholder="请输入月支出"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-form-item label="资产说明" name="assetDescription">
                <a-textarea 
                  v-model:value="customerFormState.assetDescription" 
                  :rows="4"
                  placeholder="请输入资产说明（房产、车辆等）"
                />
              </a-form-item>
            </a-tab-pane>

            <!-- 其他信息 -->
            <a-tab-pane key="other" tab="其他信息">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="客户等级" name="level">
                    <a-select v-model:value="customerFormState.level" placeholder="请选择客户等级">
                      <a-select-option value="VIP">VIP客户</a-select-option>
                      <a-select-option value="A">A级客户</a-select-option>
                      <a-select-option value="B">B级客户</a-select-option>
                      <a-select-option value="C">C级客户</a-select-option>
                      <a-select-option value="D">D级客户</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="风险等级" name="riskLevel">
                    <a-select v-model:value="customerFormState.riskLevel" placeholder="请选择风险等级">
                      <a-select-option value="low">低风险</a-select-option>
                      <a-select-option value="medium">中风险</a-select-option>
                      <a-select-option value="high">高风险</a-select-option>
                      <a-select-option value="extreme">极高风险</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-form-item label="客户标签" name="tags">
                <a-select 
                  v-model:value="customerFormState.tags" 
                  mode="tags"
                  placeholder="请输入或选择标签"
                >
                  <a-select-option value="诚信">诚信</a-select-option>
                  <a-select-option value="配合">配合</a-select-option>
                  <a-select-option value="困难">困难</a-select-option>
                  <a-select-option value="抗拒">抗拒</a-select-option>
                  <a-select-option value="失联">失联</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="备注信息" name="remark">
                <a-textarea 
                  v-model:value="customerFormState.remark" 
                  :rows="4"
                  placeholder="请输入备注信息"
                />
              </a-form-item>
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </a-modal>

      <!-- 客户详情弹窗 -->
      <a-modal
        v-model:open="detailModalVisible"
        title="客户详情"
        width="900px"
        :footer="null"
      >
        <a-descriptions :column="2" bordered class="customer-detail">
          <a-descriptions-item label="客户姓名">{{ customerDetail?.name }}</a-descriptions-item>
          <a-descriptions-item label="身份证号">{{ customerDetail?.idCard }}</a-descriptions-item>
          <a-descriptions-item label="性别">{{ customerDetail?.gender === 'male' ? '男' : '女' }}</a-descriptions-item>
          <a-descriptions-item label="年龄">{{ customerDetail?.age }}岁</a-descriptions-item>
          <a-descriptions-item label="手机号码">{{ customerDetail?.phone }}</a-descriptions-item>
          <a-descriptions-item label="邮箱地址">{{ customerDetail?.email }}</a-descriptions-item>
          <a-descriptions-item label="居住地址" :span="2">{{ customerDetail?.address }}</a-descriptions-item>
          
          <a-descriptions-item label="职业类型">{{ getOccupationText(customerDetail?.occupation) }}</a-descriptions-item>
          <a-descriptions-item label="工作单位">{{ customerDetail?.company }}</a-descriptions-item>
          <a-descriptions-item label="职位">{{ customerDetail?.position }}</a-descriptions-item>
          <a-descriptions-item label="月收入">¥{{ customerDetail?.income?.toLocaleString() }}</a-descriptions-item>
          
          <a-descriptions-item label="客户等级">
            <a-tag :color="getLevelColor(customerDetail?.level)">
              {{ customerDetail?.level }}级客户
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getRiskColor(customerDetail?.riskLevel)">
              {{ getRiskText(customerDetail?.riskLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="客户状态">
            <a-badge :status="getStatusType(customerDetail?.status)" :text="getStatusText(customerDetail?.status)" />
          </a-descriptions-item>
          <a-descriptions-item label="信用评分">{{ customerDetail?.creditScore }}</a-descriptions-item>
          
          <a-descriptions-item label="客户标签" :span="2">
            <a-space size="small">
              <a-tag v-for="tag in customerDetail?.tags" :key="tag" size="small">{{ tag }}</a-tag>
            </a-space>
          </a-descriptions-item>
          
          <a-descriptions-item label="创建时间">{{ customerDetail?.createTime }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ customerDetail?.updateTime }}</a-descriptions-item>
        </a-descriptions>

        <!-- 360度画像 -->
        <div class="customer-portrait">
          <h4>客户360度画像</h4>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card size="small">
                <a-statistic 
                  title="案件数量" 
                  :value="customerDetail?.caseCount || 0"
                  :value-style="{ fontSize: '16px' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small">
                <a-statistic 
                  title="债务总额" 
                  :value="customerDetail?.totalDebtAmount || 0"
                  :precision="2"
                  prefix="¥"
                  :value-style="{ fontSize: '16px' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small">
                <a-statistic 
                  title="还款总额" 
                  :value="customerDetail?.totalPayment || 0"
                  :precision="2"
                  prefix="¥"
                  :value-style="{ fontSize: '16px' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small">
                <a-statistic 
                  title="还款率" 
                  :value="customerDetail?.paymentRate || 0"
                  suffix="%"
                  :value-style="{ fontSize: '16px' }"
                />
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-modal>

      <!-- 导入客户弹窗 -->
      <a-modal
        v-model:open="showImportModal"
        title="导入客户"
        width="600px"
        @ok="handleImport"
      >
        <a-upload-dragger
          v-model:file-list="importFileList"
          :before-upload="beforeImportUpload"
          accept=".xlsx,.xls,.csv"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持 .xlsx, .xls, .csv 格式的文件
          </p>
        </a-upload-dragger>
        
        <a-alert
          message="导入说明"
          description="请按照模板格式准备数据，系统将自动验证数据完整性。"
          type="info"
          show-icon
          style="margin-top: 16px"
        />
        
        <div style="margin-top: 16px">
          <a href="#" @click="downloadTemplate">下载导入模板</a>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { 
  PlusOutlined, 
  SearchOutlined, 
  ReloadOutlined,
  UpOutlined,
  DownOutlined,
  UploadOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 状态管理
const loading = ref(false)
const selectedRowKeys = ref([])
const total = ref(236)
const showAdvanced = ref(false)
const customerModalVisible = ref(false)
const detailModalVisible = ref(false)
const showImportModal = ref(false)
const isEdit = ref(false)
const activeTab = ref('basic')
const importFileList = ref([])

// 搜索参数
const searchParams = reactive({
  name: '',
  idCard: '',
  phone: '',
  level: undefined,
  riskLevel: undefined,
  status: undefined,
  createDateRange: [],
  occupation: undefined,
  incomeRange: undefined,
  region: [],
  ageRange: [18, 65]
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 236
})

// 客户表单
const customerFormState = reactive({
  name: '',
  idCard: '',
  gender: 'male',
  birthDate: undefined,
  phone: '',
  email: '',
  address: '',
  occupation: undefined,
  company: '',
  position: '',
  income: undefined,
  companyAddress: '',
  totalAssets: undefined,
  totalDebt: undefined,
  monthlyIncome: undefined,
  monthlyExpense: undefined,
  assetDescription: '',
  level: 'C',
  riskLevel: 'medium',
  tags: [],
  remark: ''
})

// 客户详情
const customerDetail = ref(null)

// 地区选项
const regionOptions = [
  {
    value: 'beijing',
    label: '北京',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海',
    children: [
      { value: 'pudong', label: '浦东新区' },
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' }
    ]
  }
]

// 表格列配置
const columns = [
  {
    title: '客户姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    fixed: 'left'
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    key: 'idCard',
    width: 180
  },
  {
    title: '手机号码',
    dataIndex: 'phone',
    key: 'phone',
    width: 120
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 80,
    sorter: true
  },
  {
    title: '客户等级',
    dataIndex: 'level',
    key: 'level',
    width: 100
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '信用评分',
    dataIndex: 'creditScore',
    key: 'creditScore',
    width: 100,
    sorter: true
  },
  {
    title: '客户状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '案件数量',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 100,
    sorter: true
  },
  {
    title: '债务总额',
    dataIndex: 'totalDebt',
    key: 'totalDebt',
    width: 120,
    sorter: true,
    customRender: ({ text }) => `¥${text.toLocaleString()}`
  },
  {
    title: '标签',
    dataIndex: 'tags',
    key: 'tags',
    width: 200
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 模拟数据
const dataSource = ref([
  {
    key: '1',
    name: '张三',
    idCard: '110101199001011234',
    phone: '13812345678',
    age: 34,
    level: 'A',
    riskLevel: 'medium',
    creditScore: 680,
    status: 'normal',
    caseCount: 3,
    totalDebt: 150000,
    tags: ['诚信', '配合'],
    createTime: '2024-01-15 10:30'
  },
  {
    key: '2',
    name: '李四',
    idCard: '110101198505052345',
    phone: '13923456789',
    age: 39,
    level: 'B',
    riskLevel: 'high',
    creditScore: 580,
    status: 'attention',
    caseCount: 5,
    totalDebt: 280000,
    tags: ['困难', '失联'],
    createTime: '2024-01-10 14:20'
  },
  {
    key: '3',
    name: '王五',
    idCard: '110101197808083456',
    phone: '13634567890',
    age: 46,
    level: 'VIP',
    riskLevel: 'low',
    creditScore: 750,
    status: 'normal',
    caseCount: 1,
    totalDebt: 50000,
    tags: ['诚信', 'VIP'],
    createTime: '2024-01-08 09:15'
  }
])

// 方法
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询成功')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      if (key === 'ageRange') {
        searchParams[key] = [18, 65]
      } else {
        searchParams[key] = []
      }
    } else {
      searchParams[key] = undefined
    }
  })
}

const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const viewCustomerDetail = (record) => {
  customerDetail.value = {
    ...record,
    gender: 'male',
    email: '<EMAIL>',
    address: '北京市朝阳区某某街道某某小区',
    occupation: 'employed',
    company: '某某科技有限公司',
    position: '技术经理',
    income: 15000,
    caseCount: 3,
    totalDebtAmount: 150000,
    totalPayment: 50000,
    paymentRate: 33.3,
    updateTime: '2024-01-20 16:30'
  }
  detailModalVisible.value = true
}

const editCustomer = (record) => {
  isEdit.value = true
  // 填充表单数据
  Object.keys(customerFormState).forEach(key => {
    customerFormState[key] = record[key] || customerFormState[key]
  })
  customerModalVisible.value = true
}

const handleSaveCustomer = () => {
  // 验证必填字段
  if (!customerFormState.name || !customerFormState.idCard || !customerFormState.phone) {
    message.warning('请填写必填字段')
    return
  }
  
  message.loading('正在保存...')
  setTimeout(() => {
    message.success(isEdit.value ? '客户信息更新成功' : '客户创建成功')
    customerModalVisible.value = false
    handleSearch()
  }, 1500)
}

const handleMoreAction = (action, record) => {
  switch (action) {
    case 'contact':
      message.info('管理联系人')
      break
    case 'assessment':
      message.info('信用评估')
      break
    case 'tag':
      message.info('设置标签')
      break
    case 'blacklist':
      message.confirm({
        title: '确认加入黑名单？',
        content: '加入黑名单后，该客户将被限制相关操作。',
        onOk() {
          message.success('已加入黑名单')
        }
      })
      break
    case 'delete':
      message.confirm({
        title: '确认删除客户？',
        content: '删除后将无法恢复，请谨慎操作。',
        onOk() {
          message.success('客户删除成功')
        }
      })
      break
  }
}

const handleBatchUpdate = () => {
  message.info(`批量更新 ${selectedRowKeys.value.length} 个客户`)
}

const handleBatchTag = () => {
  message.info(`批量设置标签 ${selectedRowKeys.value.length} 个客户`)
}

const handleBatchExport = () => {
  message.info(`导出 ${selectedRowKeys.value.length} 个客户`)
}

const handleExportAll = () => {
  message.info('导出全部客户')
}

const beforeImportUpload = (file) => {
  importFileList.value = [file]
  return false
}

const handleImport = () => {
  if (!importFileList.value.length) {
    message.warning('请选择要导入的文件')
    return
  }
  
  message.loading('正在导入...')
  setTimeout(() => {
    message.success('导入成功')
    showImportModal.value = false
    importFileList.value = []
    handleSearch()
  }, 2000)
}

const downloadTemplate = () => {
  message.info('下载导入模板')
}

// 工具方法
const getLevelColor = (level) => {
  const colors = {
    'VIP': 'gold',
    'A': 'blue',
    'B': 'green',
    'C': 'default',
    'D': 'gray'
  }
  return colors[level] || 'default'
}

const getRiskColor = (risk) => {
  const colors = {
    'low': 'green',
    'medium': 'orange',
    'high': 'red',
    'extreme': 'purple'
  }
  return colors[risk] || 'default'
}

const getRiskText = (risk) => {
  const texts = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'extreme': '极高风险'
  }
  return texts[risk] || risk
}

const getStatusType = (status) => {
  const types = {
    'normal': 'success',
    'blacklist': 'error',
    'attention': 'warning',
    'lost': 'default'
  }
  return types[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    'normal': '正常',
    'blacklist': '黑名单',
    'attention': '重点关注',
    'lost': '失联'
  }
  return texts[status] || status
}

const getOccupationText = (occupation) => {
  const texts = {
    'employed': '在职',
    'business': '经商',
    'freelance': '自由职业',
    'retired': '退休',
    'unemployed': '无业'
  }
  return texts[occupation] || occupation
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  
  .search-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}

.table-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.customer-detail {
  margin-bottom: 24px;
}

.customer-portrait {
  margin-top: 24px;
  
  h4 {
    margin-bottom: 16px;
    font-weight: 500;
  }
}
</style>