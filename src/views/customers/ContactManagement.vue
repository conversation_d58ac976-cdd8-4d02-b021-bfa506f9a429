<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>联系人管理</h2>
      </div>

      <!-- 搜索区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input 
                  v-model:value="searchParams.customerName" 
                  placeholder="请输入客户姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="联系人">
                <a-input 
                  v-model:value="searchParams.contactName" 
                  placeholder="请输入联系人姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="联系电话">
                <a-input 
                  v-model:value="searchParams.phone" 
                  placeholder="请输入联系电话"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <div class="search-actions">
                <div class="search-buttons">
                  <a-space>
                    <a-button type="primary" html-type="submit">
                      <search-outlined />
                      查询
                    </a-button>
                    <a-button @click="handleReset">
                      <reload-outlined />
                      重置
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 客户选择区域 -->
      <a-row :gutter="16">
        <!-- 左侧客户列表 -->
        <a-col :xs="24" :md="8">
          <a-card title="客户列表" class="customer-list-card">
            <template #extra>
              <a-input-search
                v-model:value="customerSearchText"
                placeholder="搜索客户"
                size="small"
                style="width: 150px"
                @search="handleCustomerSearch"
              />
            </template>
            
            <a-list
              :data-source="customerList"
              :loading="customerLoading"
              size="small"
              :pagination="{
                current: customerPagination.current,
                pageSize: customerPagination.pageSize,
                total: customerPagination.total,
                size: 'small',
                onChange: handleCustomerPageChange
              }"
            >
              <template #renderItem="{ item }">
                <a-list-item
                  :class="['customer-item', { active: selectedCustomer?.key === item.key }]"
                  @click="selectCustomer(item)"
                >
                  <a-list-item-meta>
                    <template #title>
                      <div class="customer-title">
                        <span>{{ item.name }}</span>
                        <a-tag size="small" :color="getLevelColor(item.level)">
                          {{ item.level }}
                        </a-tag>
                      </div>
                    </template>
                    <template #description>
                      <div class="customer-info">
                        <div>身份证：{{ item.idCard }}</div>
                        <div>电话：{{ item.phone }}</div>
                        <div>
                          联系人：
                          <a-badge :count="item.contactCount" :number-style="{ backgroundColor: '#52c41a' }" />
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <!-- 右侧联系人管理 -->
        <a-col :xs="24" :md="16">
          <a-card 
            :title="selectedCustomer ? `${selectedCustomer.name} 的联系人` : '请选择客户'" 
            class="contact-management-card"
          >
            <template #extra v-if="selectedCustomer">
              <a-button type="primary" @click="showAddContactModal = true">
                <plus-outlined />
                添加联系人
              </a-button>
            </template>

            <div v-if="selectedCustomer">
              <!-- 联系方式统计 -->
              <a-row :gutter="16" class="contact-stats">
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="主要联系方式"
                      :value="contactStats.primary"
                      :value-style="{ fontSize: '20px' }"
                    />
                  </a-card>
                </a-col>
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="备用联系方式"
                      :value="contactStats.backup"
                      :value-style="{ fontSize: '20px' }"
                    />
                  </a-card>
                </a-col>
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="紧急联系人"
                      :value="contactStats.emergency"
                      :value-style="{ fontSize: '20px' }"
                    />
                  </a-card>
                </a-col>
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="有效联系率"
                      :value="contactStats.validRate"
                      suffix="%"
                      :value-style="{ fontSize: '20px' }"
                    />
                  </a-card>
                </a-col>
              </a-row>

              <!-- 联系方式标签页 -->
              <a-tabs v-model:activeKey="activeTab" class="contact-tabs">
                <a-tab-pane key="phone" tab="电话号码">
                  <a-list
                    :data-source="phoneContacts"
                    :loading="contactLoading"
                    item-layout="horizontal"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: getContactTypeColor(item.type) }">
                              <phone-outlined />
                            </a-avatar>
                          </template>
                          <template #title>
                            <div class="contact-title">
                              <span>{{ item.phone }}</span>
                              <a-tag size="small" :color="getContactTypeColor(item.type)">
                                {{ getContactTypeText(item.type) }}
                              </a-tag>
                              <a-tag v-if="item.isPrimary" size="small" color="blue">主要</a-tag>
                              <a-tag v-if="!item.isValid" size="small" color="red">无效</a-tag>
                            </div>
                          </template>
                          <template #description>
                            <div class="contact-desc">
                              <span v-if="item.relationship">关系：{{ item.relationship }}</span>
                              <span v-if="item.lastContactTime">最后联系：{{ item.lastContactTime }}</span>
                              <span>成功率：{{ item.successRate }}%</span>
                            </div>
                          </template>
                        </a-list-item-meta>
                        <template #actions>
                          <a @click="testContact(item, 'phone')">测试</a>
                          <a @click="editContact(item)">编辑</a>
                          <a @click="deleteContact(item)">删除</a>
                        </template>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>

                <a-tab-pane key="address" tab="地址信息">
                  <a-list
                    :data-source="addressContacts"
                    :loading="contactLoading"
                    item-layout="vertical"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: getAddressTypeColor(item.type) }">
                              <home-outlined />
                            </a-avatar>
                          </template>
                          <template #title>
                            <div class="contact-title">
                              <span>{{ item.address }}</span>
                              <a-tag size="small" :color="getAddressTypeColor(item.type)">
                                {{ getAddressTypeText(item.type) }}
                              </a-tag>
                              <a-tag v-if="item.isPrimary" size="small" color="blue">主要</a-tag>
                              <a-tag v-if="!item.isValid" size="small" color="red">无效</a-tag>
                            </div>
                          </template>
                          <template #description>
                            <div class="contact-desc">
                              <span v-if="item.detailAddress">详细地址：{{ item.detailAddress }}</span>
                              <span v-if="item.lastVerifyTime">最后验证：{{ item.lastVerifyTime }}</span>
                            </div>
                          </template>
                        </a-list-item-meta>
                        <template #actions>
                          <a @click="showMap(item)">查看地图</a>
                          <a @click="editContact(item)">编辑</a>
                          <a @click="deleteContact(item)">删除</a>
                        </template>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>

                <a-tab-pane key="email" tab="电子邮箱">
                  <a-list
                    :data-source="emailContacts"
                    :loading="contactLoading"
                    item-layout="horizontal"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: '#1890ff' }">
                              <mail-outlined />
                            </a-avatar>
                          </template>
                          <template #title>
                            <div class="contact-title">
                              <span>{{ item.email }}</span>
                              <a-tag size="small" :color="item.type === 'work' ? 'blue' : 'green'">
                                {{ item.type === 'work' ? '工作邮箱' : '个人邮箱' }}
                              </a-tag>
                              <a-tag v-if="item.isPrimary" size="small" color="blue">主要</a-tag>
                              <a-tag v-if="!item.isValid" size="small" color="red">无效</a-tag>
                            </div>
                          </template>
                          <template #description>
                            <div class="contact-desc">
                              <span v-if="item.lastSendTime">最后发送：{{ item.lastSendTime }}</span>
                              <span>送达率：{{ item.deliveryRate }}%</span>
                            </div>
                          </template>
                        </a-list-item-meta>
                        <template #actions>
                          <a @click="testContact(item, 'email')">测试</a>
                          <a @click="editContact(item)">编辑</a>
                          <a @click="deleteContact(item)">删除</a>
                        </template>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>

                <a-tab-pane key="emergency" tab="紧急联系人">
                  <a-list
                    :data-source="emergencyContacts"
                    :loading="contactLoading"
                    item-layout="horizontal"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: '#ff4d4f' }">
                              <user-outlined />
                            </a-avatar>
                          </template>
                          <template #title>
                            <div class="contact-title">
                              <span>{{ item.name }}</span>
                              <a-tag size="small" color="orange">{{ item.relationship }}</a-tag>
                              <a-tag v-if="item.isPrimary" size="small" color="blue">主要</a-tag>
                            </div>
                          </template>
                          <template #description>
                            <div class="emergency-contact-info">
                              <div>电话：{{ item.phone }}</div>
                              <div v-if="item.workUnit">工作单位：{{ item.workUnit }}</div>
                              <div v-if="item.address">地址：{{ item.address }}</div>
                              <div v-if="item.remark">备注：{{ item.remark }}</div>
                            </div>
                          </template>
                        </a-list-item-meta>
                        <template #actions>
                          <a @click="callEmergencyContact(item)">拨打</a>
                          <a @click="editContact(item)">编辑</a>
                          <a @click="deleteContact(item)">删除</a>
                        </template>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>
              </a-tabs>

              <!-- 联系优先级推荐 -->
              <a-card title="智能联系推荐" size="small" class="contact-recommendation">
                <a-alert
                  message="推荐联系策略"
                  :description="contactRecommendation.strategy"
                  type="info"
                  show-icon
                />
                <div class="best-contact-time">
                  <h4>最佳联系时间</h4>
                  <a-timeline mode="left">
                    <a-timeline-item v-for="time in contactRecommendation.bestTimes" :key="time.period">
                      <template #dot>
                        <clock-circle-outlined style="font-size: 16px;" />
                      </template>
                      <div>
                        <strong>{{ time.period }}</strong>
                        <div>成功率：{{ time.successRate }}%</div>
                        <div>推荐方式：{{ time.recommendMethod }}</div>
                      </div>
                    </a-timeline-item>
                  </a-timeline>
                </div>
              </a-card>
            </div>

            <!-- 未选择客户提示 -->
            <div v-else class="empty-state">
              <a-empty description="请从左侧选择一个客户查看联系人信息" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 添加/编辑联系人弹窗 -->
      <a-modal
        v-model:open="contactModalVisible"
        :title="isEditContact ? '编辑联系人' : '添加联系人'"
        width="600px"
        @ok="handleSaveContact"
      >
        <a-form
          :model="contactForm"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-item label="联系类型" required>
            <a-radio-group v-model:value="contactForm.category">
              <a-radio-button value="phone">电话</a-radio-button>
              <a-radio-button value="address">地址</a-radio-button>
              <a-radio-button value="email">邮箱</a-radio-button>
              <a-radio-button value="emergency">紧急联系人</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <!-- 电话类型表单 -->
          <template v-if="contactForm.category === 'phone'">
            <a-form-item label="电话号码" required>
              <a-input v-model:value="contactForm.phone" placeholder="请输入电话号码" />
            </a-form-item>
            <a-form-item label="号码类型" required>
              <a-select v-model:value="contactForm.phoneType" placeholder="请选择号码类型">
                <a-select-option value="mobile">手机</a-select-option>
                <a-select-option value="home">家庭电话</a-select-option>
                <a-select-option value="work">工作电话</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <!-- 地址类型表单 -->
          <template v-if="contactForm.category === 'address'">
            <a-form-item label="地址类型" required>
              <a-select v-model:value="contactForm.addressType" placeholder="请选择地址类型">
                <a-select-option value="home">居住地址</a-select-option>
                <a-select-option value="work">工作地址</a-select-option>
                <a-select-option value="registered">户籍地址</a-select-option>
                <a-select-option value="other">其他地址</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="省市区" required>
              <a-cascader
                v-model:value="contactForm.region"
                :options="regionOptions"
                placeholder="请选择省市区"
              />
            </a-form-item>
            <a-form-item label="详细地址" required>
              <a-input v-model:value="contactForm.detailAddress" placeholder="请输入详细地址" />
            </a-form-item>
          </template>

          <!-- 邮箱类型表单 -->
          <template v-if="contactForm.category === 'email'">
            <a-form-item label="邮箱地址" required>
              <a-input v-model:value="contactForm.email" placeholder="请输入邮箱地址" />
            </a-form-item>
            <a-form-item label="邮箱类型" required>
              <a-select v-model:value="contactForm.emailType" placeholder="请选择邮箱类型">
                <a-select-option value="personal">个人邮箱</a-select-option>
                <a-select-option value="work">工作邮箱</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <!-- 紧急联系人表单 -->
          <template v-if="contactForm.category === 'emergency'">
            <a-form-item label="联系人姓名" required>
              <a-input v-model:value="contactForm.name" placeholder="请输入联系人姓名" />
            </a-form-item>
            <a-form-item label="关系" required>
              <a-select v-model:value="contactForm.relationship" placeholder="请选择关系">
                <a-select-option value="spouse">配偶</a-select-option>
                <a-select-option value="parent">父母</a-select-option>
                <a-select-option value="child">子女</a-select-option>
                <a-select-option value="sibling">兄弟姐妹</a-select-option>
                <a-select-option value="friend">朋友</a-select-option>
                <a-select-option value="colleague">同事</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="联系电话" required>
              <a-input v-model:value="contactForm.emergencyPhone" placeholder="请输入联系电话" />
            </a-form-item>
            <a-form-item label="工作单位">
              <a-input v-model:value="contactForm.workUnit" placeholder="请输入工作单位" />
            </a-form-item>
            <a-form-item label="联系地址">
              <a-input v-model:value="contactForm.emergencyAddress" placeholder="请输入联系地址" />
            </a-form-item>
          </template>

          <a-form-item label="设为主要">
            <a-switch v-model:checked="contactForm.isPrimary" />
          </a-form-item>

          <a-form-item label="备注">
            <a-textarea 
              v-model:value="contactForm.remark" 
              :rows="3"
              placeholder="请输入备注信息"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { 
  SearchOutlined, 
  ReloadOutlined,
  PlusOutlined,
  PhoneOutlined,
  HomeOutlined,
  MailOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 状态管理
const loading = ref(false)
const customerLoading = ref(false)
const contactLoading = ref(false)
const selectedCustomer = ref(null)
const activeTab = ref('phone')
const contactModalVisible = ref(false)
const showAddContactModal = ref(false)
const isEditContact = ref(false)
const customerSearchText = ref('')

// 搜索参数
const searchParams = reactive({
  customerName: '',
  contactName: '',
  phone: ''
})

// 客户分页
const customerPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50
})

// 客户列表
const customerList = ref([
  {
    key: '1',
    name: '张三',
    idCard: '110101199001011234',
    phone: '13812345678',
    level: 'A',
    contactCount: 8
  },
  {
    key: '2',
    name: '李四',
    idCard: '110101198505052345',
    phone: '13923456789',
    level: 'B',
    contactCount: 5
  },
  {
    key: '3',
    name: '王五',
    idCard: '110101197808083456',
    phone: '13634567890',
    level: 'VIP',
    contactCount: 12
  }
])

// 联系方式统计
const contactStats = reactive({
  primary: 3,
  backup: 5,
  emergency: 2,
  validRate: 85.5
})

// 电话联系方式
const phoneContacts = ref([
  {
    id: 1,
    phone: '13812345678',
    type: 'mobile',
    isPrimary: true,
    isValid: true,
    relationship: '本人',
    lastContactTime: '2024-01-18 14:30',
    successRate: 85
  },
  {
    id: 2,
    phone: '010-12345678',
    type: 'home',
    isPrimary: false,
    isValid: true,
    lastContactTime: '2024-01-15 10:20',
    successRate: 60
  },
  {
    id: 3,
    phone: '13998765432',
    type: 'work',
    isPrimary: false,
    isValid: false,
    lastContactTime: '2024-01-10 09:15',
    successRate: 0
  }
])

// 地址联系方式
const addressContacts = ref([
  {
    id: 4,
    address: '北京市朝阳区某某街道',
    detailAddress: '某某小区1号楼3单元502室',
    type: 'home',
    isPrimary: true,
    isValid: true,
    lastVerifyTime: '2024-01-10'
  },
  {
    id: 5,
    address: '北京市海淀区某某路',
    detailAddress: '某某大厦15层',
    type: 'work',
    isPrimary: false,
    isValid: true,
    lastVerifyTime: '2024-01-05'
  }
])

// 邮箱联系方式
const emailContacts = ref([
  {
    id: 6,
    email: '<EMAIL>',
    type: 'personal',
    isPrimary: true,
    isValid: true,
    lastSendTime: '2024-01-16 11:30',
    deliveryRate: 95
  },
  {
    id: 7,
    email: '<EMAIL>',
    type: 'work',
    isPrimary: false,
    isValid: true,
    lastSendTime: '2024-01-14 09:20',
    deliveryRate: 98
  }
])

// 紧急联系人
const emergencyContacts = ref([
  {
    id: 8,
    name: '张太太',
    relationship: '配偶',
    phone: '13687654321',
    workUnit: '某某公司',
    address: '北京市朝阳区',
    isPrimary: true,
    remark: '上午10-11点较容易联系'
  },
  {
    id: 9,
    name: '张先生',
    relationship: '父亲',
    phone: '13576543210',
    address: '北京市东城区',
    isPrimary: false,
    remark: '退休在家'
  }
])

// 联系推荐
const contactRecommendation = reactive({
  strategy: '根据历史数据分析，建议优先通过手机联系本人，成功率最高。如无法联系，可尝试工作电话或联系配偶。',
  bestTimes: [
    {
      period: '上午 10:00-11:00',
      successRate: 85,
      recommendMethod: '手机通话'
    },
    {
      period: '下午 14:00-15:00',
      successRate: 78,
      recommendMethod: '工作电话'
    },
    {
      period: '晚上 19:00-20:00',
      successRate: 92,
      recommendMethod: '手机通话'
    }
  ]
})

// 联系人表单
const contactForm = reactive({
  category: 'phone',
  phone: '',
  phoneType: 'mobile',
  addressType: 'home',
  region: [],
  detailAddress: '',
  email: '',
  emailType: 'personal',
  name: '',
  relationship: undefined,
  emergencyPhone: '',
  workUnit: '',
  emergencyAddress: '',
  isPrimary: false,
  remark: ''
})

// 地区选项
const regionOptions = [
  {
    value: 'beijing',
    label: '北京',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海',
    children: [
      { value: 'pudong', label: '浦东新区' },
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' }
    ]
  }
]

// 监听添加联系人弹窗
watch(showAddContactModal, (val) => {
  if (val) {
    isEditContact.value = false
    contactModalVisible.value = true
    showAddContactModal.value = false
    // 重置表单
    resetContactForm()
  }
})

// 方法
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询成功')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
}

const handleCustomerSearch = () => {
  customerLoading.value = true
  setTimeout(() => {
    customerLoading.value = false
  }, 1000)
}

const handleCustomerPageChange = (page) => {
  customerPagination.current = page
  handleCustomerSearch()
}

const selectCustomer = (customer) => {
  selectedCustomer.value = customer
  // 加载该客户的联系方式
  loadCustomerContacts(customer.key)
}

const loadCustomerContacts = (customerId) => {
  contactLoading.value = true
  setTimeout(() => {
    contactLoading.value = false
  }, 1000)
}

const testContact = (contact, type) => {
  if (type === 'phone') {
    message.info(`正在测试电话 ${contact.phone}...`)
  } else if (type === 'email') {
    message.info(`正在发送测试邮件到 ${contact.email}...`)
  }
}

const editContact = (contact) => {
  isEditContact.value = true
  // 根据不同类型填充表单
  if (contact.phone) {
    contactForm.category = 'phone'
    contactForm.phone = contact.phone
    contactForm.phoneType = contact.type
  } else if (contact.email) {
    contactForm.category = 'email'
    contactForm.email = contact.email
    contactForm.emailType = contact.type
  } else if (contact.address) {
    contactForm.category = 'address'
    contactForm.detailAddress = contact.detailAddress
    contactForm.addressType = contact.type
  } else if (contact.relationship) {
    contactForm.category = 'emergency'
    contactForm.name = contact.name
    contactForm.relationship = contact.relationship
    contactForm.emergencyPhone = contact.phone
    contactForm.workUnit = contact.workUnit
    contactForm.emergencyAddress = contact.address
  }
  contactForm.isPrimary = contact.isPrimary
  contactForm.remark = contact.remark || ''
  
  contactModalVisible.value = true
}

const deleteContact = (contact) => {
  message.confirm({
    title: '确认删除',
    content: '确定要删除这个联系方式吗？',
    onOk() {
      message.success('删除成功')
    }
  })
}

const showMap = (address) => {
  message.info('打开地图查看地址')
}

const callEmergencyContact = (contact) => {
  message.info(`正在拨打 ${contact.name} 的电话 ${contact.phone}`)
}

const handleSaveContact = () => {
  // 验证必填字段
  if (contactForm.category === 'phone' && !contactForm.phone) {
    message.warning('请输入电话号码')
    return
  }
  if (contactForm.category === 'address' && (!contactForm.region.length || !contactForm.detailAddress)) {
    message.warning('请填写完整的地址信息')
    return
  }
  if (contactForm.category === 'email' && !contactForm.email) {
    message.warning('请输入邮箱地址')
    return
  }
  if (contactForm.category === 'emergency' && (!contactForm.name || !contactForm.relationship || !contactForm.emergencyPhone)) {
    message.warning('请填写紧急联系人必填信息')
    return
  }
  
  message.loading('正在保存...')
  setTimeout(() => {
    message.success(isEditContact.value ? '联系方式更新成功' : '联系方式添加成功')
    contactModalVisible.value = false
    loadCustomerContacts(selectedCustomer.value.key)
  }, 1500)
}

const resetContactForm = () => {
  Object.keys(contactForm).forEach(key => {
    if (key === 'category') {
      contactForm[key] = 'phone'
    } else if (key === 'phoneType') {
      contactForm[key] = 'mobile'
    } else if (key === 'addressType') {
      contactForm[key] = 'home'
    } else if (key === 'emailType') {
      contactForm[key] = 'personal'
    } else if (key === 'isPrimary') {
      contactForm[key] = false
    } else if (key === 'region') {
      contactForm[key] = []
    } else {
      contactForm[key] = ''
    }
  })
}

// 工具方法
const getLevelColor = (level) => {
  const colors = {
    'VIP': 'gold',
    'A': 'blue',
    'B': 'green',
    'C': 'default',
    'D': 'gray'
  }
  return colors[level] || 'default'
}

const getContactTypeColor = (type) => {
  const colors = {
    'mobile': 'green',
    'home': 'blue',
    'work': 'orange',
    'other': 'default'
  }
  return colors[type] || 'default'
}

const getContactTypeText = (type) => {
  const texts = {
    'mobile': '手机',
    'home': '家庭电话',
    'work': '工作电话',
    'other': '其他'
  }
  return texts[type] || type
}

const getAddressTypeColor = (type) => {
  const colors = {
    'home': 'green',
    'work': 'blue',
    'registered': 'orange',
    'other': 'default'
  }
  return colors[type] || 'default'
}

const getAddressTypeText = (type) => {
  const texts = {
    'home': '居住地址',
    'work': '工作地址',
    'registered': '户籍地址',
    'other': '其他地址'
  }
  return texts[type] || type
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  
  .search-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}

.customer-list-card {
  height: calc(100vh - 250px);
  
  :deep(.ant-card-body) {
    height: calc(100% - 57px);
    overflow-y: auto;
    padding: 0;
  }
  
  .customer-item {
    cursor: pointer;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s;
    
    &:hover {
      background: #fafafa;
    }
    
    &.active {
      background: #e6f7ff;
      border-left: 3px solid #1890ff;
    }
    
    .customer-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .customer-info {
      font-size: 12px;
      color: #666;
      
      div {
        margin-top: 2px;
      }
    }
  }
}

.contact-management-card {
  height: calc(100vh - 250px);
  
  :deep(.ant-card-body) {
    height: calc(100% - 57px);
    overflow-y: auto;
  }
}

.contact-stats {
  margin-bottom: 16px;
}

.contact-tabs {
  margin-top: 16px;
  
  .contact-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .contact-desc {
    font-size: 12px;
    color: #666;
    display: flex;
    gap: 16px;
  }
  
  .emergency-contact-info {
    font-size: 12px;
    color: #666;
    
    div {
      margin-top: 4px;
    }
  }
}

.contact-recommendation {
  margin-top: 16px;
  
  .best-contact-time {
    margin-top: 16px;
    
    h4 {
      margin-bottom: 12px;
      font-weight: 500;
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}
</style>
