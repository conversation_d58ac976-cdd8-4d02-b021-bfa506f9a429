<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>案件分配</h2>
      </div>

      <!-- 搜索区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input 
                  v-model:value="searchParams.caseNo" 
                  placeholder="请输入案件编号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="债务人">
                <a-input 
                  v-model:value="searchParams.debtor" 
                  placeholder="请输入债务人姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="优先级">
                <a-select 
                  v-model:value="searchParams.priority"
                  placeholder="请选择优先级"
                  allow-clear
                >
                  <a-select-option value="紧急">紧急</a-select-option>
                  <a-select-option value="高">高</a-select-option>
                  <a-select-option value="中">中</a-select-option>
                  <a-select-option value="低">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="地区">
                <a-select 
                  v-model:value="searchParams.region"
                  placeholder="请选择地区"
                  allow-clear
                >
                  <a-select-option value="北京">北京</a-select-option>
                  <a-select-option value="上海">上海</a-select-option>
                  <a-select-option value="广州">广州</a-select-option>
                  <a-select-option value="深圳">深圳</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="债务金额">
                <a-input-number 
                  v-model:value="searchParams.minAmount"
                  placeholder="最小金额"
                  :min="0"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="至">
                <a-input-number 
                  v-model:value="searchParams.maxAmount"
                  placeholder="最大金额"
                  :min="0"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <div class="search-actions">
                <div class="search-buttons">
                  <a-space>
                    <a-button type="primary" html-type="submit">
                      <search-outlined />
                      查询
                    </a-button>
                    <a-button @click="handleReset">
                      <reload-outlined />
                      重置
                    </a-button>
                  </a-space>
                </div>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="executeAutoAllocation" type="primary">
                      <thunderbolt-outlined />
                      执行自动分配
                    </a-button>
                    <a-button @click="exportData">
                      <download-outlined />
                      导出数据
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
      
      <!-- 分配规则设置 -->
      <a-card title="自动分配规则" class="rule-card">
        <template #extra>
          <a-switch 
            v-model:checked="autoAllocationEnabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </template>
        
        <a-form :model="allocationRules" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="分配策略">
                <a-select v-model:value="allocationRules.strategy">
                  <a-select-option value="round-robin">轮询分配</a-select-option>
                  <a-select-option value="weighted">加权分配</a-select-option>
                  <a-select-option value="skill-based">技能匹配</a-select-option>
                  <a-select-option value="performance">绩效优先</a-select-option>
                  <a-select-option value="location">地域就近</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            
            <a-col :span="8">
              <a-form-item label="工作量平衡">
                <a-input-number 
                  v-model:value="allocationRules.maxCases"
                  :min="1"
                  :max="200"
                  style="width: 100%"
                >
                  <template #addonAfter>件/人</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            
            <a-col :span="8">
              <a-form-item label="优先级权重">
                <a-slider 
                  v-model:value="allocationRules.priorityWeight"
                  :min="0"
                  :max="100"
                  :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="分配条件">
            <a-space direction="vertical" style="width: 100%">
              <a-checkbox-group v-model:value="allocationRules.conditions">
                <a-checkbox value="amount">按债务金额分配</a-checkbox>
                <a-checkbox value="overdue">按逾期天数分配</a-checkbox>
                <a-checkbox value="region">按地区分配</a-checkbox>
                <a-checkbox value="type">按案件类型分配</a-checkbox>
              </a-checkbox-group>
            </a-space>
          </a-form-item>
          
          <a-form-item>
            <a-button type="primary" @click="saveAllocationRules">
              保存规则
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
      
      <!-- 待分配案件 -->
      <a-card title="待分配案件" class="allocation-card">
        <template #extra>
          <span>共 {{ unallocatedCases.length }} 件待分配</span>
        </template>
        
        <a-table
          :columns="caseColumns"
          :data-source="unallocatedCases"
          :row-selection="{
            selectedRowKeys: selectedCaseKeys,
            onChange: onCaseSelectChange
          }"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ record.priority }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-card>
      
      <!-- 催收员工作量 -->
      <a-card title="催收员工作量统计" class="workload-card">
        <a-row :gutter="16">
          <a-col 
            v-for="collector in collectors" 
            :key="collector.id"
            :xs="24" 
            :sm="12" 
            :md="8" 
            :lg="6"
          >
            <div 
              class="collector-card"
              :class="{ selected: selectedCollectorId === collector.id }"
              @click="selectCollector(collector)"
            >
              <div class="collector-info">
                <a-avatar :size="48">{{ collector.name[0] }}</a-avatar>
                <div class="collector-details">
                  <h4>{{ collector.name }}</h4>
                  <p>{{ collector.team }}</p>
                </div>
              </div>
              
              <div class="collector-stats">
                <a-row>
                  <a-col :span="12">
                    <a-statistic 
                      title="当前案件" 
                      :value="collector.currentCases"
                      :value-style="{ fontSize: '16px' }"
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-statistic 
                      title="成功率" 
                      :value="collector.successRate"
                      suffix="%"
                      :value-style="{ fontSize: '16px' }"
                    />
                  </a-col>
                </a-row>
                
                <a-progress 
                  :percent="(collector.currentCases / collector.maxCases) * 100"
                  :stroke-color="getWorkloadColor(collector.currentCases, collector.maxCases)"
                  size="small"
                />
              </div>
              
              <div class="collector-skills">
                <a-tag 
                  v-for="skill in collector.skills" 
                  :key="skill"
                  size="small"
                >
                  {{ skill }}
                </a-tag>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
      
      <!-- 手动分配 -->
      <a-card title="手动分配" v-if="selectedCaseKeys.length && selectedCollectorId">
        <a-space direction="vertical" style="width: 100%">
          <a-alert
            :message="`将 ${selectedCaseKeys.length} 个案件分配给 ${getCollectorName(selectedCollectorId)}`"
            type="info"
            show-icon
          />
          
          <a-form layout="inline">
            <a-form-item label="分配说明">
              <a-input 
                v-model:value="allocationRemark"
                placeholder="请输入分配说明（选填）"
                style="width: 300px"
              />
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" @click="executeManualAllocation">
                确认分配
              </a-button>
              <a-button @click="cancelAllocation" style="margin-left: 8px">
                取消
              </a-button>
            </a-form-item>
          </a-form>
        </a-space>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { 
  SearchOutlined, 
  ReloadOutlined,
  ThunderboltOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 自动分配开关
const autoAllocationEnabled = ref(true)

// 搜索参数
const searchParams = reactive({
  caseNo: '',
  debtor: '',
  priority: undefined,
  region: undefined,
  minAmount: undefined,
  maxAmount: undefined
})

// 分配规则
const allocationRules = reactive({
  strategy: 'round-robin',
  maxCases: 50,
  priorityWeight: 70,
  conditions: ['amount', 'overdue']
})

// 选中的案件和催收员
const selectedCaseKeys = ref([])
const selectedCollectorId = ref(null)
const allocationRemark = ref('')

// 案件列配置
const caseColumns = [
  {
    title: '案件编号',
    dataIndex: 'caseNo',
    key: 'caseNo',
    width: 120
  },
  {
    title: '债务人',
    dataIndex: 'debtorName',
    key: 'debtorName',
    width: 100
  },
  {
    title: '债务金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }) => `¥${text.toLocaleString()}`
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDays',
    key: 'overdueDays',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 80
  },
  {
    title: '地区',
    dataIndex: 'region',
    key: 'region',
    width: 100
  }
]

// 待分配案件
const unallocatedCases = ref([
  {
    key: '1',
    caseNo: 'CS202401101',
    debtorName: '张三',
    amount: 50000,
    overdueDays: 30,
    priority: '高',
    region: '北京'
  },
  {
    key: '2',
    caseNo: 'CS202401102',
    debtorName: '李四',
    amount: 120000,
    overdueDays: 60,
    priority: '紧急',
    region: '上海'
  },
  {
    key: '3',
    caseNo: 'CS202401103',
    debtorName: '王五',
    amount: 35000,
    overdueDays: 90,
    priority: '中',
    region: '广州'
  }
])

// 催收员列表
const collectors = ref([
  {
    id: 1,
    name: '李催收',
    team: '一组',
    currentCases: 45,
    maxCases: 50,
    successRate: 85.5,
    skills: ['电话催收', '大额案件']
  },
  {
    id: 2,
    name: '王催收',
    team: '一组',
    currentCases: 38,
    maxCases: 50,
    successRate: 78.3,
    skills: ['外访', '小额案件']
  },
  {
    id: 3,
    name: '赵催收',
    team: '二组',
    currentCases: 22,
    maxCases: 50,
    successRate: 92.1,
    skills: ['法务', '疑难案件']
  },
  {
    id: 4,
    name: '钱催收',
    team: '二组',
    currentCases: 48,
    maxCases: 50,
    successRate: 81.7,
    skills: ['协商', '分期']
  }
])

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colors = {
    '紧急': 'red',
    '高': 'orange',
    '中': 'blue',
    '低': 'default'
  }
  return colors[priority] || 'default'
}

// 获取工作量颜色
const getWorkloadColor = (current, max) => {
  const ratio = current / max
  if (ratio >= 0.9) return '#ff4d4f'
  if (ratio >= 0.7) return '#faad14'
  return '#52c41a'
}

// 案件选择变化
const onCaseSelectChange = (keys) => {
  selectedCaseKeys.value = keys
}

// 选择催收员
const selectCollector = (collector) => {
  selectedCollectorId.value = collector.id
}

// 获取催收员姓名
const getCollectorName = (id) => {
  const collector = collectors.value.find(c => c.id === id)
  return collector ? collector.name : ''
}

// 保存分配规则
const saveAllocationRules = () => {
  message.success('分配规则保存成功')
}

// 执行自动分配
const executeAutoAllocation = () => {
  message.loading('正在执行自动分配...')
  setTimeout(() => {
    message.success('自动分配完成')
  }, 2000)
}

// 执行手动分配
const executeManualAllocation = () => {
  message.success(`成功将 ${selectedCaseKeys.value.length} 个案件分配给 ${getCollectorName(selectedCollectorId.value)}`)
  selectedCaseKeys.value = []
  selectedCollectorId.value = null
  allocationRemark.value = ''
}

// 取消分配
const cancelAllocation = () => {
  selectedCaseKeys.value = []
  selectedCollectorId.value = null
  allocationRemark.value = ''
}

// 搜索处理
const handleSearch = () => {
  message.success('搜索案件成功')
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = undefined
  })
}

// 导出数据
const exportData = () => {
  message.info('导出待分配案件数据')
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  
  .search-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.rule-card,
.allocation-card,
.workload-card {
  margin-bottom: 16px;
}

.collector-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &.selected {
    border-color: #1890ff;
    background: #f0f5ff;
  }
  
  .collector-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .collector-details {
      margin-left: 12px;
      
      h4 {
        margin: 0;
        font-size: 16px;
      }
      
      p {
        margin: 0;
        color: #999;
        font-size: 14px;
      }
    }
  }
  
  .collector-stats {
    margin-bottom: 12px;
  }
  
  .collector-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}
</style>