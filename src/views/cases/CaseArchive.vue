<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>案件归档</h2>
        <a-space>
          <a-button @click="showBatchArchiveModal = true" :disabled="!selectedRowKeys.length">
            <folder-add-outlined />
            批量归档
          </a-button>
          <a-button type="primary" @click="handleRetrieve" :disabled="!selectedRowKeys.length">
            <rollback-outlined />
            撤销归档
          </a-button>
        </a-space>
      </div>

      <!-- 搜索区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input 
                  v-model:value="searchParams.caseNo" 
                  placeholder="请输入案件编号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="债务人">
                <a-input 
                  v-model:value="searchParams.debtor" 
                  placeholder="请输入债务人姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="归档状态">
                <a-select 
                  v-model:value="searchParams.archiveStatus"
                  placeholder="请选择归档状态"
                  allow-clear
                >
                  <a-select-option value="pending">待归档</a-select-option>
                  <a-select-option value="archived">已归档</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="归档类型">
                <a-select 
                  v-model:value="searchParams.archiveType"
                  placeholder="请选择归档类型"
                  allow-clear
                >
                  <a-select-option value="closed">正常结案</a-select-option>
                  <a-select-option value="writeoff">核销归档</a-select-option>
                  <a-select-option value="legal">法务归档</a-select-option>
                  <a-select-option value="timeout">超时归档</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="归档时间">
                <a-range-picker 
                  v-model:value="searchParams.archiveDateRange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="归档人">
                <a-select 
                  v-model:value="searchParams.archiveOperator"
                  placeholder="请选择归档人"
                  allow-clear
                >
                  <a-select-option value="1">张主管</a-select-option>
                  <a-select-option value="2">李经理</a-select-option>
                  <a-select-option value="3">王主管</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <div class="search-actions">
                <div class="search-buttons">
                  <a-space>
                    <a-button type="primary" html-type="submit">
                      <search-outlined />
                      查询
                    </a-button>
                    <a-button @click="handleReset">
                      <reload-outlined />
                      重置
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="待归档案件"
              :value="stats.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="本月归档"
              :value="stats.monthlyArchived"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <calendar-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="累计归档"
              :value="stats.totalArchived"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <folder-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="归档率"
              :value="stats.archiveRate"
              suffix="%"
              :precision="2"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <percentage-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 案件列表 -->
      <a-card class="case-list-card">
        <template #title>
          <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
            <a-tab-pane key="pending" tab="待归档案件" />
            <a-tab-pane key="archived" tab="已归档案件" />
          </a-tabs>
        </template>

        <a-table
          :columns="columns"
          :data-source="caseList"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: getCheckboxProps
          }"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true
          }"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'caseNo'">
              <a @click="viewCaseDetail(record)">{{ record.caseNo }}</a>
            </template>
            <template v-else-if="column.key === 'archiveStatus'">
              <a-tag :color="record.archiveStatus === 'archived' ? 'green' : 'orange'">
                {{ record.archiveStatus === 'archived' ? '已归档' : '待归档' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'archiveType'">
              <a-tag :color="getArchiveTypeColor(record.archiveType)">
                {{ getArchiveTypeText(record.archiveType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button 
                  v-if="record.archiveStatus === 'pending'"
                  type="link" 
                  size="small" 
                  @click="showArchiveModal(record)"
                >
                  归档
                </a-button>
                <a-button 
                  v-else
                  type="link" 
                  size="small" 
                  @click="handleRetrieveSingle(record)"
                >
                  撤销
                </a-button>
                <a-button type="link" size="small" @click="viewArchiveDetail(record)">
                  详情
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 单个归档弹窗 -->
      <a-modal
        v-model:open="archiveModalVisible"
        title="案件归档"
        width="600px"
        @ok="handleArchive"
      >
        <a-form :model="archiveForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="案件编号">
            <span>{{ currentCase?.caseNo }}</span>
          </a-form-item>
          
          <a-form-item label="债务人">
            <span>{{ currentCase?.debtorName }}</span>
          </a-form-item>
          
          <a-form-item label="归档类型" required>
            <a-select v-model:value="archiveForm.type" placeholder="请选择归档类型">
              <a-select-option value="closed">正常结案</a-select-option>
              <a-select-option value="writeoff">核销归档</a-select-option>
              <a-select-option value="legal">法务归档</a-select-option>
              <a-select-option value="timeout">超时归档</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="归档原因" required>
            <a-select v-model:value="archiveForm.reason" placeholder="请选择归档原因">
              <a-select-option value="full_payment">全额还款</a-select-option>
              <a-select-option value="settlement">和解结清</a-select-option>
              <a-select-option value="bad_debt">确认坏账</a-select-option>
              <a-select-option value="legal_process">转法务处理</a-select-option>
              <a-select-option value="timeout">超时未催</a-select-option>
              <a-select-option value="other">其他原因</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="归档说明" required>
            <a-textarea 
              v-model:value="archiveForm.remark" 
              :rows="4"
              placeholder="请详细说明归档原因和处理结果..."
            />
          </a-form-item>
          
          <a-form-item label="相关附件">
            <a-upload
              v-model:file-list="archiveForm.fileList"
              :before-upload="beforeUpload"
            >
              <a-button>
                <upload-outlined />
                上传附件
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量归档弹窗 -->
      <a-modal
        v-model:open="showBatchArchiveModal"
        title="批量归档"
        width="600px"
        @ok="handleBatchArchive"
      >
        <a-alert
          :message="`您选择了 ${selectedRowKeys.length} 个案件进行批量归档`"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <a-form :model="batchArchiveForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="归档类型" required>
            <a-select v-model:value="batchArchiveForm.type" placeholder="请选择归档类型">
              <a-select-option value="closed">正常结案</a-select-option>
              <a-select-option value="writeoff">核销归档</a-select-option>
              <a-select-option value="legal">法务归档</a-select-option>
              <a-select-option value="timeout">超时归档</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="归档原因" required>
            <a-select v-model:value="batchArchiveForm.reason" placeholder="请选择归档原因">
              <a-select-option value="batch_settlement">批量和解</a-select-option>
              <a-select-option value="batch_writeoff">批量核销</a-select-option>
              <a-select-option value="batch_timeout">批量超时</a-select-option>
              <a-select-option value="other">其他原因</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="归档说明" required>
            <a-textarea 
              v-model:value="batchArchiveForm.remark" 
              :rows="4"
              placeholder="请说明批量归档的原因..."
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 归档详情弹窗 -->
      <a-modal
        v-model:open="detailModalVisible"
        title="归档详情"
        width="700px"
        :footer="null"
      >
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="案件编号">{{ archiveDetail?.caseNo }}</a-descriptions-item>
          <a-descriptions-item label="债务人">{{ archiveDetail?.debtorName }}</a-descriptions-item>
          <a-descriptions-item label="债务金额">¥{{ archiveDetail?.amount?.toLocaleString() }}</a-descriptions-item>
          <a-descriptions-item label="归档状态">
            <a-tag :color="archiveDetail?.archiveStatus === 'archived' ? 'green' : 'orange'">
              {{ archiveDetail?.archiveStatus === 'archived' ? '已归档' : '待归档' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="归档类型">
            <a-tag :color="getArchiveTypeColor(archiveDetail?.archiveType)">
              {{ getArchiveTypeText(archiveDetail?.archiveType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="归档时间">{{ archiveDetail?.archiveTime }}</a-descriptions-item>
          <a-descriptions-item label="归档人">{{ archiveDetail?.archiveOperator }}</a-descriptions-item>
          <a-descriptions-item label="审批人">{{ archiveDetail?.approver }}</a-descriptions-item>
          <a-descriptions-item label="归档原因" :span="2">{{ archiveDetail?.archiveReason }}</a-descriptions-item>
          <a-descriptions-item label="归档说明" :span="2">{{ archiveDetail?.archiveRemark }}</a-descriptions-item>
        </a-descriptions>
        
        <div v-if="archiveDetail?.attachments?.length" style="margin-top: 16px">
          <h4>相关附件</h4>
          <a-list
            :data-source="archiveDetail.attachments"
            :bordered="true"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a :href="item.url" target="_blank">
                  <paper-clip-outlined /> {{ item.name }}
                </a>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  FolderAddOutlined, 
  RollbackOutlined,
  SearchOutlined, 
  ReloadOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  FolderOutlined,
  PercentageOutlined,
  UploadOutlined,
  PaperClipOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 状态管理
const loading = ref(false)
const selectedRowKeys = ref([])
const activeTab = ref('pending')
const archiveModalVisible = ref(false)
const showBatchArchiveModal = ref(false)
const detailModalVisible = ref(false)
const currentCase = ref(null)
const archiveDetail = ref(null)

// 搜索参数
const searchParams = reactive({
  caseNo: '',
  debtor: '',
  archiveStatus: undefined,
  archiveType: undefined,
  archiveDateRange: [],
  archiveOperator: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50
})

// 统计数据
const stats = reactive({
  pending: 156,
  monthlyArchived: 89,
  totalArchived: 1234,
  archiveRate: 78.5
})

// 归档表单
const archiveForm = reactive({
  type: undefined,
  reason: undefined,
  remark: '',
  fileList: []
})

// 批量归档表单
const batchArchiveForm = reactive({
  type: undefined,
  reason: undefined,
  remark: ''
})

// 表格列配置
const columns = computed(() => {
  const baseColumns = [
    {
      title: '案件编号',
      dataIndex: 'caseNo',
      key: 'caseNo',
      width: 120
    },
    {
      title: '债务人',
      dataIndex: 'debtorName',
      key: 'debtorName',
      width: 100
    },
    {
      title: '债务金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      customRender: ({ text }) => `¥${text.toLocaleString()}`
    },
    {
      title: '归档状态',
      dataIndex: 'archiveStatus',
      key: 'archiveStatus',
      width: 100
    }
  ]
  
  if (activeTab.value === 'archived') {
    baseColumns.push(
      {
        title: '归档类型',
        dataIndex: 'archiveType',
        key: 'archiveType',
        width: 100
      },
      {
        title: '归档时间',
        dataIndex: 'archiveTime',
        key: 'archiveTime',
        width: 150
      },
      {
        title: '归档人',
        dataIndex: 'archiveOperator',
        key: 'archiveOperator',
        width: 100
      }
    )
  } else {
    baseColumns.push(
      {
        title: '结案时间',
        dataIndex: 'closeTime',
        key: 'closeTime',
        width: 150
      },
      {
        title: '等待天数',
        dataIndex: 'waitDays',
        key: 'waitDays',
        width: 100,
        customRender: ({ text }) => `${text}天`
      }
    )
  }
  
  baseColumns.push({
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  })
  
  return baseColumns
})

// 案件列表数据
const caseList = ref([])

// 待归档案件数据
const pendingCases = [
  {
    key: '1',
    caseNo: 'CS202401001',
    debtorName: '张三',
    amount: 50000,
    archiveStatus: 'pending',
    closeTime: '2024-01-15 10:30',
    waitDays: 5
  },
  {
    key: '2',
    caseNo: 'CS202401002',
    debtorName: '李四',
    amount: 120000,
    archiveStatus: 'pending',
    closeTime: '2024-01-10 14:20',
    waitDays: 10
  },
  {
    key: '3',
    caseNo: 'CS202401003',
    debtorName: '王五',
    amount: 35000,
    archiveStatus: 'pending',
    closeTime: '2024-01-08 09:15',
    waitDays: 12
  }
]

// 已归档案件数据
const archivedCases = [
  {
    key: '4',
    caseNo: 'CS202312001',
    debtorName: '赵六',
    amount: 80000,
    archiveStatus: 'archived',
    archiveType: 'closed',
    archiveTime: '2024-01-05 16:30',
    archiveOperator: '张主管'
  },
  {
    key: '5',
    caseNo: 'CS202312002',
    debtorName: '钱七',
    amount: 150000,
    archiveStatus: 'archived',
    archiveType: 'writeoff',
    archiveTime: '2024-01-03 11:20',
    archiveOperator: '李经理'
  },
  {
    key: '6',
    caseNo: 'CS202312003',
    debtorName: '孙八',
    amount: 45000,
    archiveStatus: 'archived',
    archiveType: 'legal',
    archiveTime: '2024-01-02 14:45',
    archiveOperator: '王主管'
  }
]

// 方法
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const getCheckboxProps = (record) => ({
  disabled: activeTab.value === 'archived' && record.archiveStatus === 'archived',
})

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询成功')
    updateCaseList()
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = undefined
    }
  })
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const handleTabChange = (key) => {
  activeTab.value = key
  selectedRowKeys.value = []
  updateCaseList()
}

const updateCaseList = () => {
  caseList.value = activeTab.value === 'pending' ? pendingCases : archivedCases
  pagination.total = caseList.value.length
}

const viewCaseDetail = (record) => {
  console.log('查看案件详情', record)
}

const showArchiveModal = (record) => {
  currentCase.value = record
  archiveModalVisible.value = true
  // 重置表单
  Object.keys(archiveForm).forEach(key => {
    if (key === 'fileList') {
      archiveForm[key] = []
    } else {
      archiveForm[key] = undefined
    }
  })
}

const handleArchive = () => {
  if (!archiveForm.type) {
    message.warning('请选择归档类型')
    return
  }
  if (!archiveForm.reason) {
    message.warning('请选择归档原因')
    return
  }
  if (!archiveForm.remark) {
    message.warning('请填写归档说明')
    return
  }
  
  message.loading('正在归档...')
  setTimeout(() => {
    message.success('归档成功')
    archiveModalVisible.value = false
    handleSearch()
  }, 1500)
}

const handleBatchArchive = () => {
  if (!batchArchiveForm.type) {
    message.warning('请选择归档类型')
    return
  }
  if (!batchArchiveForm.reason) {
    message.warning('请选择归档原因')
    return
  }
  if (!batchArchiveForm.remark) {
    message.warning('请填写归档说明')
    return
  }
  
  message.loading('正在批量归档...')
  setTimeout(() => {
    message.success(`成功归档 ${selectedRowKeys.value.length} 个案件`)
    showBatchArchiveModal.value = false
    selectedRowKeys.value = []
    handleSearch()
  }, 2000)
}

const handleRetrieve = () => {
  message.loading('正在撤销归档...')
  setTimeout(() => {
    message.success(`成功撤销 ${selectedRowKeys.value.length} 个案件的归档`)
    selectedRowKeys.value = []
    handleSearch()
  }, 1500)
}

const handleRetrieveSingle = (record) => {
  message.loading('正在撤销归档...')
  setTimeout(() => {
    message.success('撤销归档成功')
    handleSearch()
  }, 1000)
}

const viewArchiveDetail = (record) => {
  archiveDetail.value = {
    ...record,
    archiveReason: '全额还款',
    archiveRemark: '债务人已全额还清所有欠款，包括本金和利息。经财务确认无误后进行归档。',
    approver: '王经理',
    attachments: [
      { id: 1, name: '还款凭证.pdf', url: '#' },
      { id: 2, name: '结清证明.pdf', url: '#' }
    ]
  }
  detailModalVisible.value = true
}

const beforeUpload = (file) => {
  archiveForm.fileList.push(file)
  return false
}

const getArchiveTypeColor = (type) => {
  const colors = {
    closed: 'green',
    writeoff: 'red',
    legal: 'purple',
    timeout: 'orange'
  }
  return colors[type] || 'default'
}

const getArchiveTypeText = (type) => {
  const texts = {
    closed: '正常结案',
    writeoff: '核销归档',
    legal: '法务归档',
    timeout: '超时归档'
  }
  return texts[type] || type
}

// 生命周期
onMounted(() => {
  updateCaseList()
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  
  .search-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}

.stats-cards {
  margin-bottom: 16px;
  
  .ant-card {
    height: 100%;
  }
}

.case-list-card {
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
}
</style>