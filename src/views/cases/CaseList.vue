<template>
  <div class="case-list">
    <!-- 增强搜索区域 -->
    <a-card class="search-card enhanced-search">
      <template #title>
        <div class="search-header">
          <span>案件搜索</span>
          <div class="search-stats">
            <a-statistic-countdown 
              :value="nextRefreshTime" 
              format="mm:ss" 
              @finish="autoRefresh"
              title="下次自动刷新"
            />
          </div>
        </div>
      </template>
      <template #extra>
        <a-space>
          <a-button @click="toggleAdvanced">
            {{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}
            <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-button type="primary" @click="handleQuickSearch">
            <template #icon><SearchOutlined /></template>
            快速搜索
          </a-button>
        </a-space>
      </template>
      
      <a-form :model="searchForm" @submit="handleSearch">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="案件编号">
              <a-input
                v-model:value="searchForm.caseNumber"
                placeholder="请输入案件编号"
                allow-clear
              >
                <template #prefix><FileTextOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="债务人">
              <a-auto-complete
                v-model:value="searchForm.debtorName"
                :options="debtorOptions"
                placeholder="请输入债务人姓名/手机号"
                allow-clear
                @search="handleDebtorSearch"
              >
                <template #prefix><UserOutlined /></template>
              </a-auto-complete>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="案件状态">
              <a-select
                v-model:value="searchForm.status"
                placeholder="请选择案件状态"
                allow-clear
              >
                <a-select-option value="new">
                  <a-tag color="blue">新案件</a-tag>
                </a-select-option>
                <a-select-option value="processing">
                  <a-tag color="orange">处理中</a-tag>
                </a-select-option>
                <a-select-option value="promised">
                  <a-tag color="cyan">已承诺</a-tag>
                </a-select-option>
                <a-select-option value="partial">
                  <a-tag color="purple">部分还款</a-tag>
                </a-select-option>
                <a-select-option value="settled">
                  <a-tag color="green">已结清</a-tag>
                </a-select-option>
                <a-select-option value="closed">
                  <a-tag color="gray">已关闭</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="催收员">
              <a-select
                v-model:value="searchForm.collectorId"
                placeholder="请选择催收员"
                allow-clear
                show-search
                :filter-option="filterOption"
              >
                <a-select-option v-for="collector in collectors" :key="collector.id" :value="collector.id">
                  <div class="collector-option">
                    <a-avatar :size="20" :src="collector.avatar">{{ collector.name[0] }}</a-avatar>
                    <span style="margin-left: 8px;">{{ collector.name }}</span>
                    <a-tag size="small" :color="collector.online ? 'green' : 'gray'">
                      {{ collector.online ? '在线' : '离线' }}
                    </a-tag>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 高级搜索 -->
        <a-row :gutter="16" v-show="showAdvanced">
          <a-col :span="6">
            <a-form-item label="债务金额">
              <a-space>
                <a-input-number
                  v-model:value="searchForm.amountMin"
                  placeholder="最小金额"
                  :min="0"
                  style="width: 100px"
                />
                <span>-</span>
                <a-input-number
                  v-model:value="searchForm.amountMax"
                  placeholder="最大金额"
                  :min="0"
                  style="width: 100px"
                />
              </a-space>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="逾期天数">
              <a-space>
                <a-input-number
                  v-model:value="searchForm.overdueDaysMin"
                  placeholder="最少天数"
                  :min="0"
                  style="width: 100px"
                />
                <span>-</span>
                <a-input-number
                  v-model:value="searchForm.overdueDaysMax"
                  placeholder="最多天数"
                  :min="0"
                  style="width: 100px"
                />
              </a-space>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="委托方">
              <a-select
                v-model:value="searchForm.clientId"
                placeholder="请选择委托方"
                allow-clear
              >
                <a-select-option v-for="client in clients" :key="client.id" :value="client.id">
                  {{ client.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="产品类型">
              <a-select
                v-model:value="searchForm.productType"
                placeholder="请选择产品类型"
                allow-clear
              >
                <a-select-option value="credit_card">信用卡</a-select-option>
                <a-select-option value="personal_loan">个人贷款</a-select-option>
                <a-select-option value="car_loan">车贷</a-select-option>
                <a-select-option value="mortgage">房贷</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="地区">
              <a-cascader
                v-model:value="searchForm.region"
                :options="regionOptions"
                placeholder="请选择地区"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="分案时间">
              <a-range-picker
                v-model:value="searchForm.assignDateRange"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="优先级">
              <a-select
                v-model:value="searchForm.priority"
                placeholder="请选择优先级"
                allow-clear
              >
                <a-select-option value="urgent">紧急</a-select-option>
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="风险等级">
              <a-select
                v-model:value="searchForm.riskLevel"
                placeholder="请选择风险等级"
                allow-clear
              >
                <a-select-option value="high">高风险</a-select-option>
                <a-select-option value="medium">中风险</a-select-option>
                <a-select-option value="low">低风险</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" html-type="submit">
                  <search-outlined />
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <reload-outlined />
                  重置
                </a-button>
                <a-button type="link" @click="showAdvanced = !showAdvanced">
                  {{ showAdvanced ? '收起' : '展开高级搜索' }}
                  <DownOutlined v-if="!showAdvanced" />
                  <UpOutlined v-else />
                </a-button>
              </a-space>
              
              <div class="action-buttons">
                <a-space>
                  <a-button type="primary" @click="showCreateModal = true">
                    <PlusOutlined /> 新建案件
                  </a-button>
                  <a-button @click="handleImport">
                    <UploadOutlined /> 导入案件
                  </a-button>
                  <a-dropdown v-if="selectedRowKeys.length > 0">
                    <template #overlay>
                      <a-menu @click="handleBatchAction">
                        <a-menu-item key="assign">
                          <UserAddOutlined /> 批量分配
                        </a-menu-item>
                        <a-menu-item key="transfer">
                          <SwapOutlined /> 批量转移
                        </a-menu-item>
                        <a-menu-item key="close">
                          <CloseCircleOutlined /> 批量关闭
                        </a-menu-item>
                        <a-menu-item key="export">
                          <ExportOutlined /> 导出选中
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <a-button>
                      批量操作 ({{ selectedRowKeys.length }})
                      <DownOutlined />
                    </a-button>
                  </a-dropdown>
                  <a-button @click="handleExportAll">
                    <ExportOutlined /> 导出全部
                  </a-button>
                  <a-button @click="showColumnConfig = true">
                    <SettingOutlined /> 列配置
                  </a-button>
                  <a-button @click="handleRefresh">
                    <ReloadOutlined /> 刷新
                  </a-button>
                </a-space>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 数据表格 -->
    <div class="data-section">
      <a-table
        :columns="visibleColumns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true
        }"
        @change="handleTableChange"
        :scroll="{ x: 1500, y: 600 }"
        :row-key="record => record.id"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'caseNumber'">
            案件编号
            <a-tooltip title="点击查看案件详情">
              <InfoCircleOutlined style="margin-left: 4px; color: #999" />
            </a-tooltip>
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'caseNumber'">
            <a @click="viewDetail(record)">{{ record.caseNumber }}</a>
          </template>

          <template v-else-if="column.key === 'debtorName'">
            <div>
              <div>{{ record.debtorName }}</div>
              <div style="font-size: 12px; color: #999">{{ record.debtorPhone }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'amount'">
            <div>
              <div style="font-weight: 600">¥{{ formatAmount(record.totalAmount) }}</div>
              <div style="font-size: 12px; color: #999">
                已还: ¥{{ formatAmount(record.paidAmount) }}
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'overdueDays'">
            <span :class="{ 'text-danger': record.overdueDays > 90 }">
              {{ record.overdueDays }}天
            </span>
          </template>

          <template v-else-if="column.key === 'lastFollowUp'">
            <div v-if="record.lastFollowUp">
              <div>{{ record.lastFollowUp.type }}</div>
              <div style="font-size: 12px; color: #999">
                {{ formatDate(record.lastFollowUp.time) }}
              </div>
            </div>
            <span v-else style="color: #999">暂无</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleFollow(record)">
                <PhoneOutlined /> 跟进
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多 <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleAction(key, record)">
                    <a-menu-item key="detail">
                      <EyeOutlined /> 查看详情
                    </a-menu-item>
                    <a-menu-item key="assign">
                      <UserAddOutlined /> 分配
                    </a-menu-item>
                    <a-menu-item key="transfer">
                      <SwapOutlined /> 转移
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="plan">
                      <CalendarOutlined /> 还款计划
                    </a-menu-item>
                    <a-menu-item key="history">
                      <HistoryOutlined /> 跟进记录
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="close">
                      <CloseCircleOutlined /> 关闭案件
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>

        <template #summary>
          <a-table-summary>
            <a-table-summary-row>
              <a-table-summary-cell :index="0" :col-span="3">
                合计
              </a-table-summary-cell>
              <a-table-summary-cell :index="3">
                <div style="font-weight: 600">
                  ¥{{ formatAmount(summaryData.totalAmount) }}
                </div>
              </a-table-summary-cell>
              <a-table-summary-cell :index="4">
                <div style="font-weight: 600">
                  ¥{{ formatAmount(summaryData.paidAmount) }}
                </div>
              </a-table-summary-cell>
              <a-table-summary-cell :index="5" :col-span="7">
                回收率: {{ (summaryData.recoveryRate * 100).toFixed(2) }}%
              </a-table-summary-cell>
            </a-table-summary-row>
          </a-table-summary>
        </template>
      </a-table>
    </div>

    <!-- 新建案件弹窗 -->
    <a-modal
      v-model:open="showCreateModal"
      title="新建案件"
      width="800px"
      :confirmLoading="createLoading"
      @ok="handleCreate"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="债务人姓名" name="debtorName">
              <a-input v-model:value="createForm.debtorName" placeholder="请输入债务人姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="身份证号" name="idNumber">
              <a-input v-model:value="createForm.idNumber" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="手机号码" name="phone">
              <a-input v-model:value="createForm.phone" placeholder="请输入手机号码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="委托方" name="clientId">
              <a-select v-model:value="createForm.clientId" placeholder="请选择委托方">
                <a-select-option v-for="client in clients" :key="client.id" :value="client.id">
                  {{ client.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="产品类型" name="productType">
              <a-select v-model:value="createForm.productType" placeholder="请选择产品类型">
                <a-select-option value="credit_card">信用卡</a-select-option>
                <a-select-option value="personal_loan">个人贷款</a-select-option>
                <a-select-option value="car_loan">车贷</a-select-option>
                <a-select-option value="mortgage">房贷</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="债务金额" name="totalAmount">
              <a-input-number
                v-model:value="createForm.totalAmount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入债务金额"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="逾期天数" name="overdueDays">
              <a-input-number
                v-model:value="createForm.overdueDays"
                :min="0"
                style="width: 100%"
                placeholder="请输入逾期天数"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="优先级" name="priority">
              <a-select v-model:value="createForm.priority" placeholder="请选择优先级">
                <a-select-option value="urgent">紧急</a-select-option>
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="联系地址" name="address">
          <a-input v-model:value="createForm.address" placeholder="请输入联系地址" />
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="createForm.remark"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量分配弹窗 -->
    <a-modal
      v-model:open="showAssignModal"
      title="批量分配案件"
      width="600px"
      @ok="handleBatchAssign"
    >
      <a-form
        :model="assignForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="选中案件数">
          <span>{{ selectedRowKeys.length }} 个案件</span>
        </a-form-item>

        <a-form-item label="分配策略" name="strategy">
          <a-radio-group v-model:value="assignForm.strategy">
            <a-radio value="manual">手动分配</a-radio>
            <a-radio value="average">平均分配</a-radio>
            <a-radio value="workload">按工作量分配</a-radio>
            <a-radio value="skill">按技能分配</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item 
          v-if="assignForm.strategy === 'manual'" 
          label="催收员" 
          name="collectorId"
        >
          <a-select
            v-model:value="assignForm.collectorId"
            placeholder="请选择催收员"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="collector in collectors" :key="collector.id" :value="collector.id">
              <div style="display: flex; justify-content: space-between">
                <span>{{ collector.name }}</span>
                <span style="color: #999">在催: {{ collector.caseCount }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item 
          v-if="assignForm.strategy !== 'manual'" 
          label="催收组" 
          name="groupId"
        >
          <a-select
            v-model:value="assignForm.groupId"
            placeholder="请选择催收组"
          >
            <a-select-option v-for="group in collectorGroups" :key="group.id" :value="group.id">
              {{ group.name }} ({{ group.memberCount }}人)
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="分配原因" name="reason">
          <a-textarea
            v-model:value="assignForm.reason"
            :rows="3"
            placeholder="请输入分配原因"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 列配置弹窗 -->
    <a-modal
      v-model:open="showColumnConfig"
      title="自定义列显示"
      width="600px"
      @ok="handleColumnConfig"
    >
      <a-checkbox-group v-model:value="checkedColumns" style="width: 100%">
        <a-row>
          <a-col :span="8" v-for="col in allColumns" :key="col.key" style="margin-bottom: 8px">
            <a-checkbox :value="col.key" :disabled="col.fixed">
              {{ col.title }}
            </a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-modal>

    <!-- 导入弹窗 -->
    <a-modal
      v-model:open="showImportModal"
      title="导入案件"
      width="600px"
      @ok="handleImportConfirm"
    >
      <a-upload-dragger
        v-model:fileList="importFileList"
        :max-count="1"
        :before-upload="beforeUpload"
        accept=".xlsx,.xls,.csv"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持 xlsx、xls、csv 格式，文件大小不超过10MB
        </p>
      </a-upload-dragger>

      <div style="margin-top: 16px">
        <a @click="downloadTemplate">
          <DownloadOutlined /> 下载导入模板
        </a>
      </div>
    </a-modal>
    <!-- 跟进弹窗 -->
    <a-modal
      v-model:open="showFollowModal"
      title="案件跟进"
      width="700px"
      @ok="handleFollowConfirm"
    >
      <a-form
        :model="followForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="案件编号">
          <span>{{ currentCase?.caseNumber }}</span>
        </a-form-item>
        
        <a-form-item label="债务人">
          <span>{{ currentCase?.debtorName }} ({{ currentCase?.debtorPhone }})</span>
        </a-form-item>
        
        <a-form-item label="跟进方式" name="type" required>
          <a-radio-group v-model:value="followForm.type">
            <a-radio value="phone">电话</a-radio>
            <a-radio value="sms">短信</a-radio>
            <a-radio value="visit">外访</a-radio>
            <a-radio value="letter">信函</a-radio>
            <a-radio value="email">邮件</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="联系结果" name="result" required>
          <a-select v-model:value="followForm.result" placeholder="请选择联系结果">
            <a-select-option value="connected">已接通</a-select-option>
            <a-select-option value="no_answer">无人接听</a-select-option>
            <a-select-option value="busy">忙线/挂断</a-select-option>
            <a-select-option value="wrong_number">错号/空号</a-select-option>
            <a-select-option value="promised">承诺还款</a-select-option>
            <a-select-option value="refused">拒绝还款</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item v-if="followForm.result === 'promised'" label="承诺金额">
          <a-input-number
            v-model:value="followForm.promiseAmount"
            :min="0"
            :precision="2"
            style="width: 200px"
            placeholder="请输入承诺还款金额"
          />
        </a-form-item>
        
        <a-form-item v-if="followForm.result === 'promised'" label="承诺日期">
          <a-date-picker
            v-model:value="followForm.promiseDate"
            style="width: 200px"
            placeholder="请选择承诺还款日期"
          />
        </a-form-item>
        
        <a-form-item label="跟进内容" name="content" required>
          <a-textarea
            v-model:value="followForm.content"
            :rows="4"
            placeholder="请输入跟进内容"
          />
        </a-form-item>
        
        <a-form-item label="下次跟进">
          <a-space>
            <a-date-picker
              v-model:value="followForm.nextFollowDate"
              show-time
              placeholder="选择下次跟进时间"
            />
            <a-input
              v-model:value="followForm.nextFollowRemark"
              placeholder="下次跟进备注"
              style="width: 200px"
            />
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 转移弹窗 -->
    <a-modal
      v-model:open="showTransferModal"
      title="案件转移"
      width="600px"
      @ok="handleTransferConfirm"
    >
      <a-form
        :model="transferForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="转移案件">
          <span>{{ transferCases.length }} 个案件</span>
        </a-form-item>
        
        <a-form-item label="转移类型" name="type" required>
          <a-radio-group v-model:value="transferForm.type">
            <a-radio value="internal">组内转移</a-radio>
            <a-radio value="cross">跨组转移</a-radio>
            <a-radio value="upgrade">升级转移</a-radio>
            <a-radio value="outsource">外包转移</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="目标催收员" name="targetCollectorId" required>
          <a-select
            v-model:value="transferForm.targetCollectorId"
            placeholder="请选择目标催收员"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="collector in collectors" :key="collector.id" :value="collector.id">
              <div style="display: flex; justify-content: space-between">
                <span>{{ collector.name }}</span>
                <span style="color: #999">在催: {{ collector.caseCount }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="转移原因" name="reason" required>
          <a-textarea
            v-model:value="transferForm.reason"
            :rows="3"
            placeholder="请输入转移原因"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 还款计划弹窗 -->
    <a-modal
      v-model:open="showPaymentPlanModal"
      title="还款计划"
      width="800px"
      :footer="null"
    >
      <div v-if="currentCase">
        <a-descriptions :column="2" style="margin-bottom: 16px">
          <a-descriptions-item label="案件编号">{{ currentCase.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="债务人">{{ currentCase.debtorName }}</a-descriptions-item>
          <a-descriptions-item label="债务总额">¥{{ formatAmount(currentCase.totalAmount) }}</a-descriptions-item>
          <a-descriptions-item label="已还金额">¥{{ formatAmount(currentCase.paidAmount) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-button type="primary" @click="showCreatePlanModal = true" style="margin-bottom: 16px">
          <PlusOutlined /> 创建还款计划
        </a-button>
        
        <a-table
          :columns="planColumns"
          :data-source="paymentPlans"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getPlanStatusColor(record.status)">
                {{ getPlanStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewPlanDetail(record)">查看</a-button>
                <a-button type="link" size="small" @click="recordPayment(record)">记录还款</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 创建还款计划弹窗 -->
    <a-modal
      v-model:open="showCreatePlanModal"
      title="创建还款计划"
      width="600px"
      @ok="handleCreatePlan"
    >
      <a-form
        :model="planForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="计划类型" name="type">
          <a-radio-group v-model:value="planForm.type">
            <a-radio value="full">全额还款</a-radio>
            <a-radio value="installment">分期还款</a-radio>
            <a-radio value="reduction">减免还款</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item v-if="planForm.type === 'installment'" label="分期期数">
          <a-input-number
            v-model:value="planForm.periods"
            :min="2"
            :max="36"
            placeholder="请输入分期期数"
          />
        </a-form-item>
        
        <a-form-item label="计划金额">
          <a-input-number
            v-model:value="planForm.amount"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入计划还款金额"
          />
        </a-form-item>
        
        <a-form-item label="首期还款日">
          <a-date-picker
            v-model:value="planForm.firstPaymentDate"
            style="width: 100%"
            placeholder="请选择首期还款日期"
          />
        </a-form-item>
        
        <a-form-item label="备注">
          <a-textarea
            v-model:value="planForm.remark"
            :rows="3"
            placeholder="请输入备注"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 跟进历史弹窗 -->
    <a-modal
      v-model:open="showHistoryModal"
      title="跟进历史"
      width="900px"
      :footer="null"
    >
      <div v-if="currentCase">
        <a-descriptions :column="3" style="margin-bottom: 16px">
          <a-descriptions-item label="案件编号">{{ currentCase.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="债务人">{{ currentCase.debtorName }}</a-descriptions-item>
          <a-descriptions-item label="总跟进次数">{{ followHistory.length }} 次</a-descriptions-item>
        </a-descriptions>
        
        <a-timeline>
          <a-timeline-item v-for="(item, index) in followHistory" :key="index" :color="getTimelineColor(item.type)">
            <template #dot>
              <component :is="getTimelineIcon(item.type)" />
            </template>
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="timeline-title">{{ item.type }} - {{ item.result }}</span>
                <span class="timeline-time">{{ formatDate(item.time) }}</span>
              </div>
              <div class="timeline-body">
                <p>{{ item.content }}</p>
                <div v-if="item.promiseAmount" class="timeline-extra">
                  承诺还款: ¥{{ formatAmount(item.promiseAmount) }} ({{ item.promiseDate }})
                </div>
              </div>
              <div class="timeline-footer">
                <span>{{ item.operator }}</span>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>

    <!-- 关闭案件弹窗 -->
    <a-modal
      v-model:open="showCloseModal"
      title="关闭案件"
      width="600px"
      @ok="handleCloseCase"
    >
      <a-form
        :model="closeForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="案件编号">
          <span>{{ currentCase?.caseNumber }}</span>
        </a-form-item>
        
        <a-form-item label="关闭原因" name="reason" required>
          <a-select v-model:value="closeForm.reason" placeholder="请选择关闭原因">
            <a-select-option value="settled">已结清</a-select-option>
            <a-select-option value="writeoff">核销</a-select-option>
            <a-select-option value="legal">转法务</a-select-option>
            <a-select-option value="lost">失联</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="详细说明" name="description" required>
          <a-textarea
            v-model:value="closeForm.description"
            :rows="4"
            placeholder="请输入详细说明"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 案件详情弹窗 -->
    <a-modal
      v-model:open="showCaseDetailModal"
      title="案件详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="currentCase" class="case-detail-content">
        <!-- 基本信息 -->
        <a-card title="基本信息" size="small" style="margin-bottom: 16px">
          <a-descriptions :column="3" bordered>
            <a-descriptions-item label="案件编号">{{ currentCase.caseNumber }}</a-descriptions-item>
            <a-descriptions-item label="债务人姓名">{{ currentCase.debtorName }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ currentCase.debtorPhone }}</a-descriptions-item>
            
            <a-descriptions-item label="身份证号">{{ currentCase.debtorIdCard || '132****678901234567' }}</a-descriptions-item>
            <a-descriptions-item label="性别">{{ currentCase.debtorGender }}</a-descriptions-item>
            <a-descriptions-item label="年龄">{{ currentCase.debtorAge }}岁</a-descriptions-item>
            
            <a-descriptions-item label="邮箱地址">{{ currentCase.debtorEmail }}</a-descriptions-item>
            <a-descriptions-item label="职业">{{ currentCase.debtorOccupation }}</a-descriptions-item>
            <a-descriptions-item label="工作单位">{{ currentCase.debtorCompany }}</a-descriptions-item>
            
            <a-descriptions-item label="月收入">¥{{ currentCase.debtorIncome?.toLocaleString() }}</a-descriptions-item>
            <a-descriptions-item label="案件状态">
              <a-tag :color="getStatusColor(currentCase.status)">
                {{ getStatusText(currentCase.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="优先级">
              <a-tag :color="getPriorityColor(currentCase.priority)">
                {{ getPriorityText(currentCase.priority) }}
              </a-tag>
            </a-descriptions-item>
            
            <a-descriptions-item label="居住地址" :span="3">{{ currentCase.debtorAddress }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 债务信息 -->
        <a-card title="债务信息" size="small" style="margin-bottom: 16px">
          <a-descriptions :column="3" bordered>
            <a-descriptions-item label="产品类型">{{ currentCase.productDetails?.type }}</a-descriptions-item>
            <a-descriptions-item label="账户号码">{{ currentCase.productDetails?.accountNumber }}</a-descriptions-item>
            <a-descriptions-item label="授信额度">¥{{ currentCase.productDetails?.creditLimit?.toLocaleString() }}</a-descriptions-item>
            
            <a-descriptions-item label="债务总额">¥{{ currentCase.totalAmount?.toLocaleString() }}</a-descriptions-item>
            <a-descriptions-item label="已还金额">¥{{ currentCase.paidAmount?.toLocaleString() }}</a-descriptions-item>
            <a-descriptions-item label="逾期金额">¥{{ currentCase.productDetails?.overdueAmount?.toLocaleString() }}</a-descriptions-item>
            
            <a-descriptions-item label="逾期天数">{{ currentCase.overdueDays }}天</a-descriptions-item>
            <a-descriptions-item label="利率">{{ (currentCase.productDetails?.interestRate * 100).toFixed(2) }}%</a-descriptions-item>
            <a-descriptions-item label="违约金率">{{ (currentCase.productDetails?.penaltyRate * 100).toFixed(2) }}%</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 催收信息 -->
        <a-card title="催收信息" size="small" style="margin-bottom: 16px">
          <a-descriptions :column="3" bordered>
            <a-descriptions-item label="分案日期">{{ currentCase.collectionInfo?.assignDate }}</a-descriptions-item>
            <a-descriptions-item label="首次联系">{{ currentCase.collectionInfo?.firstContactDate }}</a-descriptions-item>
            <a-descriptions-item label="最后联系">{{ currentCase.collectionInfo?.lastContactDate }}</a-descriptions-item>
            
            <a-descriptions-item label="联系次数">{{ currentCase.collectionInfo?.contactCount }}次</a-descriptions-item>
            <a-descriptions-item label="承诺次数">{{ currentCase.collectionInfo?.promiseCount }}次</a-descriptions-item>
            <a-descriptions-item label="外访次数">{{ currentCase.collectionInfo?.visitCount }}次</a-descriptions-item>
            
            <a-descriptions-item label="催收员">{{ currentCase.collectorName }}</a-descriptions-item>
            <a-descriptions-item label="委托方">{{ currentCase.clientName }}</a-descriptions-item>
            <a-descriptions-item label="分案时间">{{ formatDate(currentCase.assignTime) }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 法务信息 -->
        <a-card title="法务信息" size="small" style="margin-bottom: 16px">
          <a-descriptions :column="3" bordered>
            <a-descriptions-item label="是否起诉">
              <a-tag :color="currentCase.legalInfo?.hasCourt ? 'red' : 'green'">
                {{ currentCase.legalInfo?.hasCourt ? '已起诉' : '未起诉' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="资产冻结">
              <a-tag :color="currentCase.legalInfo?.hasAssetFreeze ? 'red' : 'green'">
                {{ currentCase.legalInfo?.hasAssetFreeze ? '已冻结' : '未冻结' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="担保情况">
              <a-tag :color="currentCase.legalInfo?.hasGuarantee ? 'blue' : 'default'">
                {{ currentCase.legalInfo?.hasGuarantee ? '有担保' : '无担保' }}
              </a-tag>
            </a-descriptions-item>
            
            <a-descriptions-item label="担保人姓名" v-if="currentCase.legalInfo?.hasGuarantee">
              {{ currentCase.legalInfo?.guaranteeName }}
            </a-descriptions-item>
            <a-descriptions-item label="担保人电话" v-if="currentCase.legalInfo?.hasGuarantee">
              {{ currentCase.legalInfo?.guaranteePhone }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 风险评估 -->
        <a-card title="风险评估" size="small" style="margin-bottom: 16px">
          <a-descriptions :column="3" bordered>
            <a-descriptions-item label="还款意愿">{{ currentCase.riskAssessment?.paymentWillingness }}</a-descriptions-item>
            <a-descriptions-item label="还款能力">{{ currentCase.riskAssessment?.paymentAbility }}</a-descriptions-item>
            <a-descriptions-item label="联系稳定性">{{ currentCase.riskAssessment?.contactStability }}</a-descriptions-item>
            
            <a-descriptions-item label="资产状况">{{ currentCase.riskAssessment?.assetStatus }}</a-descriptions-item>
            <a-descriptions-item label="风险等级">
              <a-tag :color="getRiskLevelColor(currentCase.riskAssessment?.riskLevel)">
                {{ currentCase.riskAssessment?.riskLevel }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin-top: 24px">
          <a-space>
            <a-button type="primary" @click="handleFollow(currentCase); showCaseDetailModal = false">
              <phone-outlined />
              添加跟进
            </a-button>
            <a-button @click="showPaymentPlanModal = true; showCaseDetailModal = false">
              <calendar-outlined />
              还款计划
            </a-button>
            <a-button @click="showHistoryModal = true; showCaseDetailModal = false">
              <history-outlined />
              跟进历史
            </a-button>
            <a-button @click="showCaseDetailModal = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  ExportOutlined,
  SettingOutlined,
  ReloadOutlined,
  DownOutlined,
  UpOutlined,
  UserAddOutlined,
  SwapOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  EyeOutlined,
  CalendarOutlined,
  HistoryOutlined,
  InboxOutlined,
  DownloadOutlined,
  MessageOutlined,
  CarOutlined,
  MailOutlined,
  SearchOutlined,
  FileTextOutlined,
  UserOutlined
} from '@ant-design/icons-vue'

// 搜索表单
const searchForm = reactive({
  caseNumber: '',
  debtorName: '',
  status: undefined,
  collectorId: undefined,
  amountMin: undefined,
  amountMax: undefined,
  overdueDaysMin: undefined,
  overdueDaysMax: undefined,
  clientId: undefined,
  productType: undefined,
  region: [],
  assignDateRange: [],
  priority: undefined,
  riskLevel: undefined
})

// 展开高级搜索
const showAdvanced = ref(false)

// 自动刷新时间
const nextRefreshTime = ref(Date.now() + 5 * 60 * 1000) // 5分钟后

// 债务人搜索选项
const debtorOptions = ref([])

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 债务人搜索处理
const handleDebtorSearch = (value) => {
  if (!value) {
    debtorOptions.value = []
    return
  }
  
  // 模拟搜索建议
  debtorOptions.value = [
    { value: '张三 - 138****1234' },
    { value: '李四 - 139****5678' },
    { value: '王五 - 137****9012' }
  ].filter(option => option.value.includes(value))
}

// 快速搜索
const handleQuickSearch = () => {
  console.log('执行快速搜索')
  handleSearch()
}

// 自动刷新
const autoRefresh = () => {
  console.log('自动刷新数据')
  nextRefreshTime.value = Date.now() + 5 * 60 * 1000
  loadData()
}

// 表格相关
const loading = ref(false)
const tableData = ref([])
const selectedRowKeys = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total) => `共 ${total} 条记录`
})

// 汇总数据
const summaryData = computed(() => {
  const data = tableData.value
  const totalAmount = data.reduce((sum, item) => sum + item.totalAmount, 0)
  const paidAmount = data.reduce((sum, item) => sum + item.paidAmount, 0)
  return {
    totalAmount,
    paidAmount,
    recoveryRate: totalAmount > 0 ? paidAmount / totalAmount : 0
  }
})

// 弹窗相关
const showCreateModal = ref(false)
const showAssignModal = ref(false)
const showColumnConfig = ref(false)
const showImportModal = ref(false)
const createLoading = ref(false)

// 新建表单
const createFormRef = ref()
const createForm = reactive({
  debtorName: '',
  idNumber: '',
  phone: '',
  clientId: undefined,
  productType: undefined,
  totalAmount: undefined,
  overdueDays: undefined,
  priority: 'medium',
  address: '',
  remark: ''
})

// 新建表单验证规则
const createRules = {
  debtorName: [
    { required: true, message: '请输入债务人姓名', trigger: 'blur' }
  ],
  idNumber: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/, message: '身份证号格式不正确', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
  ],
  clientId: [
    { required: true, message: '请选择委托方', trigger: 'change' }
  ],
  productType: [
    { required: true, message: '请选择产品类型', trigger: 'change' }
  ],
  totalAmount: [
    { required: true, message: '请输入债务金额', trigger: 'blur' }
  ],
  overdueDays: [
    { required: true, message: '请输入逾期天数', trigger: 'blur' }
  ]
}

// 分配表单
const assignForm = reactive({
  strategy: 'manual',
  collectorId: undefined,
  groupId: undefined,
  reason: ''
})

// 导入文件列表
const importFileList = ref([])

// 跟进相关
const showFollowModal = ref(false)
const showCaseDetailModal = ref(false)
const currentCase = ref(null)
const followForm = reactive({
  type: 'phone',
  result: '',
  content: '',
  promiseAmount: undefined,
  promiseDate: null,
  nextFollowDate: null,
  nextFollowRemark: ''
})

// 转移相关
const showTransferModal = ref(false)
const transferCases = ref([])
const transferForm = reactive({
  type: 'internal',
  targetCollectorId: undefined,
  reason: ''
})

// 还款计划相关
const showPaymentPlanModal = ref(false)
const showCreatePlanModal = ref(false)
const paymentPlans = ref([])
const planForm = reactive({
  type: 'full',
  periods: 3,
  amount: undefined,
  firstPaymentDate: null,
  remark: ''
})

// 还款计划列
const planColumns = [
  { title: '计划编号', dataIndex: 'planNo', key: 'planNo' },
  { title: '计划类型', dataIndex: 'type', key: 'type' },
  { title: '计划金额', dataIndex: 'amount', key: 'amount', customRender: ({ text }) => `¥${formatAmount(text)}` },
  { title: '已还金额', dataIndex: 'paidAmount', key: 'paidAmount', customRender: ({ text }) => `¥${formatAmount(text)}` },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '操作', key: 'action' }
]

// 跟进历史相关
const showHistoryModal = ref(false)
const followHistory = ref([])

// 关闭案件相关
const showCloseModal = ref(false)
const closeForm = reactive({
  reason: '',
  description: ''
})

// 列配置
const allColumns = [
  { title: '案件编号', key: 'caseNumber', dataIndex: 'caseNumber', width: 150, fixed: 'left' },
  { title: '债务人', key: 'debtorName', dataIndex: 'debtorName', width: 120 },
  { title: '债务金额', key: 'amount', dataIndex: 'totalAmount', width: 150, sorter: true },
  { title: '已还金额', key: 'paidAmount', dataIndex: 'paidAmount', width: 150 },
  { title: '逾期天数', key: 'overdueDays', dataIndex: 'overdueDays', width: 100, sorter: true },
  { title: '案件状态', key: 'status', dataIndex: 'status', width: 100 },
  { title: '优先级', key: 'priority', dataIndex: 'priority', width: 80 },
  { title: '催收员', key: 'collectorName', dataIndex: 'collectorName', width: 100 },
  { title: '委托方', key: 'clientName', dataIndex: 'clientName', width: 120 },
  { title: '产品类型', key: 'productType', dataIndex: 'productType', width: 100 },
  { title: '分案时间', key: 'assignTime', dataIndex: 'assignTime', width: 160, sorter: true },
  { title: '最后跟进', key: 'lastFollowUp', dataIndex: 'lastFollowUp', width: 150 },
  { title: '操作', key: 'action', width: 180, fixed: 'right' }
]

const checkedColumns = ref(allColumns.map(col => col.key))
const visibleColumns = computed(() => {
  return allColumns.filter(col => checkedColumns.value.includes(col.key))
})

// 模拟数据
const collectors = ref([
  { id: 1, name: '张三', caseCount: 45, online: true, avatar: null },
  { id: 2, name: '李四', caseCount: 38, online: true, avatar: null },
  { id: 3, name: '王五', caseCount: 52, online: false, avatar: null },
  { id: 4, name: '赵六', caseCount: 41, online: true, avatar: null }
])

const collectorGroups = ref([
  { id: 1, name: '催收一组', memberCount: 5 },
  { id: 2, name: '催收二组', memberCount: 6 },
  { id: 3, name: '催收三组', memberCount: 4 }
])

const clients = ref([
  { id: 1, name: '某某银行' },
  { id: 2, name: '某某消费金融' },
  { id: 3, name: '某某小贷公司' }
])

const regionOptions = ref([
  {
    value: 'beijing',
    label: '北京',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海',
    children: [
      { value: 'pudong', label: '浦东新区' },
      { value: 'huangpu', label: '黄浦区' }
    ]
  }
])

// 生成模拟数据
const generateMockData = () => {
  const statuses = ['new', 'processing', 'promised', 'partial', 'settled', 'closed']
  const priorities = ['urgent', 'high', 'medium', 'low']
  const productTypes = ['credit_card', 'personal_loan', 'car_loan', 'mortgage', 'other']
  
  return Array.from({ length: 100 }, (_, i) => ({
    id: i + 1,
    caseNumber: `CS202401${String(i + 1).padStart(4, '0')}`,
    debtorName: `债务人${i + 1}`,
    debtorPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
    totalAmount: Math.floor(Math.random() * 100000) + 10000,
    paidAmount: Math.floor(Math.random() * 50000),
    overdueDays: Math.floor(Math.random() * 365) + 1,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    priority: priorities[Math.floor(Math.random() * priorities.length)],
    collectorName: collectors.value[Math.floor(Math.random() * collectors.value.length)].name,
    clientName: clients.value[Math.floor(Math.random() * clients.value.length)].name,
    productType: productTypes[Math.floor(Math.random() * productTypes.length)],
    assignTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastFollowUp: Math.random() > 0.3 ? {
      type: ['电话', '短信', '外访'][Math.floor(Math.random() * 3)],
      time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    } : null
  }))
}

// 格式化金额
const formatAmount = (amount) => {
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    new: 'blue',
    processing: 'orange',
    promised: 'purple',
    partial: 'cyan',
    settled: 'green',
    closed: 'default'
  }
  return colors[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    new: '新案件',
    processing: '处理中',
    promised: '已承诺',
    partial: '部分还款',
    settled: '已结清',
    closed: '已关闭'
  }
  return texts[status] || status
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'red',
    high: 'orange',
    medium: 'blue',
    low: 'default'
  }
  return colors[priority] || 'default'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

// 获取产品类型文本
const getProductTypeText = (type) => {
  const texts = {
    credit_card: '信用卡',
    personal_loan: '个人贷款',
    car_loan: '车贷',
    mortgage: '房贷',
    other: '其他'
  }
  return texts[type] || type
}

// 搜索
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    const mockData = generateMockData()
    // 简单的过滤逻辑
    let filteredData = mockData
    
    if (searchForm.caseNumber) {
      filteredData = filteredData.filter(item => 
        item.caseNumber.includes(searchForm.caseNumber)
      )
    }
    
    if (searchForm.debtorName) {
      filteredData = filteredData.filter(item => 
        item.debtorName.includes(searchForm.debtorName) || 
        item.debtorPhone.includes(searchForm.debtorName)
      )
    }
    
    if (searchForm.status) {
      filteredData = filteredData.filter(item => item.status === searchForm.status)
    }
    
    // 分页
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    
    tableData.value = filteredData.slice(start, end)
    pagination.total = filteredData.length
    
    loading.value = false
    message.success('查询成功')
  }, 1000)
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  handleSearch()
}

// 表格变化
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

// 选择变化
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

// 查看详情
const viewDetail = (record) => {
  currentCase.value = {
    ...record,
    debtorGender: '男',
    debtorAge: 35,
    debtorEmail: '<EMAIL>',
    debtorAddress: '北京市朝阳区某某街道某某小区',
    debtorOccupation: '软件工程师',
    debtorCompany: '某某科技有限公司',
    debtorIncome: 15000,
    productDetails: {
      type: '信用卡',
      accountNumber: '6222****1234',
      creditLimit: 50000,
      overdueAmount: record.totalAmount - record.paidAmount,
      interestRate: 0.05,
      penaltyRate: 0.15
    },
    collectionInfo: {
      assignDate: '2024-01-10',
      firstContactDate: '2024-01-12',
      lastContactDate: record.lastFollowUp?.time || '2024-01-18',
      contactCount: record.followCount || 8,
      promiseCount: 3,
      visitCount: 1
    },
    legalInfo: {
      hasCourt: false,
      hasAssetFreeze: false,
      hasGuarantee: true,
      guaranteeName: '李某某',
      guaranteePhone: '139****5678'
    },
    riskAssessment: {
      paymentWillingness: '中等',
      paymentAbility: '一般',
      contactStability: '稳定',
      assetStatus: '有房产',
      riskLevel: '中风险'
    }
  }
  showCaseDetailModal.value = true
}

// 跟进
const handleFollow = (record) => {
  currentCase.value = record
  // 重置表单
  Object.assign(followForm, {
    type: 'phone',
    result: '',
    content: '',
    promiseAmount: undefined,
    promiseDate: null,
    nextFollowDate: null,
    nextFollowRemark: ''
  })
  showFollowModal.value = true
}

// 确认跟进
const handleFollowConfirm = () => {
  if (!followForm.result || !followForm.content) {
    message.error('请填写必填项')
    return
  }
  
  message.success('跟进记录保存成功')
  showFollowModal.value = false
  handleSearch()
}

// 操作
const handleAction = (action, record) => {
  const actions = {
    detail: () => viewDetail(record),
    assign: () => {
      selectedRowKeys.value = [record.id]
      showAssignModal.value = true
    },
    transfer: () => {
      currentCase.value = record
      transferCases.value = [record]
      transferForm.type = 'internal'
      transferForm.targetCollectorId = undefined
      transferForm.reason = ''
      showTransferModal.value = true
    },
    plan: () => {
      currentCase.value = record
      loadPaymentPlans(record.id)
      showPaymentPlanModal.value = true
    },
    history: () => {
      currentCase.value = record
      loadFollowHistory(record.id)
      showHistoryModal.value = true
    },
    close: () => {
      currentCase.value = record
      closeForm.reason = ''
      closeForm.description = ''
      showCloseModal.value = true
    }
  }
  
  actions[action]?.()
}

// 加载还款计划
const loadPaymentPlans = (caseId) => {
  // 模拟数据
  paymentPlans.value = [
    {
      planNo: 'PP202401001',
      type: '分期还款',
      amount: 30000,
      paidAmount: 10000,
      status: 'active',
      createTime: '2024-01-15 10:00:00'
    },
    {
      planNo: 'PP202401002',
      type: '减免还款',
      amount: 20000,
      paidAmount: 20000,
      status: 'completed',
      createTime: '2024-01-10 14:00:00'
    }
  ]
}

// 加载跟进历史
const loadFollowHistory = (caseId) => {
  // 模拟数据
  followHistory.value = [
    {
      type: '电话',
      result: '承诺还款',
      content: '客户承诺月底前还款10000元',
      promiseAmount: 10000,
      promiseDate: '2024-01-31',
      time: '2024-01-20 14:30:00',
      operator: '张三'
    },
    {
      type: '短信',
      result: '已发送',
      content: '发送还款提醒短信',
      time: '2024-01-18 10:00:00',
      operator: '张三'
    },
    {
      type: '电话',
      result: '无人接听',
      content: '多次拨打无人接听',
      time: '2024-01-15 15:20:00',
      operator: '张三'
    }
  ]
}

// 获取时间线颜色
const getTimelineColor = (type) => {
  const colors = {
    '电话': 'blue',
    '短信': 'green',
    '外访': 'orange',
    '信函': 'purple',
    '邮件': 'cyan'
  }
  return colors[type] || 'gray'
}

// 获取时间线图标
const getTimelineIcon = (type) => {
  const icons = {
    '电话': PhoneOutlined,
    '短信': MessageOutlined,
    '外访': CarOutlined,
    '信函': MailOutlined,
    '邮件': MailOutlined
  }
  return icons[type] || InfoCircleOutlined
}

// 获取计划状态颜色
const getPlanStatusColor = (status) => {
  const colors = {
    active: 'processing',
    completed: 'success',
    overdue: 'error',
    cancelled: 'default'
  }
  return colors[status] || 'default'
}

// 获取计划状态文本
const getPlanStatusText = (status) => {
  const texts = {
    active: '执行中',
    completed: '已完成',
    overdue: '已逾期',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 确认转移
const handleTransferConfirm = () => {
  if (!transferForm.targetCollectorId || !transferForm.reason) {
    message.error('请填写必填项')
    return
  }
  
  message.success(`成功转移 ${transferCases.value.length} 个案件`)
  showTransferModal.value = false
  selectedRowKeys.value = []
  handleSearch()
}

// 创建还款计划
const handleCreatePlan = () => {
  if (!planForm.amount || !planForm.firstPaymentDate) {
    message.error('请填写必填项')
    return
  }
  
  message.success('还款计划创建成功')
  showCreatePlanModal.value = false
  loadPaymentPlans(currentCase.value.id)
}

// 查看计划详情
const viewPlanDetail = (plan) => {
  message.info(`查看计划详情: ${plan.planNo}`)
}

// 记录还款
const recordPayment = (plan) => {
  message.info(`记录还款: ${plan.planNo}`)
}

// 关闭案件
const handleCloseCase = () => {
  if (!closeForm.reason || !closeForm.description) {
    message.error('请填写必填项')
    return
  }
  
  message.success('案件已关闭')
  showCloseModal.value = false
  handleSearch()
}

// 批量操作
const handleBatchAction = ({ key }) => {
  if (key === 'assign') {
    showAssignModal.value = true
  } else if (key === 'transfer') {
    const selectedCases = tableData.value.filter(item => selectedRowKeys.value.includes(item.id))
    transferCases.value = selectedCases
    transferForm.type = 'internal'
    transferForm.targetCollectorId = undefined
    transferForm.reason = ''
    showTransferModal.value = true
  } else if (key === 'close') {
    const modal = Modal.confirm({
      title: '批量关闭案件',
      content: `确定要关闭选中的 ${selectedRowKeys.value.length} 个案件吗？`,
      onOk: () => {
        message.success(`成功关闭 ${selectedRowKeys.value.length} 个案件`)
        selectedRowKeys.value = []
        handleSearch()
      }
    })
  } else if (key === 'export') {
    handleBatchExport()
  }
}

// 创建案件
const handleCreate = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true
    
    setTimeout(() => {
      message.success('案件创建成功')
      showCreateModal.value = false
      createLoading.value = false
      handleSearch()
      
      // 重置表单
      Object.keys(createForm).forEach(key => {
        createForm[key] = key === 'priority' ? 'medium' : ''
      })
    }, 1500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 批量分配
const handleBatchAssign = () => {
  if (assignForm.strategy === 'manual' && !assignForm.collectorId) {
    message.error('请选择催收员')
    return
  }
  
  if (assignForm.strategy !== 'manual' && !assignForm.groupId) {
    message.error('请选择催收组')
    return
  }
  
  message.success(`成功分配 ${selectedRowKeys.value.length} 个案件`)
  showAssignModal.value = false
  selectedRowKeys.value = []
  handleSearch()
}

// 导入
const handleImport = () => {
  showImportModal.value = true
}

// 导入确认
const handleImportConfirm = () => {
  if (importFileList.value.length === 0) {
    message.error('请选择要导入的文件')
    return
  }
  
  message.success('文件导入成功')
  showImportModal.value = false
  importFileList.value = []
  handleSearch()
}

// 上传前检查
const beforeUpload = (file) => {
  const isValidType = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'].includes(file.type)
  if (!isValidType) {
    message.error('只能上传 Excel 或 CSV 文件')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB')
    return false
  }
  
  return false // 阻止自动上传
}

// 下载模板
const downloadTemplate = () => {
  message.success('模板下载成功')
}

// 导出选中
const handleBatchExport = () => {
  message.success(`导出 ${selectedRowKeys.value.length} 条记录`)
}

// 导出全部
const handleExportAll = () => {
  message.success('正在导出全部数据...')
}

// 列配置确认
const handleColumnConfig = () => {
  showColumnConfig.value = false
  message.success('列配置已保存')
}

// 筛选选项
const filterOption = (input, option) => {
  return option.children[0].children.toLowerCase().includes(input.toLowerCase())
}

// 获取风险等级颜色
const getRiskLevelColor = (riskLevel) => {
  const colors = {
    '低风险': 'green',
    '中风险': 'orange', 
    '高风险': 'red',
    '极高风险': 'purple'
  }
  return colors[riskLevel] || 'default'
}

// 初始化
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.case-list {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

/* 增强搜索区域样式 */
.search-card {
  margin-bottom: 16px;
  
  &.enhanced-search {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: none;
    
    :deep(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #f6f8ff 0%, #e6f7ff 100%);
      
      .ant-card-head-title {
        padding: 16px 0;
      }
    }
  }
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .search-stats {
    font-size: 12px;
    color: #666;
    
    :deep(.ant-statistic-title) {
      font-size: 11px;
      color: #999;
    }
    
    :deep(.ant-statistic-content) {
      font-size: 14px;
      color: #1890ff;
    }
  }
}

.collector-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

/* 数据表格区域样式 */
.data-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 危险文本样式 */
.text-danger {
  color: #ff4d4f;
  font-weight: 600;
}

/* 时间线样式 */
.timeline-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #262626;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}

.timeline-body {
  color: #666;
  margin-bottom: 8px;
}

.timeline-body p {
  margin: 0;
}

.timeline-extra {
  background: #e6f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #1890ff;
  margin-top: 8px;
  display: inline-block;
}

.timeline-footer {
  font-size: 12px;
  color: #999;
}

/* 自定义滚动条 */
:deep(.ant-table-body) {
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 #f0f0f0;
}

:deep(.ant-table-body::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.ant-table-body::-webkit-scrollbar-track) {
  background: #f0f0f0;
  border-radius: 4px;
}

:deep(.ant-table-body::-webkit-scrollbar-thumb) {
  background: #d9d9d9;
  border-radius: 4px;
}

:deep(.ant-table-body::-webkit-scrollbar-thumb:hover) {
  background: #bfbfbf;
}

/* 表格行悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background: #fafafa;
}

/* 表格汇总行样式 */
:deep(.ant-table-summary) {
  background: #fafafa;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.ant-col) {
    width: 100% !important;
  }
}
</style>