<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>案件转移</h2>
      </div>

      <!-- 搜索区域 -->
      <a-card class="search-card">
        <a-form :model="searchParams" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input 
                  v-model:value="searchParams.caseNo" 
                  placeholder="请输入案件编号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="债务人">
                <a-input 
                  v-model:value="searchParams.debtor" 
                  placeholder="请输入债务人姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="当前催收员">
                <a-select 
                  v-model:value="searchParams.currentCollector"
                  placeholder="请选择催收员"
                  allow-clear
                >
                  <a-select-option value="1">李催收</a-select-option>
                  <a-select-option value="2">王催收</a-select-option>
                  <a-select-option value="3">赵催收</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="转移状态">
                <a-select 
                  v-model:value="searchParams.transferStatus"
                  placeholder="请选择状态"
                  allow-clear
                >
                  <a-select-option value="pending">待转移</a-select-option>
                  <a-select-option value="processing">转移中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <div class="search-buttons">
                  <a-space>
                    <a-button type="primary" html-type="submit">
                      <search-outlined />
                      查询
                    </a-button>
                    <a-button @click="handleReset">
                      <reload-outlined />
                      重置
                    </a-button>
                  </a-space>
                </div>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="batchTransfer" :disabled="selectedRowKeys.length === 0">
                      <swap-outlined />
                      批量转移
                    </a-button>
                    <a-button @click="exportData">
                      <download-outlined />
                      导出数据
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 转移类型选择 -->
      <a-card title="转移类型" class="transfer-type-card">
        <a-radio-group v-model:value="transferType" button-style="solid">
          <a-radio-button value="internal">内部转移</a-radio-button>
          <a-radio-button value="department">跨部门转移</a-radio-button>
          <a-radio-button value="upgrade">升级转移</a-radio-button>
          <a-radio-button value="batch">批量转移</a-radio-button>
        </a-radio-group>
        
        <a-alert 
          :message="transferTypeInfo[transferType].title"
          :description="transferTypeInfo[transferType].description"
          :type="transferTypeInfo[transferType].type"
          show-icon
          style="margin-top: 16px"
        />
      </a-card>

      <!-- 案件选择 -->
      <a-card title="选择案件" class="case-select-card">
        <template #extra>
          <span>已选择 {{ selectedRowKeys.length }} 个案件</span>
        </template>
        
        <a-table
          :columns="caseColumns"
          :data-source="caseList"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: getCheckboxProps
          }"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true
          }"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'caseNo'">
              <a @click="viewCaseDetail(record)">{{ record.caseNo }}</a>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.statusText }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'holdDays'">
              <span :style="{ color: record.holdDays > 30 ? '#ff4d4f' : '' }">
                {{ record.holdDays }}天
              </span>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 转移目标选择 -->
      <a-card title="转移目标" class="transfer-target-card" v-if="selectedRowKeys.length > 0">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form :model="transferForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-item label="目标部门" v-if="['department', 'upgrade'].includes(transferType)">
                <a-select v-model:value="transferForm.targetDepartment" placeholder="请选择部门">
                  <a-select-option value="1">催收一部</a-select-option>
                  <a-select-option value="2">催收二部</a-select-option>
                  <a-select-option value="3">法务部</a-select-option>
                  <a-select-option value="4">委外部</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="目标催收员" required>
                <a-select 
                  v-model:value="transferForm.targetCollector" 
                  placeholder="请选择催收员"
                  @change="onCollectorChange"
                >
                  <a-select-option 
                    v-for="collector in availableCollectors" 
                    :key="collector.id"
                    :value="collector.id"
                  >
                    {{ collector.name }} (当前{{ collector.caseCount }}件)
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="转移原因" required>
                <a-select v-model:value="transferForm.reason" placeholder="请选择转移原因">
                  <a-select-option value="workload">工作量调整</a-select-option>
                  <a-select-option value="skill">技能匹配</a-select-option>
                  <a-select-option value="performance">绩效考虑</a-select-option>
                  <a-select-option value="leave">人员离职/请假</a-select-option>
                  <a-select-option value="upgrade">案件升级</a-select-option>
                  <a-select-option value="other">其他原因</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="转移说明" required>
                <a-textarea 
                  v-model:value="transferForm.remark" 
                  :rows="4"
                  placeholder="请详细说明转移原因和注意事项..."
                />
              </a-form-item>
              
              <a-form-item label="生效时间">
                <a-radio-group v-model:value="transferForm.effectiveTime">
                  <a-radio value="immediate">立即生效</a-radio>
                  <a-radio value="scheduled">定时生效</a-radio>
                </a-radio-group>
              </a-form-item>
              
              <a-form-item 
                label="生效日期" 
                v-if="transferForm.effectiveTime === 'scheduled'"
              >
                <a-date-picker 
                  v-model:value="transferForm.scheduledDate"
                  :disabled-date="disabledDate"
                  show-time
                  style="width: 100%"
                />
              </a-form-item>
            </a-form>
          </a-col>
          
          <a-col :span="12">
            <!-- 目标催收员信息 -->
            <div v-if="selectedCollector" class="collector-info">
              <h4>催收员信息</h4>
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="姓名">{{ selectedCollector.name }}</a-descriptions-item>
                <a-descriptions-item label="部门">{{ selectedCollector.department }}</a-descriptions-item>
                <a-descriptions-item label="当前案件数">{{ selectedCollector.caseCount }}</a-descriptions-item>
                <a-descriptions-item label="成功率">{{ selectedCollector.successRate }}%</a-descriptions-item>
                <a-descriptions-item label="专长">{{ selectedCollector.skills.join('、') }}</a-descriptions-item>
              </a-descriptions>
              
              <div class="workload-chart">
                <h5>工作量分析</h5>
                <a-progress 
                  :percent="(selectedCollector.caseCount / selectedCollector.maxCases) * 100"
                  :stroke-color="getWorkloadColor(selectedCollector.caseCount, selectedCollector.maxCases)"
                />
                <p>工作饱和度：{{ ((selectedCollector.caseCount / selectedCollector.maxCases) * 100).toFixed(1) }}%</p>
              </div>
            </div>
          </a-col>
        </a-row>
        
        <!-- 操作按钮 -->
        <div class="transfer-actions">
          <a-button type="primary" @click="showConfirmModal">
            <check-circle-outlined />
            确认转移
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
        </div>
      </a-card>

      <!-- 转移历史 -->
      <a-card title="转移历史" class="history-card">
        <a-table
          :columns="historyColumns"
          :data-source="transferHistory"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'completed' ? 'green' : 'orange'">
                {{ record.status === 'completed' ? '已完成' : '待生效' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button 
                type="link" 
                size="small" 
                @click="viewTransferDetail(record)"
              >
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 确认转移弹窗 -->
      <a-modal
        v-model:open="confirmModalVisible"
        title="确认转移"
        width="600px"
        @ok="handleConfirmTransfer"
      >
        <a-alert
          message="转移确认"
          :description="`您确定要将 ${selectedRowKeys.length} 个案件转移给 ${selectedCollector?.name} 吗？`"
          type="warning"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="转移类型">{{ transferTypeInfo[transferType].title }}</a-descriptions-item>
          <a-descriptions-item label="案件数量">{{ selectedRowKeys.length }}个</a-descriptions-item>
          <a-descriptions-item label="目标催收员">{{ selectedCollector?.name }}</a-descriptions-item>
          <a-descriptions-item label="转移原因">{{ getReasonText(transferForm.reason) }}</a-descriptions-item>
          <a-descriptions-item label="生效时间">
            {{ transferForm.effectiveTime === 'immediate' ? '立即生效' : transferForm.scheduledDate }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-checkbox v-model:checked="confirmChecked" style="margin-top: 16px">
          我已确认以上信息无误，同意进行案件转移
        </a-checkbox>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { 
  SearchOutlined, 
  ReloadOutlined,
  CheckCircleOutlined,
  SwapOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 状态管理
const loading = ref(false)
const selectedRowKeys = ref([])
const transferType = ref('internal')
const confirmModalVisible = ref(false)
const confirmChecked = ref(false)

// 搜索参数
const searchParams = reactive({
  caseNo: '',
  debtor: '',
  currentCollector: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50
})

// 转移表单
const transferForm = reactive({
  targetDepartment: undefined,
  targetCollector: undefined,
  reason: undefined,
  remark: '',
  effectiveTime: 'immediate',
  scheduledDate: undefined
})

// 转移类型信息
const transferTypeInfo = {
  internal: {
    title: '内部转移',
    description: '在同一部门内进行案件转移，适用于日常工作量调整。',
    type: 'info'
  },
  department: {
    title: '跨部门转移',
    description: '在不同部门之间进行案件转移，需要部门主管审批。',
    type: 'warning'
  },
  upgrade: {
    title: '升级转移',
    description: '将疑难案件升级到专业部门处理，如法务部或委外部。',
    type: 'error'
  },
  batch: {
    title: '批量转移',
    description: '批量处理多个案件的转移，提高操作效率。',
    type: 'success'
  }
}

// 表格列配置
const caseColumns = [
  {
    title: '案件编号',
    dataIndex: 'caseNo',
    key: 'caseNo',
    width: 120
  },
  {
    title: '债务人',
    dataIndex: 'debtorName',
    key: 'debtorName',
    width: 100
  },
  {
    title: '债务金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }) => `¥${text.toLocaleString()}`
  },
  {
    title: '当前催收员',
    dataIndex: 'currentCollector',
    key: 'currentCollector',
    width: 100
  },
  {
    title: '持有天数',
    dataIndex: 'holdDays',
    key: 'holdDays',
    width: 100
  },
  {
    title: '案件状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '最后跟进',
    dataIndex: 'lastFollowTime',
    key: 'lastFollowTime',
    width: 150
  }
]

const historyColumns = [
  {
    title: '转移时间',
    dataIndex: 'transferTime',
    key: 'transferTime',
    width: 150
  },
  {
    title: '案件数量',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 100
  },
  {
    title: '原催收员',
    dataIndex: 'fromCollector',
    key: 'fromCollector',
    width: 100
  },
  {
    title: '目标催收员',
    dataIndex: 'toCollector',
    key: 'toCollector',
    width: 100
  },
  {
    title: '转移原因',
    dataIndex: 'reason',
    key: 'reason',
    width: 120
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

// 模拟数据
const caseList = ref([
  {
    key: '1',
    caseNo: 'CS202401001',
    debtorName: '张三',
    amount: 50000,
    currentCollector: '李催收',
    holdDays: 15,
    status: 'processing',
    statusText: '处理中',
    lastFollowTime: '2024-01-18 14:30'
  },
  {
    key: '2',
    caseNo: 'CS202401002',
    debtorName: '李四',
    amount: 120000,
    currentCollector: '李催收',
    holdDays: 30,
    status: 'difficult',
    statusText: '疑难',
    lastFollowTime: '2024-01-15 09:20'
  },
  {
    key: '3',
    caseNo: 'CS202401003',
    debtorName: '王五',
    amount: 35000,
    currentCollector: '王催收',
    holdDays: 45,
    status: 'processing',
    statusText: '处理中',
    lastFollowTime: '2024-01-10 16:45'
  }
])

const availableCollectors = ref([
  {
    id: '1',
    name: '赵催收',
    department: '催收一部',
    caseCount: 38,
    maxCases: 50,
    successRate: 85.5,
    skills: ['电话催收', '大额案件']
  },
  {
    id: '2',
    name: '钱催收',
    department: '催收一部',
    caseCount: 42,
    maxCases: 50,
    successRate: 78.3,
    skills: ['外访', '小额案件']
  },
  {
    id: '3',
    name: '孙催收',
    department: '催收二部',
    caseCount: 25,
    maxCases: 50,
    successRate: 92.1,
    skills: ['法务', '疑难案件']
  }
])

const transferHistory = ref([
  {
    key: '1',
    transferTime: '2024-01-18 10:30',
    caseCount: 5,
    fromCollector: '李催收',
    toCollector: '王催收',
    reason: '工作量调整',
    operator: '张主管',
    status: 'completed'
  },
  {
    key: '2',
    transferTime: '2024-01-17 15:20',
    caseCount: 10,
    fromCollector: '王催收',
    toCollector: '赵催收',
    reason: '技能匹配',
    operator: '李经理',
    status: 'completed'
  },
  {
    key: '3',
    transferTime: '2024-01-19 09:00',
    caseCount: 3,
    fromCollector: '赵催收',
    toCollector: '法务部',
    reason: '案件升级',
    operator: '王主管',
    status: 'pending'
  }
])

// 计算属性
const selectedCollector = computed(() => {
  if (!transferForm.targetCollector) return null
  return availableCollectors.value.find(c => c.id === transferForm.targetCollector)
})

// 方法
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

const getCheckboxProps = (record) => ({
  disabled: record.status === 'closed',
})

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('查询成功')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = undefined
  })
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const viewCaseDetail = (record) => {
  console.log('查看案件详情', record)
}

const onCollectorChange = (value) => {
  console.log('选择催收员', value)
}

const showConfirmModal = () => {
  if (!transferForm.targetCollector) {
    message.warning('请选择目标催收员')
    return
  }
  if (!transferForm.reason) {
    message.warning('请选择转移原因')
    return
  }
  if (!transferForm.remark) {
    message.warning('请填写转移说明')
    return
  }
  
  confirmModalVisible.value = true
  confirmChecked.value = false
}

const handleConfirmTransfer = () => {
  if (!confirmChecked.value) {
    message.warning('请确认转移信息')
    return
  }
  
  message.loading('正在处理转移...')
  setTimeout(() => {
    message.success('案件转移成功')
    confirmModalVisible.value = false
    selectedRowKeys.value = []
    // 重置表单
    Object.keys(transferForm).forEach(key => {
      if (key === 'effectiveTime') {
        transferForm[key] = 'immediate'
      } else {
        transferForm[key] = undefined
      }
    })
  }, 2000)
}

const handleCancel = () => {
  selectedRowKeys.value = []
}

const viewTransferDetail = (record) => {
  console.log('查看转移详情', record)
}

const disabledDate = (current) => {
  return current && current < dayjs().startOf('day')
}

const getStatusColor = (status) => {
  const colors = {
    processing: 'blue',
    difficult: 'orange',
    closed: 'green',
    legal: 'purple'
  }
  return colors[status] || 'default'
}

const getWorkloadColor = (current, max) => {
  const ratio = current / max
  if (ratio >= 0.9) return '#ff4d4f'
  if (ratio >= 0.7) return '#faad14'
  return '#52c41a'
}

const getReasonText = (reason) => {
  const reasons = {
    workload: '工作量调整',
    skill: '技能匹配',
    performance: '绩效考虑',
    leave: '人员离职/请假',
    upgrade: '案件升级',
    other: '其他原因'
  }
  return reasons[reason] || reason
}

const batchTransfer = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要转移的案件')
    return
  }
  message.info('批量转移功能')
}

const exportData = () => {
  message.info('导出数据功能')
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  
  .search-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.transfer-type-card,
.case-select-card,
.transfer-target-card,
.history-card {
  margin-bottom: 16px;
}

.transfer-target-card {
  .collector-info {
    padding: 16px;
    background: #f5f5f5;
    border-radius: 8px;
    
    h4 {
      margin-top: 0;
      margin-bottom: 16px;
    }
    
    .workload-chart {
      margin-top: 16px;
      
      h5 {
        margin-bottom: 8px;
      }
      
      p {
        margin-top: 8px;
        color: #666;
      }
    }
  }
  
  .transfer-actions {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
    
    .ant-btn {
      margin: 0 8px;
    }
  }
}
</style>