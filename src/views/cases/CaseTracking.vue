<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h2>案件跟进</h2>
        <div class="header-actions">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索案件编号或债务人姓名"
            style="width: 300px"
            @search="handleSearch"
          />
        </div>
      </div>

      <a-row :gutter="16">
        <!-- 左侧案件列表 -->
        <a-col :xs="24" :md="8" :lg="6">
          <a-card title="我的案件" class="case-list-card">
            <template #extra>
              <a-select v-model:value="filterStatus" style="width: 100px" @change="handleFilterChange">
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="urgent">紧急</a-select-option>
                <a-select-option value="today">今日待跟进</a-select-option>
                <a-select-option value="overdue">逾期未跟进</a-select-option>
              </a-select>
            </template>
            
            <a-list
              :data-source="filteredCases"
              :loading="loading"
              item-layout="horizontal"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item
                  :class="['case-item', { active: selectedCase?.key === item.key }]"
                  @click="selectCase(item)"
                >
                  <a-list-item-meta>
                    <template #title>
                      <div class="case-title">
                        <span>{{ item.caseNo }}</span>
                        <a-tag v-if="item.isUrgent" color="red" size="small">紧急</a-tag>
                      </div>
                    </template>
                    <template #description>
                      <div class="case-info">
                        <div>{{ item.debtorName }} - ¥{{ item.amount.toLocaleString() }}</div>
                        <div class="case-status">
                          <span :class="getOverdueClass(item.lastFollowDays)">
                            {{ item.lastFollowDays }}天未跟进
                          </span>
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <!-- 右侧案件详情和跟进记录 -->
        <a-col :xs="24" :md="16" :lg="18">
          <div v-if="selectedCase" class="case-detail">
            <!-- 案件基本信息 -->
            <a-card title="案件信息" class="info-card">
              <a-descriptions :column="{ xs: 1, sm: 2, md: 3 }">
                <a-descriptions-item label="案件编号">{{ selectedCase.caseNo }}</a-descriptions-item>
                <a-descriptions-item label="债务人">{{ selectedCase.debtorName }}</a-descriptions-item>
                <a-descriptions-item label="债务金额">¥{{ selectedCase.amount.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="逾期天数">{{ selectedCase.overdueDays }}天</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ selectedCase.phone }}</a-descriptions-item>
                <a-descriptions-item label="案件状态">
                  <a-tag :color="getStatusColor(selectedCase.status)">{{ selectedCase.statusText }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="上次跟进">{{ selectedCase.lastFollowTime }}</a-descriptions-item>
                <a-descriptions-item label="下次跟进">{{ selectedCase.nextFollowTime }}</a-descriptions-item>
                <a-descriptions-item label="跟进次数">{{ selectedCase.followCount }}次</a-descriptions-item>
              </a-descriptions>
              
              <!-- 快捷操作 -->
              <div class="quick-actions">
                <a-button type="primary" @click="showAddRecordModal = true">
                  <plus-outlined />
                  添加跟进记录
                </a-button>
                <a-button @click="makeCall">
                  <phone-outlined />
                  拨打电话
                </a-button>
                <a-button @click="sendSMS">
                  <message-outlined />
                  发送短信
                </a-button>
                <a-button @click="showPaymentPlanModal = true">
                  <calendar-outlined />
                  制定还款计划
                </a-button>
                <a-dropdown>
                  <a-button>
                    更多操作
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleMoreAction">
                      <a-menu-item key="transfer">转移案件</a-menu-item>
                      <a-menu-item key="upgrade">升级处理</a-menu-item>
                      <a-menu-item key="pause">暂停催收</a-menu-item>
                      <a-menu-item key="close">结案</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </a-card>

            <!-- 催收策略建议 -->
            <a-card title="催收策略建议" class="strategy-card">
              <a-alert
                :message="strategyAdvice.title"
                :description="strategyAdvice.description"
                :type="strategyAdvice.type"
                show-icon
              />
              <div class="strategy-tags">
                <a-tag v-for="tag in strategyAdvice.tags" :key="tag" color="blue">{{ tag }}</a-tag>
              </div>
            </a-card>

            <!-- 跟进记录时间线 -->
            <a-card title="跟进记录" class="timeline-card">
              <a-timeline mode="left">
                <a-timeline-item
                  v-for="record in followRecords"
                  :key="record.id"
                  :color="getRecordColor(record.type)"
                >
                  <template #dot>
                    <component :is="getRecordIcon(record.type)" />
                  </template>
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <span class="timeline-time">{{ record.time }}</span>
                      <a-tag size="small" :color="getRecordColor(record.type)">{{ record.typeText }}</a-tag>
                      <span class="timeline-operator">{{ record.operator }}</span>
                    </div>
                    <div class="timeline-body">
                      <p>{{ record.content }}</p>
                      <div v-if="record.result" class="timeline-result">
                        <strong>结果：</strong>{{ record.result }}
                      </div>
                      <div v-if="record.promise" class="timeline-promise">
                        <a-alert :message="`承诺还款：¥${record.promise.amount} - ${record.promise.date}`" type="success" />
                      </div>
                      <div v-if="record.attachments" class="timeline-attachments">
                        <a-space>
                          <a v-for="file in record.attachments" :key="file.id" :href="file.url">
                            <paper-clip-outlined /> {{ file.name }}
                          </a>
                        </a-space>
                      </div>
                    </div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </a-card>
          </div>
          
          <!-- 未选择案件提示 -->
          <div v-else class="empty-state">
            <a-empty description="请从左侧选择一个案件开始跟进" />
          </div>
        </a-col>
      </a-row>

      <!-- 添加跟进记录弹窗 -->
      <a-modal
        v-model:open="showAddRecordModal"
        title="添加跟进记录"
        width="700px"
        @ok="handleAddRecord"
      >
        <a-form :model="recordForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="跟进方式" required>
            <a-radio-group v-model:value="recordForm.type">
              <a-radio-button value="call">电话</a-radio-button>
              <a-radio-button value="sms">短信</a-radio-button>
              <a-radio-button value="visit">外访</a-radio-button>
              <a-radio-button value="email">邮件</a-radio-button>
              <a-radio-button value="other">其他</a-radio-button>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="联系结果" required>
            <a-select v-model:value="recordForm.result">
              <a-select-option value="connected">已接通</a-select-option>
              <a-select-option value="no_answer">无人接听</a-select-option>
              <a-select-option value="busy">忙线</a-select-option>
              <a-select-option value="refused">拒绝沟通</a-select-option>
              <a-select-option value="promise">承诺还款</a-select-option>
              <a-select-option value="partial">部分还款</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="跟进内容" required>
            <a-textarea 
              v-model:value="recordForm.content" 
              :rows="4"
              placeholder="请详细记录跟进情况..."
            />
          </a-form-item>
          
          <a-form-item v-if="recordForm.result === 'promise'" label="承诺还款">
            <a-space>
              <a-input-number 
                v-model:value="recordForm.promiseAmount"
                :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                placeholder="承诺金额"
                style="width: 150px"
              />
              <a-date-picker 
                v-model:value="recordForm.promiseDate"
                placeholder="承诺日期"
              />
            </a-space>
          </a-form-item>
          
          <a-form-item label="下次跟进">
            <a-space>
              <a-date-picker 
                v-model:value="recordForm.nextFollowDate"
                :disabled-date="disabledDate"
                placeholder="选择日期"
              />
              <a-time-picker 
                v-model:value="recordForm.nextFollowTime"
                format="HH:mm"
                placeholder="选择时间"
              />
            </a-space>
          </a-form-item>
          
          <a-form-item label="附件">
            <a-upload
              v-model:file-list="recordForm.fileList"
              :before-upload="beforeUpload"
            >
              <a-button>
                <upload-outlined />
                上传附件
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 制定还款计划弹窗 -->
      <a-modal
        v-model:open="showPaymentPlanModal"
        title="制定还款计划"
        width="600px"
        @ok="handleCreatePaymentPlan"
      >
        <a-form :model="paymentPlanForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="还款方式">
            <a-radio-group v-model:value="paymentPlanForm.type">
              <a-radio value="full">一次性还清</a-radio>
              <a-radio value="installment">分期还款</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <template v-if="paymentPlanForm.type === 'full'">
            <a-form-item label="还款金额">
              <a-input-number 
                v-model:value="paymentPlanForm.amount"
                :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="还款日期">
              <a-date-picker v-model:value="paymentPlanForm.date" style="width: 100%" />
            </a-form-item>
          </template>
          
          <template v-else>
            <a-form-item label="分期期数">
              <a-input-number v-model:value="paymentPlanForm.periods" :min="2" :max="24" />
            </a-form-item>
            <a-form-item label="每期金额">
              <a-input-number 
                v-model:value="paymentPlanForm.periodAmount"
                :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="首期还款日">
              <a-date-picker v-model:value="paymentPlanForm.firstDate" style="width: 100%" />
            </a-form-item>
          </template>
          
          <a-form-item label="备注">
            <a-textarea v-model:value="paymentPlanForm.remark" :rows="3" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  PlusOutlined, 
  PhoneOutlined, 
  MessageOutlined, 
  CalendarOutlined,
  DownOutlined,
  UploadOutlined,
  PaperClipOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 状态管理
const loading = ref(false)
const searchText = ref('')
const filterStatus = ref('all')
const selectedCase = ref(null)
const showAddRecordModal = ref(false)
const showPaymentPlanModal = ref(false)

// 案件列表数据
const cases = ref([
  {
    key: '1',
    caseNo: 'CS202401001',
    debtorName: '张三',
    amount: 50000,
    overdueDays: 30,
    status: 'processing',
    statusText: '跟进中',
    phone: '138****5678',
    lastFollowTime: '2024-01-18 14:30',
    nextFollowTime: '2024-01-20 10:00',
    followCount: 5,
    lastFollowDays: 2,
    isUrgent: true
  },
  {
    key: '2',
    caseNo: 'CS202401002',
    debtorName: '李四',
    amount: 120000,
    overdueDays: 60,
    status: 'processing',
    statusText: '跟进中',
    phone: '139****1234',
    lastFollowTime: '2024-01-15 09:20',
    nextFollowTime: '2024-01-19 14:00',
    followCount: 8,
    lastFollowDays: 5,
    isUrgent: false
  },
  {
    key: '3',
    caseNo: 'CS202401003',
    debtorName: '王五',
    amount: 35000,
    overdueDays: 90,
    status: 'difficult',
    statusText: '疑难',
    phone: '137****9876',
    lastFollowTime: '2024-01-10 16:45',
    nextFollowTime: '2024-01-19 09:00',
    followCount: 12,
    lastFollowDays: 10,
    isUrgent: false
  }
])

// 跟进记录数据
const followRecords = ref([
  {
    id: 1,
    type: 'call',
    typeText: '电话',
    time: '2024-01-18 14:30:00',
    operator: '李催收',
    content: '致电债务人，已接通。债务人表示目前资金困难，但有还款意愿。',
    result: '承诺还款',
    promise: {
      amount: 10000,
      date: '2024-01-25'
    }
  },
  {
    id: 2,
    type: 'sms',
    typeText: '短信',
    time: '2024-01-17 10:15:00',
    operator: '李催收',
    content: '发送催收短信，提醒债务人尽快还款。',
    result: '已发送'
  },
  {
    id: 3,
    type: 'call',
    typeText: '电话',
    time: '2024-01-15 16:20:00',
    operator: '李催收',
    content: '致电债务人，无人接听。',
    result: '无人接听'
  },
  {
    id: 4,
    type: 'visit',
    typeText: '外访',
    time: '2024-01-12 14:00:00',
    operator: '李催收',
    content: '上门拜访债务人住址，家中有人但债务人不在。已向家属说明情况。',
    result: '债务人不在',
    attachments: [
      { id: 1, name: '外访照片.jpg', url: '#' }
    ]
  }
])

// 催收策略建议
const strategyAdvice = computed(() => {
  if (!selectedCase.value) return {}
  
  const overdueDays = selectedCase.value.overdueDays
  const followCount = selectedCase.value.followCount
  
  if (overdueDays > 90) {
    return {
      type: 'error',
      title: '建议升级处理',
      description: '该案件逾期时间较长，建议考虑法务途径或委外处理。',
      tags: ['法务介入', '委外处理', '资产保全']
    }
  } else if (overdueDays > 60) {
    return {
      type: 'warning',
      title: '加强催收力度',
      description: '建议增加催收频次，可考虑上门外访或联系紧急联系人。',
      tags: ['外访', '紧急联系人', '增加频次']
    }
  } else if (followCount > 5) {
    return {
      type: 'info',
      title: '调整催收策略',
      description: '已多次跟进，建议调整沟通方式，了解债务人实际困难。',
      tags: ['协商还款', '分期方案', '减免政策']
    }
  } else {
    return {
      type: 'success',
      title: '正常催收',
      description: '继续保持当前催收节奏，密切关注债务人还款意愿。',
      tags: ['电话催收', '短信提醒', '还款承诺']
    }
  }
})

// 表单数据
const recordForm = reactive({
  type: 'call',
  result: 'connected',
  content: '',
  promiseAmount: undefined,
  promiseDate: undefined,
  nextFollowDate: undefined,
  nextFollowTime: undefined,
  fileList: []
})

const paymentPlanForm = reactive({
  type: 'full',
  amount: undefined,
  date: undefined,
  periods: 3,
  periodAmount: undefined,
  firstDate: undefined,
  remark: ''
})

// 计算过滤后的案件列表
const filteredCases = computed(() => {
  let result = cases.value
  
  // 搜索过滤
  if (searchText.value) {
    result = result.filter(item => 
      item.caseNo.includes(searchText.value) || 
      item.debtorName.includes(searchText.value)
    )
  }
  
  // 状态过滤
  switch (filterStatus.value) {
    case 'urgent':
      result = result.filter(item => item.isUrgent)
      break
    case 'today':
      result = result.filter(item => {
        const nextFollow = dayjs(item.nextFollowTime)
        return nextFollow.isSame(dayjs(), 'day')
      })
      break
    case 'overdue':
      result = result.filter(item => item.lastFollowDays > 3)
      break
  }
  
  return result
})

// 方法
const selectCase = (caseItem) => {
  selectedCase.value = caseItem
  // 加载该案件的跟进记录
  loadFollowRecords(caseItem.key)
}

const loadFollowRecords = (caseKey) => {
  // 模拟加载跟进记录
  console.log('加载案件跟进记录:', caseKey)
}

const handleSearch = () => {
  console.log('搜索:', searchText.value)
}

const handleFilterChange = () => {
  console.log('过滤条件变更:', filterStatus.value)
}

const makeCall = () => {
  message.info('正在拨打电话...')
}

const sendSMS = () => {
  message.info('打开短信发送界面')
}

const handleMoreAction = ({ key }) => {
  message.info(`执行操作: ${key}`)
}

const handleAddRecord = () => {
  console.log('添加跟进记录:', recordForm)
  message.success('跟进记录添加成功')
  showAddRecordModal.value = false
}

const handleCreatePaymentPlan = () => {
  console.log('创建还款计划:', paymentPlanForm)
  message.success('还款计划创建成功')
  showPaymentPlanModal.value = false
}

const beforeUpload = (file) => {
  recordForm.fileList.push(file)
  return false
}

const disabledDate = (current) => {
  return current && current < dayjs().startOf('day')
}

// 工具方法
const getStatusColor = (status) => {
  const colors = {
    processing: 'blue',
    difficult: 'orange',
    promise: 'green',
    legal: 'purple'
  }
  return colors[status] || 'default'
}

const getOverdueClass = (days) => {
  if (days > 7) return 'text-danger'
  if (days > 3) return 'text-warning'
  return 'text-normal'
}

const getRecordColor = (type) => {
  const colors = {
    call: 'blue',
    sms: 'green',
    visit: 'orange',
    email: 'purple',
    other: 'default'
  }
  return colors[type] || 'default'
}

const getRecordIcon = (type) => {
  const icons = {
    call: PhoneOutlined,
    sms: MessageOutlined,
    visit: ClockCircleOutlined,
    email: ExclamationCircleOutlined,
    other: CheckCircleOutlined
  }
  return icons[type] || CheckCircleOutlined
}

// 生命周期
onMounted(() => {
  // 默认选中第一个案件
  if (cases.value.length > 0) {
    selectCase(cases.value[0])
  }
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
  }
}

.case-list-card {
  height: calc(100vh - 180px);
  
  :deep(.ant-card-body) {
    height: calc(100% - 57px);
    overflow-y: auto;
    padding: 0;
  }
  
  .case-item {
    cursor: pointer;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s;
    
    &:hover {
      background: #fafafa;
    }
    
    &.active {
      background: #e6f7ff;
      border-left: 3px solid #1890ff;
    }
    
    .case-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
    
    .case-info {
      font-size: 12px;
      color: #666;
      
      .case-status {
        margin-top: 4px;
        
        .text-danger {
          color: #ff4d4f;
        }
        
        .text-warning {
          color: #faad14;
        }
        
        .text-normal {
          color: #52c41a;
        }
      }
    }
  }
}

.case-detail {
  .info-card {
    margin-bottom: 16px;
    
    .quick-actions {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      gap: 8px;
    }
  }
  
  .strategy-card {
    margin-bottom: 16px;
    
    .strategy-tags {
      margin-top: 12px;
      display: flex;
      gap: 8px;
    }
  }
  
  .timeline-card {
    .timeline-content {
      .timeline-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        .timeline-time {
          font-weight: 500;
        }
        
        .timeline-operator {
          margin-left: auto;
          color: #999;
          font-size: 12px;
        }
      }
      
      .timeline-body {
        p {
          margin: 0;
          color: #666;
        }
        
        .timeline-result {
          margin-top: 8px;
          padding: 8px;
          background: #f5f5f5;
          border-radius: 4px;
          font-size: 13px;
        }
        
        .timeline-promise {
          margin-top: 8px;
        }
        
        .timeline-attachments {
          margin-top: 8px;
          
          a {
            font-size: 13px;
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}
</style>