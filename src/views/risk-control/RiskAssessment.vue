<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>风险评估</h2>
      
      <!-- 统计概览 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="评估任务数" 
              :value="stats.totalTasks"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>今日新增 <span class="stat-change up">+{{ stats.todayNew }}</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="高风险案例" 
              :value="stats.highRisk" 
              suffix="个"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <WarningOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>占比 <span class="stat-percent">{{ stats.highRiskRatio }}%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均评估分" 
              :value="stats.avgScore" 
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>较上月 <span class="stat-change up">+2.1</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="评估准确率" 
              :value="stats.accuracy" 
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>AI预测 <span class="stat-ai">{{ stats.aiAccuracy }}%</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作区域 -->
      <a-card class="action-card">
        <div class="action-section">
          <div class="search-filters">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-input
                  v-model:value="searchForm.keyword"
                  placeholder="搜索客户姓名或案件编号"
                  allow-clear
                >
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="4">
                <a-select v-model:value="searchForm.riskLevel" placeholder="风险等级" allow-clear>
                  <a-select-option value="low">低风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="critical">极高风险</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-select v-model:value="searchForm.status" placeholder="评估状态" allow-clear>
                  <a-select-option value="pending">待评估</a-select-option>
                  <a-select-option value="in_progress">评估中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="reviewed">已审核</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  placeholder="选择时间范围"
                  style="width: 100%"
                />
              </a-col>
              <a-col :span="4">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>
          
          <div class="action-buttons">
            <a-button type="primary" @click="showCreateModal">
              <PlusOutlined />
              创建评估任务
            </a-button>
            <a-button @click="showBatchAssessModal">
              <ThunderboltOutlined />
              批量评估
            </a-button>
            <a-button @click="showModelConfigModal">
              <SettingOutlined />
              模型配置
            </a-button>
            <a-button @click="showReportModal">
              <BarChartOutlined />
              评估报告
            </a-button>
            <a-button @click="exportData">
              <DownloadOutlined />
              导出数据
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 风险评估列表 -->
      <a-card>
        <a-table 
          :columns="columns" 
          :data-source="assessmentList" 
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'customerInfo'">
              <div>
                <div class="customer-name">{{ record.customerName }}</div>
                <div class="customer-detail">{{ record.customerPhone }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'caseInfo'">
              <div>
                <div class="case-number">{{ record.caseNumber }}</div>
                <div class="case-amount">¥{{ record.amount?.toLocaleString() }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'riskScore'">
              <div class="risk-score-container">
                <a-progress 
                  type="circle" 
                  :percent="record.riskScore" 
                  :width="50"
                  :stroke-color="getRiskScoreColor(record.riskScore)"
                />
                <div class="score-text">{{ record.riskScore }}分</div>
              </div>
            </template>
            
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'factors'">
              <div class="risk-factors">
                <a-tag 
                  v-for="factor in record.riskFactors?.slice(0, 2)" 
                  :key="factor"
                  size="small"
                  color="orange"
                >
                  {{ factor }}
                </a-tag>
                <span v-if="record.riskFactors?.length > 2" class="more-factors">
                  +{{ record.riskFactors.length - 2 }}
                </span>
              </div>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">
                  <EyeOutlined />
                  详情
                </a-button>
                <a-button 
                  v-if="record.status === 'pending'"
                  type="link" 
                  size="small" 
                  @click="startAssessment(record)"
                >
                  <PlayCircleOutlined />
                  评估
                </a-button>
                <a-button 
                  v-if="record.status === 'completed'"
                  type="link" 
                  size="small" 
                  @click="reviewAssessment(record)"
                >
                  <AuditOutlined />
                  审核
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="showHistoryModal(record)">
                        <HistoryOutlined />
                        评估历史
                      </a-menu-item>
                      <a-menu-item @click="generateReport(record)">
                        <FileTextOutlined />
                        生成报告
                      </a-menu-item>
                      <a-menu-item @click="reAssess(record)">
                        <ReloadOutlined />
                        重新评估
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 风险分析图表 -->
      <a-row :gutter="16" class="charts-section">
        <a-col :span="12">
          <a-card title="风险等级分布" :bordered="false">
            <div id="risk-distribution-chart" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="评估趋势分析" :bordered="false">
            <div id="assessment-trend-chart" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 创建评估任务弹窗 -->
      <a-modal
        v-model:open="createModalVisible"
        title="创建风险评估任务"
        width="800px"
        @ok="handleCreateSubmit"
        @cancel="createModalVisible = false"
      >
        <a-form ref="createFormRef" :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="客户信息" name="customerId" :rules="[{ required: true, message: '请选择客户' }]">
            <a-select
              v-model:value="createForm.customerId"
              placeholder="请选择客户"
              show-search
              :filter-option="filterCustomerOption"
              @change="onCustomerSelect"
            >
              <a-select-option v-for="customer in customers" :key="customer.id" :value="customer.id">
                {{ customer.name }} - {{ customer.phone }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="关联案件" name="caseId" :rules="[{ required: true, message: '请选择案件' }]">
            <a-select
              v-model:value="createForm.caseId"
              placeholder="请选择案件"
              :disabled="!createForm.customerId"
            >
              <a-select-option v-for="caseItem in customerCases" :key="caseItem.id" :value="caseItem.id">
                {{ caseItem.caseNumber }} - ¥{{ caseItem.amount.toLocaleString() }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="评估类型" name="assessmentType" :rules="[{ required: true, message: '请选择评估类型' }]">
            <a-select v-model:value="createForm.assessmentType" placeholder="请选择评估类型">
              <a-select-option value="initial">初始评估</a-select-option>
              <a-select-option value="periodic">定期评估</a-select-option>
              <a-select-option value="special">特殊评估</a-select-option>
              <a-select-option value="emergency">紧急评估</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="评估模型" name="modelId" :rules="[{ required: true, message: '请选择评估模型' }]">
            <a-select v-model:value="createForm.modelId" placeholder="请选择评估模型">
              <a-select-option value="default">标准评估模型</a-select-option>
              <a-select-option value="advanced">高级评估模型</a-select-option>
              <a-select-option value="ai">AI智能模型</a-select-option>
              <a-select-option value="custom">自定义模型</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="评估人员" name="assessorId">
            <a-select v-model:value="createForm.assessorId" placeholder="请选择评估人员">
              <a-select-option value="auto">自动分配</a-select-option>
              <a-select-option value="user1">张评估员</a-select-option>
              <a-select-option value="user2">李评估员</a-select-option>
              <a-select-option value="user3">王评估员</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="优先级" name="priority" :rules="[{ required: true, message: '请选择优先级' }]">
            <a-radio-group v-model:value="createForm.priority">
              <a-radio value="low">低</a-radio>
              <a-radio value="medium">中</a-radio>
              <a-radio value="high">高</a-radio>
              <a-radio value="urgent">紧急</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="备注说明" name="remark">
            <a-textarea v-model:value="createForm.remark" :rows="3" placeholder="请输入备注说明" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 评估详情弹窗 -->
      <a-modal
        v-model:open="detailModalVisible"
        title="风险评估详情"
        width="1200px"
        :footer="null"
      >
        <div v-if="currentAssessment">
          <a-tabs>
            <a-tab-pane key="basic" tab="基本信息">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-descriptions title="客户信息" bordered size="small">
                    <a-descriptions-item label="客户姓名">{{ currentAssessment.customerName }}</a-descriptions-item>
                    <a-descriptions-item label="联系电话">{{ currentAssessment.customerPhone }}</a-descriptions-item>
                    <a-descriptions-item label="案件编号">{{ currentAssessment.caseNumber }}</a-descriptions-item>
                    <a-descriptions-item label="案件金额">¥{{ currentAssessment.amount?.toLocaleString() }}</a-descriptions-item>
                  </a-descriptions>
                </a-col>
                <a-col :span="12">
                  <a-descriptions title="评估信息" bordered size="small">
                    <a-descriptions-item label="风险评分">
                      <a-progress 
                        :percent="currentAssessment.riskScore" 
                        :stroke-color="getRiskScoreColor(currentAssessment.riskScore)"
                      />
                    </a-descriptions-item>
                    <a-descriptions-item label="风险等级">
                      <a-tag :color="getRiskLevelColor(currentAssessment.riskLevel)">
                        {{ getRiskLevelText(currentAssessment.riskLevel) }}
                      </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="评估状态">
                      <a-tag :color="getStatusColor(currentAssessment.status)">
                        {{ getStatusText(currentAssessment.status) }}
                      </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="评估时间">{{ currentAssessment.assessmentDate }}</a-descriptions-item>
                  </a-descriptions>
                </a-col>
              </a-row>
            </a-tab-pane>
            
            <a-tab-pane key="factors" tab="风险因子">
              <a-table
                :columns="factorColumns"
                :data-source="currentAssessment.riskFactorDetails"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'weight'">
                    <a-progress :percent="record.weight * 100" size="small" />
                  </template>
                  <template v-if="column.key === 'score'">
                    <span :style="{ color: getFactorScoreColor(record.score) }">
                      {{ record.score }}
                    </span>
                  </template>
                  <template v-if="column.key === 'impact'">
                    <a-tag :color="getImpactColor(record.impact)">
                      {{ record.impact }}
                    </a-tag>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
            
            <a-tab-pane key="analysis" tab="分析报告">
              <div class="analysis-content">
                <h4>评估结论</h4>
                <p>{{ currentAssessment.conclusion }}</p>
                
                <h4>风险要点</h4>
                <ul>
                  <li v-for="point in currentAssessment.riskPoints" :key="point">{{ point }}</li>
                </ul>
                
                <h4>建议措施</h4>
                <ul>
                  <li v-for="suggestion in currentAssessment.suggestions" :key="suggestion">{{ suggestion }}</li>
                </ul>
              </div>
            </a-tab-pane>
            
            <a-tab-pane key="chart" tab="可视化分析">
              <div id="detail-risk-chart" style="height: 400px;"></div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-modal>

      <!-- 批量评估弹窗 -->
      <a-modal
        v-model:open="batchAssessModalVisible"
        title="批量风险评估"
        width="800px"
        @ok="handleBatchAssessSubmit"
        @cancel="batchAssessModalVisible = false"
      >
        <a-form :model="batchAssessForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选择范围">
            <a-radio-group v-model:value="batchAssessForm.scope">
              <a-radio value="selected">已选中案件 ({{ selectedRowKeys.length }}个)</a-radio>
              <a-radio value="filter">按条件筛选</a-radio>
              <a-radio value="all">全部待评估案件</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <div v-if="batchAssessForm.scope === 'filter'">
            <a-form-item label="风险等级">
              <a-select v-model:value="batchAssessForm.filterRiskLevel" placeholder="请选择" allow-clear>
                <a-select-option value="low">低风险</a-select-option>
                <a-select-option value="medium">中风险</a-select-option>
                <a-select-option value="high">高风险</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="金额范围">
              <a-input-group compact>
                <a-input-number
                  v-model:value="batchAssessForm.minAmount"
                  placeholder="最小金额"
                  style="width: 50%"
                />
                <a-input-number
                  v-model:value="batchAssessForm.maxAmount"
                  placeholder="最大金额"
                  style="width: 50%"
                />
              </a-input-group>
            </a-form-item>
          </div>
          
          <a-form-item label="评估模型">
            <a-select v-model:value="batchAssessForm.modelId" placeholder="请选择评估模型">
              <a-select-option value="default">标准评估模型</a-select-option>
              <a-select-option value="advanced">高级评估模型</a-select-option>
              <a-select-option value="ai">AI智能模型</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="执行方式">
            <a-radio-group v-model:value="batchAssessForm.executeMode">
              <a-radio value="immediate">立即执行</a-radio>
              <a-radio value="scheduled">定时执行</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item v-if="batchAssessForm.executeMode === 'scheduled'" label="执行时间">
            <a-date-picker
              v-model:value="batchAssessForm.scheduleTime"
              show-time
              placeholder="选择执行时间"
              style="width: 100%"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 模型配置弹窗 -->
      <a-modal
        v-model:open="modelConfigModalVisible"
        title="评估模型配置"
        width="1000px"
        @ok="handleModelConfigSubmit"
        @cancel="modelConfigModalVisible = false"
      >
        <a-tabs>
          <a-tab-pane key="models" tab="模型管理">
            <a-table
              :columns="modelColumns"
              :data-source="models"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 'active' ? 'green' : 'default'">
                    {{ record.status === 'active' ? '启用' : '禁用' }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'accuracy'">
                  <a-progress :percent="record.accuracy" size="small" />
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="editModel(record)">编辑</a-button>
                    <a-button type="link" size="small" @click="testModel(record)">测试</a-button>
                    <a-button 
                      type="link" 
                      size="small" 
                      :disabled="record.status === 'active'"
                      @click="toggleModelStatus(record)"
                    >
                      {{ record.status === 'active' ? '禁用' : '启用' }}
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="factors" tab="风险因子">
            <div class="factor-config">
              <a-button type="primary" @click="addFactor" style="margin-bottom: 16px;">
                <PlusOutlined />
                添加风险因子
              </a-button>
              
              <a-table
                :columns="factorConfigColumns"
                :data-source="riskFactors"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'weight'">
                    <a-slider
                      v-model:value="record.weight"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      style="width: 100px;"
                    />
                  </template>
                  <template v-if="column.key === 'threshold'">
                    <a-input-number
                      v-model:value="record.threshold"
                      :min="0"
                      :max="100"
                      size="small"
                    />
                  </template>
                  <template v-if="column.key === 'enabled'">
                    <a-switch v-model:checked="record.enabled" />
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-button type="link" size="small" danger @click="removeFactor(record)">
                      删除
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  PlusOutlined,
  SearchOutlined,
  DownloadOutlined,
  DownOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  AuditOutlined,
  HistoryOutlined,
  FileTextOutlined,
  ReloadOutlined,
  WarningOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])

// 统计数据
const stats = reactive({
  totalTasks: 1268,
  todayNew: 45,
  highRisk: 156,
  highRiskRatio: 12.3,
  avgScore: 67.8,
  accuracy: 89.2,
  aiAccuracy: 92.5
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  riskLevel: undefined,
  status: undefined,
  dateRange: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 1268,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '客户信息',
    key: 'customerInfo',
    width: 150
  },
  {
    title: '案件信息',
    key: 'caseInfo',
    width: 150
  },
  {
    title: '风险评分',
    key: 'riskScore',
    width: 120,
    align: 'center'
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '评估状态',
    key: 'status',
    width: 100
  },
  {
    title: '主要风险因子',
    key: 'factors',
    width: 200
  },
  {
    title: '评估人员',
    dataIndex: 'assessor',
    key: 'assessor',
    width: 100
  },
  {
    title: '评估时间',
    dataIndex: 'assessmentDate',
    key: 'assessmentDate',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模拟数据
const assessmentList = ref([
  {
    id: 1,
    customerName: '张三',
    customerPhone: '13800138001',
    caseNumber: 'CS2024001',
    amount: 120000,
    riskScore: 85,
    riskLevel: 'high',
    status: 'completed',
    riskFactors: ['逾期记录', '收入下降', '联系困难'],
    assessor: '李评估员',
    assessmentDate: '2024-01-15',
    conclusion: '该客户风险较高，建议加强催收措施并密切关注还款情况。',
    riskPoints: [
      '客户有多次逾期记录，还款意愿有待观察',
      '近期收入来源不稳定，还款能力下降',
      '联系方式经常变更，存在失联风险'
    ],
    suggestions: [
      '增加催收频次，采用多渠道联系方式',
      '要求客户提供担保或抵押物',
      '考虑启动法律程序保障债权'
    ],
    riskFactorDetails: [
      { factor: '逾期记录', weight: 0.3, score: 90, impact: '高', description: '客户有3次以上逾期记录' },
      { factor: '收入稳定性', weight: 0.25, score: 70, impact: '中', description: '收入来源相对稳定但有波动' },
      { factor: '还款意愿', weight: 0.2, score: 80, impact: '高', description: '主动联系较少，配合度一般' },
      { factor: '资产状况', weight: 0.15, score: 60, impact: '中', description: '有一定资产但流动性较差' },
      { factor: '社会关系', weight: 0.1, score: 75, impact: '低', description: '社会关系网络一般' }
    ]
  },
  {
    id: 2,
    customerName: '李四',
    customerPhone: '13800138002',
    caseNumber: 'CS2024002',
    amount: 80000,
    riskScore: 45,
    riskLevel: 'medium',
    status: 'in_progress',
    riskFactors: ['收入波动', '负债较高'],
    assessor: '王评估员',
    assessmentDate: '2024-01-14',
    conclusion: '客户风险中等，需要持续关注但可采用常规催收方式。',
    riskPoints: [
      '收入有一定波动但总体稳定',
      '负债比例较高但在可控范围内'
    ],
    suggestions: [
      '定期跟进还款计划执行情况',
      '建议客户优化债务结构'
    ],
    riskFactorDetails: [
      { factor: '逾期记录', weight: 0.3, score: 40, impact: '低', description: '偶有逾期但及时处理' },
      { factor: '收入稳定性', weight: 0.25, score: 55, impact: '中', description: '收入有波动但趋势向好' },
      { factor: '还款意愿', weight: 0.2, score: 70, impact: '中', description: '配合度较好，主动沟通' },
      { factor: '资产状况', weight: 0.15, score: 45, impact: '中', description: '资产有限但增长潜力' },
      { factor: '社会关系', weight: 0.1, score: 60, impact: '低', description: '社会关系较为稳定' }
    ]
  }
])

// 弹窗状态
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const batchAssessModalVisible = ref(false)
const modelConfigModalVisible = ref(false)

// 当前评估对象
const currentAssessment = ref(null)

// 创建表单
const createForm = reactive({
  customerId: undefined,
  caseId: undefined,
  assessmentType: undefined,
  modelId: undefined,
  assessorId: undefined,
  priority: 'medium',
  remark: ''
})

// 批量评估表单
const batchAssessForm = reactive({
  scope: 'selected',
  filterRiskLevel: undefined,
  minAmount: undefined,
  maxAmount: undefined,
  modelId: 'default',
  executeMode: 'immediate',
  scheduleTime: undefined
})

// 客户和案件数据
const customers = ref([
  { id: 1, name: '张三', phone: '13800138001' },
  { id: 2, name: '李四', phone: '13800138002' },
  { id: 3, name: '王五', phone: '13800138003' }
])

const customerCases = ref([])

// 风险因子详情列
const factorColumns = [
  { title: '风险因子', dataIndex: 'factor', key: 'factor' },
  { title: '权重', key: 'weight', width: 120 },
  { title: '评分', key: 'score', width: 80 },
  { title: '影响程度', key: 'impact', width: 100 },
  { title: '说明', dataIndex: 'description', key: 'description' }
]

// 评估模型数据
const models = ref([
  {
    id: 1,
    name: '标准评估模型',
    version: 'v1.2',
    accuracy: 87,
    status: 'active',
    description: '基于传统风险因子的标准评估模型'
  },
  {
    id: 2,
    name: 'AI智能模型',
    version: 'v2.0',
    accuracy: 93,
    status: 'active',
    description: '基于机器学习的智能风险评估模型'
  },
  {
    id: 3,
    name: '高级评估模型',
    version: 'v1.5',
    accuracy: 90,
    status: 'inactive',
    description: '结合多维度数据的高级评估模型'
  }
])

const modelColumns = [
  { title: '模型名称', dataIndex: 'name', key: 'name' },
  { title: '版本', dataIndex: 'version', key: 'version' },
  { title: '准确率', key: 'accuracy', width: 120 },
  { title: '状态', key: 'status', width: 100 },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '操作', key: 'action', width: 180 }
]

// 风险因子配置
const riskFactors = ref([
  { id: 1, name: '逾期记录', weight: 0.3, threshold: 80, enabled: true },
  { id: 2, name: '收入稳定性', weight: 0.25, threshold: 70, enabled: true },
  { id: 3, name: '还款意愿', weight: 0.2, threshold: 75, enabled: true },
  { id: 4, name: '资产状况', weight: 0.15, threshold: 60, enabled: true },
  { id: 5, name: '社会关系', weight: 0.1, threshold: 50, enabled: true }
])

const factorConfigColumns = [
  { title: '因子名称', dataIndex: 'name', key: 'name' },
  { title: '权重', key: 'weight', width: 150 },
  { title: '阈值', key: 'threshold', width: 100 },
  { title: '启用', key: 'enabled', width: 80 },
  { title: '操作', key: 'action', width: 80 }
]

// 监听客户选择
watch(() => createForm.customerId, (customerId) => {
  if (customerId) {
    customerCases.value = [
      { id: 1, caseNumber: 'CS2024001', amount: 120000 },
      { id: 2, caseNumber: 'CS2024002', amount: 80000 }
    ]
  } else {
    customerCases.value = []
  }
  createForm.caseId = undefined
})

// 颜色和状态方法
const getRiskScoreColor = (score) => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#fa8c16'
  if (score >= 40) return '#1890ff'
  return '#52c41a'
}

const getRiskLevelColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'purple'
  }
  return colors[level] || 'default'
}

const getRiskLevelText = (level) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险',
    critical: '极高风险'
  }
  return texts[level] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    in_progress: 'blue',
    completed: 'green',
    reviewed: 'purple'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待评估',
    in_progress: '评估中',
    completed: '已完成',
    reviewed: '已审核'
  }
  return texts[status] || '未知'
}

const getFactorScoreColor = (score) => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#fa8c16'
  return '#52c41a'
}

const getImpactColor = (impact) => {
  const colors = {
    低: 'green',
    中: 'orange',
    高: 'red'
  }
  return colors[impact] || 'default'
}

// 事件处理方法
const handleSearch = () => {
  console.log('搜索参数:', searchForm)
  message.success('搜索完成')
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const showCreateModal = () => {
  createModalVisible.value = true
}

const handleCreateSubmit = () => {
  console.log('创建评估任务:', createForm)
  message.success('评估任务创建成功')
  createModalVisible.value = false
}

const viewDetails = (record) => {
  currentAssessment.value = record
  detailModalVisible.value = true
  setTimeout(() => {
    initDetailChart()
  }, 100)
}

const startAssessment = (record) => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    record.status = 'in_progress'
    message.success('评估任务已启动')
  }, 2000)
}

const reviewAssessment = (record) => {
  record.status = 'reviewed'
  message.success('评估结果已审核')
}

const showHistoryModal = (record) => {
  message.info('查看评估历史功能开发中')
}

const generateReport = (record) => {
  message.success('正在生成评估报告...')
}

const reAssess = (record) => {
  record.status = 'in_progress'
  message.success('重新评估已启动')
}

const showBatchAssessModal = () => {
  batchAssessModalVisible.value = true
}

const handleBatchAssessSubmit = () => {
  console.log('批量评估:', batchAssessForm)
  message.success('批量评估任务已创建')
  batchAssessModalVisible.value = false
}

const showModelConfigModal = () => {
  modelConfigModalVisible.value = true
}

const handleModelConfigSubmit = () => {
  message.success('模型配置已保存')
  modelConfigModalVisible.value = false
}

const showReportModal = () => {
  message.info('评估报告功能开发中')
}

const exportData = () => {
  message.success('数据导出成功')
}

const onCustomerSelect = (customerId) => {
  // 模拟获取客户案件列表
}

const filterCustomerOption = (input, option) => {
  return option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 模型管理方法
const editModel = (model) => {
  message.info(`编辑模型: ${model.name}`)
}

const testModel = (model) => {
  message.success(`模型 ${model.name} 测试完成`)
}

const toggleModelStatus = (model) => {
  model.status = model.status === 'active' ? 'inactive' : 'active'
  message.success(`模型状态已更新`)
}

// 风险因子管理
const addFactor = () => {
  const newFactor = {
    id: riskFactors.value.length + 1,
    name: '新风险因子',
    weight: 0.1,
    threshold: 50,
    enabled: true
  }
  riskFactors.value.push(newFactor)
}

const removeFactor = (factor) => {
  const index = riskFactors.value.findIndex(f => f.id === factor.id)
  if (index > -1) {
    riskFactors.value.splice(index, 1)
    message.success('风险因子已删除')
  }
}

// 图表初始化
const initCharts = () => {
  // 风险等级分布图
  const distributionChart = echarts.init(document.getElementById('risk-distribution-chart'))
  const distributionOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '5%',
      left: 'center'
    },
    series: [
      {
        name: '风险等级',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 435, name: '低风险', itemStyle: { color: '#52c41a' } },
          { value: 310, name: '中风险', itemStyle: { color: '#1890ff' } },
          { value: 234, name: '高风险', itemStyle: { color: '#fa8c16' } },
          { value: 156, name: '极高风险', itemStyle: { color: '#ff4d4f' } }
        ]
      }
    ]
  }
  distributionChart.setOption(distributionOption)

  // 评估趋势分析图
  const trendChart = echarts.init(document.getElementById('assessment-trend-chart'))
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['评估数量', '高风险占比'],
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '占比(%)',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '评估数量',
        type: 'bar',
        data: [180, 210, 195, 234, 267, 298],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '高风险占比',
        type: 'line',
        yAxisIndex: 1,
        data: [15.2, 13.8, 16.5, 14.2, 12.8, 11.9],
        smooth: true,
        itemStyle: { color: '#ff4d4f' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 响应式调整
  window.addEventListener('resize', () => {
    distributionChart.resize()
    trendChart.resize()
  })
}

// 详情图表初始化
const initDetailChart = () => {
  if (!currentAssessment.value) return
  
  const detailChart = echarts.init(document.getElementById('detail-risk-chart'))
  const detailOption = {
    title: {
      text: '风险因子分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentAssessment.value.riskFactorDetails.map(item => item.factor)
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: '风险评分',
        type: 'bar',
        data: currentAssessment.value.riskFactorDetails.map(item => ({
          value: item.score,
          itemStyle: {
            color: getFactorScoreColor(item.score)
          }
        })),
        label: {
          show: true,
          position: 'top'
        }
      }
    ]
  }
  detailChart.setOption(detailOption)
}

// 初始化
onMounted(() => {
  handleSearch()
  setTimeout(() => {
    initCharts()
  }, 100)
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-filters {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.charts-section {
  margin-top: 16px;
}

.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-change {
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-percent,
.stat-ai {
  color: #1890ff;
  font-weight: 500;
}

.customer-name {
  font-weight: 500;
  color: #1890ff;
}

.customer-detail {
  font-size: 12px;
  color: #666;
}

.case-number {
  font-weight: 500;
}

.case-amount {
  font-size: 12px;
  color: #666;
}

.risk-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-text {
  font-size: 12px;
  color: #666;
}

.risk-factors {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-factors {
  font-size: 12px;
  color: #999;
}

.analysis-content h4 {
  margin: 16px 0 8px 0;
  color: #1890ff;
}

.analysis-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.analysis-content li {
  margin: 4px 0;
}

.factor-config {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-wrapper {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .action-section {
    gap: 12px;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .stats-cards .ant-col {
    margin-bottom: 16px;
  }
}
</style>