<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>风险监控</h2>
      
      <!-- 实时监控概览 -->
      <a-row :gutter="16" class="monitoring-overview">
        <a-col :span="6">
          <a-card class="monitoring-card">
            <a-statistic 
              title="实时风险案件" 
              :value="overviewData.riskCases" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <a-badge status="processing" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="monitoring-card">
            <a-statistic 
              title="高风险预警" 
              :value="overviewData.highRiskAlerts" 
              :value-style="{ color: '#faad14' }"
            >
              <template #suffix>
                <alert-outlined style="color: #faad14" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="monitoring-card">
            <a-statistic 
              title="系统健康度" 
              :value="overviewData.systemHealth" 
              suffix="%" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <a-progress 
                  type="circle" 
                  :percent="overviewData.systemHealth" 
                  :width="40" 
                  :stroke-color="getHealthColor(overviewData.systemHealth)"
                />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="monitoring-card">
            <a-statistic 
              title="今日检测量" 
              :value="overviewData.todayDetections" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <scan-outlined style="color: #1890ff" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作工具栏 -->
      <a-card class="toolbar-card">
        <a-row :gutter="16" align="middle">
          <a-col :span="12">
            <a-space>
              <a-button type="primary" @click="handleRefresh">
                <reload-outlined />
                刷新监控
              </a-button>
              <a-button @click="handleExport">
                <download-outlined />
                导出报告
              </a-button>
              <a-button @click="handleConfigMonitoring">
                <setting-outlined />
                监控配置
              </a-button>
            </a-space>
          </a-col>
          <a-col :span="12">
            <div class="auto-refresh">
              <a-space>
                <span>自动刷新:</span>
                <a-switch v-model:checked="autoRefresh" @change="handleAutoRefreshChange" />
                <a-select v-model:value="refreshInterval" style="width: 120px" :disabled="!autoRefresh">
                  <a-select-option value="5">5秒</a-select-option>
                  <a-select-option value="10">10秒</a-select-option>
                  <a-select-option value="30">30秒</a-select-option>
                  <a-select-option value="60">1分钟</a-select-option>
                </a-select>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 风险监控仪表板 -->
      <a-row :gutter="16" class="dashboard-section">
        <a-col :span="16">
          <a-card title="实时风险态势" :body-style="{ padding: '20px' }">
            <div ref="riskTrendChart" style="height: 350px"></div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="风险等级分布" :body-style="{ padding: '20px' }">
            <div ref="riskDistributionChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="dashboard-section">
        <a-col :span="12">
          <a-card title="监控指标趋势" :body-style="{ padding: '20px' }">
            <div ref="metricsChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="热力地图" :body-style="{ padding: '20px' }">
            <div ref="heatmapChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 实时风险事件 -->
      <a-card title="实时风险事件" class="events-card">
        <template #extra>
          <a-space>
            <a-badge :count="newEventsCount" :show-zero="false">
              <a-button size="small" @click="handleMarkAllRead">
                标记已读
              </a-button>
            </a-badge>
            <a-button size="small" @click="handleClearEvents">
              清空事件
            </a-button>
          </a-space>
        </template>
        
        <div class="events-list">
          <a-timeline>
            <a-timeline-item 
              v-for="event in riskEvents" 
              :key="event.id"
              :color="getEventColor(event.level)"
            >
              <template #dot>
                <alert-outlined v-if="event.level === 'high'" style="color: #ff4d4f" />
                <warning-outlined v-else-if="event.level === 'medium'" style="color: #faad14" />
                <info-circle-outlined v-else style="color: #1890ff" />
              </template>
              
              <div class="event-item" :class="{ 'new-event': event.isNew }">
                <div class="event-header">
                  <span class="event-title">{{ event.title }}</span>
                  <a-tag :color="getEventColor(event.level)">
                    {{ getLevelText(event.level) }}
                  </a-tag>
                  <span class="event-time">{{ event.time }}</span>
                </div>
                <div class="event-content">{{ event.description }}</div>
                <div class="event-actions">
                  <a-space size="small">
                    <a-button size="small" type="link" @click="handleEventDetail(event)">
                      查看详情
                    </a-button>
                    <a-button size="small" type="link" @click="handleEventHandle(event)">
                      立即处理
                    </a-button>
                    <a-button size="small" type="link" @click="handleEventIgnore(event)">
                      忽略
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-card>

      <!-- 监控规则管理 -->
      <a-card title="监控规则管理">
        <template #extra>
          <a-button type="primary" @click="showRuleModal = true">
            <plus-outlined />
            新增规则
          </a-button>
        </template>
        
        <a-table 
          :columns="ruleColumns" 
          :data-source="monitoringRules" 
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch 
                v-model:checked="record.enabled" 
                size="small"
                @change="handleRuleToggle(record)"
              />
            </template>
            <template v-if="column.key === 'level'">
              <a-tag :color="getEventColor(record.level)">
                {{ getLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space size="small">
                <a-button size="small" type="link" @click="handleEditRule(record)">
                  编辑
                </a-button>
                <a-button size="small" type="link" danger @click="handleDeleteRule(record)">
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 监控配置弹窗 -->
    <a-modal 
      v-model:open="showConfigModal" 
      title="监控配置" 
      width="800px"
      @ok="handleConfigSave"
    >
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="数据刷新间隔">
              <a-select v-model:value="config.refreshInterval" style="width: 100%">
                <a-select-option value="5">5秒</a-select-option>
                <a-select-option value="10">10秒</a-select-option>
                <a-select-option value="30">30秒</a-select-option>
                <a-select-option value="60">1分钟</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="告警阈值">
              <a-input-number 
                v-model:value="config.alertThreshold" 
                :min="1" 
                :max="100" 
                style="width: 100%"
                addon-after="%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="历史数据保留">
              <a-select v-model:value="config.dataRetention" style="width: 100%">
                <a-select-option value="7">7天</a-select-option>
                <a-select-option value="30">30天</a-select-option>
                <a-select-option value="90">90天</a-select-option>
                <a-select-option value="365">1年</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="告警通知方式">
              <a-checkbox-group v-model:value="config.notificationMethods">
                <a-checkbox value="email">邮件</a-checkbox>
                <a-checkbox value="sms">短信</a-checkbox>
                <a-checkbox value="system">系统通知</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 新增/编辑规则弹窗 -->
    <a-modal 
      v-model:open="showRuleModal" 
      :title="editingRule ? '编辑监控规则' : '新增监控规则'" 
      width="600px"
      @ok="handleRuleSave"
    >
      <a-form layout="vertical">
        <a-form-item label="规则名称" required>
          <a-input v-model:value="ruleForm.name" placeholder="请输入规则名称" />
        </a-form-item>
        
        <a-form-item label="监控类型" required>
          <a-select v-model:value="ruleForm.type" placeholder="请选择监控类型">
            <a-select-option value="threshold">阈值监控</a-select-option>
            <a-select-option value="trend">趋势监控</a-select-option>
            <a-select-option value="anomaly">异常监控</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="风险等级" required>
          <a-select v-model:value="ruleForm.level" placeholder="请选择风险等级">
            <a-select-option value="high">高风险</a-select-option>
            <a-select-option value="medium">中风险</a-select-option>
            <a-select-option value="low">低风险</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="触发条件" required>
          <a-textarea 
            v-model:value="ruleForm.condition" 
            placeholder="请输入触发条件"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="处理建议">
          <a-textarea 
            v-model:value="ruleForm.suggestion" 
            placeholder="请输入处理建议"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 事件详情弹窗 -->
    <a-modal 
      v-model:open="showEventDetailModal" 
      title="风险事件详情" 
      width="800px"
      :footer="null"
    >
      <div v-if="selectedEvent">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="事件标题" :span="2">
            {{ selectedEvent.title }}
          </a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getEventColor(selectedEvent.level)">
              {{ getLevelText(selectedEvent.level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发生时间">
            {{ selectedEvent.time }}
          </a-descriptions-item>
          <a-descriptions-item label="影响范围">
            {{ selectedEvent.scope || '全系统' }}
          </a-descriptions-item>
          <a-descriptions-item label="检测规则">
            {{ selectedEvent.rule || '智能检测' }}
          </a-descriptions-item>
          <a-descriptions-item label="事件描述" :span="2">
            {{ selectedEvent.description }}
          </a-descriptions-item>
          <a-descriptions-item label="处理建议" :span="2">
            {{ selectedEvent.suggestion || '请及时关注并处理' }}
          </a-descriptions-item>
        </a-descriptions>
        
        <div class="event-actions-detail">
          <a-space>
            <a-button type="primary" @click="handleEventHandle(selectedEvent)">
              立即处理
            </a-button>
            <a-button @click="handleEventIgnore(selectedEvent)">
              忽略事件
            </a-button>
            <a-button @click="showEventDetailModal = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  AlertOutlined, 
  ScanOutlined, 
  ReloadOutlined, 
  DownloadOutlined, 
  SettingOutlined,
  PlusOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const refreshInterval = ref('10')
const newEventsCount = ref(3)

// 概览数据
const overviewData = reactive({
  riskCases: 128,
  highRiskAlerts: 15,
  systemHealth: 94,
  todayDetections: 2456
})

// 弹窗控制
const showConfigModal = ref(false)
const showRuleModal = ref(false)
const showEventDetailModal = ref(false)
const editingRule = ref(null)
const selectedEvent = ref(null)

// 配置数据
const config = reactive({
  refreshInterval: '10',
  alertThreshold: 80,
  dataRetention: '30',
  notificationMethods: ['email', 'system']
})

// 规则表单
const ruleForm = reactive({
  name: '',
  type: '',
  level: '',
  condition: '',
  suggestion: ''
})

// 实时风险事件
const riskEvents = ref([
  {
    id: 1,
    title: '异常登录行为检测',
    description: '检测到用户admin在非工作时间从异常IP地址登录系统',
    level: 'high',
    time: '2024-01-15 23:45:23',
    isNew: true,
    scope: '用户认证系统',
    rule: '异常登录检测规则',
    suggestion: '立即验证用户身份并检查账户安全'
  },
  {
    id: 2,
    title: '催收成功率异常下降',
    description: '催收二部本日催收成功率较昨日下降15%，超过预警阈值',
    level: 'medium',
    time: '2024-01-15 22:30:15',
    isNew: true,
    scope: '催收二部',
    rule: '成功率监控规则',
    suggestion: '检查催收策略和人员配置'
  },
  {
    id: 3,
    title: '数据库查询异常',
    description: '检测到大量慢查询，可能影响系统性能',
    level: 'medium',
    time: '2024-01-15 21:15:42',
    isNew: true,
    scope: '数据库系统',
    rule: '性能监控规则',
    suggestion: '优化数据库查询或扩容资源'
  },
  {
    id: 4,
    title: '系统正常运行',
    description: '所有关键指标正常，系统运行稳定',
    level: 'low',
    time: '2024-01-15 20:00:00',
    isNew: false,
    scope: '全系统',
    rule: '健康检查规则',
    suggestion: '继续保持'
  }
])

// 监控规则表格列
const ruleColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '监控类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '风险等级',
    key: 'level',
    width: 100
  },
  {
    title: '触发条件',
    dataIndex: 'condition',
    key: 'condition',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 监控规则数据
const monitoringRules = ref([
  {
    id: 1,
    name: '异常登录检测',
    type: '异常监控',
    level: 'high',
    condition: '非工作时间或异常IP登录',
    enabled: true
  },
  {
    id: 2,
    name: '催收成功率监控',
    type: '阈值监控',
    level: 'medium',
    condition: '成功率下降超过10%',
    enabled: true
  },
  {
    id: 3,
    name: '系统性能监控',
    type: '趋势监控',
    level: 'medium',
    condition: 'CPU使用率超过80%',
    enabled: true
  },
  {
    id: 4,
    name: '数据一致性检查',
    type: '异常监控',
    level: 'high',
    condition: '数据同步失败或不一致',
    enabled: false
  }
])

// 图表引用
const riskTrendChart = ref()
const riskDistributionChart = ref()
const metricsChart = ref()
const heatmapChart = ref()

// 定时器
let refreshTimer = null

// 辅助方法
const getHealthColor = (health) => {
  if (health >= 90) return '#52c41a'
  if (health >= 70) return '#faad14'
  return '#ff4d4f'
}

const getEventColor = (level) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[level] || '#1890ff'
}

const getLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || '未知'
}

// 事件处理方法
const handleRefresh = () => {
  loading.value = true
  
  // 模拟数据更新
  setTimeout(() => {
    overviewData.riskCases = Math.floor(Math.random() * 50) + 100
    overviewData.highRiskAlerts = Math.floor(Math.random() * 10) + 10
    overviewData.systemHealth = Math.floor(Math.random() * 20) + 80
    overviewData.todayDetections = Math.floor(Math.random() * 1000) + 2000
    
    loading.value = false
    message.success('监控数据已刷新')
    initCharts()
  }, 1000)
}

const handleExport = () => {
  const reportData = {
    timestamp: new Date().toISOString(),
    overview: overviewData,
    events: riskEvents.value,
    rules: monitoringRules.value
  }
  
  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `risk-monitoring-report-${new Date().toISOString().split('T')[0]}.json`
  link.click()
  URL.revokeObjectURL(url)
  
  message.success('监控报告导出成功')
}

const handleConfigMonitoring = () => {
  showConfigModal.value = true
}

const handleAutoRefreshChange = (checked) => {
  if (checked) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh()
  refreshTimer = setInterval(() => {
    handleRefresh()
  }, parseInt(refreshInterval.value) * 1000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const handleMarkAllRead = () => {
  riskEvents.value.forEach(event => {
    event.isNew = false
  })
  newEventsCount.value = 0
  message.success('所有事件已标记为已读')
}

const handleClearEvents = () => {
  Modal.confirm({
    title: '确认清空',
    content: '确定要清空所有风险事件吗？',
    onOk: () => {
      riskEvents.value = []
      newEventsCount.value = 0
      message.success('风险事件已清空')
    }
  })
}

const handleEventDetail = (event) => {
  selectedEvent.value = event
  showEventDetailModal.value = true
}

const handleEventHandle = (event) => {
  Modal.confirm({
    title: '处理风险事件',
    content: `确定要处理事件"${event.title}"吗？`,
    onOk: () => {
      // 移除事件
      const index = riskEvents.value.findIndex(e => e.id === event.id)
      if (index > -1) {
        riskEvents.value.splice(index, 1)
        if (event.isNew) newEventsCount.value--
      }
      showEventDetailModal.value = false
      message.success('事件处理完成')
    }
  })
}

const handleEventIgnore = (event) => {
  const index = riskEvents.value.findIndex(e => e.id === event.id)
  if (index > -1) {
    riskEvents.value.splice(index, 1)
    if (event.isNew) newEventsCount.value--
  }
  showEventDetailModal.value = false
  message.success('事件已忽略')
}

const handleRuleToggle = (rule) => {
  message.success(`规则"${rule.name}"已${rule.enabled ? '启用' : '禁用'}`)
}

const handleEditRule = (rule) => {
  editingRule.value = rule
  ruleForm.name = rule.name
  ruleForm.type = rule.type
  ruleForm.level = rule.level
  ruleForm.condition = rule.condition
  ruleForm.suggestion = rule.suggestion || ''
  showRuleModal.value = true
}

const handleDeleteRule = (rule) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除规则"${rule.name}"吗？`,
    onOk: () => {
      const index = monitoringRules.value.findIndex(r => r.id === rule.id)
      if (index > -1) {
        monitoringRules.value.splice(index, 1)
        message.success('规则删除成功')
      }
    }
  })
}

const handleConfigSave = () => {
  message.success('监控配置保存成功')
  showConfigModal.value = false
  
  // 应用新的刷新间隔
  if (autoRefresh.value) {
    refreshInterval.value = config.refreshInterval
    startAutoRefresh()
  }
}

const handleRuleSave = () => {
  if (!ruleForm.name || !ruleForm.type || !ruleForm.level || !ruleForm.condition) {
    message.error('请填写完整的规则信息')
    return
  }
  
  if (editingRule.value) {
    // 编辑规则
    Object.assign(editingRule.value, {
      name: ruleForm.name,
      type: ruleForm.type,
      level: ruleForm.level,
      condition: ruleForm.condition,
      suggestion: ruleForm.suggestion
    })
    message.success('规则更新成功')
  } else {
    // 新增规则
    const newRule = {
      id: Date.now(),
      name: ruleForm.name,
      type: ruleForm.type,
      level: ruleForm.level,
      condition: ruleForm.condition,
      suggestion: ruleForm.suggestion,
      enabled: true
    }
    monitoringRules.value.push(newRule)
    message.success('规则创建成功')
  }
  
  // 重置表单
  editingRule.value = null
  Object.assign(ruleForm, {
    name: '',
    type: '',
    level: '',
    condition: '',
    suggestion: ''
  })
  showRuleModal.value = false
}

// 图表初始化
const initCharts = () => {
  // 风险态势图
  const trendChart = echarts.init(riskTrendChart.value)
  const trendOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      name: '风险指数'
    },
    series: [
      {
        name: '高风险',
        type: 'line',
        data: [12, 8, 15, 23, 18, 25, 20],
        itemStyle: { color: '#ff4d4f' },
        areaStyle: { opacity: 0.3 }
      },
      {
        name: '中风险',
        type: 'line',
        data: [35, 28, 42, 38, 45, 40, 38],
        itemStyle: { color: '#faad14' },
        areaStyle: { opacity: 0.3 }
      },
      {
        name: '低风险',
        type: 'line',
        data: [58, 65, 52, 48, 55, 62, 60],
        itemStyle: { color: '#52c41a' },
        areaStyle: { opacity: 0.3 }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 风险分布图
  const distributionChart = echarts.init(riskDistributionChart.value)
  const distributionOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '风险等级',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 15, name: '高风险', itemStyle: { color: '#ff4d4f' } },
          { value: 38, name: '中风险', itemStyle: { color: '#faad14' } },
          { value: 75, name: '低风险', itemStyle: { color: '#52c41a' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  distributionChart.setOption(distributionOption)

  // 监控指标图
  const metricsChartInstance = echarts.init(metricsChart.value)
  const metricsOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '检测数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '准确率(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '风险检测',
        type: 'bar',
        data: [2156, 2345, 2890, 2567, 2123, 1890, 1456],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '准确率',
        type: 'line',
        yAxisIndex: 1,
        data: [94.5, 95.2, 93.8, 96.1, 94.9, 95.7, 96.3],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  metricsChartInstance.setOption(metricsOption)

  // 热力地图
  const heatmapChartInstance = echarts.init(heatmapChart.value)
  const hours = ['12a', '1a', '2a', '3a', '4a', '5a', '6a', '7a', '8a', '9a', '10a', '11a',
    '12p', '1p', '2p', '3p', '4p', '5p', '6p', '7p', '8p', '9p', '10p', '11p']
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  
  const data = []
  for (let i = 0; i < 7; i++) {
    for (let j = 0; j < 24; j++) {
      data.push([j, i, Math.floor(Math.random() * 100)])
    }
  }

  const heatmapOption = {
    title: {
      text: '风险事件时间分布',
      left: 'center'
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#50a3ba', '#eac736', '#d94e5d']
      }
    },
    series: [
      {
        name: '风险事件数',
        type: 'heatmap',
        data: data,
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  heatmapChartInstance.setOption(heatmapOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    trendChart.resize()
    distributionChart.resize()
    metricsChartInstance.resize()
    heatmapChartInstance.resize()
  })
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCharts()
    if (autoRefresh.value) {
      startAutoRefresh()
    }
  })
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.monitoring-overview {
  margin-bottom: 16px;
}

.monitoring-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.toolbar-card {
  margin-bottom: 16px;
}

.auto-refresh {
  text-align: right;
}

.dashboard-section {
  margin-bottom: 16px;
}

.events-card {
  margin-bottom: 16px;
}

.events-list {
  max-height: 400px;
  overflow-y: auto;
}

.event-item {
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  border-left: 4px solid #d9d9d9;
}

.event-item.new-event {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.event-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.event-title {
  font-weight: 500;
  flex: 1;
}

.event-time {
  color: #666;
  font-size: 12px;
}

.event-content {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.event-actions {
  text-align: right;
}

.event-actions-detail {
  margin-top: 16px;
  text-align: right;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

:deep(.ant-timeline-item-content) {
  padding-bottom: 16px;
}
</style>