<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>反欺诈</h2>
      
      <!-- 反欺诈概览 -->
      <a-row :gutter="16" class="overview-section">
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="今日检测量" 
              :value="overviewData.todayDetections" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <scan-outlined style="color: #1890ff" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="欺诈案件" 
              :value="overviewData.fraudCases" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <warning-outlined style="color: #ff4d4f" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="风险预警" 
              :value="overviewData.riskAlerts" 
              :value-style="{ color: '#faad14' }"
            >
              <template #suffix>
                <alert-outlined style="color: #faad14" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="检测准确率" 
              :value="overviewData.accuracy" 
              suffix="%" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <check-circle-outlined style="color: #52c41a" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="检测类型">
                <a-select v-model:value="searchForm.detectionType" placeholder="请选择检测类型" allow-clear>
                  <a-select-option value="identity">身份欺诈</a-select-option>
                  <a-select-option value="application">申请欺诈</a-select-option>
                  <a-select-option value="transaction">交易欺诈</a-select-option>
                  <a-select-option value="collusion">团伙欺诈</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="风险等级">
                <a-select v-model:value="searchForm.riskLevel" placeholder="请选择风险等级" allow-clear>
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input v-model:value="searchForm.customerName" placeholder="请输入客户姓名" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="检测时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 100%"
                  @change="handleDateChange"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="handleBatchCheck">
                      <safety-certificate-outlined />
                      批量检测
                    </a-button>
                    <a-button @click="handleExportResults">
                      <download-outlined />
                      导出结果
                    </a-button>
                    <a-button @click="handleModelConfig">
                      <setting-outlined />
                      模型配置
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 欺诈检测图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="欺诈类型分布" :body-style="{ padding: '20px' }">
            <div ref="fraudTypeChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="检测趋势分析" :body-style="{ padding: '20px' }">
            <div ref="trendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="风险热力图" :body-style="{ padding: '20px' }">
            <div ref="riskHeatmapChart" style="height: 400px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 检测结果列表 -->
      <a-card title="检测结果">
        <template #extra>
          <a-space>
            <a-button type="primary" @click="showAddModal = true">
              <plus-outlined />
              新增检测
            </a-button>
            <a-button @click="handleRefresh">
              <reload-outlined />
              刷新
            </a-button>
          </a-space>
        </template>
        
        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1200 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'riskScore'">
              <div class="risk-score-container">
                <a-progress 
                  type="circle" 
                  :percent="record.riskScore" 
                  :width="50"
                  :stroke-color="getRiskScoreColor(record.riskScore)"
                />
                <div class="score-text">{{ record.riskScore }}分</div>
              </div>
            </template>
            <template v-if="column.key === 'detectionType'">
              <a-tag :color="getDetectionTypeColor(record.detectionType)">
                {{ getDetectionTypeText(record.detectionType) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space size="small">
                <a-button size="small" type="link" @click="handleViewDetail(record)">
                  查看详情
                </a-button>
                <a-button size="small" type="link" @click="handleProcessFraud(record)">
                  处理
                </a-button>
                <a-button size="small" type="link" @click="handleRecheck(record)">
                  复检
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="handleMoreAction($event, record)">
                      <a-menu-item key="whitelist">加入白名单</a-menu-item>
                      <a-menu-item key="blacklist">加入黑名单</a-menu-item>
                      <a-menu-item key="archive">归档处理</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small" type="link">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新增检测弹窗 -->
    <a-modal 
      v-model:open="showAddModal" 
      title="新增反欺诈检测" 
      width="800px"
      @ok="handleAddSave"
    >
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="客户姓名" required>
              <a-input v-model:value="addForm.customerName" placeholder="请输入客户姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="身份证号" required>
              <a-input v-model:value="addForm.idNumber" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="检测类型" required>
              <a-select v-model:value="addForm.detectionType" placeholder="请选择检测类型">
                <a-select-option value="identity">身份欺诈</a-select-option>
                <a-select-option value="application">申请欺诈</a-select-option>
                <a-select-option value="transaction">交易欺诈</a-select-option>
                <a-select-option value="collusion">团伙欺诈</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号码">
              <a-input v-model:value="addForm.phoneNumber" placeholder="请输入手机号码" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="检测说明">
          <a-textarea 
            v-model:value="addForm.description" 
            placeholder="请输入检测说明"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情查看弹窗 -->
    <a-modal 
      v-model:open="showDetailModal" 
      title="欺诈检测详情" 
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="客户姓名">
            {{ selectedRecord.customerName }}
          </a-descriptions-item>
          <a-descriptions-item label="身份证号">
            {{ selectedRecord.idNumber }}
          </a-descriptions-item>
          <a-descriptions-item label="检测类型">
            <a-tag :color="getDetectionTypeColor(selectedRecord.detectionType)">
              {{ getDetectionTypeText(selectedRecord.detectionType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getRiskLevelColor(selectedRecord.riskLevel)">
              {{ getRiskLevelText(selectedRecord.riskLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="风险评分">
            {{ selectedRecord.riskScore }}分
          </a-descriptions-item>
          <a-descriptions-item label="检测时间">
            {{ selectedRecord.detectionTime }}
          </a-descriptions-item>
          <a-descriptions-item label="检测模型">
            {{ selectedRecord.model || 'AI智能模型' }}
          </a-descriptions-item>
          <a-descriptions-item label="处理状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 风险因子分析 -->
        <a-divider>风险因子分析</a-divider>
        <a-list size="small" :data-source="selectedRecord.riskFactors || []">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span :style="{ color: getRiskFactorColor(item.level) }">
                    {{ item.factor }}
                  </span>
                </template>
                <template #description>
                  <div>
                    <div>风险分值: {{ item.score }}分</div>
                    <div>{{ item.description }}</div>
                  </div>
                </template>
              </a-list-item-meta>
              <div>
                <a-tag :color="getRiskFactorColor(item.level)">
                  {{ item.level }}
                </a-tag>
              </div>
            </a-list-item>
          </template>
        </a-list>

        <!-- 关联案件信息 -->
        <a-divider>关联案件信息</a-divider>
        <a-table 
          :columns="relatedCaseColumns" 
          :data-source="selectedRecord.relatedCases || []" 
          :pagination="false"
          size="small"
        />

        <!-- 处理建议 -->
        <a-divider>AI处理建议</a-divider>
        <a-alert 
          :message="selectedRecord.suggestion || 'AI正在分析中...'" 
          type="info" 
          show-icon 
        />
      </div>
    </a-modal>

    <!-- 批量检测弹窗 -->
    <a-modal 
      v-model:open="showBatchModal" 
      title="批量反欺诈检测" 
      width="600px"
      @ok="handleBatchSave"
    >
      <a-form layout="vertical">
        <a-form-item label="检测类型" required>
          <a-select v-model:value="batchForm.detectionType" placeholder="请选择检测类型">
            <a-select-option value="identity">身份欺诈</a-select-option>
            <a-select-option value="application">申请欺诈</a-select-option>
            <a-select-option value="transaction">交易欺诈</a-select-option>
            <a-select-option value="collusion">团伙欺诈</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="客户数据上传" required>
          <a-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            @remove="handleRemoveFile"
            accept=".csv,.xlsx,.xls"
          >
            <a-button>
              <upload-outlined />
              选择文件
            </a-button>
          </a-upload>
          <div class="upload-tip">
            支持CSV、Excel格式，文件大小不超过10MB
          </div>
        </a-form-item>
        
        <a-form-item label="检测说明">
          <a-textarea 
            v-model:value="batchForm.description" 
            placeholder="请输入批量检测说明"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 模型配置弹窗 -->
    <a-modal 
      v-model:open="showConfigModal" 
      title="反欺诈模型配置" 
      width="800px"
      @ok="handleConfigSave"
    >
      <a-form layout="vertical">
        <a-tabs v-model:activeKey="configActiveTab">
          <a-tab-pane key="threshold" tab="阈值配置">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="高风险阈值">
                  <a-input-number 
                    v-model:value="modelConfig.highRiskThreshold" 
                    :min="0" 
                    :max="100" 
                    style="width: 100%"
                    addon-after="分"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="中风险阈值">
                  <a-input-number 
                    v-model:value="modelConfig.mediumRiskThreshold" 
                    :min="0" 
                    :max="100" 
                    style="width: 100%"
                    addon-after="分"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="低风险阈值">
                  <a-input-number 
                    v-model:value="modelConfig.lowRiskThreshold" 
                    :min="0" 
                    :max="100" 
                    style="width: 100%"
                    addon-after="分"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="features" tab="特征权重">
            <a-list :data-source="modelConfig.features">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                  <div>
                    <a-slider 
                      v-model:value="item.weight" 
                      :min="0" 
                      :max="100" 
                      style="width: 200px"
                    />
                    <span style="margin-left: 16px">{{ item.weight }}%</span>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
          
          <a-tab-pane key="rules" tab="规则引擎">
            <a-table 
              :columns="ruleColumns" 
              :data-source="modelConfig.rules" 
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'enabled'">
                  <a-switch v-model:checked="record.enabled" size="small" />
                </template>
                <template v-if="column.key === 'actions'">
                  <a-space size="small">
                    <a-button size="small" type="link">编辑</a-button>
                    <a-button size="small" type="link" danger>删除</a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  ScanOutlined,
  WarningOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  SearchOutlined,
  ReloadOutlined,
  SafetyCertificateOutlined,
  DownloadOutlined,
  SettingOutlined,
  PlusOutlined,
  DownOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)

// 概览数据
const overviewData = reactive({
  todayDetections: 1456,
  fraudCases: 23,
  riskAlerts: 67,
  accuracy: 94.2
})

// 搜索表单
const searchForm = reactive({
  detectionType: '',
  riskLevel: '',
  customerName: '',
  dateRange: [dayjs().subtract(7, 'day'), dayjs()]
})

// 弹窗控制
const showAddModal = ref(false)
const showDetailModal = ref(false)
const showBatchModal = ref(false)
const showConfigModal = ref(false)
const selectedRecord = ref(null)

// 表单数据
const addForm = reactive({
  customerName: '',
  idNumber: '',
  detectionType: '',
  phoneNumber: '',
  description: ''
})

const batchForm = reactive({
  detectionType: '',
  description: ''
})

// 文件上传
const fileList = ref([])

// 模型配置
const configActiveTab = ref('threshold')
const modelConfig = reactive({
  highRiskThreshold: 80,
  mediumRiskThreshold: 60,
  lowRiskThreshold: 40,
  features: [
    { name: '身份验证', description: '身份证、手机号验证', weight: 25 },
    { name: '行为模式', description: '用户行为分析', weight: 20 },
    { name: '设备指纹', description: '设备信息识别', weight: 15 },
    { name: '地理位置', description: '地理位置异常', weight: 15 },
    { name: '关联网络', description: '社交网络分析', weight: 15 },
    { name: '历史记录', description: '历史风险记录', weight: 10 }
  ],
  rules: [
    { id: 1, name: '身份证重复规则', condition: '同一身份证多次申请', enabled: true },
    { id: 2, name: '设备指纹规则', condition: '同一设备多账户操作', enabled: true },
    { id: 3, name: '地理位置规则', condition: '异常地理位置登录', enabled: false },
    { id: 4, name: '时间窗口规则', condition: '短时间内频繁操作', enabled: true }
  ]
})

// 表格配置
const columns = [
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '身份证号',
    dataIndex: 'idNumber',
    key: 'idNumber',
    width: 180
  },
  {
    title: '检测类型',
    key: 'detectionType',
    width: 120
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '风险评分',
    key: 'riskScore',
    width: 120
  },
  {
    title: '检测时间',
    dataIndex: 'detectionTime',
    key: 'detectionTime',
    width: 160
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 关联案件表格列
const relatedCaseColumns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber'
  },
  {
    title: '案件类型',
    dataIndex: 'caseType',
    key: 'caseType'
  },
  {
    title: '关联度',
    dataIndex: 'relevance',
    key: 'relevance'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  }
]

// 模型配置规则表格列
const ruleColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '触发条件',
    dataIndex: 'condition',
    key: 'condition'
  },
  {
    title: '启用状态',
    key: 'enabled'
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    customerName: '张三',
    idNumber: '110101199001011234',
    detectionType: 'identity',
    riskLevel: 'high',
    riskScore: 85,
    detectionTime: '2024-01-15 14:30:25',
    status: 'pending',
    model: 'AI智能模型v2.0',
    riskFactors: [
      { factor: '身份证重复使用', score: 30, level: '高风险', description: '该身份证在7天内被3个不同账户使用' },
      { factor: '设备指纹异常', score: 25, level: '高风险', description: '检测到设备指纹伪造痕迹' },
      { factor: '地理位置跳跃', score: 20, level: '中风险', description: '1小时内从北京跳转到上海' },
      { factor: '行为模式异常', score: 10, level: '低风险', description: '操作速度过快，疑似脚本操作' }
    ],
    relatedCases: [
      { caseNumber: 'CASE001', caseType: '身份盗用', relevance: '95%', status: '处理中' },
      { caseNumber: 'CASE002', caseType: '虚假申请', relevance: '78%', status: '已结案' }
    ],
    suggestion: '建议立即冻结账户，联系客户进行身份验证，并启动人工审核流程。'
  },
  {
    id: 2,
    customerName: '李四',
    idNumber: '330102199502152345',
    detectionType: 'application',
    riskLevel: 'medium',
    riskScore: 65,
    detectionTime: '2024-01-15 13:45:12',
    status: 'processing',
    model: 'AI智能模型v2.0',
    riskFactors: [
      { factor: '申请信息不一致', score: 35, level: '中风险', description: '申请表中的工作单位与征信记录不符' },
      { factor: '联系方式异常', score: 20, level: '低风险', description: '手机号归属地与居住地不符' },
      { factor: '收入证明疑点', score: 10, level: '低风险', description: '收入水平与职业不匹配' }
    ],
    relatedCases: [],
    suggestion: '建议加强资料审核，要求提供更多证明材料。'
  },
  {
    id: 3,
    customerName: '王五',
    idNumber: '440301199803203456',
    detectionType: 'transaction',
    riskLevel: 'low',
    riskScore: 35,
    detectionTime: '2024-01-15 12:20:08',
    status: 'completed',
    model: 'AI智能模型v2.0',
    riskFactors: [
      { factor: '交易频率正常', score: -10, level: '正常', description: '交易频率符合用户画像' },
      { factor: '金额范围合理', score: -15, level: '正常', description: '交易金额在用户历史范围内' },
      { factor: '时间规律正常', score: -10, level: '正常', description: '交易时间符合用户习惯' }
    ],
    relatedCases: [],
    suggestion: '风险较低，可正常处理。'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const fraudTypeChart = ref()
const trendChart = ref()
const riskHeatmapChart = ref()

// 辅助方法
const getRiskLevelColor = (level) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[level] || '#1890ff'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || '未知'
}

const getRiskScoreColor = (score) => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#faad14'
  return '#52c41a'
}

const getDetectionTypeColor = (type) => {
  const colors = {
    identity: '#722ed1',
    application: '#1890ff',
    transaction: '#52c41a',
    collusion: '#fa541c'
  }
  return colors[type] || '#666'
}

const getDetectionTypeText = (type) => {
  const texts = {
    identity: '身份欺诈',
    application: '申请欺诈',
    transaction: '交易欺诈',
    collusion: '团伙欺诈'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    pending: '#faad14',
    processing: '#1890ff',
    completed: '#52c41a',
    rejected: '#ff4d4f'
  }
  return colors[status] || '#666'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return texts[status] || '未知'
}

const getRiskFactorColor = (level) => {
  const colors = {
    '高风险': '#ff4d4f',
    '中风险': '#faad14',
    '低风险': '#52c41a',
    '正常': '#1890ff'
  }
  return colors[level] || '#666'
}

// 事件处理方法
const handleSearch = () => {
  console.log('搜索参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.detectionType = ''
  searchForm.riskLevel = ''
  searchForm.customerName = ''
  searchForm.dateRange = [dayjs().subtract(7, 'day'), dayjs()]
  handleSearch()
}

const handleDateChange = (dates) => {
  if (dates) {
    console.log('日期范围变更:', dates)
  }
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const handleViewDetail = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleProcessFraud = (record) => {
  Modal.confirm({
    title: '确认处理',
    content: `确定要处理客户"${record.customerName}"的欺诈检测结果吗？`,
    onOk: () => {
      record.status = 'processing'
      message.success('已开始处理该欺诈案件')
    }
  })
}

const handleRecheck = (record) => {
  Modal.confirm({
    title: '确认复检',
    content: `确定要重新检测客户"${record.customerName}"吗？`,
    onOk: () => {
      message.success('已重新提交检测，请稍后查看结果')
    }
  })
}

const handleMoreAction = (event, record) => {
  const { key } = event
  switch (key) {
    case 'whitelist':
      message.success(`客户"${record.customerName}"已加入白名单`)
      break
    case 'blacklist':
      message.success(`客户"${record.customerName}"已加入黑名单`)
      break
    case 'archive':
      message.success(`案件已归档处理`)
      break
  }
}

const handleBatchCheck = () => {
  showBatchModal.value = true
}

const handleExportResults = () => {
  const exportData = tableData.value.map(item => ({
    客户姓名: item.customerName,
    身份证号: item.idNumber,
    检测类型: getDetectionTypeText(item.detectionType),
    风险等级: getRiskLevelText(item.riskLevel),
    风险评分: item.riskScore,
    检测时间: item.detectionTime,
    状态: getStatusText(item.status)
  }))
  
  const csvContent = [
    Object.keys(exportData[0]).join(','),
    ...exportData.map(row => Object.values(row).join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `fraud-detection-results-${dayjs().format('YYYY-MM-DD')}.csv`
  link.click()
  URL.revokeObjectURL(url)
  
  message.success('检测结果导出成功')
}

const handleModelConfig = () => {
  showConfigModal.value = true
}

const handleAddSave = () => {
  if (!addForm.customerName || !addForm.idNumber || !addForm.detectionType) {
    message.error('请填写完整的检测信息')
    return
  }
  
  const newRecord = {
    id: Date.now(),
    customerName: addForm.customerName,
    idNumber: addForm.idNumber,
    detectionType: addForm.detectionType,
    riskLevel: 'medium',
    riskScore: Math.floor(Math.random() * 100),
    detectionTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: 'pending'
  }
  
  tableData.value.unshift(newRecord)
  pagination.total = tableData.value.length
  
  // 重置表单
  Object.assign(addForm, {
    customerName: '',
    idNumber: '',
    detectionType: '',
    phoneNumber: '',
    description: ''
  })
  
  showAddModal.value = false
  message.success('检测任务已创建')
}

const beforeUpload = (file) => {
  const isValidType = file.type === 'text/csv' || 
    file.type === 'application/vnd.ms-excel' ||
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  
  if (!isValidType) {
    message.error('只能上传CSV或Excel文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  fileList.value = [file]
  return false
}

const handleRemoveFile = () => {
  fileList.value = []
}

const handleBatchSave = () => {
  if (!batchForm.detectionType || fileList.value.length === 0) {
    message.error('请选择检测类型并上传文件')
    return
  }
  
  message.success('批量检测任务已提交，请稍后查看结果')
  showBatchModal.value = false
  
  // 重置表单
  Object.assign(batchForm, {
    detectionType: '',
    description: ''
  })
  fileList.value = []
}

const handleConfigSave = () => {
  message.success('模型配置已保存')
  showConfigModal.value = false
}

// 图表初始化
const initCharts = () => {
  // 欺诈类型分布图
  const typeChart = echarts.init(fraudTypeChart.value)
  const typeOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '欺诈类型',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 35, name: '身份欺诈', itemStyle: { color: '#722ed1' } },
          { value: 28, name: '申请欺诈', itemStyle: { color: '#1890ff' } },
          { value: 22, name: '交易欺诈', itemStyle: { color: '#52c41a' } },
          { value: 15, name: '团伙欺诈', itemStyle: { color: '#fa541c' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  typeChart.setOption(typeOption)

  // 趋势分析图
  const trendChartInstance = echarts.init(trendChart.value)
  const trendOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '检测数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '欺诈率(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '总检测量',
        type: 'bar',
        data: [320, 302, 301, 334, 390, 330, 320],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '欺诈案件',
        type: 'bar',
        data: [15, 12, 18, 22, 25, 19, 16],
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '欺诈率',
        type: 'line',
        yAxisIndex: 1,
        data: [4.7, 4.0, 6.0, 6.6, 6.4, 5.8, 5.0],
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  trendChartInstance.setOption(trendOption)

  // 风险热力图
  const heatmapChartInstance = echarts.init(riskHeatmapChart.value)
  const hours = ['00', '02', '04', '06', '08', '10', '12', '14', '16', '18', '20', '22']
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  
  const data = []
  for (let i = 0; i < 7; i++) {
    for (let j = 0; j < 12; j++) {
      data.push([j, i, Math.floor(Math.random() * 50)])
    }
  }

  const heatmapOption = {
    title: {
      text: '风险事件时间热力图',
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: (params) => {
        return `${days[params.data[1]]} ${hours[params.data[0]]}:00<br/>风险事件: ${params.data[2]}起`
      }
    },
    grid: {
      height: '60%',
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 50,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#50a3ba', '#eac736', '#d94e5d']
      }
    },
    series: [
      {
        name: '风险事件',
        type: 'heatmap',
        data: data,
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  heatmapChartInstance.setOption(heatmapOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    typeChart.resize()
    trendChartInstance.resize()
    heatmapChartInstance.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.overview-section {
  margin-bottom: 16px;
}

.overview-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.chart-section {
  margin-bottom: 16px;
}

.risk-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-text {
  font-size: 12px;
  color: #666;
}

.upload-tip {
  color: #666;
  font-size: 12px;
  margin-top: 8px;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

:deep(.ant-descriptions-item-label) {
  width: 120px;
}
</style>