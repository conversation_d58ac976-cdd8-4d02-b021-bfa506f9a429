<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>合规检查</h2>
      
      <!-- 合规概览 -->
      <a-row :gutter="16" class="overview-section">
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="今日检查任务" 
              :value="overviewData.todayTasks" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <audit-outlined style="color: #1890ff" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="违规发现" 
              :value="overviewData.violations" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <exclamation-circle-outlined style="color: #ff4d4f" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="合规率" 
              :value="overviewData.complianceRate" 
              suffix="%" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <check-circle-outlined style="color: #52c41a" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="待整改项" 
              :value="overviewData.pendingItems" 
              :value-style="{ color: '#faad14' }"
            >
              <template #suffix>
                <clock-circle-outlined style="color: #faad14" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 查询条件 -->
      <a-card class="search-card">
        <a-form @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="检查类型">
                <a-select v-model:value="searchForm.checkType" placeholder="请选择检查类型" allow-clear>
                  <a-select-option value="operation">操作合规</a-select-option>
                  <a-select-option value="data">数据合规</a-select-option>
                  <a-select-option value="legal">法律合规</a-select-option>
                  <a-select-option value="financial">财务合规</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="检查状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="pending">待检查</a-select-option>
                  <a-select-option value="checking">检查中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="failed">不合规</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="负责部门">
                <a-select v-model:value="searchForm.department" placeholder="请选择部门" allow-clear>
                  <a-select-option value="collection">催收部</a-select-option>
                  <a-select-option value="legal">法务部</a-select-option>
                  <a-select-option value="finance">财务部</a-select-option>
                  <a-select-option value="risk">风控部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="检查时间">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 100%"
                  @change="handleDateChange"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row>
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    查询
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
                
                <div class="action-buttons">
                  <a-space>
                    <a-button @click="handleBatchCheck">
                      <safety-certificate-outlined />
                      批量检查
                    </a-button>
                    <a-button @click="handleExportReport">
                      <download-outlined />
                      导出报告
                    </a-button>
                    <a-button @click="handleRuleConfig">
                      <setting-outlined />
                      规则配置
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 合规统计图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="合规检查分布" :body-style="{ padding: '20px' }">
            <div ref="complianceDistributionChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="合规趋势分析" :body-style="{ padding: '20px' }">
            <div ref="complianceTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <a-col :span="24">
          <a-card title="部门合规状况" :body-style="{ padding: '20px' }">
            <div ref="departmentComplianceChart" style="height: 350px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 检查任务列表 -->
      <a-card title="合规检查任务">
        <template #extra>
          <a-space>
            <a-button type="primary" @click="showCreateModal = true">
              <plus-outlined />
              新建检查
            </a-button>
            <a-button @click="handleRefresh">
              <reload-outlined />
              刷新
            </a-button>
          </a-space>
        </template>
        
        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'checkType'">
              <a-tag :color="getCheckTypeColor(record.checkType)">
                {{ getCheckTypeText(record.checkType) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'complianceScore'">
              <div class="compliance-score-container">
                <a-progress 
                  type="circle" 
                  :percent="record.complianceScore" 
                  :width="50"
                  :stroke-color="getComplianceScoreColor(record.complianceScore)"
                />
                <div class="score-text">{{ record.complianceScore }}分</div>
              </div>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskLevelColor(record.riskLevel)">
                {{ getRiskLevelText(record.riskLevel) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space size="small">
                <a-button size="small" type="link" @click="handleViewDetail(record)">
                  查看详情
                </a-button>
                <a-button size="small" type="link" @click="handleStartCheck(record)" v-if="record.status === 'pending'">
                  开始检查
                </a-button>
                <a-button size="small" type="link" @click="handleViewResult(record)" v-if="record.status === 'completed' || record.status === 'failed'">
                  查看结果
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="handleMoreAction($event, record)">
                      <a-menu-item key="edit">编辑</a-menu-item>
                      <a-menu-item key="duplicate">复制</a-menu-item>
                      <a-menu-item key="archive">归档</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" danger>删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small" type="link">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建检查任务弹窗 -->
    <a-modal 
      v-model:open="showCreateModal" 
      title="新建合规检查任务" 
      width="800px"
      @ok="handleCreateSave"
    >
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="检查标题" required>
              <a-input v-model:value="createForm.title" placeholder="请输入检查标题" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查类型" required>
              <a-select v-model:value="createForm.checkType" placeholder="请选择检查类型">
                <a-select-option value="operation">操作合规</a-select-option>
                <a-select-option value="data">数据合规</a-select-option>
                <a-select-option value="legal">法律合规</a-select-option>
                <a-select-option value="financial">财务合规</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="负责部门" required>
              <a-select v-model:value="createForm.department" placeholder="请选择负责部门">
                <a-select-option value="collection">催收部</a-select-option>
                <a-select-option value="legal">法务部</a-select-option>
                <a-select-option value="finance">财务部</a-select-option>
                <a-select-option value="risk">风控部</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="优先级" required>
              <a-select v-model:value="createForm.priority" placeholder="请选择优先级">
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="检查员">
              <a-select v-model:value="createForm.inspector" placeholder="请选择检查员">
                <a-select-option value="zhang_san">张三</a-select-option>
                <a-select-option value="li_si">李四</a-select-option>
                <a-select-option value="wang_wu">王五</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="计划完成时间">
              <a-date-picker 
                v-model:value="createForm.planDate" 
                style="width: 100%"
                placeholder="请选择计划完成时间"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="检查范围" required>
          <a-checkbox-group v-model:value="createForm.checkScope">
            <a-row>
              <a-col :span="6">
                <a-checkbox value="process">流程规范</a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox value="document">文档记录</a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox value="operation">操作行为</a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox value="system">系统配置</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="检查描述">
          <a-textarea 
            v-model:value="createForm.description" 
            placeholder="请输入检查描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 检查详情弹窗 -->
    <a-modal 
      v-model:open="showDetailModal" 
      title="合规检查详情" 
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="检查标题" :span="2">
            {{ selectedRecord.title }}
          </a-descriptions-item>
          <a-descriptions-item label="检查类型">
            <a-tag :color="getCheckTypeColor(selectedRecord.checkType)">
              {{ getCheckTypeText(selectedRecord.checkType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="检查状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="负责部门">
            {{ getDepartmentText(selectedRecord.department) }}
          </a-descriptions-item>
          <a-descriptions-item label="检查员">
            {{ selectedRecord.inspector }}
          </a-descriptions-item>
          <a-descriptions-item label="合规评分">
            {{ selectedRecord.complianceScore }}分
          </a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getRiskLevelColor(selectedRecord.riskLevel)">
              {{ getRiskLevelText(selectedRecord.riskLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedRecord.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="完成时间">
            {{ selectedRecord.completeTime || '未完成' }}
          </a-descriptions-item>
          <a-descriptions-item label="检查描述" :span="2">
            {{ selectedRecord.description }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 检查项目列表 -->
        <a-divider>检查项目</a-divider>
        <a-table 
          :columns="checkItemColumns" 
          :data-source="selectedRecord.checkItems || []" 
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record: item }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getCheckItemStatusColor(item.status)">
                {{ getCheckItemStatusText(item.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'result'">
              <a-tag :color="item.result === 'pass' ? 'green' : 'red'">
                {{ item.result === 'pass' ? '通过' : '不通过' }}
              </a-tag>
            </template>
          </template>
        </a-table>

        <!-- 违规问题列表 -->
        <a-divider>发现问题</a-divider>
        <a-list 
          v-if="selectedRecord.violations && selectedRecord.violations.length > 0"
          :data-source="selectedRecord.violations"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span :style="{ color: getViolationLevelColor(item.level) }">
                    {{ item.title }}
                  </span>
                </template>
                <template #description>
                  <div>
                    <div>问题描述: {{ item.description }}</div>
                    <div>整改建议: {{ item.suggestion }}</div>
                    <div>责任人: {{ item.responsible }}</div>
                  </div>
                </template>
              </a-list-item-meta>
              <div>
                <a-tag :color="getViolationLevelColor(item.level)">
                  {{ item.level }}
                </a-tag>
              </div>
            </a-list-item>
          </template>
        </a-list>
        <a-empty v-else description="未发现合规问题" />
      </div>
    </a-modal>

    <!-- 检查结果弹窗 -->
    <a-modal 
      v-model:open="showResultModal" 
      title="检查结果报告" 
      width="800px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-result
          :status="selectedRecord.status === 'completed' ? 'success' : 'warning'"
          :title="selectedRecord.status === 'completed' ? '检查完成' : '发现违规'"
          :sub-title="`合规评分: ${selectedRecord.complianceScore}分`"
        >
          <template #extra>
            <a-space>
              <a-button type="primary" @click="handleDownloadReport">
                <download-outlined />
                下载报告
              </a-button>
              <a-button @click="showResultModal = false">
                关闭
              </a-button>
            </a-space>
          </template>
        </a-result>
        
        <!-- 检查摘要 -->
        <a-card title="检查摘要" size="small">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="检查项目" :value="selectedRecord.checkItems?.length || 0" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="通过项目" :value="selectedRecord.passCount || 0" suffix="项" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="发现问题" :value="selectedRecord.violations?.length || 0" suffix="个" />
            </a-col>
          </a-row>
        </a-card>
      </div>
    </a-modal>

    <!-- 批量检查弹窗 -->
    <a-modal 
      v-model:open="showBatchModal" 
      title="批量合规检查" 
      width="600px"
      @ok="handleBatchSave"
    >
      <a-form layout="vertical">
        <a-form-item label="检查范围" required>
          <a-checkbox-group v-model:value="batchForm.departments">
            <a-row>
              <a-col :span="12">
                <a-checkbox value="collection">催收部</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="legal">法务部</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="finance">财务部</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="risk">风控部</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="检查类型" required>
          <a-checkbox-group v-model:value="batchForm.checkTypes">
            <a-row>
              <a-col :span="12">
                <a-checkbox value="operation">操作合规</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="data">数据合规</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="legal">法律合规</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="financial">财务合规</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="检查说明">
          <a-textarea 
            v-model:value="batchForm.description" 
            placeholder="请输入批量检查说明"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 规则配置弹窗 -->
    <a-modal 
      v-model:open="showRuleModal" 
      title="合规规则配置" 
      width="900px"
      @ok="handleRuleSave"
    >
      <a-tabs v-model:activeKey="ruleActiveTab">
        <a-tab-pane key="rules" tab="检查规则">
          <a-table 
            :columns="ruleColumns" 
            :data-source="complianceRules" 
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'enabled'">
                <a-switch v-model:checked="record.enabled" size="small" />
              </template>
              <template v-if="column.key === 'level'">
                <a-tag :color="getRiskLevelColor(record.level)">
                  {{ getRiskLevelText(record.level) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'actions'">
                <a-space size="small">
                  <a-button size="small" type="link">编辑</a-button>
                  <a-button size="small" type="link" danger>删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="templates" tab="检查模板">
          <a-list :data-source="checkTemplates">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>{{ item.name }}</template>
                  <template #description>{{ item.description }}</template>
                </a-list-item-meta>
                <div>
                  <a-space>
                    <a-tag>{{ item.type }}</a-tag>
                    <a-button size="small" type="link">使用</a-button>
                    <a-button size="small" type="link">编辑</a-button>
                  </a-space>
                </div>
              </a-list-item>
            </template>
          </a-list>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  AuditOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  ReloadOutlined,
  SafetyCertificateOutlined,
  DownloadOutlined,
  SettingOutlined,
  PlusOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)

// 概览数据
const overviewData = reactive({
  todayTasks: 42,
  violations: 8,
  complianceRate: 87.5,
  pendingItems: 15
})

// 搜索表单
const searchForm = reactive({
  checkType: '',
  status: '',
  department: '',
  dateRange: [dayjs().subtract(7, 'day'), dayjs()]
})

// 弹窗控制
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showResultModal = ref(false)
const showBatchModal = ref(false)
const showRuleModal = ref(false)
const selectedRecord = ref(null)

// 表单数据
const createForm = reactive({
  title: '',
  checkType: '',
  department: '',
  priority: '',
  inspector: '',
  planDate: null,
  checkScope: [],
  description: ''
})

const batchForm = reactive({
  departments: [],
  checkTypes: [],
  description: ''
})

// 规则配置
const ruleActiveTab = ref('rules')
const complianceRules = ref([
  {
    id: 1,
    name: '操作日志完整性检查',
    type: '操作合规',
    condition: '操作日志记录完整性验证',
    level: 'high',
    enabled: true
  },
  {
    id: 2,
    name: '数据访问权限检查',
    type: '数据合规',
    condition: '用户数据访问权限合规性检查',
    level: 'high',
    enabled: true
  },
  {
    id: 3,
    name: '客户信息保护检查',
    type: '法律合规',
    condition: '客户隐私信息保护合规检查',
    level: 'medium',
    enabled: true
  },
  {
    id: 4,
    name: '财务报表准确性检查',
    type: '财务合规',
    condition: '财务数据准确性和完整性检查',
    level: 'high',
    enabled: false
  }
])

const checkTemplates = ref([
  {
    id: 1,
    name: '操作合规标准模板',
    type: '操作合规',
    description: '包含操作流程、权限管理、日志记录等检查项'
  },
  {
    id: 2,
    name: '数据合规标准模板',
    type: '数据合规',
    description: '包含数据安全、隐私保护、访问控制等检查项'
  },
  {
    id: 3,
    name: '法律合规标准模板',
    type: '法律合规',
    description: '包含合同管理、法律条款、监管要求等检查项'
  }
])

// 表格配置
const columns = [
  {
    title: '检查标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    fixed: 'left'
  },
  {
    title: '检查类型',
    key: 'checkType',
    width: 120
  },
  {
    title: '负责部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '检查员',
    dataIndex: 'inspector',
    key: 'inspector',
    width: 100
  },
  {
    title: '优先级',
    key: 'priority',
    width: 80
  },
  {
    title: '合规评分',
    key: 'complianceScore',
    width: 120
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 检查项目表格列
const checkItemColumns = [
  {
    title: '检查项目',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '检查内容',
    dataIndex: 'content',
    key: 'content'
  },
  {
    title: '状态',
    key: 'status'
  },
  {
    title: '检查结果',
    key: 'result'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark'
  }
]

// 规则配置表格列
const ruleColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '检查类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '检查条件',
    dataIndex: 'condition',
    key: 'condition'
  },
  {
    title: '风险等级',
    key: 'level',
    width: 100
  },
  {
    title: '启用状态',
    key: 'enabled',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    title: '2024年第一季度操作合规检查',
    checkType: 'operation',
    department: 'collection',
    inspector: '张三',
    priority: 'high',
    complianceScore: 92,
    riskLevel: 'low',
    status: 'completed',
    createTime: '2024-01-15 09:00:00',
    completeTime: '2024-01-20 16:30:00',
    description: '对催收部门操作流程进行全面合规检查',
    checkItems: [
      { name: '操作流程规范性', content: '检查操作是否符合标准流程', status: 'completed', result: 'pass', remark: '流程规范' },
      { name: '权限管理合规性', content: '检查用户权限分配是否合理', status: 'completed', result: 'pass', remark: '权限设置合理' },
      { name: '日志记录完整性', content: '检查操作日志是否完整记录', status: 'completed', result: 'fail', remark: '部分日志缺失' }
    ],
    violations: [
      {
        title: '操作日志记录不完整',
        description: '发现部分关键操作未记录日志',
        suggestion: '完善日志记录机制，确保所有操作都有日志',
        level: '中风险',
        responsible: '技术部-李四'
      }
    ],
    passCount: 2
  },
  {
    id: 2,
    title: '客户数据保护合规检查',
    checkType: 'data',
    department: 'risk',
    inspector: '李四',
    priority: 'high',
    complianceScore: 78,
    riskLevel: 'medium',
    status: 'failed',
    createTime: '2024-01-12 14:20:00',
    completeTime: '2024-01-18 11:45:00',
    description: '检查客户数据处理是否符合数据保护法规',
    checkItems: [
      { name: '数据加密检查', content: '检查敏感数据是否加密存储', status: 'completed', result: 'pass', remark: '加密措施到位' },
      { name: '访问权限控制', content: '检查数据访问权限控制', status: 'completed', result: 'fail', remark: '权限过于宽泛' },
      { name: '数据备份验证', content: '检查数据备份策略执行情况', status: 'completed', result: 'pass', remark: '备份正常' }
    ],
    violations: [
      {
        title: '数据访问权限过于宽泛',
        description: '发现多个员工具有不必要的敏感数据访问权限',
        suggestion: '重新梳理数据访问权限，实施最小权限原则',
        level: '高风险',
        responsible: '风控部-王五'
      },
      {
        title: '数据使用记录不完整',
        description: '部分数据使用情况未有完整记录',
        suggestion: '建立完善的数据使用追踪机制',
        level: '中风险',
        responsible: '风控部-赵六'
      }
    ],
    passCount: 2
  },
  {
    id: 3,
    title: '财务报表准确性检查',
    checkType: 'financial',
    department: 'finance',
    inspector: '王五',
    priority: 'medium',
    complianceScore: 95,
    riskLevel: 'low',
    status: 'checking',
    createTime: '2024-01-10 08:30:00',
    description: '检查财务报表数据的准确性和完整性',
    checkItems: [
      { name: '收入确认检查', content: '检查收入确认是否符合会计准则', status: 'completed', result: 'pass', remark: '确认准确' },
      { name: '成本核算验证', content: '验证成本核算的准确性', status: 'checking', result: '', remark: '检查中' },
      { name: '报表披露完整性', content: '检查财务报表披露是否完整', status: 'pending', result: '', remark: '待检查' }
    ],
    violations: [],
    passCount: 1
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: tableData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const complianceDistributionChart = ref()
const complianceTrendChart = ref()
const departmentComplianceChart = ref()

// 辅助方法
const getCheckTypeColor = (type) => {
  const colors = {
    operation: '#1890ff',
    data: '#52c41a',
    legal: '#722ed1',
    financial: '#fa541c'
  }
  return colors[type] || '#666'
}

const getCheckTypeText = (type) => {
  const texts = {
    operation: '操作合规',
    data: '数据合规',
    legal: '法律合规',
    financial: '财务合规'
  }
  return texts[type] || '未知'
}

const getPriorityColor = (priority) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[priority] || '#666'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || '未知'
}

const getComplianceScoreColor = (score) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#faad14'
  return '#ff4d4f'
}

const getStatusColor = (status) => {
  const colors = {
    pending: '#faad14',
    checking: '#1890ff',
    completed: '#52c41a',
    failed: '#ff4d4f'
  }
  return colors[status] || '#666'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待检查',
    checking: '检查中',
    completed: '已完成',
    failed: '不合规'
  }
  return texts[status] || '未知'
}

const getRiskLevelColor = (level) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[level] || '#666'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || '未知'
}

const getDepartmentText = (dept) => {
  const texts = {
    collection: '催收部',
    legal: '法务部',
    finance: '财务部',
    risk: '风控部'
  }
  return texts[dept] || '未知'
}

const getCheckItemStatusColor = (status) => {
  const colors = {
    pending: '#faad14',
    checking: '#1890ff',
    completed: '#52c41a'
  }
  return colors[status] || '#666'
}

const getCheckItemStatusText = (status) => {
  const texts = {
    pending: '待检查',
    checking: '检查中',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

const getViolationLevelColor = (level) => {
  const colors = {
    '高风险': '#ff4d4f',
    '中风险': '#faad14',
    '低风险': '#52c41a'
  }
  return colors[level] || '#666'
}

// 事件处理方法
const handleSearch = () => {
  console.log('搜索参数:', searchForm)
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已更新')
  }, 1000)
}

const resetSearch = () => {
  searchForm.checkType = ''
  searchForm.status = ''
  searchForm.department = ''
  searchForm.dateRange = [dayjs().subtract(7, 'day'), dayjs()]
  handleSearch()
}

const handleDateChange = (dates) => {
  if (dates) {
    console.log('日期范围变更:', dates)
  }
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const handleViewDetail = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleStartCheck = (record) => {
  Modal.confirm({
    title: '确认开始检查',
    content: `确定要开始检查"${record.title}"吗？`,
    onOk: () => {
      record.status = 'checking'
      message.success('检查任务已开始')
    }
  })
}

const handleViewResult = (record) => {
  selectedRecord.value = record
  showResultModal.value = true
}

const handleMoreAction = (event, record) => {
  const { key } = event
  switch (key) {
    case 'edit':
      message.info('编辑功能开发中')
      break
    case 'duplicate':
      message.success('任务已复制')
      break
    case 'archive':
      message.success('任务已归档')
      break
    case 'delete':
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除"${record.title}"吗？`,
        onOk: () => {
          const index = tableData.value.findIndex(item => item.id === record.id)
          if (index > -1) {
            tableData.value.splice(index, 1)
            pagination.total = tableData.value.length
            message.success('删除成功')
          }
        }
      })
      break
  }
}

const handleBatchCheck = () => {
  showBatchModal.value = true
}

const handleExportReport = () => {
  const exportData = tableData.value.map(item => ({
    检查标题: item.title,
    检查类型: getCheckTypeText(item.checkType),
    负责部门: getDepartmentText(item.department),
    检查员: item.inspector,
    合规评分: item.complianceScore,
    风险等级: getRiskLevelText(item.riskLevel),
    状态: getStatusText(item.status),
    创建时间: item.createTime
  }))
  
  const csvContent = [
    Object.keys(exportData[0]).join(','),
    ...exportData.map(row => Object.values(row).join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `compliance-check-report-${dayjs().format('YYYY-MM-DD')}.csv`
  link.click()
  URL.revokeObjectURL(url)
  
  message.success('报告导出成功')
}

const handleRuleConfig = () => {
  showRuleModal.value = true
}

const handleCreateSave = () => {
  if (!createForm.title || !createForm.checkType || !createForm.department || !createForm.priority) {
    message.error('请填写完整的检查信息')
    return
  }
  
  const newRecord = {
    id: Date.now(),
    title: createForm.title,
    checkType: createForm.checkType,
    department: createForm.department,
    inspector: createForm.inspector || '系统分配',
    priority: createForm.priority,
    complianceScore: 0,
    riskLevel: 'medium',
    status: 'pending',
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    description: createForm.description,
    checkItems: [],
    violations: []
  }
  
  tableData.value.unshift(newRecord)
  pagination.total = tableData.value.length
  
  // 重置表单
  Object.assign(createForm, {
    title: '',
    checkType: '',
    department: '',
    priority: '',
    inspector: '',
    planDate: null,
    checkScope: [],
    description: ''
  })
  
  showCreateModal.value = false
  message.success('检查任务已创建')
}

const handleBatchSave = () => {
  if (!batchForm.departments.length || !batchForm.checkTypes.length) {
    message.error('请选择检查范围和检查类型')
    return
  }
  
  message.success('批量检查任务已创建')
  showBatchModal.value = false
  
  // 重置表单
  Object.assign(batchForm, {
    departments: [],
    checkTypes: [],
    description: ''
  })
}

const handleRuleSave = () => {
  message.success('规则配置已保存')
  showRuleModal.value = false
}

const handleDownloadReport = () => {
  message.success('检查报告下载成功')
}

// 图表初始化
const initCharts = () => {
  // 合规检查分布图
  const distributionChart = echarts.init(complianceDistributionChart.value)
  const distributionOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '检查类型',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 35, name: '操作合规', itemStyle: { color: '#1890ff' } },
          { value: 28, name: '数据合规', itemStyle: { color: '#52c41a' } },
          { value: 22, name: '法律合规', itemStyle: { color: '#722ed1' } },
          { value: 15, name: '财务合规', itemStyle: { color: '#fa541c' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  distributionChart.setOption(distributionOption)

  // 合规趋势图
  const trendChart = echarts.init(complianceTrendChart.value)
  const trendOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '检查数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '合规率(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '检查任务',
        type: 'bar',
        data: [45, 52, 48, 61, 55, 67],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '违规数量',
        type: 'bar',
        data: [8, 12, 9, 15, 11, 13],
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '合规率',
        type: 'line',
        yAxisIndex: 1,
        data: [82.2, 76.9, 81.3, 75.4, 80.0, 80.6],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 部门合规状况图
  const departmentChart = echarts.init(departmentComplianceChart.value)
  const departmentOption = {
    title: {
      text: '各部门合规状况对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['催收部', '法务部', '财务部', '风控部']
    },
    yAxis: [
      {
        type: 'value',
        name: '任务数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '合规评分',
        position: 'right',
        max: 100
      }
    ],
    series: [
      {
        name: '总任务',
        type: 'bar',
        data: [25, 18, 15, 22],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '已完成',
        type: 'bar',
        data: [20, 15, 12, 18],
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '违规数量',
        type: 'bar',
        data: [3, 2, 1, 4],
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '合规评分',
        type: 'line',
        yAxisIndex: 1,
        data: [88, 89, 93, 82],
        itemStyle: { color: '#faad14' },
        lineStyle: { width: 3 }
      }
    ]
  }
  departmentChart.setOption(departmentOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    distributionChart.resize()
    trendChart.resize()
    departmentChart.resize()
  })
}

// 生命周期
onMounted(() => {
  handleSearch()
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.overview-section {
  margin-bottom: 16px;
}

.overview-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.action-buttons {
  /* 操作按钮容器样式 */
}

.chart-section {
  margin-bottom: 16px;
}

.compliance-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-text {
  font-size: 12px;
  color: #666;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

:deep(.ant-descriptions-item-label) {
  width: 120px;
}
</style>