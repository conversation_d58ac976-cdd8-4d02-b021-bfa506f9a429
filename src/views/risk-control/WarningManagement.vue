<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>预警管理</h2>
      
      <!-- 预警概览 -->
      <a-row :gutter="16" class="overview-section">
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="今日预警" 
              :value="overviewData.todayWarnings" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <bell-outlined style="color: #ff4d4f" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="高风险预警" 
              :value="overviewData.highRiskWarnings" 
              :value-style="{ color: '#faad14' }"
            >
              <template #suffix>
                <warning-outlined style="color: '#faad14" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="处理率" 
              :value="overviewData.processRate" 
              suffix="%" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <check-circle-outlined style="color: '#52c41a" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic 
              title="待处理" 
              :value="overviewData.pendingWarnings" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <clock-circle-outlined style="color: '#1890ff" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 预警规则管理 -->
      <a-card title="预警规则" class="rules-card">
        <template #extra>
          <a-space>
            <a-button type="primary" @click="showRuleModal = true">
              <plus-outlined />
              新增规则
            </a-button>
            <a-button @click="handleBatchControl">
              <control-outlined />
              批量控制
            </a-button>
          </a-space>
        </template>
        
        <a-table 
          :columns="ruleColumns" 
          :data-source="warningRules" 
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'enabled'">
              <a-switch 
                v-model:checked="record.enabled" 
                size="small"
                @change="handleRuleToggle(record)"
              />
            </template>
            <template v-if="column.key === 'level'">
              <a-tag :color="getRiskLevelColor(record.level)">
                {{ getRiskLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'warningCount'">
              <a-badge :count="record.warningCount" :overflow-count="99">
                <span>{{ record.warningCount }}</span>
              </a-badge>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space size="small">
                <a-button size="small" type="link" @click="handleEditRule(record)">
                  编辑
                </a-button>
                <a-button size="small" type="link" @click="handleTestRule(record)">
                  测试
                </a-button>
                <a-button size="small" type="link" danger @click="handleDeleteRule(record)">
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 预警事件列表 -->
      <a-card title="预警事件" class="warnings-card">
        <template #extra>
          <a-space>
            <a-button @click="handleRefresh">
              <reload-outlined />
              刷新
            </a-button>
            <a-button @click="handleExportWarnings">
              <download-outlined />
              导出预警
            </a-button>
            <a-button @click="showFilterModal = true">
              <filter-outlined />
              高级筛选
            </a-button>
          </a-space>
        </template>
        
        <!-- 快速筛选 -->
        <div class="quick-filters">
          <a-space wrap>
            <a-tag 
              :color="activeFilter === 'all' ? 'blue' : 'default'"
              @click="handleQuickFilter('all')"
            >
              全部 ({{ warningEvents.length }})
            </a-tag>
            <a-tag 
              :color="activeFilter === 'pending' ? 'orange' : 'default'"
              @click="handleQuickFilter('pending')"
            >
              待处理 ({{ getPendingCount() }})
            </a-tag>
            <a-tag 
              :color="activeFilter === 'high' ? 'red' : 'default'"
              @click="handleQuickFilter('high')"
            >
              高风险 ({{ getHighRiskCount() }})
            </a-tag>
            <a-tag 
              :color="activeFilter === 'today' ? 'green' : 'default'"
              @click="handleQuickFilter('today')"
            >
              今日预警 ({{ getTodayCount() }})
            </a-tag>
          </a-space>
        </div>
        
        <a-table 
          :columns="warningColumns" 
          :data-source="filteredWarnings" 
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
          :row-selection="rowSelection"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'level'">
              <a-tag :color="getRiskLevelColor(record.level)">
                {{ getRiskLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'type'">
              <a-tag :color="getWarningTypeColor(record.type)">
                {{ getWarningTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space size="small">
                <a-button size="small" type="link" @click="handleViewDetail(record)">
                  查看详情
                </a-button>
                <a-button size="small" type="link" @click="handleProcessWarning(record)" v-if="record.status === 'pending'">
                  立即处理
                </a-button>
                <a-button size="small" type="link" @click="handleIgnoreWarning(record)" v-if="record.status === 'pending'">
                  忽略
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="handleMoreAction($event, record)">
                      <a-menu-item key="assign">分配处理人</a-menu-item>
                      <a-menu-item key="escalate">升级处理</a-menu-item>
                      <a-menu-item key="history">查看历史</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="archive">归档</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small" type="link">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
        
        <!-- 批量操作 -->
        <div v-if="selectedRowKeys.length > 0" class="batch-actions">
          <a-space>
            <span>已选择 {{ selectedRowKeys.length }} 项</span>
            <a-button size="small" @click="handleBatchProcess">
              批量处理
            </a-button>
            <a-button size="small" @click="handleBatchIgnore">
              批量忽略
            </a-button>
            <a-button size="small" @click="handleBatchAssign">
              批量分配
            </a-button>
            <a-button size="small" type="link" @click="clearSelection">
              清空选择
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 预警统计图表 -->
      <a-row :gutter="16" class="chart-section">
        <a-col :span="12">
          <a-card title="预警趋势分析" :body-style="{ padding: '20px' }">
            <div ref="warningTrendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="预警类型分布" :body-style="{ padding: '20px' }">
            <div ref="warningTypeChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 新增/编辑预警规则弹窗 -->
    <a-modal 
      v-model:open="showRuleModal" 
      :title="editingRule ? '编辑预警规则' : '新增预警规则'" 
      width="800px"
      @ok="handleRuleSave"
    >
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="规则名称" required>
              <a-input v-model:value="ruleForm.name" placeholder="请输入规则名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预警类型" required>
              <a-select v-model:value="ruleForm.type" placeholder="请选择预警类型">
                <a-select-option value="overdue">逾期预警</a-select-option>
                <a-select-option value="amount">金额异常</a-select-option>
                <a-select-option value="frequency">频率异常</a-select-option>
                <a-select-option value="risk">风险预警</a-select-option>
                <a-select-option value="system">系统预警</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="风险等级" required>
              <a-select v-model:value="ruleForm.level" placeholder="请选择风险等级">
                <a-select-option value="high">高风险</a-select-option>
                <a-select-option value="medium">中风险</a-select-option>
                <a-select-option value="low">低风险</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预警阈值" required>
              <a-input-number 
                v-model:value="ruleForm.threshold" 
                :min="0" 
                style="width: 100%"
                placeholder="请输入预警阈值"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="触发条件" required>
          <a-textarea 
            v-model:value="ruleForm.condition" 
            placeholder="请输入触发条件"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="处理建议">
          <a-textarea 
            v-model:value="ruleForm.suggestion" 
            placeholder="请输入处理建议"
            :rows="3"
          />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="通知方式">
              <a-checkbox-group v-model:value="ruleForm.notificationMethods">
                <a-checkbox value="email">邮件</a-checkbox>
                <a-checkbox value="sms">短信</a-checkbox>
                <a-checkbox value="system">系统通知</a-checkbox>
                <a-checkbox value="webhook">Webhook</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查频率">
              <a-select v-model:value="ruleForm.frequency" placeholder="请选择检查频率">
                <a-select-option value="realtime">实时</a-select-option>
                <a-select-option value="hourly">每小时</a-select-option>
                <a-select-option value="daily">每日</a-select-option>
                <a-select-option value="weekly">每周</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 预警详情弹窗 -->
    <a-modal 
      v-model:open="showDetailModal" 
      title="预警详情" 
      width="900px"
      :footer="null"
    >
      <div v-if="selectedWarning">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="预警标题" :span="2">
            {{ selectedWarning.title }}
          </a-descriptions-item>
          <a-descriptions-item label="预警类型">
            <a-tag :color="getWarningTypeColor(selectedWarning.type)">
              {{ getWarningTypeText(selectedWarning.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getRiskLevelColor(selectedWarning.level)">
              {{ getRiskLevelText(selectedWarning.level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedWarning.priority)">
              {{ getPriorityText(selectedWarning.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedWarning.status)">
              {{ getStatusText(selectedWarning.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="触发时间">
            {{ selectedWarning.triggerTime }}
          </a-descriptions-item>
          <a-descriptions-item label="处理时间">
            {{ selectedWarning.processTime || '未处理' }}
          </a-descriptions-item>
          <a-descriptions-item label="处理人">
            {{ selectedWarning.processor || '未分配' }}
          </a-descriptions-item>
          <a-descriptions-item label="影响对象">
            {{ selectedWarning.target }}
          </a-descriptions-item>
          <a-descriptions-item label="预警描述" :span="2">
            {{ selectedWarning.description }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 预警详细信息 -->
        <a-divider>详细信息</a-divider>
        <a-tabs v-model:activeKey="detailActiveTab">
          <a-tab-pane key="details" tab="详细数据">
            <a-descriptions size="small" bordered>
              <a-descriptions-item 
                v-for="(value, key) in selectedWarning.details" 
                :key="key" 
                :label="key"
              >
                {{ value }}
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          
          <a-tab-pane key="history" tab="处理历史">
            <a-timeline>
              <a-timeline-item 
                v-for="item in selectedWarning.history || []" 
                :key="item.id"
                :color="getHistoryColor(item.action)"
              >
                <div class="history-item">
                  <div class="history-header">
                    <strong>{{ item.action }}</strong>
                    <span class="history-time">{{ item.time }}</span>
                  </div>
                  <div class="history-content">{{ item.description }}</div>
                  <div class="history-operator">操作人: {{ item.operator }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-tab-pane>
          
          <a-tab-pane key="related" tab="相关事件">
            <a-list 
              :data-source="selectedWarning.relatedEvents || []"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a href="#" @click="handleViewRelatedEvent(item)">{{ item.title }}</a>
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                  <div>
                    <a-tag :color="getRiskLevelColor(item.level)">
                      {{ getRiskLevelText(item.level) }}
                    </a-tag>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
        </a-tabs>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <a-space>
            <a-button type="primary" @click="handleProcessWarning(selectedWarning)" v-if="selectedWarning.status === 'pending'">
              立即处理
            </a-button>
            <a-button @click="handleAssignWarning(selectedWarning)">
              分配处理人
            </a-button>
            <a-button @click="handleEscalateWarning(selectedWarning)">
              升级处理
            </a-button>
            <a-button @click="handleIgnoreWarning(selectedWarning)" v-if="selectedWarning.status === 'pending'">
              忽略预警
            </a-button>
            <a-button @click="showDetailModal = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 高级筛选弹窗 -->
    <a-modal 
      v-model:open="showFilterModal" 
      title="高级筛选" 
      width="600px"
      @ok="handleFilterSave"
    >
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="预警类型">
              <a-select v-model:value="filterForm.type" placeholder="请选择预警类型" allow-clear>
                <a-select-option value="overdue">逾期预警</a-select-option>
                <a-select-option value="amount">金额异常</a-select-option>
                <a-select-option value="frequency">频率异常</a-select-option>
                <a-select-option value="risk">风险预警</a-select-option>
                <a-select-option value="system">系统预警</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="风险等级">
              <a-select v-model:value="filterForm.level" placeholder="请选择风险等级" allow-clear>
                <a-select-option value="high">高风险</a-select-option>
                <a-select-option value="medium">中风险</a-select-option>
                <a-select-option value="low">低风险</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="处理状态">
              <a-select v-model:value="filterForm.status" placeholder="请选择状态" allow-clear>
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="processing">处理中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="ignored">已忽略</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="优先级">
              <a-select v-model:value="filterForm.priority" placeholder="请选择优先级" allow-clear>
                <a-select-option value="urgent">紧急</a-select-option>
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="时间范围">
          <a-range-picker 
            v-model:value="filterForm.dateRange" 
            style="width: 100%"
            :presets="datePresets"
          />
        </a-form-item>
        
        <a-form-item label="影响对象">
          <a-input v-model:value="filterForm.target" placeholder="请输入影响对象" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量操作弹窗 -->
    <a-modal 
      v-model:open="showBatchModal" 
      :title="batchAction === 'process' ? '批量处理预警' : batchAction === 'assign' ? '批量分配处理人' : '批量操作'" 
      width="500px"
      @ok="handleBatchConfirm"
    >
      <div v-if="batchAction === 'process'">
        <p>确定要批量处理选中的 {{ selectedRowKeys.length }} 个预警吗？</p>
        <a-form-item label="处理说明">
          <a-textarea v-model:value="batchForm.remark" placeholder="请输入处理说明" :rows="3" />
        </a-form-item>
      </div>
      
      <div v-else-if="batchAction === 'assign'">
        <p>为选中的 {{ selectedRowKeys.length }} 个预警分配处理人：</p>
        <a-form-item label="处理人" required>
          <a-select v-model:value="batchForm.processor" placeholder="请选择处理人">
            <a-select-option value="zhang_san">张三</a-select-option>
            <a-select-option value="li_si">李四</a-select-option>
            <a-select-option value="wang_wu">王五</a-select-option>
          </a-select>
        </a-form-item>
      </div>
      
      <div v-else>
        <p>确定要批量{{ batchAction === 'ignore' ? '忽略' : '操作' }}选中的 {{ selectedRowKeys.length }} 个预警吗？</p>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  BellOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  ControlOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const activeFilter = ref('all')

// 概览数据
const overviewData = reactive({
  todayWarnings: 45,
  highRiskWarnings: 12,
  processRate: 78.5,
  pendingWarnings: 23
})

// 弹窗控制
const showRuleModal = ref(false)
const showDetailModal = ref(false)
const showFilterModal = ref(false)
const showBatchModal = ref(false)
const editingRule = ref(null)
const selectedWarning = ref(null)
const detailActiveTab = ref('details')
const batchAction = ref('')

// 表单数据
const ruleForm = reactive({
  name: '',
  type: '',
  level: '',
  threshold: null,
  condition: '',
  suggestion: '',
  notificationMethods: [],
  frequency: ''
})

const filterForm = reactive({
  type: '',
  level: '',
  status: '',
  priority: '',
  dateRange: null,
  target: ''
})

const batchForm = reactive({
  remark: '',
  processor: ''
})

// 日期预设
const datePresets = {
  '今天': [dayjs().startOf('day'), dayjs().endOf('day')],
  '昨天': [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')],
  '最近7天': [dayjs().subtract(7, 'day'), dayjs()],
  '最近30天': [dayjs().subtract(30, 'day'), dayjs()],
  '本月': [dayjs().startOf('month'), dayjs().endOf('month')]
}

// 预警规则数据
const warningRules = ref([
  {
    id: 1,
    name: '逾期30天预警',
    type: 'overdue',
    level: 'high',
    threshold: 30,
    condition: '案件逾期天数超过30天',
    enabled: true,
    warningCount: 15,
    lastTrigger: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    name: '大额催收预警',
    type: 'amount',
    level: 'medium',
    threshold: 100000,
    condition: '单笔催收金额超过10万元',
    enabled: true,
    warningCount: 8,
    lastTrigger: '2024-01-15 13:45:00'
  },
  {
    id: 3,
    name: '频繁操作预警',
    type: 'frequency',
    level: 'medium',
    threshold: 50,
    condition: '单用户单日操作次数超过50次',
    enabled: false,
    warningCount: 3,
    lastTrigger: '2024-01-14 16:20:00'
  },
  {
    id: 4,
    name: '系统异常预警',
    type: 'system',
    level: 'high',
    threshold: 5,
    condition: '系统错误率超过5%',
    enabled: true,
    warningCount: 2,
    lastTrigger: '2024-01-15 12:10:00'
  }
])

// 预警事件数据
const warningEvents = ref([
  {
    id: 1,
    title: '客户张三逾期案件超过30天',
    type: 'overdue',
    level: 'high',
    priority: 'urgent',
    status: 'pending',
    target: '张三 (案件编号: CASE001)',
    description: '客户张三的催收案件已逾期32天，超过预警阈值',
    triggerTime: '2024-01-15 14:30:25',
    details: {
      '客户姓名': '张三',
      '案件编号': 'CASE001',
      '逾期天数': '32天',
      '欠款金额': '¥58,000',
      '最后联系时间': '2024-01-10 16:20:00'
    },
    history: [
      {
        id: 1,
        action: '预警触发',
        description: '系统检测到逾期天数超过阈值，自动触发预警',
        operator: '系统',
        time: '2024-01-15 14:30:25'
      }
    ],
    relatedEvents: [
      {
        id: 1,
        title: '同客户其他逾期案件',
        description: '该客户还有2个其他逾期案件',
        level: 'medium'
      }
    ]
  },
  {
    id: 2,
    title: '大额催收异常预警',
    type: 'amount',
    level: 'medium',
    priority: 'high',
    status: 'processing',
    target: '李四 (案件编号: CASE002)',
    description: '检测到单笔催收金额120,000元，超过预警阈值',
    triggerTime: '2024-01-15 13:45:12',
    processTime: '2024-01-15 14:00:00',
    processor: '王五',
    details: {
      '客户姓名': '李四',
      '案件编号': 'CASE002',
      '催收金额': '¥120,000',
      '催收人员': '赵六',
      '操作时间': '2024-01-15 13:45:00'
    },
    history: [
      {
        id: 1,
        action: '预警触发',
        description: '检测到大额催收操作',
        operator: '系统',
        time: '2024-01-15 13:45:12'
      },
      {
        id: 2,
        action: '开始处理',
        description: '已分配给王五处理',
        operator: '管理员',
        time: '2024-01-15 14:00:00'
      }
    ]
  },
  {
    id: 3,
    title: '系统错误率异常',
    type: 'system',
    level: 'high',
    priority: 'urgent',
    status: 'completed',
    target: '催收系统',
    description: '过去1小时系统错误率达到8.5%，超过预警阈值',
    triggerTime: '2024-01-15 12:10:30',
    processTime: '2024-01-15 12:35:00',
    processor: '技术部',
    details: {
      '错误率': '8.5%',
      '时间窗口': '过去1小时',
      '错误数量': '127次',
      '总请求数': '1,494次',
      '主要错误': '数据库连接超时'
    },
    history: [
      {
        id: 1,
        action: '预警触发',
        description: '系统错误率超过阈值',
        operator: '系统',
        time: '2024-01-15 12:10:30'
      },
      {
        id: 2,
        action: '问题处理',
        description: '重启数据库连接池，错误率恢复正常',
        operator: '技术部-张工',
        time: '2024-01-15 12:35:00'
      },
      {
        id: 3,
        action: '预警关闭',
        description: '问题已解决，预警自动关闭',
        operator: '系统',
        time: '2024-01-15 12:40:00'
      }
    ]
  }
])

// 表格列配置
const ruleColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '预警类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '风险等级',
    key: 'level',
    width: 100
  },
  {
    title: '阈值',
    dataIndex: 'threshold',
    key: 'threshold',
    width: 80
  },
  {
    title: '触发条件',
    dataIndex: 'condition',
    key: 'condition',
    ellipsis: true
  },
  {
    title: '预警次数',
    key: 'warningCount',
    width: 100
  },
  {
    title: '最后触发',
    dataIndex: 'lastTrigger',
    key: 'lastTrigger',
    width: 160
  },
  {
    title: '状态',
    key: 'enabled',
    width: 80
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

const warningColumns = [
  {
    title: '预警标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    fixed: 'left'
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '等级',
    key: 'level',
    width: 80
  },
  {
    title: '优先级',
    key: 'priority',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '影响对象',
    dataIndex: 'target',
    key: 'target',
    width: 180,
    ellipsis: true
  },
  {
    title: '触发时间',
    dataIndex: 'triggerTime',
    key: 'triggerTime',
    width: 160
  },
  {
    title: '处理人',
    dataIndex: 'processor',
    key: 'processor',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 图表引用
const warningTrendChart = ref()
const warningTypeChart = ref()

// 计算属性
const filteredWarnings = computed(() => {
  let filtered = warningEvents.value

  // 应用快速筛选
  switch (activeFilter.value) {
    case 'pending':
      filtered = filtered.filter(w => w.status === 'pending')
      break
    case 'high':
      filtered = filtered.filter(w => w.level === 'high')
      break
    case 'today':
      const today = dayjs().format('YYYY-MM-DD')
      filtered = filtered.filter(w => w.triggerTime.startsWith(today))
      break
  }

  // 应用高级筛选
  if (filterForm.type) {
    filtered = filtered.filter(w => w.type === filterForm.type)
  }
  if (filterForm.level) {
    filtered = filtered.filter(w => w.level === filterForm.level)
  }
  if (filterForm.status) {
    filtered = filtered.filter(w => w.status === filterForm.status)
  }
  if (filterForm.priority) {
    filtered = filtered.filter(w => w.priority === filterForm.priority)
  }
  if (filterForm.target) {
    filtered = filtered.filter(w => w.target.includes(filterForm.target))
  }
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const [start, end] = filterForm.dateRange
    filtered = filtered.filter(w => {
      const triggerTime = dayjs(w.triggerTime)
      return triggerTime.isAfter(start) && triggerTime.isBefore(end)
    })
  }

  pagination.total = filtered.length
  return filtered
})

// 辅助方法
const getRiskLevelColor = (level) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[level] || '#666'
}

const getRiskLevelText = (level) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level] || '未知'
}

const getWarningTypeColor = (type) => {
  const colors = {
    overdue: '#ff4d4f',
    amount: '#faad14',
    frequency: '#1890ff',
    risk: '#722ed1',
    system: '#52c41a'
  }
  return colors[type] || '#666'
}

const getWarningTypeText = (type) => {
  const texts = {
    overdue: '逾期预警',
    amount: '金额异常',
    frequency: '频率异常',
    risk: '风险预警',
    system: '系统预警'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    pending: '#faad14',
    processing: '#1890ff',
    completed: '#52c41a',
    ignored: '#666'
  }
  return colors[status] || '#666'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    ignored: '已忽略'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: '#ff4d4f',
    high: '#fa8c16',
    medium: '#faad14',
    low: '#52c41a'
  }
  return colors[priority] || '#666'
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || '未知'
}

const getHistoryColor = (action) => {
  const colors = {
    '预警触发': '#ff4d4f',
    '开始处理': '#1890ff',
    '问题处理': '#52c41a',
    '预警关闭': '#666'
  }
  return colors[action] || '#1890ff'
}

const getPendingCount = () => {
  return warningEvents.value.filter(w => w.status === 'pending').length
}

const getHighRiskCount = () => {
  return warningEvents.value.filter(w => w.level === 'high').length
}

const getTodayCount = () => {
  const today = dayjs().format('YYYY-MM-DD')
  return warningEvents.value.filter(w => w.triggerTime.startsWith(today)).length
}

// 事件处理方法
const handleQuickFilter = (filter) => {
  activeFilter.value = filter
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleViewDetail = (record) => {
  selectedWarning.value = record
  showDetailModal.value = true
}

const handleProcessWarning = (record) => {
  Modal.confirm({
    title: '确认处理预警',
    content: `确定要处理预警"${record.title}"吗？`,
    onOk: () => {
      record.status = 'processing'
      record.processTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      record.processor = '当前用户'
      message.success('预警处理已开始')
    }
  })
}

const handleIgnoreWarning = (record) => {
  Modal.confirm({
    title: '确认忽略预警',
    content: `确定要忽略预警"${record.title}"吗？`,
    onOk: () => {
      record.status = 'ignored'
      record.processTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      message.success('预警已忽略')
    }
  })
}

const handleMoreAction = (event, record) => {
  const { key } = event
  switch (key) {
    case 'assign':
      handleAssignWarning(record)
      break
    case 'escalate':
      handleEscalateWarning(record)
      break
    case 'history':
      handleViewDetail(record)
      detailActiveTab.value = 'history'
      break
    case 'archive':
      message.success('预警已归档')
      break
  }
}

const handleAssignWarning = (record) => {
  Modal.confirm({
    title: '分配处理人',
    content: '请选择处理人...',
    onOk: () => {
      record.processor = '张三'
      message.success('处理人分配成功')
    }
  })
}

const handleEscalateWarning = (record) => {
  Modal.confirm({
    title: '升级处理',
    content: `确定要将预警"${record.title}"升级处理吗？`,
    onOk: () => {
      record.priority = 'urgent'
      message.success('预警已升级处理')
    }
  })
}

const handleViewRelatedEvent = (event) => {
  message.info(`查看相关事件: ${event.title}`)
}

const handleBatchProcess = () => {
  batchAction.value = 'process'
  showBatchModal.value = true
}

const handleBatchIgnore = () => {
  batchAction.value = 'ignore'
  showBatchModal.value = true
}

const handleBatchAssign = () => {
  batchAction.value = 'assign'
  showBatchModal.value = true
}

const handleBatchControl = () => {
  message.info('批量控制功能')
}

const handleBatchConfirm = () => {
  const count = selectedRowKeys.value.length
  
  switch (batchAction.value) {
    case 'process':
      selectedRowKeys.value.forEach(id => {
        const warning = warningEvents.value.find(w => w.id === id)
        if (warning) {
          warning.status = 'processing'
          warning.processTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        }
      })
      message.success(`已批量处理 ${count} 个预警`)
      break
    case 'ignore':
      selectedRowKeys.value.forEach(id => {
        const warning = warningEvents.value.find(w => w.id === id)
        if (warning) {
          warning.status = 'ignored'
          warning.processTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        }
      })
      message.success(`已批量忽略 ${count} 个预警`)
      break
    case 'assign':
      if (!batchForm.processor) {
        message.error('请选择处理人')
        return
      }
      selectedRowKeys.value.forEach(id => {
        const warning = warningEvents.value.find(w => w.id === id)
        if (warning) {
          warning.processor = batchForm.processor
        }
      })
      message.success(`已为 ${count} 个预警分配处理人`)
      break
  }
  
  showBatchModal.value = false
  clearSelection()
  
  // 重置表单
  Object.assign(batchForm, {
    remark: '',
    processor: ''
  })
}

const clearSelection = () => {
  selectedRowKeys.value = []
}

const handleExportWarnings = () => {
  const exportData = filteredWarnings.value.map(item => ({
    预警标题: item.title,
    预警类型: getWarningTypeText(item.type),
    风险等级: getRiskLevelText(item.level),
    优先级: getPriorityText(item.priority),
    状态: getStatusText(item.status),
    影响对象: item.target,
    触发时间: item.triggerTime,
    处理人: item.processor || '未分配'
  }))
  
  const csvContent = [
    Object.keys(exportData[0]).join(','),
    ...exportData.map(row => Object.values(row).join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `warning-events-${dayjs().format('YYYY-MM-DD')}.csv`
  link.click()
  URL.revokeObjectURL(url)
  
  message.success('预警数据导出成功')
}

const handleFilterSave = () => {
  showFilterModal.value = false
  message.success('筛选条件已应用')
}

const handleRuleToggle = (rule) => {
  message.success(`规则"${rule.name}"已${rule.enabled ? '启用' : '禁用'}`)
}

const handleEditRule = (rule) => {
  editingRule.value = rule
  Object.assign(ruleForm, {
    name: rule.name,
    type: rule.type,
    level: rule.level,
    threshold: rule.threshold,
    condition: rule.condition,
    suggestion: rule.suggestion || '',
    notificationMethods: ['email', 'system'],
    frequency: 'realtime'
  })
  showRuleModal.value = true
}

const handleTestRule = (rule) => {
  message.success(`规则"${rule.name}"测试通过`)
}

const handleDeleteRule = (rule) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除规则"${rule.name}"吗？`,
    onOk: () => {
      const index = warningRules.value.findIndex(r => r.id === rule.id)
      if (index > -1) {
        warningRules.value.splice(index, 1)
        message.success('规则删除成功')
      }
    }
  })
}

const handleRuleSave = () => {
  if (!ruleForm.name || !ruleForm.type || !ruleForm.level || !ruleForm.condition) {
    message.error('请填写完整的规则信息')
    return
  }
  
  if (editingRule.value) {
    // 编辑规则
    Object.assign(editingRule.value, {
      name: ruleForm.name,
      type: ruleForm.type,
      level: ruleForm.level,
      threshold: ruleForm.threshold,
      condition: ruleForm.condition,
      suggestion: ruleForm.suggestion
    })
    message.success('规则更新成功')
  } else {
    // 新增规则
    const newRule = {
      id: Date.now(),
      name: ruleForm.name,
      type: ruleForm.type,
      level: ruleForm.level,
      threshold: ruleForm.threshold,
      condition: ruleForm.condition,
      suggestion: ruleForm.suggestion,
      enabled: true,
      warningCount: 0,
      lastTrigger: '-'
    }
    warningRules.value.push(newRule)
    message.success('规则创建成功')
  }
  
  // 重置表单
  editingRule.value = null
  Object.assign(ruleForm, {
    name: '',
    type: '',
    level: '',
    threshold: null,
    condition: '',
    suggestion: '',
    notificationMethods: [],
    frequency: ''
  })
  showRuleModal.value = false
}

// 图表初始化
const initCharts = () => {
  // 预警趋势图
  const trendChart = echarts.init(warningTrendChart.value)
  const trendOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      name: '预警数量'
    },
    series: [
      {
        name: '总预警',
        type: 'line',
        data: [23, 18, 32, 28, 35, 19, 26],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '高风险',
        type: 'line',
        data: [8, 6, 12, 10, 13, 7, 9],
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '已处理',
        type: 'line',
        data: [18, 15, 25, 22, 28, 16, 21],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 预警类型分布图
  const typeChart = echarts.init(warningTypeChart.value)
  const typeOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '预警类型',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 35, name: '逾期预警', itemStyle: { color: '#ff4d4f' } },
          { value: 28, name: '金额异常', itemStyle: { color: '#faad14' } },
          { value: 18, name: '频率异常', itemStyle: { color: '#1890ff' } },
          { value: 12, name: '风险预警', itemStyle: { color: '#722ed1' } },
          { value: 7, name: '系统预警', itemStyle: { color: '#52c41a' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  typeChart.setOption(typeOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    trendChart.resize()
    typeChart.resize()
  })
}

// 生命周期
onMounted(() => {
  pagination.total = warningEvents.value.length
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.overview-section {
  margin-bottom: 16px;
}

.overview-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.rules-card {
  margin-bottom: 16px;
}

.warnings-card {
  margin-bottom: 16px;
}

.quick-filters {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.chart-section {
  margin-top: 16px;
}

.batch-actions {
  margin-top: 16px;
  padding: 12px;
  background: #e6f7ff;
  border-radius: 6px;
  border: 1px solid #91d5ff;
}

.history-item {
  margin-bottom: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-time {
  color: #666;
  font-size: 12px;
}

.history-content {
  color: #666;
  margin-bottom: 4px;
}

.history-operator {
  color: #999;
  font-size: 12px;
}

.detail-actions {
  margin-top: 16px;
  text-align: right;
}

.ant-statistic {
  text-align: center;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

:deep(.ant-descriptions-item-label) {
  width: 120px;
}
</style>