<template>
  <div class="performance-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>绩效分析</h2>
      <div class="header-actions">
        <a-button @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新数据
        </a-button>
        <a-button @click="exportReport">
          <template #icon><ExportOutlined /></template>
          导出报告
        </a-button>
        <a-button type="primary" @click="generateReport">
          <template #icon><FileTextOutlined /></template>
          生成报告
        </a-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <a-card>
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="分析维度">
            <a-select v-model:value="filterForm.dimension" style="width: 150px" @change="handleDimensionChange">
              <a-select-option value="individual">个人分析</a-select-option>
              <a-select-option value="team">团队分析</a-select-option>
              <a-select-option value="department">部门分析</a-select-option>
              <a-select-option value="trend">趋势分析</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间范围">
            <a-range-picker 
              v-model:value="filterForm.dateRange"
              :placeholder="['开始时间', '结束时间']"
              @change="handleDateChange"
            />
          </a-form-item>
          <a-form-item label="部门">
            <a-select v-model:value="filterForm.department" style="width: 120px" allowClear>
              <a-select-option value="collection">催收部</a-select-option>
              <a-select-option value="customer">客服部</a-select-option>
              <a-select-option value="sales">销售部</a-select-option>
              <a-select-option value="risk">风控部</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="指标类型">
            <a-select v-model:value="filterForm.metrics" mode="multiple" style="width: 200px">
              <a-select-option value="recovery_rate">回收率</a-select-option>
              <a-select-option value="recovery_amount">回收金额</a-select-option>
              <a-select-option value="case_count">案件数量</a-select-option>
              <a-select-option value="satisfaction">满意度</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="updateAnalysis">分析</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 综合概览 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均绩效得分" 
              :value="overview.avgScore" 
              :precision="1"
              suffix="分"
              :value-style="{ color: '#1890ff' }"
            />
            <div class="trend-indicator">
              <span :class="getTrendClass(overview.scoreTrend)">
                <component :is="getTrendIcon(overview.scoreTrend)" />
                {{ Math.abs(overview.scoreTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="目标完成率" 
              :value="overview.targetCompletion" 
              :precision="1"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
            <div class="trend-indicator">
              <span :class="getTrendClass(overview.completionTrend)">
                <component :is="getTrendIcon(overview.completionTrend)" />
                {{ Math.abs(overview.completionTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="团队效能指数" 
              :value="overview.efficiency" 
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
            <div class="trend-indicator">
              <span :class="getTrendClass(overview.efficiencyTrend)">
                <component :is="getTrendIcon(overview.efficiencyTrend)" />
                {{ Math.abs(overview.efficiencyTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="改进潜力" 
              :value="overview.improvementPotential" 
              :precision="1"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
            <div class="trend-indicator">
              <span :class="getTrendClass(overview.potentialTrend)">
                <component :is="getTrendIcon(overview.potentialTrend)" />
                {{ Math.abs(overview.potentialTrend) }}%
              </span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-card title="绩效趋势分析" class="chart-card">
            <div ref="trendChart" style="width: 100%; height: 350px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="部门绩效对比" class="chart-card">
            <div ref="departmentChart" style="width: 100%; height: 350px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div class="charts-section">
      <a-row :gutter="24">
        <a-col :span="8">
          <a-card title="绩效分布" class="chart-card">
            <div ref="distributionChart" style="width: 100%; height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="16">
          <a-card title="关键指标热力图" class="chart-card">
            <div ref="heatmapChart" style="width: 100%; height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细分析 -->
    <div class="detail-analysis">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="individual" tab="个人绩效分析">
          <div class="individual-analysis">
            <a-table
              :columns="individualColumns"
              :data-source="individualData"
              :pagination="{ pageSize: 10 }"
              :scroll="{ x: 1200 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <div class="employee-info">
                    <a-avatar size="small">{{ record.name.charAt(0) }}</a-avatar>
                    <span style="margin-left: 8px">{{ record.name }}</span>
                  </div>
                </template>
                <template v-else-if="column.key === 'score'">
                  <div class="score-cell">
                    <span :style="{ color: getScoreColor(record.score) }">{{ record.score }}</span>
                    <a-progress 
                      :percent="record.score" 
                      size="small" 
                      :show-info="false"
                      style="margin-top: 4px"
                    />
                  </div>
                </template>
                <template v-else-if="column.key === 'trend'">
                  <span :class="getTrendClass(record.trend)">
                    <component :is="getTrendIcon(record.trend)" />
                    {{ Math.abs(record.trend) }}%
                  </span>
                </template>
                <template v-else-if="column.key === 'performance'">
                  <a-tag :color="getPerformanceColor(record.performance)">
                    {{ getPerformanceText(record.performance) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewDetailAnalysis(record)">
                      详细分析
                    </a-button>
                    <a-button type="link" size="small" @click="generatePersonalReport(record)">
                      生成报告
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <a-tab-pane key="team" tab="团队绩效分析">
          <div class="team-analysis">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="团队协作效率" size="small">
                  <div ref="collaborationChart" style="width: 100%; height: 250px;"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="团队成员贡献度" size="small">
                  <div ref="contributionChart" style="width: 100%; height: 250px;"></div>
                </a-card>
              </a-col>
            </a-row>
            <a-divider />
            <a-table
              :columns="teamColumns"
              :data-source="teamData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'teamName'">
                  <div class="team-info">
                    <TeamOutlined style="margin-right: 8px; color: #1890ff" />
                    <strong>{{ record.teamName }}</strong>
                  </div>
                </template>
                <template v-else-if="column.key === 'efficiency'">
                  <a-progress :percent="record.efficiency" size="small" />
                </template>
                <template v-else-if="column.key === 'synergy'">
                  <a-rate :value="record.synergy" :count="5" :allow-half="true" disabled />
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <a-tab-pane key="insights" tab="智能洞察">
          <div class="insights-analysis">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="关键发现" class="insights-card">
                  <div class="insights-list">
                    <div v-for="insight in keyInsights" :key="insight.id" class="insight-item">
                      <div class="insight-header">
                        <component :is="insight.icon" :style="{ color: insight.color }" />
                        <span class="insight-title">{{ insight.title }}</span>
                        <a-tag :color="insight.priority === 'high' ? 'red' : insight.priority === 'medium' ? 'orange' : 'blue'">
                          {{ getPriorityText(insight.priority) }}
                        </a-tag>
                      </div>
                      <div class="insight-description">{{ insight.description }}</div>
                      <div class="insight-impact">预期影响：{{ insight.impact }}</div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="改进建议" class="recommendations-card">
                  <div class="recommendations-list">
                    <div v-for="rec in recommendations" :key="rec.id" class="recommendation-item">
                      <div class="rec-header">
                        <BulbOutlined style="color: #faad14; margin-right: 8px" />
                        <span class="rec-title">{{ rec.title }}</span>
                      </div>
                      <div class="rec-description">{{ rec.description }}</div>
                      <div class="rec-actions">
                        <a-button size="small" type="primary" @click="implementRecommendation(rec)">
                          实施建议
                        </a-button>
                        <a-button size="small" @click="viewRecommendationDetail(rec)">
                          查看详情
                        </a-button>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 分析报告模态框 -->
    <a-modal
      v-model:open="reportModalVisible"
      title="绩效分析报告"
      width="1000px"
      :footer="null"
    >
      <div class="analysis-report">
        <div class="report-header">
          <h3>{{ reportData.title }}</h3>
          <div class="report-meta">
            <span>生成时间：{{ reportData.generateTime }}</span>
            <span>分析周期：{{ reportData.period }}</span>
          </div>
        </div>
        
        <a-divider>执行摘要</a-divider>
        <div class="executive-summary">
          <p>{{ reportData.summary }}</p>
        </div>

        <a-divider>关键指标</a-divider>
        <a-row :gutter="16">
          <a-col :span="6" v-for="metric in reportData.keyMetrics" :key="metric.name">
            <a-statistic 
              :title="metric.name" 
              :value="metric.value" 
              :suffix="metric.unit"
              :precision="metric.precision"
            />
          </a-col>
        </a-row>

        <a-divider>详细分析</a-divider>
        <div class="detailed-analysis">
          <div v-for="section in reportData.sections" :key="section.title" class="analysis-section">
            <h4>{{ section.title }}</h4>
            <p>{{ section.content }}</p>
          </div>
        </div>

        <div class="report-actions">
          <a-button type="primary" @click="downloadReport">下载报告</a-button>
          <a-button @click="shareReport">分享报告</a-button>
          <a-button @click="scheduleReport">定期生成</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined,
  ExportOutlined,
  FileTextOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  MinusOutlined,
  TeamOutlined,
  BulbOutlined,
  TrophyOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

const activeTab = ref('individual')
const reportModalVisible = ref(false)
const trendChart = ref(null)
const departmentChart = ref(null)
const distributionChart = ref(null)
const heatmapChart = ref(null)
const collaborationChart = ref(null)
const contributionChart = ref(null)

const filterForm = reactive({
  dimension: 'individual',
  dateRange: [],
  department: undefined,
  metrics: ['recovery_rate', 'recovery_amount']
})

const overview = reactive({
  avgScore: 85.6,
  scoreTrend: 3.2,
  targetCompletion: 78.5,
  completionTrend: -1.8,
  efficiency: 92.3,
  efficiencyTrend: 5.1,
  improvementPotential: 15.7,
  potentialTrend: 2.3
})

const individualData = ref([
  {
    id: 1,
    name: '张三',
    department: '催收部',
    score: 95.2,
    trend: 3.5,
    performance: 'excellent',
    recoveryRate: 92.5,
    recoveryAmount: 126.8,
    satisfaction: 94.2
  },
  {
    id: 2,
    name: '李四',
    department: '催收部',
    score: 87.6,
    trend: -2.1,
    performance: 'good',
    recoveryRate: 89.3,
    recoveryAmount: 108.4,
    satisfaction: 88.7
  },
  {
    id: 3,
    name: '王五',
    department: '客服部',
    score: 91.3,
    trend: 1.8,
    performance: 'excellent',
    recoveryRate: 85.7,
    recoveryAmount: 95.2,
    satisfaction: 96.8
  }
])

const teamData = ref([
  {
    id: 1,
    teamName: '催收一组',
    members: 8,
    efficiency: 89,
    synergy: 4.2,
    avgScore: 88.5
  },
  {
    id: 2,
    teamName: '催收二组',
    members: 6,
    efficiency: 92,
    synergy: 4.7,
    avgScore: 91.2
  },
  {
    id: 3,
    teamName: '客服团队',
    members: 12,
    efficiency: 85,
    synergy: 4.0,
    avgScore: 86.3
  }
])

const keyInsights = ref([
  {
    id: 1,
    title: '回收率显著提升',
    description: '本月整体回收率较上月提升3.2%，主要归功于新的催收策略实施。',
    impact: '预计月度回收金额增加150万元',
    priority: 'high',
    icon: TrophyOutlined,
    color: '#52c41a'
  },
  {
    id: 2,
    title: '客户满意度波动',
    description: '部分催收员的客户满意度出现下降趋势，需要加强软技能培训。',
    impact: '可能影响长期客户关系维护',
    priority: 'medium',
    icon: WarningOutlined,
    color: '#faad14'
  },
  {
    id: 3,
    title: '团队协作优化',
    description: '跨部门协作效率提升明显，信息共享机制发挥积极作用。',
    impact: '整体工作效率提升8%',
    priority: 'low',
    icon: CheckCircleOutlined,
    color: '#1890ff'
  }
])

const recommendations = ref([
  {
    id: 1,
    title: '加强个性化催收培训',
    description: '针对不同类型客户制定专门的沟通技巧培训课程，提升催收成功率。'
  },
  {
    id: 2,
    title: '优化绩效考核体系',
    description: '调整KPI权重，增加客户满意度在绩效评估中的比重。'
  },
  {
    id: 3,
    title: '建立mentor制度',
    description: '让高绩效员工担任新员工导师，加速团队整体能力提升。'
  }
])

const reportData = reactive({
  title: '月度绩效分析报告',
  generateTime: '2024-03-15 14:30:00',
  period: '2024年2月',
  summary: '本月整体绩效表现良好，平均得分85.6分，较上月提升3.2%。回收率达到新高，但客户满意度存在改进空间。建议加强软技能培训，优化绩效考核体系。',
  keyMetrics: [
    { name: '平均得分', value: 85.6, unit: '分', precision: 1 },
    { name: '目标完成率', value: 78.5, unit: '%', precision: 1 },
    { name: '团队效能', value: 92.3, unit: '', precision: 1 },
    { name: '改进潜力', value: 15.7, unit: '%', precision: 1 }
  ],
  sections: [
    {
      title: '绩效亮点',
      content: '本月催收团队表现突出，整体回收率较上月提升3.2%，达到89.5%的历史新高。张三等核心员工持续发挥标杆作用，带动团队整体水平提升。'
    },
    {
      title: '待改进领域',
      content: '客户满意度方面存在波动，部分新员工的沟通技巧有待提升。建议加强针对性培训，特别是情绪管理和客户心理分析能力。'
    },
    {
      title: '发展趋势',
      content: '从数据趋势看，团队协作效率持续改善，信息化工具使用率提高。预计下月绩效将继续稳步提升。'
    }
  ]
})

const individualColumns = [
  { title: '姓名', key: 'name', width: 120 },
  { title: '部门', dataIndex: 'department', key: 'department', width: 100 },
  { title: '综合得分', key: 'score', width: 120 },
  { title: '趋势', key: 'trend', width: 100 },
  { title: '绩效等级', key: 'performance', width: 100 },
  { title: '回收率', dataIndex: 'recoveryRate', key: 'recoveryRate', width: 100, customRender: ({ text }) => `${text}%` },
  { title: '回收金额', dataIndex: 'recoveryAmount', key: 'recoveryAmount', width: 100, customRender: ({ text }) => `${text}万` },
  { title: '满意度', dataIndex: 'satisfaction', key: 'satisfaction', width: 100, customRender: ({ text }) => `${text}分` },
  { title: '操作', key: 'action', width: 150, fixed: 'right' }
]

const teamColumns = [
  { title: '团队名称', key: 'teamName', width: 150 },
  { title: '成员数', dataIndex: 'members', key: 'members', width: 100, customRender: ({ text }) => `${text}人` },
  { title: '团队效率', key: 'efficiency', width: 150 },
  { title: '协作指数', key: 'synergy', width: 150 },
  { title: '平均得分', dataIndex: 'avgScore', key: 'avgScore', width: 100, customRender: ({ text }) => `${text}分` }
]

const getTrendClass = (trend) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

const getTrendIcon = (trend) => {
  if (trend > 0) return CaretUpOutlined
  if (trend < 0) return CaretDownOutlined
  return MinusOutlined
}

const getScoreColor = (score) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getPerformanceColor = (performance) => {
  const colors = {
    excellent: 'green',
    good: 'blue',
    average: 'orange',
    poor: 'red'
  }
  return colors[performance] || 'default'
}

const getPerformanceText = (performance) => {
  const texts = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return texts[performance] || performance
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const initCharts = () => {
  // 趋势分析图表
  if (trendChart.value) {
    const chart1 = echarts.init(trendChart.value)
    const option1 = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['回收率', '回收金额', '满意度'] },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '回收率',
          type: 'line',
          data: [78, 82, 85, 87, 89, 91],
          smooth: true,
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '回收金额',
          type: 'line',
          data: [120, 135, 148, 162, 178, 195],
          smooth: true,
          itemStyle: { color: '#52c41a' }
        },
        {
          name: '满意度',
          type: 'line',
          data: [85, 87, 84, 88, 86, 89],
          smooth: true,
          itemStyle: { color: '#faad14' }
        }
      ]
    }
    chart1.setOption(option1)
  }

  // 部门对比图表
  if (departmentChart.value) {
    const chart2 = echarts.init(departmentChart.value)
    const option2 = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['催收部', '客服部', '销售部', '风控部'] },
      xAxis: {
        type: 'category',
        data: ['回收率', '回收金额', '案件量', '满意度']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '催收部',
          type: 'bar',
          data: [89, 156, 245, 88],
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '客服部',
          type: 'bar',
          data: [76, 98, 180, 94],
          itemStyle: { color: '#52c41a' }
        },
        {
          name: '销售部',
          type: 'bar',
          data: [82, 134, 210, 85],
          itemStyle: { color: '#faad14' }
        },
        {
          name: '风控部',
          type: 'bar',
          data: [71, 89, 156, 91],
          itemStyle: { color: '#722ed1' }
        }
      ]
    }
    chart2.setOption(option2)
  }

  // 绩效分布图表
  if (distributionChart.value) {
    const chart3 = echarts.init(distributionChart.value)
    const option3 = {
      tooltip: { trigger: 'item' },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 35, name: '优秀(90-100)', itemStyle: { color: '#52c41a' } },
            { value: 45, name: '良好(80-89)', itemStyle: { color: '#1890ff' } },
            { value: 15, name: '一般(70-79)', itemStyle: { color: '#faad14' } },
            { value: 5, name: '较差(60-69)', itemStyle: { color: '#ff4d4f' } }
          ]
        }
      ]
    }
    chart3.setOption(option3)
  }

  // 热力图
  if (heatmapChart.value) {
    const chart4 = echarts.init(heatmapChart.value)
    const option4 = {
      tooltip: { position: 'top' },
      grid: {
        height: '50%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: ['张三', '李四', '王五', '赵六', '钱七', '孙八'],
        splitArea: { show: true }
      },
      yAxis: {
        type: 'category',
        data: ['回收率', '回收金额', '案件量', '满意度'],
        splitArea: { show: true }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [
        {
          type: 'heatmap',
          data: [
            [0, 0, 92], [1, 0, 89], [2, 0, 87], [3, 0, 85], [4, 0, 83], [5, 0, 81],
            [0, 1, 95], [1, 1, 88], [2, 1, 91], [3, 1, 84], [4, 1, 87], [5, 1, 82],
            [0, 2, 88], [1, 2, 92], [2, 2, 85], [3, 2, 89], [4, 2, 86], [5, 2, 84],
            [0, 3, 94], [1, 3, 86], [2, 3, 96], [3, 3, 88], [4, 4, 91], [5, 3, 87]
          ]
        }
      ]
    }
    chart4.setOption(option4)
  }

  // 团队协作图表
  if (collaborationChart.value) {
    const chart5 = echarts.init(collaborationChart.value)
    const option5 = {
      tooltip: { trigger: 'item' },
      series: [
        {
          type: 'radar',
          data: [
            {
              value: [85, 90, 88, 92, 87],
              name: '催收一组',
              itemStyle: { color: '#1890ff' }
            },
            {
              value: [92, 88, 91, 89, 94],
              name: '催收二组',
              itemStyle: { color: '#52c41a' }
            }
          ],
          radar: {
            indicator: [
              { name: '沟通效率', max: 100 },
              { name: '任务协调', max: 100 },
              { name: '信息共享', max: 100 },
              { name: '目标一致', max: 100 },
              { name: '冲突解决', max: 100 }
            ]
          }
        }
      ]
    }
    chart5.setOption(option5)
  }

  // 贡献度图表
  if (contributionChart.value) {
    const chart6 = echarts.init(contributionChart.value)
    const option6 = {
      tooltip: { trigger: 'item' },
      series: [
        {
          type: 'pie',
          radius: '70%',
          data: [
            { value: 25, name: '张三', itemStyle: { color: '#1890ff' } },
            { value: 22, name: '李四', itemStyle: { color: '#52c41a' } },
            { value: 18, name: '王五', itemStyle: { color: '#faad14' } },
            { value: 15, name: '赵六', itemStyle: { color: '#722ed1' } },
            { value: 20, name: '其他', itemStyle: { color: '#d9d9d9' } }
          ]
        }
      ]
    }
    chart6.setOption(option6)
  }
}

const handleDimensionChange = () => {
  updateAnalysis()
}

const handleDateChange = () => {
  updateAnalysis()
}

const handleTabChange = (key) => {
  activeTab.value = key
  nextTick(() => {
    if (key === 'team') {
      initCharts()
    }
  })
}

const updateAnalysis = () => {
  message.info('正在更新分析数据...')
  setTimeout(() => {
    message.success('分析数据已更新')
  }, 1000)
}

const refreshData = () => {
  message.success('数据已刷新')
}

const exportReport = () => {
  message.success('报告导出成功')
}

const generateReport = () => {
  reportModalVisible.value = true
}

const viewDetailAnalysis = (record) => {
  message.info(`查看${record.name}的详细分析`)
}

const generatePersonalReport = (record) => {
  message.info(`生成${record.name}的个人报告`)
}

const implementRecommendation = (rec) => {
  message.success(`开始实施建议：${rec.title}`)
}

const viewRecommendationDetail = (rec) => {
  message.info(`查看建议详情：${rec.title}`)
}

const downloadReport = () => {
  message.success('报告下载成功')
}

const shareReport = () => {
  message.success('报告分享成功')
}

const scheduleReport = () => {
  message.success('已设置定期生成报告')
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.performance-analysis {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  margin-bottom: 24px;
}

.overview-section {
  margin-bottom: 24px;
}

.trend-indicator {
  margin-top: 8px;
  font-size: 12px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-stable {
  color: #666;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 100%;
}

.detail-analysis {
  margin-bottom: 24px;
}

.employee-info {
  display: flex;
  align-items: center;
}

.score-cell {
  text-align: center;
}

.team-info {
  display: flex;
  align-items: center;
}

.insights-analysis {
  margin-top: 16px;
}

.insights-card,
.recommendations-card {
  height: 100%;
}

.insights-list,
.recommendations-list {
  max-height: 400px;
  overflow-y: auto;
}

.insight-item,
.recommendation-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
}

.insight-header,
.rec-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.insight-title,
.rec-title {
  font-weight: 500;
  flex: 1;
}

.insight-description,
.rec-description {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.5;
}

.insight-impact {
  font-size: 12px;
  color: #1890ff;
}

.rec-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.analysis-report {
  padding: 16px 0;
}

.report-header {
  margin-bottom: 24px;
}

.report-header h3 {
  margin: 0;
  margin-bottom: 8px;
}

.report-meta {
  color: #666;
  font-size: 12px;
}

.report-meta span {
  margin-right: 16px;
}

.executive-summary {
  line-height: 1.6;
  color: #666;
}

.detailed-analysis {
  line-height: 1.6;
}

.analysis-section {
  margin-bottom: 16px;
}

.analysis-section h4 {
  margin-bottom: 8px;
  color: #1890ff;
}

.report-actions {
  text-align: center;
  margin-top: 24px;
}

.report-actions .ant-btn {
  margin: 0 8px;
}
</style>