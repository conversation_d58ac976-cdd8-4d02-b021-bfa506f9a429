<template>
  <div class="target-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>目标管理</h2>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          设置目标
        </a-button>
        <a-button @click="syncTargets">
          <template #icon><SyncOutlined /></template>
          同步目标
        </a-button>
        <a-button @click="exportData">
          <template #icon><ExportOutlined /></template>
          导出报告
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总目标数" 
              :value="stats.totalTargets" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已完成" 
              :value="stats.completed" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="完成率" 
              :value="stats.completionRate" 
              suffix="%"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="超额完成" 
              :value="stats.overachieved" 
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 目标进度概览 -->
    <div class="progress-overview">
      <a-card title="目标进度概览">
        <div class="chart-container">
          <div ref="progressChart" style="width: 100%; height: 300px;"></div>
        </div>
      </a-card>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="目标名称">
            <a-input 
              v-model:value="searchForm.name" 
              placeholder="请输入目标名称"
              allowClear
            />
          </a-form-item>
          <a-form-item label="目标类型">
            <a-select v-model:value="searchForm.type" placeholder="选择类型" allowClear>
              <a-select-option value="individual">个人目标</a-select-option>
              <a-select-option value="team">团队目标</a-select-option>
              <a-select-option value="department">部门目标</a-select-option>
              <a-select-option value="company">公司目标</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="选择状态" allowClear>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="active">进行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="overdue">已逾期</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="负责人">
            <a-select v-model:value="searchForm.assignee" placeholder="选择负责人" allowClear>
              <a-select-option value="1">张三</a-select-option>
              <a-select-option value="2">李四</a-select-option>
              <a-select-option value="3">王五</a-select-option>
              <a-select-option value="4">赵六</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间范围">
            <a-range-picker 
              v-model:value="searchForm.dateRange"
              :placeholder="['开始时间', '结束时间']"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 目标列表 -->
    <div class="table-section">
      <a-card>
        <div class="table-header">
          <div class="table-actions">
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchActivate"
            >
              批量激活
            </a-button>
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchComplete"
            >
              批量完成
            </a-button>
            <a-button 
              danger
              :disabled="!selectedRowKeys.length"
              @click="batchDelete"
            >
              批量删除
            </a-button>
          </div>
        </div>

        <a-table
          :columns="columns"
          :data-source="targetList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange
          }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'progress'">
              <div class="progress-container">
                <a-progress 
                  :percent="record.progress" 
                  :status="getProgressStatus(record)"
                  size="small"
                />
                <span class="progress-text">
                  {{ record.currentValue }} / {{ record.targetValue }}
                </span>
              </div>
            </template>
            <template v-else-if="column.key === 'completion'">
              <span :style="{ color: getCompletionColor(record.completion) }">
                {{ record.completion }}%
              </span>
            </template>
            <template v-else-if="column.key === 'assignee'">
              <a-avatar size="small">{{ record.assignee.charAt(0) }}</a-avatar>
              <span style="margin-left: 8px">{{ record.assignee }}</span>
            </template>
            <template v-else-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="editTarget(record)"
                  :disabled="record.status === 'completed'"
                >
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="record.status === 'draft'" @click="activateTarget(record)">
                        激活目标
                      </a-menu-item>
                      <a-menu-item v-if="record.status === 'active'" @click="completeTarget(record)">
                        完成目标
                      </a-menu-item>
                      <a-menu-item @click="duplicateTarget(record)">
                        复制目标
                      </a-menu-item>
                      <a-menu-item @click="viewHistory(record)">
                        查看历史
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="deleteTarget(record)">
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑目标模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      :title="editingTarget ? '编辑目标' : '创建目标'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="目标名称" required>
          <a-input v-model:value="formData.name" placeholder="请输入目标名称" />
        </a-form-item>
        <a-form-item label="目标类型" required>
          <a-select v-model:value="formData.type" placeholder="选择目标类型">
            <a-select-option value="individual">个人目标</a-select-option>
            <a-select-option value="team">团队目标</a-select-option>
            <a-select-option value="department">部门目标</a-select-option>
            <a-select-option value="company">公司目标</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="目标周期" required>
          <a-range-picker 
            v-model:value="formData.dateRange" 
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
          />
        </a-form-item>
        <a-form-item label="负责人" required>
          <a-select v-model:value="formData.assignee" placeholder="选择负责人">
            <a-select-option value="张三">张三</a-select-option>
            <a-select-option value="李四">李四</a-select-option>
            <a-select-option value="王五">王五</a-select-option>
            <a-select-option value="赵六">赵六</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" required>
          <a-select v-model:value="formData.priority" placeholder="选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="目标指标">
          <div class="metrics-config">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="指标类型" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-select v-model:value="formData.metrics.type" placeholder="选择指标类型">
                    <a-select-option value="amount">金额</a-select-option>
                    <a-select-option value="rate">比率</a-select-option>
                    <a-select-option value="count">数量</a-select-option>
                    <a-select-option value="score">分数</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="目标值" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-input-number 
                    v-model:value="formData.metrics.targetValue" 
                    placeholder="目标值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="单位" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-input v-model:value="formData.metrics.unit" placeholder="单位" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form-item>
        <a-form-item label="权重配置">
          <a-slider 
            v-model:value="formData.weight" 
            :min="0" 
            :max="100" 
            :tooltip-formatter="(value) => `${value}%`"
          />
          <div style="text-align: center; margin-top: 8px">
            权重：{{ formData.weight }}%
          </div>
        </a-form-item>
        <a-form-item label="目标描述">
          <a-textarea 
            v-model:value="formData.description" 
            :rows="3"
            placeholder="请输入目标描述"
          />
        </a-form-item>
        <a-form-item label="关联目标">
          <a-select 
            v-model:value="formData.parentTargets" 
            mode="multiple"
            placeholder="选择关联的上级目标"
            allowClear
          >
            <a-select-option value="1">2024年度营收目标</a-select-option>
            <a-select-option value="2">Q1回收率目标</a-select-option>
            <a-select-option value="3">客户满意度提升</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 目标详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="目标详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedTarget" class="target-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="目标名称">
            {{ selectedTarget.name }}
          </a-descriptions-item>
          <a-descriptions-item label="目标类型">
            <a-tag :color="getTypeColor(selectedTarget.type)">
              {{ getTypeText(selectedTarget.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedTarget.status)">
              {{ getStatusText(selectedTarget.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedTarget.priority)">
              {{ getPriorityText(selectedTarget.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="负责人">
            <a-avatar size="small">{{ selectedTarget.assignee.charAt(0) }}</a-avatar>
            <span style="margin-left: 8px">{{ selectedTarget.assignee }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="权重">
            {{ selectedTarget.weight }}%
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ selectedTarget.startDate }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ selectedTarget.endDate }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>目标进度</a-divider>
        <div class="target-progress">
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="progress-section">
                <h4>完成进度</h4>
                <a-progress 
                  type="circle" 
                  :percent="selectedTarget.progress"
                  :status="getProgressStatus(selectedTarget)"
                  :width="120"
                />
                <div class="progress-info">
                  <p>当前值：{{ selectedTarget.currentValue }} {{ selectedTarget.unit }}</p>
                  <p>目标值：{{ selectedTarget.targetValue }} {{ selectedTarget.unit }}</p>
                  <p>完成率：{{ selectedTarget.completion }}%</p>
                </div>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="timeline-section">
                <h4>关键里程碑</h4>
                <a-timeline>
                  <a-timeline-item v-for="milestone in selectedTarget.milestones" :key="milestone.id">
                    <template #dot>
                      <CheckCircleOutlined v-if="milestone.completed" style="color: #52c41a" />
                      <ClockCircleOutlined v-else style="color: #1890ff" />
                    </template>
                    <div>
                      <strong>{{ milestone.name }}</strong>
                      <div style="color: #666">{{ milestone.date }}</div>
                      <div v-if="milestone.description">{{ milestone.description }}</div>
                    </div>
                  </a-timeline-item>
                </a-timeline>
              </div>
            </a-col>
          </a-row>
        </div>

        <a-divider>子目标</a-divider>
        <a-table
          :columns="subTargetColumns"
          :data-source="selectedTarget.subTargets"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'progress'">
              <a-progress 
                :percent="record.progress" 
                size="small"
                :status="record.progress === 100 ? 'success' : 'active'"
              />
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 目标历史模态框 -->
    <a-modal
      v-model:open="historyModalVisible"
      title="目标历史"
      width="800px"
      :footer="null"
    >
      <a-timeline>
        <a-timeline-item v-for="history in targetHistory" :key="history.id">
          <div>
            <strong>{{ history.action }}</strong>
            <div style="color: #666">{{ history.date }}</div>
            <div>{{ history.description }}</div>
            <div v-if="history.operator" style="color: #999; font-size: 12px">
              操作人：{{ history.operator }}
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ExportOutlined, 
  SyncOutlined,
  DownOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined 
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

const loading = ref(false)
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const historyModalVisible = ref(false)
const editingTarget = ref(null)
const selectedTarget = ref(null)
const selectedRowKeys = ref([])
const progressChart = ref(null)

const stats = reactive({
  totalTargets: 156,
  completed: 89,
  completionRate: 78.5,
  overachieved: 23
})

const searchForm = reactive({
  name: '',
  type: undefined,
  status: undefined,
  assignee: undefined,
  dateRange: []
})

const formData = reactive({
  name: '',
  type: undefined,
  dateRange: [],
  assignee: undefined,
  priority: undefined,
  metrics: {
    type: undefined,
    targetValue: undefined,
    unit: ''
  },
  weight: 50,
  description: '',
  parentTargets: []
})

const targetList = ref([
  {
    id: 1,
    name: '2024年Q1回收率目标',
    type: 'department',
    status: 'active',
    assignee: '张三',
    priority: 'high',
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    targetValue: 85,
    currentValue: 78.5,
    unit: '%',
    progress: 92,
    completion: 92.4,
    weight: 30,
    description: '第一季度部门回收率目标',
    milestones: [
      { id: 1, name: '目标设定', date: '2024-01-01', completed: true, description: '完成目标设定和分解' },
      { id: 2, name: '中期检查', date: '2024-02-15', completed: true, description: '进行中期进度检查' },
      { id: 3, name: '最终评估', date: '2024-03-31', completed: false, description: '进行最终评估和总结' }
    ],
    subTargets: [
      { id: 11, name: '个人回收率-张三', progress: 95, status: 'active' },
      { id: 12, name: '个人回收率-李四', progress: 88, status: 'active' },
      { id: 13, name: '个人回收率-王五', progress: 92, status: 'active' }
    ]
  },
  {
    id: 2,
    name: '客户满意度提升目标',
    type: 'team',
    status: 'active',
    assignee: '李四',
    priority: 'medium',
    startDate: '2024-01-01',
    endDate: '2024-06-30',
    targetValue: 90,
    currentValue: 85,
    unit: '分',
    progress: 75,
    completion: 94.4,
    weight: 25,
    description: '提升客户服务满意度',
    milestones: [
      { id: 1, name: '服务培训', date: '2024-01-15', completed: true, description: '完成客服团队培训' },
      { id: 2, name: '流程优化', date: '2024-03-15', completed: true, description: '优化服务流程' },
      { id: 3, name: '效果评估', date: '2024-06-30', completed: false, description: '评估改善效果' }
    ],
    subTargets: [
      { id: 21, name: '电话服务满意度', progress: 88, status: 'active' },
      { id: 22, name: '在线服务满意度', progress: 82, status: 'active' }
    ]
  },
  {
    id: 3,
    name: '新员工培训完成率',
    type: 'individual',
    status: 'completed',
    assignee: '王五',
    priority: 'low',
    startDate: '2024-01-01',
    endDate: '2024-02-29',
    targetValue: 100,
    currentValue: 100,
    unit: '%',
    progress: 100,
    completion: 100,
    weight: 15,
    description: '确保新员工培训全部完成',
    milestones: [
      { id: 1, name: '培训计划制定', date: '2024-01-05', completed: true, description: '制定详细培训计划' },
      { id: 2, name: '培训实施', date: '2024-02-15', completed: true, description: '执行培训计划' },
      { id: 3, name: '考核评估', date: '2024-02-29', completed: true, description: '完成培训考核' }
    ],
    subTargets: [
      { id: 31, name: '理论培训', progress: 100, status: 'completed' },
      { id: 32, name: '实操培训', progress: 100, status: 'completed' }
    ]
  }
])

const targetHistory = ref([
  { id: 1, action: '创建目标', date: '2024-01-01 09:00:00', description: '创建了新的目标', operator: '张三' },
  { id: 2, action: '更新进度', date: '2024-01-15 14:30:00', description: '更新目标完成进度至30%', operator: '张三' },
  { id: 3, action: '添加里程碑', date: '2024-02-01 10:00:00', description: '添加了中期检查里程碑', operator: '李四' },
  { id: 4, action: '进度更新', date: '2024-02-15 16:20:00', description: '达成中期里程碑，进度更新至60%', operator: '张三' }
])

const columns = [
  {
    title: '目标名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '负责人',
    key: 'assignee',
    width: 120
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100
  },
  {
    title: '目标进度',
    key: 'progress',
    width: 200
  },
  {
    title: '完成率',
    key: 'completion',
    width: 100
  },
  {
    title: '权重',
    dataIndex: 'weight',
    key: 'weight',
    width: 80,
    customRender: ({ text }) => `${text}%`
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

const subTargetColumns = [
  {
    title: '子目标名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '进度',
    key: 'progress'
  },
  {
    title: '状态',
    key: 'status'
  }
]

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

const getTypeColor = (type) => {
  const colors = {
    individual: 'blue',
    team: 'green',
    department: 'orange',
    company: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    individual: '个人目标',
    team: '团队目标',
    department: '部门目标',
    company: '公司目标'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'gray',
    active: 'blue',
    completed: 'green',
    overdue: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    active: '进行中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return texts[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getProgressStatus = (record) => {
  if (record.completion >= 100) return 'success'
  if (record.status === 'overdue') return 'exception'
  return 'active'
}

const getCompletionColor = (completion) => {
  if (completion >= 100) return '#52c41a'
  if (completion >= 80) return '#1890ff'
  if (completion >= 60) return '#faad14'
  return '#ff4d4f'
}

const initChart = () => {
  if (!progressChart.value) return
  
  const chart = echarts.init(progressChart.value)
  const option = {
    title: {
      text: '目标完成情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '目标状态',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 45, name: '已完成', itemStyle: { color: '#52c41a' } },
          { value: 67, name: '进行中', itemStyle: { color: '#1890ff' } },
          { value: 23, name: '超期', itemStyle: { color: '#ff4d4f' } },
          { value: 21, name: '草稿', itemStyle: { color: '#d9d9d9' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
  
  // 响应式处理
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

const showCreateModal = () => {
  editingTarget.value = null
  resetForm()
  createModalVisible.value = true
}

const editTarget = (record) => {
  editingTarget.value = record
  Object.assign(formData, {
    name: record.name,
    type: record.type,
    dateRange: [record.startDate, record.endDate],
    assignee: record.assignee,
    priority: record.priority,
    metrics: {
      type: record.metrics?.type || 'amount',
      targetValue: record.targetValue,
      unit: record.unit
    },
    weight: record.weight,
    description: record.description,
    parentTargets: record.parentTargets || []
  })
  createModalVisible.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: undefined,
    dateRange: [],
    assignee: undefined,
    priority: undefined,
    metrics: {
      type: undefined,
      targetValue: undefined,
      unit: ''
    },
    weight: 50,
    description: '',
    parentTargets: []
  })
}

const handleSubmit = () => {
  if (editingTarget.value) {
    message.success('目标更新成功')
  } else {
    message.success('目标创建成功')
  }
  createModalVisible.value = false
  resetForm()
}

const handleCancel = () => {
  createModalVisible.value = false
  resetForm()
}

const viewDetails = (record) => {
  selectedTarget.value = record
  detailModalVisible.value = true
}

const viewHistory = (record) => {
  selectedTarget.value = record
  historyModalVisible.value = true
}

const activateTarget = (record) => {
  Modal.confirm({
    title: '确认激活目标',
    content: `确定要激活目标"${record.name}"吗？`,
    onOk() {
      message.success('目标激活成功')
    }
  })
}

const completeTarget = (record) => {
  Modal.confirm({
    title: '确认完成目标',
    content: `确定要完成目标"${record.name}"吗？`,
    onOk() {
      message.success('目标完成成功')
    }
  })
}

const duplicateTarget = (record) => {
  message.success('目标复制成功')
}

const deleteTarget = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除目标"${record.name}"吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('删除成功')
    }
  })
}

const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys
}

const batchActivate = () => {
  Modal.confirm({
    title: '批量激活目标',
    content: `确定要激活选中的 ${selectedRowKeys.value.length} 个目标吗？`,
    onOk() {
      message.success('批量激活成功')
      selectedRowKeys.value = []
    }
  })
}

const batchComplete = () => {
  Modal.confirm({
    title: '批量完成目标',
    content: `确定要完成选中的 ${selectedRowKeys.value.length} 个目标吗？`,
    onOk() {
      message.success('批量完成成功')
      selectedRowKeys.value = []
    }
  })
}

const batchDelete = () => {
  Modal.confirm({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个目标吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('批量删除成功')
      selectedRowKeys.value = []
    }
  })
}

const handleSearch = () => {
  message.info('搜索功能已触发')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    type: undefined,
    status: undefined,
    assignee: undefined,
    dateRange: []
  })
  message.info('搜索条件已重置')
}

const syncTargets = () => {
  message.success('目标同步成功')
}

const exportData = () => {
  message.success('报告导出成功')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.target-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.progress-overview {
  margin-bottom: 24px;
}

.chart-container {
  padding: 20px;
}

.search-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

.metrics-config {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.target-detail {
  padding: 8px 0;
}

.target-progress {
  margin: 16px 0;
}

.progress-section {
  text-align: center;
}

.progress-section h4 {
  margin-bottom: 16px;
}

.progress-info {
  margin-top: 16px;
  text-align: left;
}

.progress-info p {
  margin: 4px 0;
  color: #666;
}

.timeline-section h4 {
  margin-bottom: 16px;
}
</style>