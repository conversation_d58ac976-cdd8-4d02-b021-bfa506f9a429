<template>
  <div class="performance-assessment">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>绩效考核</h2>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          创建考核
        </a-button>
        <a-button @click="exportData">
          <template #icon><ExportOutlined /></template>
          导出数据
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中考核" 
              :value="stats.ongoing" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已完成考核" 
              :value="stats.completed" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均分数" 
              :value="stats.avgScore" 
              suffix="分"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="优秀率" 
              :value="stats.excellentRate" 
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="考核名称">
            <a-input 
              v-model:value="searchForm.name" 
              placeholder="请输入考核名称"
              allowClear
            />
          </a-form-item>
          <a-form-item label="考核周期">
            <a-select v-model:value="searchForm.period" placeholder="选择周期" allowClear>
              <a-select-option value="monthly">月度</a-select-option>
              <a-select-option value="quarterly">季度</a-select-option>
              <a-select-option value="yearly">年度</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="考核状态">
            <a-select v-model:value="searchForm.status" placeholder="选择状态" allowClear>
              <a-select-option value="pending">待开始</a-select-option>
              <a-select-option value="ongoing">进行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="archived">已归档</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="部门">
            <a-select v-model:value="searchForm.department" placeholder="选择部门" allowClear>
              <a-select-option value="collection">催收部</a-select-option>
              <a-select-option value="legal">法务部</a-select-option>
              <a-select-option value="customer">客服部</a-select-option>
              <a-select-option value="risk">风控部</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 考核列表 -->
    <div class="table-section">
      <a-card>
        <div class="table-header">
          <div class="table-actions">
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchStart"
            >
              批量启动
            </a-button>
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchComplete"
            >
              批量完成
            </a-button>
            <a-button 
              danger
              :disabled="!selectedRowKeys.length"
              @click="batchDelete"
            >
              批量删除
            </a-button>
          </div>
        </div>

        <a-table
          :columns="columns"
          :data-source="assessmentList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: (record) => ({
              disabled: record.status === 'archived'
            })
          }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'participants'">
              <a-avatar-group :max-count="3">
                <a-avatar v-for="participant in record.participants" :key="participant.id">
                  {{ participant.name.charAt(0) }}
                </a-avatar>
              </a-avatar-group>
              <span style="margin-left: 8px">{{ record.participants.length }}人</span>
            </template>
            <template v-else-if="column.key === 'progress'">
              <a-progress 
                :percent="record.progress" 
                :status="record.progress === 100 ? 'success' : 'active'"
                size="small"
              />
            </template>
            <template v-else-if="column.key === 'score'">
              <span v-if="record.score !== null" :style="{ color: getScoreColor(record.score) }">
                {{ record.score }}分
              </span>
              <span v-else style="color: #999">未评分</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="editAssessment(record)"
                  :disabled="record.status === 'archived'"
                >
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="record.status === 'pending'" @click="startAssessment(record)">
                        启动考核
                      </a-menu-item>
                      <a-menu-item v-if="record.status === 'ongoing'" @click="completeAssessment(record)">
                        完成考核
                      </a-menu-item>
                      <a-menu-item @click="duplicateAssessment(record)">
                        复制考核
                      </a-menu-item>
                      <a-menu-item @click="exportAssessment(record)">
                        导出报告
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="deleteAssessment(record)">
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑考核模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      :title="editingAssessment ? '编辑考核' : '创建考核'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="考核名称" required>
          <a-input v-model:value="formData.name" placeholder="请输入考核名称" />
        </a-form-item>
        <a-form-item label="考核周期" required>
          <a-select v-model:value="formData.period" placeholder="选择考核周期">
            <a-select-option value="monthly">月度考核</a-select-option>
            <a-select-option value="quarterly">季度考核</a-select-option>
            <a-select-option value="yearly">年度考核</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="考核时间" required>
          <a-range-picker 
            v-model:value="formData.dateRange" 
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
          />
        </a-form-item>
        <a-form-item label="考核部门" required>
          <a-select v-model:value="formData.department" placeholder="选择考核部门">
            <a-select-option value="collection">催收部</a-select-option>
            <a-select-option value="legal">法务部</a-select-option>
            <a-select-option value="customer">客服部</a-select-option>
            <a-select-option value="risk">风控部</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="考核模板">
          <a-select v-model:value="formData.templateId" placeholder="选择考核模板" allowClear>
            <a-select-option value="1">催收员月度考核模板</a-select-option>
            <a-select-option value="2">客服季度考核模板</a-select-option>
            <a-select-option value="3">管理层年度考核模板</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="权重配置">
          <div class="weight-config">
            <div class="weight-item">
              <span>业绩指标：</span>
              <a-slider v-model:value="formData.weights.performance" :max="100" style="flex: 1" />
              <span>{{ formData.weights.performance }}%</span>
            </div>
            <div class="weight-item">
              <span>行为指标：</span>
              <a-slider v-model:value="formData.weights.behavior" :max="100" style="flex: 1" />
              <span>{{ formData.weights.behavior }}%</span>
            </div>
            <div class="weight-item">
              <span>能力指标：</span>
              <a-slider v-model:value="formData.weights.ability" :max="100" style="flex: 1" />
              <span>{{ formData.weights.ability }}%</span>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="考核说明">
          <a-textarea 
            v-model:value="formData.description" 
            :rows="3"
            placeholder="请输入考核说明"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 考核详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="考核详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedAssessment" class="assessment-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="考核名称">
            {{ selectedAssessment.name }}
          </a-descriptions-item>
          <a-descriptions-item label="考核周期">
            {{ getPeriodText(selectedAssessment.period) }}
          </a-descriptions-item>
          <a-descriptions-item label="考核状态">
            <a-tag :color="getStatusColor(selectedAssessment.status)">
              {{ getStatusText(selectedAssessment.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="考核部门">
            {{ getDepartmentText(selectedAssessment.department) }}
          </a-descriptions-item>
          <a-descriptions-item label="考核时间">
            {{ selectedAssessment.startDate }} ~ {{ selectedAssessment.endDate }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedAssessment.createTime }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>参与人员 ({{ selectedAssessment.participants.length }}人)</a-divider>
        <a-table
          :columns="participantColumns"
          :data-source="selectedAssessment.participants"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar>{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <template v-else-if="column.key === 'score'">
              <span v-if="record.score !== null" :style="{ color: getScoreColor(record.score) }">
                {{ record.score }}分
              </span>
              <span v-else style="color: #999">未评分</span>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getParticipantStatusColor(record.status)">
                {{ getParticipantStatusText(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>

        <a-divider>考核指标</a-divider>
        <div class="kpi-metrics">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card size="small" title="业绩指标">
                <div class="metric-item">
                  <span>回收率目标</span>
                  <span>{{ selectedAssessment.kpis.recoveryRate }}%</span>
                </div>
                <div class="metric-item">
                  <span>回收金额目标</span>
                  <span>{{ selectedAssessment.kpis.recoveryAmount }}万元</span>
                </div>
                <div class="metric-item">
                  <span>案件处理量</span>
                  <span>{{ selectedAssessment.kpis.caseVolume }}件</span>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" title="行为指标">
                <div class="metric-item">
                  <span>出勤率</span>
                  <span>{{ selectedAssessment.kpis.attendance }}%</span>
                </div>
                <div class="metric-item">
                  <span>培训完成率</span>
                  <span>{{ selectedAssessment.kpis.trainingCompletion }}%</span>
                </div>
                <div class="metric-item">
                  <span>合规得分</span>
                  <span>{{ selectedAssessment.kpis.complianceScore }}分</span>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" title="能力指标">
                <div class="metric-item">
                  <span>沟通能力</span>
                  <span>{{ selectedAssessment.kpis.communication }}分</span>
                </div>
                <div class="metric-item">
                  <span>解决问题能力</span>
                  <span>{{ selectedAssessment.kpis.problemSolving }}分</span>
                </div>
                <div class="metric-item">
                  <span>学习能力</span>
                  <span>{{ selectedAssessment.kpis.learning }}分</span>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ExportOutlined, 
  DownOutlined,
  SearchOutlined,
  ReloadOutlined 
} from '@ant-design/icons-vue'

const loading = ref(false)
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const editingAssessment = ref(null)
const selectedAssessment = ref(null)
const selectedRowKeys = ref([])

const stats = reactive({
  ongoing: 12,
  completed: 45,
  avgScore: 82.5,
  excellentRate: 65.2
})

const searchForm = reactive({
  name: '',
  period: undefined,
  status: undefined,
  department: undefined
})

const formData = reactive({
  name: '',
  period: undefined,
  dateRange: [],
  department: undefined,
  templateId: undefined,
  weights: {
    performance: 50,
    behavior: 30,
    ability: 20
  },
  description: ''
})

const assessmentList = ref([
  {
    id: 1,
    name: '2024年第一季度催收员绩效考核',
    period: 'quarterly',
    status: 'ongoing',
    department: 'collection',
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    participants: [
      { id: 1, name: '张三', position: '催收员', score: 85, status: 'completed' },
      { id: 2, name: '李四', position: '催收员', score: 92, status: 'completed' },
      { id: 3, name: '王五', position: '高级催收员', score: null, status: 'pending' }
    ],
    progress: 75,
    score: 88.5,
    createTime: '2024-01-01 09:00:00',
    kpis: {
      recoveryRate: 45,
      recoveryAmount: 200,
      caseVolume: 50,
      attendance: 95,
      trainingCompletion: 100,
      complianceScore: 90,
      communication: 85,
      problemSolving: 82,
      learning: 88
    }
  },
  {
    id: 2,
    name: '2024年2月份客服部月度考核',
    period: 'monthly',
    status: 'completed',
    department: 'customer',
    startDate: '2024-02-01',
    endDate: '2024-02-29',
    participants: [
      { id: 4, name: '赵六', position: '客服专员', score: 78, status: 'completed' },
      { id: 5, name: '钱七', position: '客服主管', score: 95, status: 'completed' }
    ],
    progress: 100,
    score: 86.5,
    createTime: '2024-02-01 09:00:00',
    kpis: {
      recoveryRate: 40,
      recoveryAmount: 150,
      caseVolume: 40,
      attendance: 98,
      trainingCompletion: 100,
      complianceScore: 95,
      communication: 90,
      problemSolving: 85,
      learning: 82
    }
  },
  {
    id: 3,
    name: '2024年风控部年度考核计划',
    period: 'yearly',
    status: 'pending',
    department: 'risk',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    participants: [
      { id: 6, name: '孙八', position: '风控专员', score: null, status: 'pending' },
      { id: 7, name: '周九', position: '风控经理', score: null, status: 'pending' }
    ],
    progress: 0,
    score: null,
    createTime: '2024-01-01 09:00:00',
    kpis: {
      recoveryRate: 50,
      recoveryAmount: 300,
      caseVolume: 60,
      attendance: 95,
      trainingCompletion: 100,
      complianceScore: 98,
      communication: 88,
      problemSolving: 90,
      learning: 85
    }
  }
])

const columns = [
  {
    title: '考核名称',
    dataIndex: 'name',
    key: 'name',
    width: 250
  },
  {
    title: '考核周期',
    dataIndex: 'period',
    key: 'period',
    width: 100,
    customRender: ({ text }) => getPeriodText(text)
  },
  {
    title: '考核状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '考核部门',
    dataIndex: 'department',
    key: 'department',
    width: 100,
    customRender: ({ text }) => getDepartmentText(text)
  },
  {
    title: '参与人员',
    key: 'participants',
    width: 150
  },
  {
    title: '考核进度',
    key: 'progress',
    width: 120
  },
  {
    title: '平均分数',
    key: 'score',
    width: 100
  },
  {
    title: '考核时间',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 180,
    customRender: ({ record }) => `${record.startDate} ~ ${record.endDate}`
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

const participantColumns = [
  {
    title: '头像',
    key: 'avatar',
    width: 80
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '职位',
    dataIndex: 'position',
    key: 'position'
  },
  {
    title: '考核分数',
    key: 'score'
  },
  {
    title: '考核状态',
    key: 'status'
  }
]

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    ongoing: 'blue',
    completed: 'green',
    archived: 'gray'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待开始',
    ongoing: '进行中',
    completed: '已完成',
    archived: '已归档'
  }
  return texts[status] || status
}

const getPeriodText = (period) => {
  const texts = {
    monthly: '月度',
    quarterly: '季度',
    yearly: '年度'
  }
  return texts[period] || period
}

const getDepartmentText = (department) => {
  const texts = {
    collection: '催收部',
    legal: '法务部',
    customer: '客服部',
    risk: '风控部'
  }
  return texts[department] || department
}

const getScoreColor = (score) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getParticipantStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    completed: 'green',
    in_progress: 'blue'
  }
  return colors[status] || 'default'
}

const getParticipantStatusText = (status) => {
  const texts = {
    pending: '待评分',
    completed: '已完成',
    in_progress: '评分中'
  }
  return texts[status] || status
}

const showCreateModal = () => {
  editingAssessment.value = null
  resetForm()
  createModalVisible.value = true
}

const editAssessment = (record) => {
  editingAssessment.value = record
  Object.assign(formData, {
    name: record.name,
    period: record.period,
    dateRange: [record.startDate, record.endDate],
    department: record.department,
    templateId: record.templateId,
    weights: { ...record.weights },
    description: record.description
  })
  createModalVisible.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    period: undefined,
    dateRange: [],
    department: undefined,
    templateId: undefined,
    weights: {
      performance: 50,
      behavior: 30,
      ability: 20
    },
    description: ''
  })
}

const handleSubmit = () => {
  if (editingAssessment.value) {
    message.success('考核更新成功')
  } else {
    message.success('考核创建成功')
  }
  createModalVisible.value = false
  resetForm()
}

const handleCancel = () => {
  createModalVisible.value = false
  resetForm()
}

const viewDetails = (record) => {
  selectedAssessment.value = record
  detailModalVisible.value = true
}

const startAssessment = (record) => {
  Modal.confirm({
    title: '确认启动考核',
    content: `确定要启动考核"${record.name}"吗？`,
    onOk() {
      message.success('考核启动成功')
    }
  })
}

const completeAssessment = (record) => {
  Modal.confirm({
    title: '确认完成考核',
    content: `确定要完成考核"${record.name}"吗？`,
    onOk() {
      message.success('考核完成成功')
    }
  })
}

const duplicateAssessment = (record) => {
  message.success('考核复制成功')
}

const deleteAssessment = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除考核"${record.name}"吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('删除成功')
    }
  })
}

const exportAssessment = (record) => {
  message.success('报告导出成功')
}

const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys
}

const batchStart = () => {
  Modal.confirm({
    title: '批量启动考核',
    content: `确定要启动选中的 ${selectedRowKeys.value.length} 个考核吗？`,
    onOk() {
      message.success('批量启动成功')
      selectedRowKeys.value = []
    }
  })
}

const batchComplete = () => {
  Modal.confirm({
    title: '批量完成考核',
    content: `确定要完成选中的 ${selectedRowKeys.value.length} 个考核吗？`,
    onOk() {
      message.success('批量完成成功')
      selectedRowKeys.value = []
    }
  })
}

const batchDelete = () => {
  Modal.confirm({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个考核吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('批量删除成功')
      selectedRowKeys.value = []
    }
  })
}

const handleSearch = () => {
  message.info('搜索功能已触发')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    period: undefined,
    status: undefined,
    department: undefined
  })
  message.info('搜索条件已重置')
}

const exportData = () => {
  message.success('数据导出成功')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

onMounted(() => {
  // 页面加载时的初始化逻辑
})
</script>

<style scoped>
.performance-assessment {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.search-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.weight-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.weight-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.weight-item span:first-child {
  width: 80px;
  flex-shrink: 0;
}

.weight-item span:last-child {
  width: 50px;
  text-align: right;
  flex-shrink: 0;
}

.assessment-detail {
  padding: 8px 0;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}
</style>