<template>
  <div class="performance-ranking">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>绩效排行榜</h2>
      <div class="header-actions">
        <a-button @click="refreshRanking">
          <template #icon><ReloadOutlined /></template>
          刷新排行
        </a-button>
        <a-button @click="exportRanking">
          <template #icon><ExportOutlined /></template>
          导出排行
        </a-button>
        <a-button type="primary" @click="showSettingsModal">
          <template #icon><SettingOutlined /></template>
          排行设置
        </a-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <a-card>
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="排行类型">
            <a-select v-model:value="filterForm.type" style="width: 150px" @change="handleTypeChange">
              <a-select-option value="individual">个人排行</a-select-option>
              <a-select-option value="team">团队排行</a-select-option>
              <a-select-option value="department">部门排行</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="排行指标">
            <a-select v-model:value="filterForm.metric" style="width: 150px" @change="handleMetricChange">
              <a-select-option value="recovery_rate">回收率</a-select-option>
              <a-select-option value="recovery_amount">回收金额</a-select-option>
              <a-select-option value="case_count">案件数量</a-select-option>
              <a-select-option value="satisfaction">满意度</a-select-option>
              <a-select-option value="comprehensive">综合得分</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间周期">
            <a-select v-model:value="filterForm.period" style="width: 120px" @change="handlePeriodChange">
              <a-select-option value="daily">日排行</a-select-option>
              <a-select-option value="weekly">周排行</a-select-option>
              <a-select-option value="monthly">月排行</a-select-option>
              <a-select-option value="quarterly">季排行</a-select-option>
              <a-select-option value="yearly">年排行</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="部门">
            <a-select v-model:value="filterForm.department" style="width: 120px" allowClear>
              <a-select-option value="collection">催收部</a-select-option>
              <a-select-option value="customer">客服部</a-select-option>
              <a-select-option value="sales">销售部</a-select-option>
              <a-select-option value="risk">风控部</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="updateRanking">更新排行</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 排行榜展示 -->
    <div class="ranking-display">
      <a-row :gutter="24">
        <!-- 前三名podium -->
        <a-col :span="16">
          <a-card title="排行榜" class="ranking-card">
            <div class="podium-section">
              <div class="podium-container">
                <!-- 第二名 -->
                <div class="podium-item second" v-if="topThree[1]">
                  <div class="avatar-container">
                    <a-avatar :size="60" class="rank-avatar">
                      {{ topThree[1].name.charAt(0) }}
                    </a-avatar>
                    <div class="rank-number">2</div>
                  </div>
                  <div class="info">
                    <div class="name">{{ topThree[1].name }}</div>
                    <div class="department">{{ getDepartmentText(topThree[1].department) }}</div>
                    <div class="score">{{ topThree[1].score }}</div>
                  </div>
                </div>
                
                <!-- 第一名 -->
                <div class="podium-item first" v-if="topThree[0]">
                  <div class="crown">👑</div>
                  <div class="avatar-container">
                    <a-avatar :size="80" class="rank-avatar">
                      {{ topThree[0].name.charAt(0) }}
                    </a-avatar>
                    <div class="rank-number champion">1</div>
                  </div>
                  <div class="info">
                    <div class="name">{{ topThree[0].name }}</div>
                    <div class="department">{{ getDepartmentText(topThree[0].department) }}</div>
                    <div class="score">{{ topThree[0].score }}</div>
                  </div>
                </div>
                
                <!-- 第三名 -->
                <div class="podium-item third" v-if="topThree[2]">
                  <div class="avatar-container">
                    <a-avatar :size="50" class="rank-avatar">
                      {{ topThree[2].name.charAt(0) }}
                    </a-avatar>
                    <div class="rank-number">3</div>
                  </div>
                  <div class="info">
                    <div class="name">{{ topThree[2].name }}</div>
                    <div class="department">{{ getDepartmentText(topThree[2].department) }}</div>
                    <div class="score">{{ topThree[2].score }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 排行榜列表 -->
            <div class="ranking-list">
              <a-table
                :columns="rankingColumns"
                :data-source="rankingList"
                :pagination="false"
                :scroll="{ y: 400 }"
                class="ranking-table"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'rank'">
                    <div class="rank-cell">
                      <span class="rank-number" :class="getRankClass(record.rank)">
                        {{ record.rank }}
                      </span>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'info'">
                    <div class="user-info">
                      <a-avatar :size="32">{{ record.name.charAt(0) }}</a-avatar>
                      <div class="user-details">
                        <div class="name">{{ record.name }}</div>
                        <div class="department">{{ getDepartmentText(record.department) }}</div>
                      </div>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'score'">
                    <div class="score-cell">
                      <span class="score-value">{{ record.score }}</span>
                      <span class="score-unit">{{ getScoreUnit(filterForm.metric) }}</span>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'trend'">
                    <div class="trend-cell">
                      <span :class="getTrendClass(record.trend)">
                        <component :is="getTrendIcon(record.trend)" />
                        {{ Math.abs(record.trend) }}
                      </span>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-button type="link" size="small" @click="viewProfile(record)">
                      查看详情
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧信息面板 -->
        <a-col :span="8">
          <!-- 竞赛活动 -->
          <a-card title="竞赛活动" class="contest-card" style="margin-bottom: 16px">
            <div class="contest-list">
              <div 
                v-for="contest in contestList" 
                :key="contest.id" 
                class="contest-item"
                @click="viewContest(contest)"
              >
                <div class="contest-header">
                  <span class="contest-title">{{ contest.title }}</span>
                  <a-tag :color="getContestStatusColor(contest.status)">
                    {{ getContestStatusText(contest.status) }}
                  </a-tag>
                </div>
                <div class="contest-info">
                  <div class="contest-period">{{ contest.startDate }} - {{ contest.endDate }}</div>
                  <div class="contest-participants">{{ contest.participants }}人参与</div>
                </div>
                <div class="contest-prize">
                  <TrophyOutlined style="color: #faad14" />
                  <span>奖金池：{{ contest.prize }}元</span>
                </div>
              </div>
            </div>
          </a-card>

          <!-- 个人表现 -->
          <a-card title="我的表现" class="performance-card">
            <div class="my-performance">
              <div class="performance-item">
                <div class="label">当前排名</div>
                <div class="value rank-value">{{ myPerformance.rank }}</div>
              </div>
              <div class="performance-item">
                <div class="label">本月得分</div>
                <div class="value">{{ myPerformance.score }}</div>
              </div>
              <div class="performance-item">
                <div class="label">排名变化</div>
                <div class="value" :class="getTrendClass(myPerformance.trend)">
                  <component :is="getTrendIcon(myPerformance.trend)" />
                  {{ Math.abs(myPerformance.trend) }}
                </div>
              </div>
              <div class="performance-item">
                <div class="label">距离上一名</div>
                <div class="value">{{ myPerformance.gap }}分</div>
              </div>
            </div>
            
            <a-divider />
            
            <div class="performance-chart">
              <div ref="performanceChart" style="width: 100%; height: 200px;"></div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 排行设置模态框 -->
    <a-modal
      v-model:open="settingsModalVisible"
      title="排行榜设置"
      width="600px"
      @ok="saveSettings"
      @cancel="cancelSettings"
    >
      <a-form :model="settingsForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="更新频率">
          <a-select v-model:value="settingsForm.updateFrequency">
            <a-select-option value="realtime">实时更新</a-select-option>
            <a-select-option value="hourly">每小时</a-select-option>
            <a-select-option value="daily">每日</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="显示数量">
          <a-input-number 
            v-model:value="settingsForm.displayCount" 
            :min="10" 
            :max="100" 
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="权重配置">
          <div class="weight-config">
            <div class="weight-item">
              <span>回收率：</span>
              <a-slider v-model:value="settingsForm.weights.recoveryRate" :max="100" style="flex: 1" />
              <span>{{ settingsForm.weights.recoveryRate }}%</span>
            </div>
            <div class="weight-item">
              <span>回收金额：</span>
              <a-slider v-model:value="settingsForm.weights.recoveryAmount" :max="100" style="flex: 1" />
              <span>{{ settingsForm.weights.recoveryAmount }}%</span>
            </div>
            <div class="weight-item">
              <span>客户满意度：</span>
              <a-slider v-model:value="settingsForm.weights.satisfaction" :max="100" style="flex: 1" />
              <span>{{ settingsForm.weights.satisfaction }}%</span>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="排行公开">
          <a-switch v-model:checked="settingsForm.isPublic" />
          <span style="margin-left: 8px">是否对所有员工公开排行榜</span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 个人详情模态框 -->
    <a-modal
      v-model:open="profileModalVisible"
      title="个人表现详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedProfile" class="profile-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="姓名">
            {{ selectedProfile.name }}
          </a-descriptions-item>
          <a-descriptions-item label="部门">
            {{ getDepartmentText(selectedProfile.department) }}
          </a-descriptions-item>
          <a-descriptions-item label="当前排名">
            第{{ selectedProfile.rank }}名
          </a-descriptions-item>
          <a-descriptions-item label="本月得分">
            {{ selectedProfile.score }}分
          </a-descriptions-item>
          <a-descriptions-item label="排名变化">
            <span :class="getTrendClass(selectedProfile.trend)">
              <component :is="getTrendIcon(selectedProfile.trend)" />
              {{ Math.abs(selectedProfile.trend) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="入职时间">
            {{ selectedProfile.joinDate }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>详细指标</a-divider>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="回收率" 
              :value="selectedProfile.metrics.recoveryRate" 
              suffix="%" 
              :precision="1"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="回收金额" 
              :value="selectedProfile.metrics.recoveryAmount" 
              suffix="万元" 
              :precision="1"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="案件数量" 
              :value="selectedProfile.metrics.caseCount" 
              suffix="件"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="满意度" 
              :value="selectedProfile.metrics.satisfaction" 
              suffix="分" 
              :precision="1"
            />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined,
  ExportOutlined,
  SettingOutlined,
  TrophyOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

const settingsModalVisible = ref(false)
const profileModalVisible = ref(false)
const selectedProfile = ref(null)
const performanceChart = ref(null)

const filterForm = reactive({
  type: 'individual',
  metric: 'comprehensive',
  period: 'monthly',
  department: undefined
})

const settingsForm = reactive({
  updateFrequency: 'daily',
  displayCount: 50,
  weights: {
    recoveryRate: 40,
    recoveryAmount: 30,
    satisfaction: 30
  },
  isPublic: true
})

const myPerformance = reactive({
  rank: 5,
  score: 88.5,
  trend: 2,
  gap: 3.2
})

const rankingList = ref([
  {
    rank: 1,
    name: '张三',
    department: 'collection',
    score: 95.8,
    trend: 1,
    metrics: {
      recoveryRate: 92.5,
      recoveryAmount: 126.8,
      caseCount: 158,
      satisfaction: 94.2
    },
    joinDate: '2022-03-15'
  },
  {
    rank: 2,
    name: '李四',
    department: 'collection',
    score: 93.2,
    trend: -1,
    metrics: {
      recoveryRate: 89.3,
      recoveryAmount: 145.2,
      caseCount: 172,
      satisfaction: 91.8
    },
    joinDate: '2021-08-20'
  },
  {
    rank: 3,
    name: '王五',
    department: 'customer',
    score: 91.7,
    trend: 2,
    metrics: {
      recoveryRate: 87.6,
      recoveryAmount: 132.4,
      caseCount: 164,
      satisfaction: 96.5
    },
    joinDate: '2023-01-10'
  },
  {
    rank: 4,
    name: '赵六',
    department: 'collection',
    score: 90.3,
    trend: 0,
    metrics: {
      recoveryRate: 85.2,
      recoveryAmount: 118.6,
      caseCount: 142,
      satisfaction: 88.9
    },
    joinDate: '2022-11-05'
  },
  {
    rank: 5,
    name: '钱七',
    department: 'customer',
    score: 88.5,
    trend: 2,
    metrics: {
      recoveryRate: 83.7,
      recoveryAmount: 108.3,
      caseCount: 139,
      satisfaction: 92.1
    },
    joinDate: '2023-06-12'
  }
])

const contestList = ref([
  {
    id: 1,
    title: '春季回收率大赛',
    status: 'active',
    startDate: '2024-03-01',
    endDate: '2024-05-31',
    participants: 45,
    prize: 50000
  },
  {
    id: 2,
    title: '客户满意度挑战赛',
    status: 'upcoming',
    startDate: '2024-06-01',
    endDate: '2024-08-31',
    participants: 38,
    prize: 30000
  },
  {
    id: 3,
    title: '新人成长赛',
    status: 'ended',
    startDate: '2024-01-01',
    endDate: '2024-02-29',
    participants: 12,
    prize: 20000
  }
])

const topThree = computed(() => {
  return rankingList.value.slice(0, 3)
})

const rankingColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '员工信息',
    key: 'info',
    width: 150
  },
  {
    title: '得分',
    key: 'score',
    width: 100,
    align: 'center'
  },
  {
    title: '变化',
    key: 'trend',
    width: 80,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    align: 'center'
  }
]

const getDepartmentText = (department) => {
  const texts = {
    collection: '催收部',
    customer: '客服部',
    sales: '销售部',
    risk: '风控部'
  }
  return texts[department] || department
}

const getScoreUnit = (metric) => {
  const units = {
    recovery_rate: '%',
    recovery_amount: '万元',
    case_count: '件',
    satisfaction: '分',
    comprehensive: '分'
  }
  return units[metric] || '分'
}

const getRankClass = (rank) => {
  if (rank <= 3) return 'top-three'
  if (rank <= 10) return 'top-ten'
  return ''
}

const getTrendClass = (trend) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

const getTrendIcon = (trend) => {
  if (trend > 0) return CaretUpOutlined
  if (trend < 0) return CaretDownOutlined
  return MinusOutlined
}

const getContestStatusColor = (status) => {
  const colors = {
    active: 'green',
    upcoming: 'blue',
    ended: 'gray'
  }
  return colors[status] || 'default'
}

const getContestStatusText = (status) => {
  const texts = {
    active: '进行中',
    upcoming: '即将开始',
    ended: '已结束'
  }
  return texts[status] || status
}

const initPerformanceChart = () => {
  if (!performanceChart.value) return
  
  const chart = echarts.init(performanceChart.value)
  const option = {
    title: {
      text: '近7日表现',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '得分',
        type: 'line',
        data: [85, 87, 89, 88, 90, 89, 88],
        smooth: true,
        itemStyle: { color: '#1890ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(24, 144, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(24, 144, 255, 0.1)'
            }]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

const handleTypeChange = () => {
  updateRanking()
}

const handleMetricChange = () => {
  updateRanking()
}

const handlePeriodChange = () => {
  updateRanking()
}

const updateRanking = () => {
  message.info('排行榜更新中...')
  // 模拟数据更新
  setTimeout(() => {
    message.success('排行榜已更新')
  }, 1000)
}

const refreshRanking = () => {
  message.success('排行榜已刷新')
}

const exportRanking = () => {
  message.success('排行榜数据导出成功')
}

const showSettingsModal = () => {
  settingsModalVisible.value = true
}

const saveSettings = () => {
  message.success('设置保存成功')
  settingsModalVisible.value = false
}

const cancelSettings = () => {
  settingsModalVisible.value = false
}

const viewProfile = (record) => {
  selectedProfile.value = record
  profileModalVisible.value = true
}

const viewContest = (contest) => {
  message.info(`查看竞赛：${contest.title}`)
}

onMounted(() => {
  nextTick(() => {
    initPerformanceChart()
  })
})
</script>

<style scoped>
.performance-ranking {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  margin-bottom: 24px;
}

.ranking-display {
  margin-bottom: 24px;
}

.ranking-card {
  height: 100%;
}

.podium-section {
  margin-bottom: 24px;
  padding: 20px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.podium-container {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 20px;
  height: 200px;
}

.podium-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.podium-item.first {
  order: 2;
}

.podium-item.second {
  order: 1;
}

.podium-item.third {
  order: 3;
}

.crown {
  font-size: 24px;
  margin-bottom: 8px;
}

.avatar-container {
  position: relative;
  margin-bottom: 12px;
}

.rank-avatar {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  font-weight: bold;
}

.rank-number {
  position: absolute;
  bottom: -5px;
  right: -5px;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.rank-number.champion {
  background: #ffd700;
  color: #333;
  width: 28px;
  height: 28px;
}

.podium-item .info {
  text-align: center;
}

.podium-item .name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.podium-item .department {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.podium-item .score {
  font-size: 18px;
  font-weight: bold;
  color: #ffd700;
}

.ranking-list {
  background: white;
}

.ranking-table :deep(.ant-table-tbody > tr:hover) {
  background: #f5f5f5;
}

.rank-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.rank-cell .rank-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background: #f0f0f0;
  color: #666;
}

.rank-cell .rank-number.top-three {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
}

.rank-cell .rank-number.top-ten {
  background: #1890ff;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-details .name {
  font-weight: 500;
  margin-bottom: 2px;
}

.user-details .department {
  font-size: 12px;
  color: #666;
}

.score-cell {
  text-align: center;
}

.score-value {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.score-unit {
  font-size: 12px;
  color: #666;
  margin-left: 2px;
}

.trend-cell {
  text-align: center;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-stable {
  color: #666;
}

.contest-card,
.performance-card {
  height: fit-content;
}

.contest-list {
  max-height: 300px;
  overflow-y: auto;
}

.contest-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.contest-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.contest-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.contest-title {
  font-weight: 500;
}

.contest-info {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.contest-period {
  margin-bottom: 2px;
}

.contest-prize {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #faad14;
}

.my-performance {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.performance-item {
  text-align: center;
}

.performance-item .label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.performance-item .value {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

.performance-item .rank-value {
  color: #ff4d4f;
}

.weight-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.weight-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.weight-item span:first-child {
  width: 80px;
  flex-shrink: 0;
}

.weight-item span:last-child {
  width: 50px;
  text-align: right;
  flex-shrink: 0;
}

.profile-detail {
  padding: 8px 0;
}
</style>