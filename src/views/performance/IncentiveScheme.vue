<template>
  <div class="incentive-scheme">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>激励方案</h2>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          创建方案
        </a-button>
        <a-button @click="previewScheme">
          <template #icon><EyeOutlined /></template>
          预览方案
        </a-button>
        <a-button @click="exportData">
          <template #icon><ExportOutlined /></template>
          导出数据
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="激励方案总数" 
              :value="stats.totalSchemes" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="活跃方案" 
              :value="stats.activeSchemes" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="本月激励总额" 
              :value="stats.monthlyIncentive" 
              suffix="万元"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="参与人数" 
              :value="stats.participants" 
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 激励效果分析 -->
    <div class="incentive-analysis">
      <a-card title="激励效果分析">
        <a-row :gutter="24">
          <a-col :span="12">
            <div ref="effectChart" style="width: 100%; height: 300px;"></div>
          </a-col>
          <a-col :span="12">
            <div ref="distributionChart" style="width: 100%; height: 300px;"></div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="方案名称">
            <a-input 
              v-model:value="searchForm.name" 
              placeholder="请输入方案名称"
              allowClear
            />
          </a-form-item>
          <a-form-item label="方案类型">
            <a-select v-model:value="searchForm.type" placeholder="选择类型" allowClear>
              <a-select-option value="commission">提成方案</a-select-option>
              <a-select-option value="bonus">奖金方案</a-select-option>
              <a-select-option value="contest">竞赛方案</a-select-option>
              <a-select-option value="longterm">长期激励</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="选择状态" allowClear>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="active">生效中</a-select-option>
              <a-select-option value="suspended">暂停</a-select-option>
              <a-select-option value="expired">已过期</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="适用部门">
            <a-select v-model:value="searchForm.department" placeholder="选择部门" allowClear>
              <a-select-option value="collection">催收部</a-select-option>
              <a-select-option value="sales">销售部</a-select-option>
              <a-select-option value="customer">客服部</a-select-option>
              <a-select-option value="all">全员</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 激励方案列表 -->
    <div class="table-section">
      <a-card>
        <div class="table-header">
          <div class="table-actions">
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchActivate"
            >
              批量启用
            </a-button>
            <a-button 
              :disabled="!selectedRowKeys.length"
              @click="batchSuspend"
            >
              批量暂停
            </a-button>
            <a-button 
              danger
              :disabled="!selectedRowKeys.length"
              @click="batchDelete"
            >
              批量删除
            </a-button>
          </div>
        </div>

        <a-table
          :columns="columns"
          :data-source="schemeList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: (record) => ({
              disabled: record.status === 'expired'
            })
          }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'participants'">
              <a-avatar-group :max-count="3">
                <a-avatar v-for="participant in record.participants.slice(0, 3)" :key="participant.id">
                  {{ participant.name.charAt(0) }}
                </a-avatar>
              </a-avatar-group>
              <span style="margin-left: 8px">{{ record.participants.length }}人</span>
            </template>
            <template v-else-if="column.key === 'budget'">
              <span style="color: #1890ff">{{ record.budget }}万元</span>
            </template>
            <template v-else-if="column.key === 'effectiveness'">
              <a-progress 
                :percent="record.effectiveness" 
                size="small"
                :status="record.effectiveness >= 80 ? 'success' : 'active'"
              />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="editScheme(record)"
                  :disabled="record.status === 'expired'"
                >
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="record.status === 'draft'" @click="activateScheme(record)">
                        启用方案
                      </a-menu-item>
                      <a-menu-item v-if="record.status === 'active'" @click="suspendScheme(record)">
                        暂停方案
                      </a-menu-item>
                      <a-menu-item @click="duplicateScheme(record)">
                        复制方案
                      </a-menu-item>
                      <a-menu-item @click="calculateReward(record)">
                        计算奖励
                      </a-menu-item>
                      <a-menu-item @click="viewHistory(record)">
                        查看历史
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="deleteScheme(record)">
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑方案模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      :title="editingScheme ? '编辑激励方案' : '创建激励方案'"
      width="1000px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="方案名称" required>
          <a-input v-model:value="formData.name" placeholder="请输入方案名称" />
        </a-form-item>
        <a-form-item label="方案类型" required>
          <a-select v-model:value="formData.type" placeholder="选择方案类型">
            <a-select-option value="commission">提成方案</a-select-option>
            <a-select-option value="bonus">奖金方案</a-select-option>
            <a-select-option value="contest">竞赛方案</a-select-option>
            <a-select-option value="longterm">长期激励</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="适用范围" required>
          <a-select v-model:value="formData.scope" placeholder="选择适用范围">
            <a-select-option value="collection">催收部</a-select-option>
            <a-select-option value="sales">销售部</a-select-option>
            <a-select-option value="customer">客服部</a-select-option>
            <a-select-option value="all">全员</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="有效期" required>
          <a-range-picker 
            v-model:value="formData.dateRange" 
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
          />
        </a-form-item>
        <a-form-item label="预算设置" required>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-input-number 
                v-model:value="formData.budget.total" 
                placeholder="总预算"
                :min="0"
                style="width: 100%"
                addon-after="万元"
              />
            </a-col>
            <a-col :span="12">
              <a-input-number 
                v-model:value="formData.budget.monthly" 
                placeholder="月度预算"
                :min="0"
                style="width: 100%"
                addon-after="万元"
              />
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="激励规则">
          <div class="rules-config">
            <div v-for="(rule, index) in formData.rules" :key="index" class="rule-item">
              <a-row :gutter="16" align="middle">
                <a-col :span="6">
                  <a-select v-model:value="rule.metric" placeholder="选择指标">
                    <a-select-option value="recovery_rate">回收率</a-select-option>
                    <a-select-option value="recovery_amount">回收金额</a-select-option>
                    <a-select-option value="case_count">案件数量</a-select-option>
                    <a-select-option value="satisfaction">满意度</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="rule.operator" placeholder="条件">
                    <a-select-option value="gte">≥</a-select-option>
                    <a-select-option value="lte">≤</a-select-option>
                    <a-select-option value="between">区间</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="5">
                  <a-input-number 
                    v-model:value="rule.threshold" 
                    placeholder="阈值"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="5">
                  <a-input-number 
                    v-model:value="rule.reward" 
                    placeholder="奖励金额"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="3">
                  <a-select v-model:value="rule.unit" placeholder="单位">
                    <a-select-option value="fixed">固定</a-select-option>
                    <a-select-option value="percent">百分比</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="1">
                  <a-button 
                    type="text" 
                    danger 
                    @click="removeRule(index)"
                    :disabled="formData.rules.length <= 1"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-col>
              </a-row>
            </div>
            <a-button type="dashed" block @click="addRule" style="margin-top: 8px">
              <PlusOutlined /> 添加规则
            </a-button>
          </div>
        </a-form-item>
        <a-form-item label="发放方式">
          <a-radio-group v-model:value="formData.paymentMethod">
            <a-radio value="monthly">按月发放</a-radio>
            <a-radio value="quarterly">按季发放</a-radio>
            <a-radio value="realtime">实时发放</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="方案描述">
          <a-textarea 
            v-model:value="formData.description" 
            :rows="3"
            placeholder="请输入方案描述"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 方案详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="激励方案详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="selectedScheme" class="scheme-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="方案名称">
            {{ selectedScheme.name }}
          </a-descriptions-item>
          <a-descriptions-item label="方案类型">
            <a-tag :color="getTypeColor(selectedScheme.type)">
              {{ getTypeText(selectedScheme.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedScheme.status)">
              {{ getStatusText(selectedScheme.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="适用范围">
            {{ getScopeText(selectedScheme.scope) }}
          </a-descriptions-item>
          <a-descriptions-item label="有效期">
            {{ selectedScheme.startDate }} ~ {{ selectedScheme.endDate }}
          </a-descriptions-item>
          <a-descriptions-item label="总预算">
            {{ selectedScheme.budget }}万元
          </a-descriptions-item>
          <a-descriptions-item label="参与人数">
            {{ selectedScheme.participants.length }}人
          </a-descriptions-item>
          <a-descriptions-item label="执行效果">
            {{ selectedScheme.effectiveness }}%
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>激励规则</a-divider>
        <a-table
          :columns="ruleColumns"
          :data-source="selectedScheme.incentiveRules"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'metric'">
              {{ getMetricText(record.metric) }}
            </template>
            <template v-else-if="column.key === 'condition'">
              {{ getOperatorText(record.operator) }} {{ record.threshold }}
            </template>
            <template v-else-if="column.key === 'reward'">
              {{ record.reward }}{{ record.unit === 'percent' ? '%' : '元' }}
            </template>
          </template>
        </a-table>

        <a-divider>参与人员</a-divider>
        <a-table
          :columns="participantColumns"
          :data-source="selectedScheme.participants"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar>{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <template v-else-if="column.key === 'performance'">
              <a-progress :percent="record.performance" size="small" />
            </template>
            <template v-else-if="column.key === 'earned'">
              <span style="color: #52c41a">{{ record.earned }}元</span>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 奖励计算模态框 -->
    <a-modal
      v-model:open="calculationModalVisible"
      title="奖励计算"
      width="800px"
      :footer="null"
    >
      <div class="calculation-result">
        <a-alert
          message="奖励计算完成"
          description="根据当前方案规则和员工表现，计算出以下奖励分配："
          type="success"
          show-icon
          style="margin-bottom: 16px"
        />
        <a-table
          :columns="calculationColumns"
          :data-source="calculationResults"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar>{{ record.name.charAt(0) }}</a-avatar>
            </template>
            <template v-else-if="column.key === 'achievement'">
              <a-progress :percent="record.achievement" size="small" />
            </template>
            <template v-else-if="column.key === 'reward'">
              <span style="color: #52c41a; font-weight: bold">{{ record.reward }}元</span>
            </template>
          </template>
        </a-table>
        <div style="text-align: right; margin-top: 16px">
          <a-button type="primary" @click="confirmRewards">
            确认发放
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ExportOutlined, 
  EyeOutlined,
  DownOutlined,
  DeleteOutlined 
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

const loading = ref(false)
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const calculationModalVisible = ref(false)
const editingScheme = ref(null)
const selectedScheme = ref(null)
const selectedRowKeys = ref([])
const effectChart = ref(null)
const distributionChart = ref(null)

const stats = reactive({
  totalSchemes: 25,
  activeSchemes: 12,
  monthlyIncentive: 45.6,
  participants: 186
})

const searchForm = reactive({
  name: '',
  type: undefined,
  status: undefined,
  department: undefined
})

const formData = reactive({
  name: '',
  type: undefined,
  scope: undefined,
  dateRange: [],
  budget: {
    total: undefined,
    monthly: undefined
  },
  rules: [
    {
      metric: undefined,
      operator: undefined,
      threshold: undefined,
      reward: undefined,
      unit: undefined
    }
  ],
  paymentMethod: 'monthly',
  description: ''
})

const schemeList = ref([
  {
    id: 1,
    name: '催收员回收率激励方案',
    type: 'commission',
    status: 'active',
    scope: 'collection',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    budget: 120,
    effectiveness: 85,
    participants: [
      { id: 1, name: '张三', department: '催收部', performance: 92, earned: 8500 },
      { id: 2, name: '李四', department: '催收部', performance: 88, earned: 7200 },
      { id: 3, name: '王五', department: '催收部', performance: 95, earned: 9800 }
    ],
    incentiveRules: [
      { metric: 'recovery_rate', operator: 'gte', threshold: 80, reward: 1000, unit: 'fixed' },
      { metric: 'recovery_rate', operator: 'gte', threshold: 90, reward: 2000, unit: 'fixed' },
      { metric: 'recovery_amount', operator: 'gte', threshold: 50, reward: 5, unit: 'percent' }
    ],
    createTime: '2024-01-01 09:00:00'
  },
  {
    id: 2,
    name: '季度团队竞赛奖励',
    type: 'contest',
    status: 'active',
    scope: 'all',
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    budget: 50,
    effectiveness: 92,
    participants: [
      { id: 4, name: '赵六', department: '客服部', performance: 89, earned: 6000 },
      { id: 5, name: '钱七', department: '销售部', performance: 94, earned: 8500 },
      { id: 6, name: '孙八', department: '风控部', performance: 87, earned: 5500 }
    ],
    incentiveRules: [
      { metric: 'case_count', operator: 'gte', threshold: 100, reward: 3000, unit: 'fixed' },
      { metric: 'satisfaction', operator: 'gte', threshold: 95, reward: 2000, unit: 'fixed' }
    ],
    createTime: '2024-04-01 09:00:00'
  },
  {
    id: 3,
    name: '年度长期激励计划',
    type: 'longterm',
    status: 'draft',
    scope: 'all',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    budget: 200,
    effectiveness: 0,
    participants: [],
    incentiveRules: [
      { metric: 'recovery_rate', operator: 'gte', threshold: 85, reward: 10, unit: 'percent' },
      { metric: 'case_count', operator: 'gte', threshold: 200, reward: 5000, unit: 'fixed' }
    ],
    createTime: '2024-01-01 09:00:00'
  }
])

const calculationResults = ref([
  { id: 1, name: '张三', department: '催收部', achievement: 92, reward: 8500 },
  { id: 2, name: '李四', department: '催收部', achievement: 88, reward: 7200 },
  { id: 3, name: '王五', department: '催收部', achievement: 95, reward: 9800 },
  { id: 4, name: '赵六', department: '客服部', achievement: 89, reward: 6000 }
])

const columns = [
  {
    title: '方案名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '方案类型',
    key: 'type',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '适用范围',
    dataIndex: 'scope',
    key: 'scope',
    width: 120,
    customRender: ({ text }) => getScopeText(text)
  },
  {
    title: '参与人员',
    key: 'participants',
    width: 150
  },
  {
    title: '预算',
    key: 'budget',
    width: 100
  },
  {
    title: '执行效果',
    key: 'effectiveness',
    width: 120
  },
  {
    title: '有效期',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 180,
    customRender: ({ record }) => `${record.startDate} ~ ${record.endDate}`
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

const ruleColumns = [
  { title: '指标', key: 'metric' },
  { title: '条件', key: 'condition' },
  { title: '奖励', key: 'reward' }
]

const participantColumns = [
  { title: '头像', key: 'avatar', width: 80 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '表现', key: 'performance' },
  { title: '已获得', key: 'earned' }
]

const calculationColumns = [
  { title: '头像', key: 'avatar', width: 80 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '达成度', key: 'achievement' },
  { title: '奖励金额', key: 'reward' }
]

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

const getTypeColor = (type) => {
  const colors = {
    commission: 'blue',
    bonus: 'green',
    contest: 'orange',
    longterm: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    commission: '提成方案',
    bonus: '奖金方案',
    contest: '竞赛方案',
    longterm: '长期激励'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'gray',
    active: 'green',
    suspended: 'orange',
    expired: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    active: '生效中',
    suspended: '暂停',
    expired: '已过期'
  }
  return texts[status] || status
}

const getScopeText = (scope) => {
  const texts = {
    collection: '催收部',
    sales: '销售部',
    customer: '客服部',
    all: '全员'
  }
  return texts[scope] || scope
}

const getMetricText = (metric) => {
  const texts = {
    recovery_rate: '回收率',
    recovery_amount: '回收金额',
    case_count: '案件数量',
    satisfaction: '满意度'
  }
  return texts[metric] || metric
}

const getOperatorText = (operator) => {
  const texts = {
    gte: '≥',
    lte: '≤',
    between: '区间'
  }
  return texts[operator] || operator
}

const initCharts = () => {
  // 激励效果图表
  if (effectChart.value) {
    const chart1 = echarts.init(effectChart.value)
    const option1 = {
      title: { text: '激励效果趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '回收率',
          type: 'line',
          data: [75, 78, 82, 85, 88, 87],
          smooth: true,
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '满意度',
          type: 'line',
          data: [80, 82, 85, 87, 89, 91],
          smooth: true,
          itemStyle: { color: '#52c41a' }
        }
      ]
    }
    chart1.setOption(option1)
  }

  // 奖励分布图表
  if (distributionChart.value) {
    const chart2 = echarts.init(distributionChart.value)
    const option2 = {
      title: { text: '奖励分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 35, name: '提成奖励', itemStyle: { color: '#1890ff' } },
            { value: 25, name: '绩效奖金', itemStyle: { color: '#52c41a' } },
            { value: 20, name: '竞赛奖励', itemStyle: { color: '#faad14' } },
            { value: 20, name: '长期激励', itemStyle: { color: '#722ed1' } }
          ]
        }
      ]
    }
    chart2.setOption(option2)
  }
}

const showCreateModal = () => {
  editingScheme.value = null
  resetForm()
  createModalVisible.value = true
}

const editScheme = (record) => {
  editingScheme.value = record
  Object.assign(formData, {
    name: record.name,
    type: record.type,
    scope: record.scope,
    dateRange: [record.startDate, record.endDate],
    budget: {
      total: record.budget,
      monthly: Math.round(record.budget / 12)
    },
    rules: [...record.incentiveRules],
    paymentMethod: record.paymentMethod || 'monthly',
    description: record.description
  })
  createModalVisible.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: undefined,
    scope: undefined,
    dateRange: [],
    budget: {
      total: undefined,
      monthly: undefined
    },
    rules: [
      {
        metric: undefined,
        operator: undefined,
        threshold: undefined,
        reward: undefined,
        unit: undefined
      }
    ],
    paymentMethod: 'monthly',
    description: ''
  })
}

const addRule = () => {
  formData.rules.push({
    metric: undefined,
    operator: undefined,
    threshold: undefined,
    reward: undefined,
    unit: undefined
  })
}

const removeRule = (index) => {
  formData.rules.splice(index, 1)
}

const handleSubmit = () => {
  if (editingScheme.value) {
    message.success('方案更新成功')
  } else {
    message.success('方案创建成功')
  }
  createModalVisible.value = false
  resetForm()
}

const handleCancel = () => {
  createModalVisible.value = false
  resetForm()
}

const viewDetails = (record) => {
  selectedScheme.value = record
  detailModalVisible.value = true
}

const activateScheme = (record) => {
  Modal.confirm({
    title: '确认启用方案',
    content: `确定要启用激励方案"${record.name}"吗？`,
    onOk() {
      message.success('方案启用成功')
    }
  })
}

const suspendScheme = (record) => {
  Modal.confirm({
    title: '确认暂停方案',
    content: `确定要暂停激励方案"${record.name}"吗？`,
    onOk() {
      message.success('方案暂停成功')
    }
  })
}

const duplicateScheme = (record) => {
  message.success('方案复制成功')
}

const calculateReward = (record) => {
  selectedScheme.value = record
  calculationModalVisible.value = true
}

const confirmRewards = () => {
  Modal.confirm({
    title: '确认发放奖励',
    content: '确定要发放计算出的奖励金额吗？',
    onOk() {
      message.success('奖励发放成功')
      calculationModalVisible.value = false
    }
  })
}

const viewHistory = (record) => {
  message.info('查看历史功能')
}

const deleteScheme = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除激励方案"${record.name}"吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('删除成功')
    }
  })
}

const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys
}

const batchActivate = () => {
  Modal.confirm({
    title: '批量启用方案',
    content: `确定要启用选中的 ${selectedRowKeys.value.length} 个方案吗？`,
    onOk() {
      message.success('批量启用成功')
      selectedRowKeys.value = []
    }
  })
}

const batchSuspend = () => {
  Modal.confirm({
    title: '批量暂停方案',
    content: `确定要暂停选中的 ${selectedRowKeys.value.length} 个方案吗？`,
    onOk() {
      message.success('批量暂停成功')
      selectedRowKeys.value = []
    }
  })
}

const batchDelete = () => {
  Modal.confirm({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个方案吗？此操作不可恢复。`,
    okType: 'danger',
    onOk() {
      message.success('批量删除成功')
      selectedRowKeys.value = []
    }
  })
}

const handleSearch = () => {
  message.info('搜索功能已触发')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    type: undefined,
    status: undefined,
    department: undefined
  })
  message.info('搜索条件已重置')
}

const previewScheme = () => {
  message.info('预览方案功能')
}

const exportData = () => {
  message.success('数据导出成功')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.incentive-scheme {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.incentive-analysis {
  margin-bottom: 24px;
}

.search-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.rules-config {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.rule-item {
  margin-bottom: 8px;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.scheme-detail {
  padding: 8px 0;
}

.calculation-result {
  padding: 8px 0;
}
</style>