<template>
  <div class="customer-portrait">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="portrait-info">
          <h2>客户画像</h2>
          <p class="portrait-desc">基于AI技术构建360度客户画像，提供精准营销决策支持</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshPortrait">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportPortrait">
            <template #icon><DownloadOutlined /></template>
            导出画像
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            生成画像
          </a-button>
        </div>
      </div>
    </div>

    <!-- 画像统计 -->
    <div class="portrait-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="画像总数" 
              :value="portraitStats.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><UserOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="标签维度" 
              :value="portraitStats.dimensions" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><TagsOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日更新" 
              :value="portraitStats.todayUpdate" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><SyncOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="预测准确率" 
              :value="portraitStats.accuracy" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><AimOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 客户搜索与选择 -->
        <a-card title="客户画像查看" class="portrait-card">
          <template #extra>
            <a-space>
              <a-select 
                v-model:value="selectedCustomer" 
                style="width: 200px"
                placeholder="选择客户"
                show-search
                @change="loadCustomerPortrait"
              >
                <a-select-option v-for="customer in customerList" :key="customer.id" :value="customer.id">
                  {{ customer.name }} - {{ customer.phone }}
                </a-select-option>
              </a-select>
              <a-input-search 
                v-model:value="searchKeyword" 
                placeholder="搜索客户" 
                style="width: 200px"
                @search="searchCustomers"
              />
            </a-space>
          </template>
          
          <div v-if="currentPortrait" class="portrait-content">
            <!-- 客户基本信息 -->
            <div class="portrait-header">
              <div class="customer-avatar">
                <a-avatar :size="80" :src="currentPortrait.avatar">
                  {{ currentPortrait.name.charAt(0) }}
                </a-avatar>
              </div>
              <div class="customer-info">
                <h3>{{ currentPortrait.name }}</h3>
                <div class="customer-meta">
                  <a-tag color="blue">{{ currentPortrait.segment }}</a-tag>
                  <a-tag :color="getRiskColor(currentPortrait.riskLevel)">
                    {{ getRiskText(currentPortrait.riskLevel) }}
                  </a-tag>
                  <span class="last-update">最后更新：{{ currentPortrait.lastUpdate }}</span>
                </div>
              </div>
              <div class="customer-score">
                <div class="score-item">
                  <div class="score-label">信用评分</div>
                  <div class="score-value">{{ currentPortrait.creditScore }}</div>
                </div>
                <div class="score-item">
                  <div class="score-label">还款意愿</div>
                  <div class="score-value">{{ currentPortrait.willingness }}%</div>
                </div>
              </div>
            </div>

            <!-- 画像维度雷达图 -->
            <a-card title="画像雷达图" class="chart-card">
              <div ref="radarChart" class="chart-container"></div>
            </a-card>

            <!-- 标签体系 -->
            <a-card title="标签体系" class="chart-card">
              <div class="tag-categories">
                <div v-for="category in currentPortrait.tagCategories" :key="category.name" class="tag-category">
                  <div class="category-header">
                    <h4>{{ category.name }}</h4>
                    <span class="tag-count">{{ category.tags.length }}个标签</span>
                  </div>
                  <div class="category-tags">
                    <a-tag 
                      v-for="tag in category.tags" 
                      :key="tag.name"
                      :color="tag.color"
                      class="portrait-tag"
                    >
                      {{ tag.name }}
                      <span class="tag-confidence">({{ tag.confidence }}%)</span>
                    </a-tag>
                  </div>
                </div>
              </div>
            </a-card>

            <!-- 行为特征分析 -->
            <a-card title="行为特征分析" class="chart-card">
              <div ref="behaviorChart" class="chart-container"></div>
            </a-card>
          </div>
          
          <a-empty v-else description="请选择客户查看画像" />
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 画像生成配置 -->
        <a-card title="画像生成" class="portrait-card">
          <div class="generation-config">
            <a-form layout="vertical">
              <a-form-item label="数据源">
                <a-checkbox-group v-model:value="generationConfig.dataSources">
                  <a-checkbox value="basic">基础信息</a-checkbox>
                  <a-checkbox value="transaction">交易记录</a-checkbox>
                  <a-checkbox value="behavior">行为数据</a-checkbox>
                  <a-checkbox value="credit">征信数据</a-checkbox>
                  <a-checkbox value="social">社交数据</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="分析维度">
                <a-select 
                  v-model:value="generationConfig.dimensions" 
                  mode="multiple"
                  placeholder="选择分析维度"
                >
                  <a-select-option value="demographic">人口统计</a-select-option>
                  <a-select-option value="financial">财务状况</a-select-option>
                  <a-select-option value="behavioral">行为特征</a-select-option>
                  <a-select-option value="preference">偏好分析</a-select-option>
                  <a-select-option value="risk">风险评估</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="模型版本">
                <a-select v-model:value="generationConfig.modelVersion">
                  <a-select-option value="v1.0">基础模型 v1.0</a-select-option>
                  <a-select-option value="v2.0">增强模型 v2.0</a-select-option>
                  <a-select-option value="v3.0">智能模型 v3.0</a-select-option>
                </a-select>
              </a-form-item>
              <a-button type="primary" block @click="generatePortrait">
                <template #icon><RobotOutlined /></template>
                生成画像
              </a-button>
            </a-form>
          </div>
        </a-card>

        <!-- 标签管理 -->
        <a-card title="标签管理" class="portrait-card">
          <template #extra>
            <a @click="showTagModal = true">添加标签</a>
          </template>
          <div class="tag-management">
            <div class="tag-search">
              <a-input-search 
                v-model:value="tagSearchKeyword" 
                placeholder="搜索标签"
                @search="searchTags"
              />
            </div>
            <div class="tag-list">
              <div v-for="tag in filteredTags" :key="tag.id" class="tag-item">
                <div class="tag-info">
                  <span class="tag-name">{{ tag.name }}</span>
                  <span class="tag-usage">{{ tag.usage }}次使用</span>
                </div>
                <div class="tag-actions">
                  <a-button type="link" size="small" @click="editTag(tag)">
                    编辑
                  </a-button>
                  <a-button type="link" size="small" danger @click="deleteTag(tag)">
                    删除
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 近期生成记录 -->
        <a-card title="生成记录" class="portrait-card">
          <div class="generation-history">
            <div v-for="record in generationHistory" :key="record.id" class="history-item">
              <div class="history-info">
                <div class="history-customer">{{ record.customerName }}</div>
                <div class="history-time">{{ record.time }}</div>
              </div>
              <div class="history-status">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 生成画像模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="批量生成客户画像"
      width="600px"
      @ok="batchGeneratePortrait"
    >
      <a-form layout="vertical">
        <a-form-item label="客户范围">
          <a-radio-group v-model:value="batchConfig.range">
            <a-radio value="all">全部客户</a-radio>
            <a-radio value="segment">指定客群</a-radio>
            <a-radio value="custom">自定义筛选</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="batchConfig.range === 'segment'" label="客户群体">
          <a-select v-model:value="batchConfig.segment" placeholder="选择客户群体">
            <a-select-option value="high-value">高价值客户</a-select-option>
            <a-select-option value="new-customer">新客户</a-select-option>
            <a-select-option value="risk-customer">风险客户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="生成模式">
          <a-radio-group v-model:value="batchConfig.mode">
            <a-radio value="incremental">增量更新</a-radio>
            <a-radio value="full">全量重建</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="执行时间">
          <a-radio-group v-model:value="batchConfig.timing">
            <a-radio value="now">立即执行</a-radio>
            <a-radio value="schedule">定时执行</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 标签编辑模态框 -->
    <a-modal
      v-model:open="showTagModal"
      title="标签管理"
      @ok="saveTag"
    >
      <a-form :model="tagForm" layout="vertical">
        <a-form-item label="标签名称" required>
          <a-input v-model:value="tagForm.name" placeholder="请输入标签名称" />
        </a-form-item>
        <a-form-item label="标签分类">
          <a-select v-model:value="tagForm.category" placeholder="选择分类">
            <a-select-option value="demographic">人口统计</a-select-option>
            <a-select-option value="financial">财务状况</a-select-option>
            <a-select-option value="behavioral">行为特征</a-select-option>
            <a-select-option value="preference">偏好分析</a-select-option>
            <a-select-option value="risk">风险评估</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标签描述">
          <a-textarea v-model:value="tagForm.description" placeholder="标签描述" :rows="3" />
        </a-form-item>
        <a-form-item label="计算规则">
          <a-textarea v-model:value="tagForm.rule" placeholder="标签计算规则" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  PlusOutlined,
  UserOutlined,
  TagsOutlined,
  SyncOutlined,
  AimOutlined,
  RobotOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showCreateModal = ref(false)
const showTagModal = ref(false)
const selectedCustomer = ref(null)
const searchKeyword = ref('')
const tagSearchKeyword = ref('')

// 图表引用
const radarChart = ref(null)
const behaviorChart = ref(null)

// 画像统计
const portraitStats = reactive({
  total: 8624,
  dimensions: 45,
  todayUpdate: 156,
  accuracy: 89.5
})

// 客户列表
const customerList = ref([
  { id: 1, name: '张三', phone: '138****1234' },
  { id: 2, name: '李四', phone: '139****5678' },
  { id: 3, name: '王五', phone: '137****9012' },
  { id: 4, name: '赵六', phone: '136****3456' }
])

// 当前客户画像
const currentPortrait = ref(null)

// 生成配置
const generationConfig = reactive({
  dataSources: ['basic', 'transaction', 'behavior'],
  dimensions: ['demographic', 'financial', 'behavioral'],
  modelVersion: 'v2.0'
})

// 批量配置
const batchConfig = reactive({
  range: 'segment',
  segment: 'high-value',
  mode: 'incremental',
  timing: 'now'
})

// 标签表单
const tagForm = reactive({
  name: '',
  category: '',
  description: '',
  rule: ''
})

// 标签列表
const tagList = ref([
  { id: 1, name: '高收入', category: 'financial', usage: 245 },
  { id: 2, name: '年轻群体', category: 'demographic', usage: 189 },
  { id: 3, name: '活跃用户', category: 'behavioral', usage: 156 },
  { id: 4, name: '风险客户', category: 'risk', usage: 98 },
  { id: 5, name: '价格敏感', category: 'preference', usage: 87 }
])

// 生成历史
const generationHistory = ref([
  { id: 1, customerName: '张三', time: '10分钟前', status: 'completed' },
  { id: 2, customerName: '李四', time: '30分钟前', status: 'processing' },
  { id: 3, customerName: '王五', time: '1小时前', status: 'completed' },
  { id: 4, customerName: '赵六', time: '2小时前', status: 'failed' }
])

// 计算属性
const filteredTags = computed(() => {
  if (!tagSearchKeyword.value) return tagList.value
  return tagList.value.filter(tag => 
    tag.name.includes(tagSearchKeyword.value)
  )
})

// 方法定义
const getRiskColor = (level) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colors[level] || 'default'
}

const getRiskText = (level) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[level] || level
}

const getStatusColor = (status) => {
  const colors = {
    completed: 'green',
    processing: 'blue',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    failed: '失败'
  }
  return texts[status] || status
}

const refreshPortrait = () => {
  console.log('刷新画像数据')
}

const exportPortrait = () => {
  console.log('导出画像数据')
}

const loadCustomerPortrait = (customerId) => {
  console.log('加载客户画像:', customerId)
  
  // 模拟加载客户画像数据
  currentPortrait.value = {
    id: customerId,
    name: '张三',
    avatar: null,
    segment: '高价值客户',
    riskLevel: 'low',
    creditScore: 756,
    willingness: 85,
    lastUpdate: '2024-01-15 14:30',
    tagCategories: [
      {
        name: '人口统计',
        tags: [
          { name: '30-40岁', color: 'blue', confidence: 95 },
          { name: '本科学历', color: 'green', confidence: 88 },
          { name: '已婚', color: 'purple', confidence: 92 }
        ]
      },
      {
        name: '财务状况',
        tags: [
          { name: '高收入', color: 'gold', confidence: 89 },
          { name: '有房产', color: 'cyan', confidence: 94 },
          { name: '稳定工作', color: 'lime', confidence: 91 }
        ]
      },
      {
        name: '行为特征',
        tags: [
          { name: '按时还款', color: 'green', confidence: 96 },
          { name: '主动沟通', color: 'blue', confidence: 87 },
          { name: '理性消费', color: 'orange', confidence: 83 }
        ]
      }
    ]
  }
  
  nextTick(() => {
    initCharts()
  })
}

const searchCustomers = () => {
  console.log('搜索客户:', searchKeyword.value)
}

const generatePortrait = () => {
  console.log('生成画像:', generationConfig)
}

const batchGeneratePortrait = () => {
  console.log('批量生成画像:', batchConfig)
  showCreateModal.value = false
}

const searchTags = () => {
  console.log('搜索标签:', tagSearchKeyword.value)
}

const editTag = (tag) => {
  Object.assign(tagForm, tag)
  showTagModal.value = true
}

const deleteTag = (tag) => {
  const index = tagList.value.findIndex(t => t.id === tag.id)
  if (index > -1) {
    tagList.value.splice(index, 1)
  }
}

const saveTag = () => {
  console.log('保存标签:', tagForm)
  showTagModal.value = false
}

// 初始化图表
const initCharts = () => {
  // 雷达图
  if (radarChart.value) {
    const chart1 = echarts.init(radarChart.value)
    chart1.setOption({
      tooltip: {},
      radar: {
        indicator: [
          { name: '信用评分', max: 1000 },
          { name: '还款能力', max: 100 },
          { name: '稳定性', max: 100 },
          { name: '活跃度', max: 100 },
          { name: '合规性', max: 100 },
          { name: '忠诚度', max: 100 }
        ],
        radius: 80
      },
      series: [{
        name: '客户画像',
        type: 'radar',
        data: [{
          value: [756, 85, 92, 78, 96, 88],
          name: '张三'
        }],
        itemStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          opacity: 0.3
        }
      }]
    })
  }

  // 行为特征图
  if (behaviorChart.value) {
    const chart2 = echarts.init(behaviorChart.value)
    chart2.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['通话频次', '还款频次', '查询频次']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '通话频次',
          type: 'line',
          data: [5, 8, 6, 12, 9, 7],
          smooth: true
        },
        {
          name: '还款频次',
          type: 'line',
          data: [2, 2, 3, 1, 2, 2],
          smooth: true
        },
        {
          name: '查询频次',
          type: 'line',
          data: [12, 15, 18, 20, 16, 14],
          smooth: true
        }
      ]
    })
  }
}

// 生命周期
onMounted(() => {
  refreshPortrait()
})
</script>

<style scoped>
.customer-portrait {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.portrait-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.portrait-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.portrait-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.portrait-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.portrait-content {
  min-height: 400px;
}

.portrait-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.customer-info {
  flex: 1;
}

.customer-info h3 {
  margin: 0 0 8px 0;
  color: #262626;
}

.customer-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.last-update {
  font-size: 12px;
  color: #999;
}

.customer-score {
  display: flex;
  gap: 24px;
}

.score-item {
  text-align: center;
}

.score-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.score-value {
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.chart-card {
  margin-bottom: 16px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.tag-categories {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tag-category {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.category-header h4 {
  margin: 0;
  color: #262626;
}

.tag-count {
  font-size: 12px;
  color: #999;
}

.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.portrait-tag {
  margin: 0;
}

.tag-confidence {
  opacity: 0.8;
  font-size: 11px;
}

.generation-config {
  padding: 8px 0;
}

.tag-management {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-search {
  margin-bottom: 12px;
}

.tag-list {
  max-height: 300px;
  overflow-y: auto;
}

.tag-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.tag-item:last-child {
  border-bottom: none;
}

.tag-info {
  flex: 1;
}

.tag-name {
  font-weight: 500;
  color: #262626;
}

.tag-usage {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.tag-actions {
  display: flex;
  gap: 4px;
}

.generation-history {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-customer {
  font-weight: 500;
  color: #262626;
}

.history-time {
  font-size: 12px;
  color: #999;
}
</style>