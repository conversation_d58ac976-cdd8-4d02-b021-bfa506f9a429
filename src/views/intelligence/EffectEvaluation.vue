<template>
  <div class="effect-evaluation">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="evaluation-info">
          <h2>效果评估</h2>
          <p class="evaluation-desc">全面评估策略执行效果，提供科学的决策依据</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshEvaluation">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportReport">
            <template #icon><DownloadOutlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="showEvaluationModal = true">
            <template #icon><BarChartOutlined /></template>
            新建评估
          </a-button>
        </div>
      </div>
    </div>

    <!-- 评估统计 -->
    <div class="evaluation-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="评估项目" 
              :value="evaluationStats.totalProjects" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ProjectOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="平均提升率" 
              :value="evaluationStats.averageImprovement" 
              :value-style="{ color: '#52c41a' }"
              suffix="%"
            >
              <template #prefix><RiseOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="ROI" 
              :value="evaluationStats.roi" 
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><DollarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="满意度" 
              :value="evaluationStats.satisfaction" 
              :value-style="{ color: '#722ed1' }"
              suffix="/5"
            >
              <template #prefix><SmileOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 效果对比分析 -->
        <a-card title="效果对比分析" class="evaluation-card">
          <template #extra>
            <a-space>
              <a-select 
                v-model:value="selectedMetric" 
                style="width: 120px"
                @change="updateComparisonChart"
              >
                <a-select-option value="success-rate">成功率</a-select-option>
                <a-select-option value="efficiency">效率</a-select-option>
                <a-select-option value="cost">成本</a-select-option>
                <a-select-option value="satisfaction">满意度</a-select-option>
              </a-select>
              <a-select 
                v-model:value="selectedTimeRange" 
                style="width: 120px"
                @change="updateComparisonChart"
              >
                <a-select-option value="7d">近7天</a-select-option>
                <a-select-option value="30d">近30天</a-select-option>
                <a-select-option value="90d">近90天</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <div ref="comparisonChart" class="chart-container"></div>
        </a-card>

        <!-- ROI分析 -->
        <a-card title="ROI投资回报分析" class="evaluation-card">
          <div class="roi-analysis">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="roiChart" class="chart-container-small"></div>
              </a-col>
              <a-col :span="12">
                <div class="roi-summary">
                  <div class="roi-item">
                    <div class="roi-label">总投入成本</div>
                    <div class="roi-value cost">¥{{ roiData.totalCost.toLocaleString() }}</div>
                  </div>
                  <div class="roi-item">
                    <div class="roi-label">总收益</div>
                    <div class="roi-value revenue">¥{{ roiData.totalRevenue.toLocaleString() }}</div>
                  </div>
                  <div class="roi-item">
                    <div class="roi-label">净收益</div>
                    <div class="roi-value profit">¥{{ roiData.netProfit.toLocaleString() }}</div>
                  </div>
                  <div class="roi-item">
                    <div class="roi-label">投资回报率</div>
                    <div class="roi-value roi">{{ roiData.roiPercentage }}%</div>
                  </div>
                  <div class="roi-breakdown">
                    <h4>成本构成</h4>
                    <div v-for="item in roiData.costBreakdown" :key="item.type" class="breakdown-item">
                      <span class="breakdown-label">{{ item.name }}：</span>
                      <span class="breakdown-value">¥{{ item.amount.toLocaleString() }}</span>
                      <span class="breakdown-percentage">({{ item.percentage }}%)</span>
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 评估报告列表 -->
        <a-card title="评估报告" class="evaluation-card">
          <template #extra>
            <a-input-search 
              v-model:value="reportSearchKeyword" 
              placeholder="搜索报告" 
              style="width: 200px"
              @search="searchReports"
            />
          </template>
          
          <a-table 
            :columns="reportColumns" 
            :data-source="filteredReports"
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="report-name-cell">
                  <div class="report-icon">
                    <FileTextOutlined :style="{ color: getReportColor(record.type) }" />
                  </div>
                  <div class="report-details">
                    <div class="report-name">{{ record.name }}</div>
                    <div class="report-description">{{ record.description }}</div>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'improvement'">
                <div class="improvement-cell">
                  <a-progress 
                    :percent="Math.abs(record.improvement)" 
                    size="small"
                    :stroke-color="record.improvement > 0 ? '#52c41a' : '#ff4d4f'"
                  />
                  <span class="improvement-text" :style="{ color: record.improvement > 0 ? '#52c41a' : '#ff4d4f' }">
                    {{ record.improvement > 0 ? '+' : '' }}{{ record.improvement }}%
                  </span>
                </div>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="viewReport(record)">
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="downloadReport(record)">
                    下载
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="shareReport(record)">分享</a-menu-item>
                        <a-menu-item @click="exportReport(record)">导出</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteReport(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时监控 -->
        <a-card title="实时监控" class="evaluation-card">
          <div class="real-time-monitor">
            <div class="monitor-metrics">
              <div v-for="metric in realtimeMetrics" :key="metric.name" class="metric-item">
                <div class="metric-header">
                  <span class="metric-name">{{ metric.name }}</span>
                  <span class="metric-trend" :class="getTrendClass(metric.trend)">
                    <component :is="getTrendIcon(metric.trend)" />
                    {{ metric.change }}%
                  </span>
                </div>
                <div class="metric-value">{{ metric.value }}{{ metric.unit }}</div>
                <a-progress 
                  :percent="metric.progress" 
                  size="small"
                  :stroke-color="getMetricColor(metric.name)"
                />
              </div>
            </div>
          </div>
        </a-card>

        <!-- 评估配置 -->
        <a-card title="评估配置" class="evaluation-card">
          <div class="evaluation-config">
            <a-form layout="vertical" size="small">
              <a-form-item label="评估维度">
                <a-checkbox-group v-model:value="evaluationConfig.dimensions">
                  <a-checkbox value="effectiveness">有效性</a-checkbox>
                  <a-checkbox value="efficiency">效率</a-checkbox>
                  <a-checkbox value="cost">成本</a-checkbox>
                  <a-checkbox value="satisfaction">满意度</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="对比基准">
                <a-select v-model:value="evaluationConfig.baseline">
                  <a-select-option value="previous">上期数据</a-select-option>
                  <a-select-option value="target">目标值</a-select-option>
                  <a-select-option value="industry">行业平均</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="评估周期">
                <a-select v-model:value="evaluationConfig.period">
                  <a-select-option value="daily">每日</a-select-option>
                  <a-select-option value="weekly">每周</a-select-option>
                  <a-select-option value="monthly">每月</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="权重设置">
                <div class="weight-settings">
                  <div v-for="weight in evaluationConfig.weights" :key="weight.name" class="weight-item">
                    <span class="weight-label">{{ weight.name }}：</span>
                    <a-slider 
                      v-model:value="weight.value" 
                      :min="0" 
                      :max="100"
                      style="flex: 1; margin: 0 8px;"
                    />
                    <span class="weight-value">{{ weight.value }}%</span>
                  </div>
                </div>
              </a-form-item>
              <a-button type="primary" block @click="applyConfig">
                应用配置
              </a-button>
            </a-form>
          </div>
        </a-card>

        <!-- 预警提醒 -->
        <a-card title="预警提醒" class="evaluation-card">
          <template #extra>
            <a-badge :count="alerts.filter(a => !a.handled).length" />
          </template>
          <div class="alert-list">
            <div v-for="alert in alerts" :key="alert.id" class="alert-item">
              <div class="alert-header">
                <div class="alert-level" :class="getAlertClass(alert.level)">
                  {{ getAlertLevelText(alert.level) }}
                </div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
              <div class="alert-content">{{ alert.message }}</div>
              <div class="alert-actions">
                <a-button 
                  v-if="!alert.handled" 
                  type="link" 
                  size="small" 
                  @click="handleAlert(alert)"
                >
                  处理
                </a-button>
                <a-button type="link" size="small" @click="viewAlertDetail(alert)">
                  详情
                </a-button>
              </div>
            </div>
            <a-empty v-if="alerts.length === 0" description="暂无预警" size="small" />
          </div>
        </a-card>

        <!-- 优化建议 -->
        <a-card title="优化建议" class="evaluation-card">
          <div class="optimization-suggestions">
            <div v-for="suggestion in optimizationSuggestions" :key="suggestion.id" class="suggestion-item">
              <div class="suggestion-header">
                <div class="suggestion-priority">
                  <a-tag :color="getPriorityColor(suggestion.priority)">
                    {{ getPriorityText(suggestion.priority) }}
                  </a-tag>
                </div>
                <div class="suggestion-impact">
                  预期提升: {{ suggestion.expectedImprovement }}%
                </div>
              </div>
              <div class="suggestion-content">{{ suggestion.content }}</div>
              <div class="suggestion-actions">
                <a-button type="link" size="small" @click="applySuggestion(suggestion)">
                  采纳
                </a-button>
                <a-button type="link" size="small" @click="ignoreSuggestion(suggestion)">
                  忽略
                </a-button>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 新建评估模态框 -->
    <a-modal
      v-model:open="showEvaluationModal"
      title="新建效果评估"
      width="600px"
      @ok="createEvaluation"
    >
      <a-form layout="vertical">
        <a-form-item label="评估名称" required>
          <a-input v-model:value="evaluationForm.name" placeholder="请输入评估名称" />
        </a-form-item>
        <a-form-item label="评估对象">
          <a-select v-model:value="evaluationForm.target" placeholder="选择评估对象">
            <a-select-option value="strategy">策略效果</a-select-option>
            <a-select-option value="campaign">活动效果</a-select-option>
            <a-select-option value="system">系统效果</a-select-option>
            <a-select-option value="team">团队效果</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="评估周期">
          <a-range-picker v-model:value="evaluationForm.period" />
        </a-form-item>
        <a-form-item label="评估指标">
          <a-checkbox-group v-model:value="evaluationForm.metrics">
            <a-checkbox value="success-rate">成功率</a-checkbox>
            <a-checkbox value="efficiency">效率</a-checkbox>
            <a-checkbox value="cost">成本效益</a-checkbox>
            <a-checkbox value="satisfaction">客户满意度</a-checkbox>
            <a-checkbox value="roi">投资回报率</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="对比基准">
          <a-radio-group v-model:value="evaluationForm.baseline">
            <a-radio value="previous">上期数据</a-radio>
            <a-radio value="target">目标值</a-radio>
            <a-radio value="industry">行业基准</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="评估描述">
          <a-textarea v-model:value="evaluationForm.description" placeholder="评估描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  BarChartOutlined,
  ProjectOutlined,
  RiseOutlined,
  DollarOutlined,
  SmileOutlined,
  FileTextOutlined,
  DownOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showEvaluationModal = ref(false)
const selectedMetric = ref('success-rate')
const selectedTimeRange = ref('30d')
const reportSearchKeyword = ref('')

// 图表引用
const comparisonChart = ref(null)
const roiChart = ref(null)

// 评估统计
const evaluationStats = reactive({
  totalProjects: 28,
  averageImprovement: 23.5,
  roi: 156.8,
  satisfaction: 4.2
})

// ROI数据
const roiData = reactive({
  totalCost: 850000,
  totalRevenue: 2180000,
  netProfit: 1330000,
  roiPercentage: 156.8,
  costBreakdown: [
    { type: 'human', name: '人力成本', amount: 450000, percentage: 52.9 },
    { type: 'system', name: '系统成本', amount: 280000, percentage: 32.9 },
    { type: 'marketing', name: '营销成本', amount: 120000, percentage: 14.1 }
  ]
})

// 报告列表
const reportList = ref([
  {
    id: 1,
    name: 'Q1催收策略效果评估',
    type: 'strategy',
    description: '第一季度催收策略整体效果分析',
    status: 'completed',
    improvement: 15.8,
    createTime: '2024-01-15',
    author: '张三'
  },
  {
    id: 2,
    name: '智能外呼系统ROI分析',
    type: 'system',
    description: '智能外呼系统投资回报率评估',
    status: 'completed',
    improvement: 28.5,
    createTime: '2024-01-12',
    author: '李四'
  },
  {
    id: 3,
    name: '客户满意度提升效果',
    type: 'satisfaction',
    description: '客户满意度改善措施效果评估',
    status: 'processing',
    improvement: -2.3,
    createTime: '2024-01-10',
    author: '王五'
  },
  {
    id: 4,
    name: '团队绩效优化报告',
    type: 'team',
    description: '催收团队绩效优化措施效果',
    status: 'completed',
    improvement: 19.6,
    createTime: '2024-01-08',
    author: '赵六'
  }
])

// 表格列定义
const reportColumns = [
  {
    title: '报告名称',
    key: 'name',
    width: '40%'
  },
  {
    title: '状态',
    key: 'status',
    width: '15%'
  },
  {
    title: '效果提升',
    key: 'improvement',
    width: '20%'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: '15%'
  },
  {
    title: '操作',
    key: 'actions',
    width: '10%'
  }
]

// 实时监控指标
const realtimeMetrics = ref([
  {
    name: '成功率',
    value: 78.5,
    unit: '%',
    progress: 78,
    trend: 'up',
    change: 5.2
  },
  {
    name: '效率',
    value: 156,
    unit: '件/天',
    progress: 65,
    trend: 'up',
    change: 12.8
  },
  {
    name: '成本',
    value: 245,
    unit: '元/件',
    progress: 45,
    trend: 'down',
    change: -8.5
  },
  {
    name: '满意度',
    value: 4.2,
    unit: '/5',
    progress: 84,
    trend: 'stable',
    change: 0.1
  }
])

// 评估配置
const evaluationConfig = reactive({
  dimensions: ['effectiveness', 'efficiency'],
  baseline: 'previous',
  period: 'monthly',
  weights: [
    { name: '有效性', value: 35 },
    { name: '效率', value: 25 },
    { name: '成本', value: 25 },
    { name: '满意度', value: 15 }
  ]
})

// 预警列表
const alerts = ref([
  {
    id: 1,
    level: 'high',
    time: '10分钟前',
    message: '催收成功率较上周下降8.5%，需要关注',
    handled: false
  },
  {
    id: 2,
    level: 'medium',
    time: '1小时前',
    message: '系统响应时间超过预期值15%',
    handled: false
  },
  {
    id: 3,
    level: 'low',
    time: '2小时前',
    message: '客户投诉率轻微上升，建议优化沟通话术',
    handled: true
  }
])

// 优化建议
const optimizationSuggestions = ref([
  {
    id: 1,
    priority: 'high',
    content: '建议调整催收时段分布，在客户接听率较高的时段增加外呼频次',
    expectedImprovement: 12
  },
  {
    id: 2,
    priority: 'medium',
    content: '优化短信模板内容，提高客户回复率和配合度',
    expectedImprovement: 8
  },
  {
    id: 3,
    priority: 'low',
    content: '完善员工培训体系，提升专业能力和沟通技巧',
    expectedImprovement: 5
  }
])

// 评估表单
const evaluationForm = reactive({
  name: '',
  target: '',
  period: null,
  metrics: [],
  baseline: 'previous',
  description: ''
})

// 计算属性
const filteredReports = computed(() => {
  if (!reportSearchKeyword.value) return reportList.value
  return reportList.value.filter(report => 
    report.name.includes(reportSearchKeyword.value) ||
    report.description.includes(reportSearchKeyword.value)
  )
})

// 方法定义
const getReportColor = (type) => {
  const colors = {
    strategy: '#1890ff',
    system: '#52c41a',
    satisfaction: '#faad14',
    team: '#722ed1'
  }
  return colors[type] || '#8c8c8c'
}

const getStatusColor = (status) => {
  const colors = {
    completed: 'green',
    processing: 'blue',
    pending: 'orange',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    processing: '进行中',
    pending: '待开始',
    failed: '失败'
  }
  return texts[status] || status
}

const getTrendClass = (trend) => {
  return `trend-${trend}`
}

const getTrendIcon = (trend) => {
  const icons = {
    up: 'CaretUpOutlined',
    down: 'CaretDownOutlined',
    stable: 'MinusOutlined'
  }
  return icons[trend] || 'MinusOutlined'
}

const getMetricColor = (name) => {
  const colors = {
    '成功率': '#1890ff',
    '效率': '#52c41a',
    '成本': '#faad14',
    '满意度': '#722ed1'
  }
  return colors[name] || '#8c8c8c'
}

const getAlertClass = (level) => {
  return `alert-${level}`
}

const getAlertLevelText = (level) => {
  const texts = {
    high: '严重',
    medium: '警告',
    low: '提醒'
  }
  return texts[level] || level
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const refreshEvaluation = () => {
  console.log('刷新评估数据')
  initCharts()
}

const exportReport = () => {
  console.log('导出评估报告')
}

const updateComparisonChart = () => {
  console.log('更新对比图表:', selectedMetric.value, selectedTimeRange.value)
  initCharts()
}

const searchReports = () => {
  console.log('搜索报告:', reportSearchKeyword.value)
}

const viewReport = (report) => {
  console.log('查看报告:', report)
}

const downloadReport = (report) => {
  console.log('下载报告:', report)
}

const shareReport = (report) => {
  console.log('分享报告:', report)
}

const deleteReport = (report) => {
  const index = reportList.value.findIndex(r => r.id === report.id)
  if (index > -1) {
    reportList.value.splice(index, 1)
  }
  console.log('删除报告:', report)
}

const applyConfig = () => {
  console.log('应用评估配置:', evaluationConfig)
}

const handleAlert = (alert) => {
  alert.handled = true
  console.log('处理预警:', alert)
}

const viewAlertDetail = (alert) => {
  console.log('查看预警详情:', alert)
}

const applySuggestion = (suggestion) => {
  console.log('采纳建议:', suggestion)
}

const ignoreSuggestion = (suggestion) => {
  const index = optimizationSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    optimizationSuggestions.value.splice(index, 1)
  }
}

const createEvaluation = () => {
  console.log('创建评估:', evaluationForm)
  showEvaluationModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 效果对比分析图
    if (comparisonChart.value) {
      const chart1 = echarts.init(comparisonChart.value)
      chart1.setOption({
        title: { text: '策略效果对比分析', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['优化前', '优化后', '目标值'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['沟通策略', '还款策略', '激励策略', '法务策略', '综合策略']
        },
        yAxis: { type: 'value', min: 0, max: 100 },
        series: [
          {
            name: '优化前',
            type: 'bar',
            data: [65, 58, 72, 45, 62],
            itemStyle: { color: '#ff7875' }
          },
          {
            name: '优化后',
            type: 'bar',
            data: [78, 73, 85, 68, 78],
            itemStyle: { color: '#73d13d' }
          },
          {
            name: '目标值',
            type: 'line',
            data: [80, 75, 80, 70, 80],
            itemStyle: { color: '#40a9ff' }
          }
        ]
      })
    }

    // ROI分析图
    if (roiChart.value) {
      const chart2 = echarts.init(roiChart.value)
      chart2.setOption({
        title: { text: 'ROI趋势分析', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [{
          name: 'ROI',
          type: 'line',
          data: [120, 135, 142, 156, 168, 175],
          smooth: true,
          itemStyle: { color: '#faad14' },
          areaStyle: {
            opacity: 0.3,
            color: '#faad14'
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshEvaluation()
})
</script>

<style scoped>
.effect-evaluation {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.evaluation-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.evaluation-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.evaluation-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.evaluation-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container {
  height: 350px;
  width: 100%;
}

.chart-container-small {
  height: 250px;
  width: 100%;
}

.roi-analysis {
  padding: 8px 0;
}

.roi-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.roi-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.roi-item:last-child {
  border-bottom: none;
}

.roi-label {
  color: #666;
  font-size: 13px;
}

.roi-value {
  font-weight: bold;
  font-size: 16px;
}

.roi-value.cost {
  color: #ff4d4f;
}

.roi-value.revenue {
  color: #52c41a;
}

.roi-value.profit {
  color: #1890ff;
}

.roi-value.roi {
  color: #faad14;
}

.roi-breakdown {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.roi-breakdown h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.breakdown-label {
  color: #666;
}

.breakdown-value {
  color: #262626;
  font-weight: 500;
}

.breakdown-percentage {
  color: #999;
}

.report-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.report-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f6f8ff;
  font-size: 14px;
}

.report-details {
  flex: 1;
}

.report-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.report-description {
  font-size: 12px;
  color: #999;
}

.improvement-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.improvement-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 50px;
}

.real-time-monitor {
  padding: 8px 0;
}

.monitor-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-name {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.metric-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-stable {
  color: #999;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 8px;
}

.evaluation-config {
  padding: 8px 0;

  .ant-btn {
    height: 36px;
    margin-top: 16px;
    font-size: 14px;
  }
}

.weight-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.weight-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weight-label {
  font-size: 12px;
  color: #666;
  min-width: 60px;
}

.weight-value {
  font-size: 12px;
  color: #262626;
  min-width: 35px;
}

.alert-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.alert-level {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.alert-high {
  background: #fff2f0;
  color: #ff4d4f;
}

.alert-medium {
  background: #fff7e6;
  color: #faad14;
}

.alert-low {
  background: #f6ffed;
  color: #52c41a;
}

.alert-time {
  font-size: 11px;
  color: #999;
}

.alert-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.optimization-suggestions {
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-impact {
  font-size: 11px;
  color: #52c41a;
  font-weight: 500;
}

.suggestion-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}
</style>