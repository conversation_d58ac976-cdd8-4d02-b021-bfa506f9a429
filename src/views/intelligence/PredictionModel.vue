<template>
  <div class="prediction-model">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="model-info">
          <h2>预测模型</h2>
          <p class="model-desc">基于机器学习构建智能预测模型，提供精准的业务预测</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshModels">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportModels">
            <template #icon><DownloadOutlined /></template>
            导出模型
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建模型
          </a-button>
        </div>
      </div>
    </div>

    <!-- 模型统计 -->
    <div class="model-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="模型总数" 
              :value="modelStats.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><ExperimentOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="运行中模型" 
              :value="modelStats.running" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="预测准确率" 
              :value="modelStats.accuracy" 
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日预测" 
              :value="modelStats.todayPredictions" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><LineChartOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 模型列表 -->
        <a-card title="预测模型列表" class="model-card">
          <template #extra>
            <a-space>
              <a-select 
                v-model:value="selectedModelType" 
                style="width: 120px"
                @change="filterModels"
              >
                <a-select-option value="all">全部类型</a-select-option>
                <a-select-option value="risk">风险预测</a-select-option>
                <a-select-option value="payment">还款预测</a-select-option>
                <a-select-option value="behavior">行为预测</a-select-option>
              </a-select>
              <a-input-search 
                v-model:value="searchKeyword" 
                placeholder="搜索模型" 
                style="width: 200px"
                @search="searchModels"
              />
            </a-space>
          </template>
          
          <a-table 
            :columns="modelColumns" 
            :data-source="filteredModelList"
            :pagination="{ pageSize: 10 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="model-name-cell">
                  <div class="model-icon">
                    <component :is="getModelIcon(record.type)" :style="{ color: getModelColor(record.type) }" />
                  </div>
                  <div class="model-details">
                    <div class="model-name">{{ record.name }}</div>
                    <div class="model-description">{{ record.description }}</div>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'accuracy'">
                <div class="accuracy-cell">
                  <a-progress 
                    :percent="record.accuracy" 
                    size="small"
                    :stroke-color="getAccuracyColor(record.accuracy)"
                  />
                  <span class="accuracy-text">{{ record.accuracy }}%</span>
                </div>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="viewModel(record)">
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="trainModel(record)">
                    训练
                  </a-button>
                  <a-button type="link" size="small" @click="predictWithModel(record)">
                    预测
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="editModel(record)">编辑</a-menu-item>
                        <a-menu-item @click="cloneModel(record)">克隆</a-menu-item>
                        <a-menu-item @click="exportModel(record)">导出</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteModel(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 模型性能分析 -->
        <a-card title="模型性能分析" class="model-card">
          <template #extra>
            <a-select v-model:value="selectedModel" style="width: 200px" @change="updatePerformanceChart">
              <a-select-option v-for="model in modelList" :key="model.id" :value="model.id">
                {{ model.name }}
              </a-select-option>
            </a-select>
          </template>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <div ref="performanceChart" class="chart-container-small"></div>
            </a-col>
            <a-col :span="12">
              <div ref="confusionMatrix" class="chart-container-small"></div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 预测结果 -->
        <a-card title="预测结果" class="model-card">
          <div class="prediction-results">
            <a-table 
              :columns="predictionColumns" 
              :data-source="predictionResults"
              :pagination="{ pageSize: 8 }"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'result'">
                  <a-tag :color="getPredictionColor(record.result)">
                    {{ record.result }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'confidence'">
                  <a-progress 
                    :percent="record.confidence" 
                    size="small"
                    :stroke-color="getConfidenceColor(record.confidence)"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 模型训练 -->
        <a-card title="模型训练" class="model-card">
          <div class="training-section">
            <a-form layout="vertical" size="small">
              <a-form-item label="选择模型">
                <a-select v-model:value="trainingConfig.modelId" placeholder="选择要训练的模型">
                  <a-select-option v-for="model in modelList" :key="model.id" :value="model.id">
                    {{ model.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="训练数据">
                <a-select v-model:value="trainingConfig.dataSource">
                  <a-select-option value="recent">最近数据</a-select-option>
                  <a-select-option value="full">全量数据</a-select-option>
                  <a-select-option value="custom">自定义</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="训练参数">
                <a-input-group compact>
                  <a-input 
                    v-model:value="trainingConfig.epochs" 
                    placeholder="训练轮数"
                    style="width: 50%"
                  />
                  <a-input 
                    v-model:value="trainingConfig.batchSize" 
                    placeholder="批次大小"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
              <a-button type="primary" block @click="startTraining">
                <template #icon><PlayCircleOutlined /></template>
                开始训练
              </a-button>
            </a-form>
          </div>
        </a-card>

        <!-- 训练进度 -->
        <a-card title="训练进度" class="model-card">
          <div class="training-progress">
            <div v-if="currentTraining" class="training-item">
              <div class="training-header">
                <span class="training-model">{{ currentTraining.modelName }}</span>
                <a-tag color="blue">训练中</a-tag>
              </div>
              <div class="training-metrics">
                <div class="metric-row">
                  <span>进度：</span>
                  <a-progress :percent="currentTraining.progress" size="small" />
                </div>
                <div class="metric-row">
                  <span>当前轮次：</span>
                  <span>{{ currentTraining.currentEpoch }}/{{ currentTraining.totalEpochs }}</span>
                </div>
                <div class="metric-row">
                  <span>准确率：</span>
                  <span>{{ currentTraining.accuracy }}%</span>
                </div>
                <div class="metric-row">
                  <span>损失：</span>
                  <span>{{ currentTraining.loss }}</span>
                </div>
              </div>
            </div>
            <a-empty v-else description="暂无训练任务" size="small" />
          </div>
        </a-card>

        <!-- 模型评估 -->
        <a-card title="模型评估" class="model-card">
          <div class="model-evaluation">
            <div v-for="evaluation in modelEvaluations" :key="evaluation.id" class="evaluation-item">
              <div class="evaluation-header">
                <span class="evaluation-model">{{ evaluation.modelName }}</span>
                <span class="evaluation-score">{{ evaluation.score }}</span>
              </div>
              <div class="evaluation-metrics">
                <div class="metric-item">
                  <span class="metric-label">精确率：</span>
                  <span class="metric-value">{{ evaluation.precision }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">召回率：</span>
                  <span class="metric-value">{{ evaluation.recall }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">F1分数：</span>
                  <span class="metric-value">{{ evaluation.f1Score }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 预测历史 -->
        <a-card title="预测历史" class="model-card">
          <div class="prediction-history">
            <div v-for="history in predictionHistory" :key="history.id" class="history-item">
              <div class="history-info">
                <div class="history-model">{{ history.modelName }}</div>
                <div class="history-time">{{ history.time }}</div>
              </div>
              <div class="history-result">
                <a-tag :color="getPredictionColor(history.result)" size="small">
                  {{ history.result }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 创建模型模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建预测模型"
      width="600px"
      @ok="createModel"
    >
      <a-form :model="modelForm" layout="vertical">
        <a-form-item label="模型名称" required>
          <a-input v-model:value="modelForm.name" placeholder="请输入模型名称" />
        </a-form-item>
        <a-form-item label="模型类型" required>
          <a-select v-model:value="modelForm.type" placeholder="选择模型类型">
            <a-select-option value="risk">风险预测</a-select-option>
            <a-select-option value="payment">还款预测</a-select-option>
            <a-select-option value="behavior">行为预测</a-select-option>
            <a-select-option value="churn">流失预测</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="算法选择">
          <a-select v-model:value="modelForm.algorithm" placeholder="选择算法">
            <a-select-option value="logistic">逻辑回归</a-select-option>
            <a-select-option value="rf">随机森林</a-select-option>
            <a-select-option value="gbdt">梯度提升树</a-select-option>
            <a-select-option value="xgboost">XGBoost</a-select-option>
            <a-select-option value="lstm">LSTM神经网络</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="特征选择">
          <a-checkbox-group v-model:value="modelForm.features">
            <a-checkbox value="demographic">人口统计</a-checkbox>
            <a-checkbox value="financial">财务状况</a-checkbox>
            <a-checkbox value="behavioral">行为特征</a-checkbox>
            <a-checkbox value="historical">历史记录</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="模型描述">
          <a-textarea v-model:value="modelForm.description" placeholder="模型描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  PlusOutlined,
  ExperimentOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  LineChartOutlined,
  DownOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showCreateModal = ref(false)
const selectedModelType = ref('all')
const selectedModel = ref(1)
const searchKeyword = ref('')

// 图表引用
const performanceChart = ref(null)
const confusionMatrix = ref(null)

// 模型统计
const modelStats = reactive({
  total: 12,
  running: 8,
  accuracy: 89.5,
  todayPredictions: 1245
})

// 模型列表
const modelList = ref([
  {
    id: 1,
    name: '客户风险评估模型',
    type: 'risk',
    description: '基于历史数据预测客户违约风险',
    status: 'running',
    accuracy: 89.5,
    algorithm: 'XGBoost',
    createTime: '2024-01-15',
    lastTrained: '2024-01-20'
  },
  {
    id: 2,
    name: '还款预测模型',
    type: 'payment',
    description: '预测客户下期还款概率',
    status: 'training',
    accuracy: 87.2,
    algorithm: 'Random Forest',
    createTime: '2024-01-10',
    lastTrained: '2024-01-18'
  },
  {
    id: 3,
    name: '行为模式识别模型',
    type: 'behavior',
    description: '识别客户异常行为模式',
    status: 'stopped',
    accuracy: 92.1,
    algorithm: 'LSTM',
    createTime: '2024-01-05',
    lastTrained: '2024-01-15'
  },
  {
    id: 4,
    name: '流失预测模型',
    type: 'churn',
    description: '预测客户流失概率',
    status: 'running',
    accuracy: 85.8,
    algorithm: 'Logistic Regression',
    createTime: '2024-01-12',
    lastTrained: '2024-01-19'
  }
])

// 表格列定义
const modelColumns = [
  {
    title: '模型名称',
    key: 'name',
    width: '40%'
  },
  {
    title: '状态',
    key: 'status',
    width: '15%'
  },
  {
    title: '准确率',
    key: 'accuracy',
    width: '20%'
  },
  {
    title: '最后训练',
    dataIndex: 'lastTrained',
    key: 'lastTrained',
    width: '15%'
  },
  {
    title: '操作',
    key: 'actions',
    width: '10%'
  }
]

// 预测结果列定义
const predictionColumns = [
  {
    title: '客户',
    dataIndex: 'customerName',
    key: 'customerName'
  },
  {
    title: '预测结果',
    key: 'result'
  },
  {
    title: '置信度',
    key: 'confidence'
  },
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time'
  }
]

// 预测结果
const predictionResults = ref([
  {
    id: 1,
    customerName: '张三',
    result: '高风险',
    confidence: 89,
    time: '10分钟前'
  },
  {
    id: 2,
    customerName: '李四',
    result: '低风险',
    confidence: 95,
    time: '15分钟前'
  },
  {
    id: 3,
    customerName: '王五',
    result: '中风险',
    confidence: 76,
    time: '20分钟前'
  },
  {
    id: 4,
    customerName: '赵六',
    result: '低风险',
    confidence: 91,
    time: '25分钟前'
  }
])

// 训练配置
const trainingConfig = reactive({
  modelId: null,
  dataSource: 'recent',
  epochs: '100',
  batchSize: '32'
})

// 当前训练
const currentTraining = ref({
  modelName: '客户风险评估模型',
  progress: 65,
  currentEpoch: 65,
  totalEpochs: 100,
  accuracy: 87.3,
  loss: 0.234
})

// 模型评估
const modelEvaluations = ref([
  {
    id: 1,
    modelName: '风险评估模型',
    score: 'A',
    precision: 89.5,
    recall: 87.2,
    f1Score: 0.883
  },
  {
    id: 2,
    modelName: '还款预测模型',
    score: 'B+',
    precision: 85.1,
    recall: 82.8,
    f1Score: 0.839
  },
  {
    id: 3,
    modelName: '行为识别模型',
    score: 'A+',
    precision: 92.1,
    recall: 89.7,
    f1Score: 0.909
  }
])

// 预测历史
const predictionHistory = ref([
  {
    id: 1,
    modelName: '风险评估',
    result: '高风险',
    time: '10分钟前'
  },
  {
    id: 2,
    modelName: '还款预测',
    result: '按时还款',
    time: '15分钟前'
  },
  {
    id: 3,
    modelName: '行为识别',
    result: '异常行为',
    time: '20分钟前'
  },
  {
    id: 4,
    modelName: '流失预测',
    result: '低流失风险',
    time: '25分钟前'
  }
])

// 模型表单
const modelForm = reactive({
  name: '',
  type: '',
  algorithm: '',
  features: [],
  description: ''
})

// 计算属性
const filteredModelList = computed(() => {
  let result = modelList.value
  
  if (selectedModelType.value !== 'all') {
    result = result.filter(model => model.type === selectedModelType.value)
  }
  
  if (searchKeyword.value) {
    result = result.filter(model => 
      model.name.includes(searchKeyword.value) ||
      model.description.includes(searchKeyword.value)
    )
  }
  
  return result
})

// 方法定义
const getModelIcon = (type) => {
  const icons = {
    risk: 'ExclamationCircleOutlined',
    payment: 'MoneyCollectOutlined',
    behavior: 'NodeIndexOutlined',
    churn: 'UserDeleteOutlined'
  }
  return icons[type] || 'ExperimentOutlined'
}

const getModelColor = (type) => {
  const colors = {
    risk: '#ff4d4f',
    payment: '#52c41a',
    behavior: '#1890ff',
    churn: '#faad14'
  }
  return colors[type] || '#8c8c8c'
}

const getStatusColor = (status) => {
  const colors = {
    running: 'green',
    training: 'blue',
    stopped: 'default',
    error: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    training: '训练中',
    stopped: '已停止',
    error: '错误'
  }
  return texts[status] || status
}

const getAccuracyColor = (accuracy) => {
  if (accuracy >= 90) return '#52c41a'
  if (accuracy >= 80) return '#faad14'
  return '#ff4d4f'
}

const getPredictionColor = (result) => {
  const colors = {
    '高风险': 'red',
    '中风险': 'orange',
    '低风险': 'green',
    '按时还款': 'green',
    '逾期风险': 'orange',
    '异常行为': 'red',
    '正常行为': 'green',
    '低流失风险': 'green',
    '高流失风险': 'red'
  }
  return colors[result] || 'default'
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 85) return '#52c41a'
  if (confidence >= 70) return '#faad14'
  return '#ff4d4f'
}

const refreshModels = () => {
  console.log('刷新模型列表')
}

const exportModels = () => {
  console.log('导出模型')
}

const filterModels = () => {
  console.log('筛选模型:', selectedModelType.value)
}

const searchModels = () => {
  console.log('搜索模型:', searchKeyword.value)
}

const viewModel = (model) => {
  console.log('查看模型:', model)
}

const trainModel = (model) => {
  console.log('训练模型:', model)
}

const predictWithModel = (model) => {
  console.log('使用模型预测:', model)
}

const editModel = (model) => {
  console.log('编辑模型:', model)
}

const cloneModel = (model) => {
  console.log('克隆模型:', model)
}

const exportModel = (model) => {
  console.log('导出模型:', model)
}

const deleteModel = (model) => {
  const index = modelList.value.findIndex(m => m.id === model.id)
  if (index > -1) {
    modelList.value.splice(index, 1)
  }
  console.log('删除模型:', model)
}

const updatePerformanceChart = () => {
  console.log('更新性能图表:', selectedModel.value)
  initCharts()
}

const startTraining = () => {
  console.log('开始训练:', trainingConfig)
}

const createModel = () => {
  console.log('创建模型:', modelForm)
  showCreateModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 性能趋势图
    if (performanceChart.value) {
      const chart1 = echarts.init(performanceChart.value)
      chart1.setOption({
        title: { text: '模型性能趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['准确率', '精确率', '召回率'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['第1轮', '第2轮', '第3轮', '第4轮', '第5轮', '第6轮']
        },
        yAxis: { type: 'value', min: 0, max: 100 },
        series: [
          {
            name: '准确率',
            type: 'line',
            data: [78, 82, 85, 87, 89, 89.5],
            smooth: true
          },
          {
            name: '精确率',
            type: 'line',
            data: [76, 80, 83, 86, 88, 89.2],
            smooth: true
          },
          {
            name: '召回率',
            type: 'line',
            data: [74, 78, 81, 84, 86, 87.1],
            smooth: true
          }
        ]
      })
    }

    // 混淆矩阵
    if (confusionMatrix.value) {
      const chart2 = echarts.init(confusionMatrix.value)
      chart2.setOption({
        title: { text: '混淆矩阵', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: {
          position: 'top',
          formatter: function (params) {
            return '实际: ' + params.data[1] + '<br/>预测: ' + params.data[0] + '<br/>数量: ' + params.data[2]
          }
        },
        grid: {
          height: '50%',
          top: '30%'
        },
        xAxis: {
          type: 'category',
          data: ['低风险', '中风险', '高风险'],
          splitArea: { show: true }
        },
        yAxis: {
          type: 'category',
          data: ['低风险', '中风险', '高风险'],
          splitArea: { show: true }
        },
        visualMap: {
          min: 0,
          max: 100,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%'
        },
        series: [{
          type: 'heatmap',
          data: [
            [0, 0, 85], [0, 1, 12], [0, 2, 3],
            [1, 0, 8], [1, 1, 78], [1, 2, 14],
            [2, 0, 2], [2, 1, 15], [2, 2, 83]
          ],
          label: { show: true },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshModels()
  initCharts()
})
</script>

<style scoped>
.prediction-model {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.model-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.model-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.model-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.model-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f6f8ff;
  font-size: 14px;
}

.model-details {
  flex: 1;
}

.model-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.model-description {
  font-size: 12px;
  color: #999;
}

.accuracy-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.accuracy-text {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

.chart-container-small {
  height: 250px;
  width: 100%;
}

.prediction-results {
  max-height: 300px;
  overflow-y: auto;
}

.training-section {
  padding: 8px 0;

  .ant-btn {
    height: 36px;
    margin-top: 16px;
    font-size: 14px;
  }
}

.training-progress {
  max-height: 200px;
  overflow-y: auto;
}

.training-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}

.training-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.training-model {
  font-weight: 500;
  color: #262626;
}

.training-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.model-evaluation {
  max-height: 250px;
  overflow-y: auto;
}

.evaluation-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.evaluation-model {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.evaluation-score {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.evaluation-metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.metric-label {
  color: #999;
}

.metric-value {
  color: #262626;
  font-weight: 500;
}

.prediction-history {
  max-height: 250px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  flex: 1;
}

.history-model {
  font-weight: 500;
  color: #262626;
  font-size: 12px;
  margin-bottom: 2px;
}

.history-time {
  font-size: 11px;
  color: #999;
}
</style>