<template>
  <div class="behavior-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="analysis-info">
          <h2>行为分析</h2>
          <p class="analysis-desc">深度挖掘客户行为模式，识别潜在风险与机会</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshAnalysis">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportReport">
            <template #icon><DownloadOutlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="showAnalysisModal = true">
            <template #icon><FundViewOutlined /></template>
            开始分析
          </a-button>
        </div>
      </div>
    </div>

    <!-- 分析统计 -->
    <div class="analysis-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="分析客户数" 
              :value="analysisStats.totalCustomers" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><UserOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="行为模式" 
              :value="analysisStats.behaviorPatterns" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><NodeIndexOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="异常行为" 
              :value="analysisStats.anomalies" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><WarningOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="预测准确率" 
              :value="analysisStats.accuracy" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 行为轨迹分析 -->
        <a-card title="行为轨迹分析" class="analysis-card">
          <template #extra>
            <a-space>
              <a-select 
                v-model:value="selectedTimeRange" 
                style="width: 120px"
                @change="updateTrajectoryChart"
              >
                <a-select-option value="7d">近7天</a-select-option>
                <a-select-option value="30d">近30天</a-select-option>
                <a-select-option value="90d">近90天</a-select-option>
              </a-select>
              <a-select 
                v-model:value="selectedCustomerGroup" 
                style="width: 150px"
                @change="updateTrajectoryChart"
              >
                <a-select-option value="all">全部客户</a-select-option>
                <a-select-option value="high-risk">高风险客户</a-select-option>
                <a-select-option value="active">活跃客户</a-select-option>
                <a-select-option value="inactive">非活跃客户</a-select-option>
              </a-select>
            </a-space>
          </template>
          
          <div ref="trajectoryChart" class="chart-container"></div>
        </a-card>

        <!-- 行为模式识别 -->
        <a-card title="行为模式识别" class="analysis-card">
          <div class="pattern-analysis">
            <a-row :gutter="16">
              <a-col :span="12">
                <div ref="patternChart" class="chart-container-small"></div>
              </a-col>
              <a-col :span="12">
                <div class="pattern-list">
                  <div v-for="pattern in behaviorPatterns" :key="pattern.id" class="pattern-item">
                    <div class="pattern-header">
                      <h4>{{ pattern.name }}</h4>
                      <a-tag :color="getPatternColor(pattern.type)">{{ pattern.type }}</a-tag>
                    </div>
                    <div class="pattern-description">{{ pattern.description }}</div>
                    <div class="pattern-metrics">
                      <div class="metric-item">
                        <span class="metric-label">影响客户：</span>
                        <span class="metric-value">{{ pattern.affectedCustomers }}人</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">置信度：</span>
                        <span class="metric-value">{{ pattern.confidence }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 风险行为识别 -->
        <a-card title="风险行为识别" class="analysis-card">
          <div class="risk-behavior-analysis">
            <div class="risk-summary">
              <a-row :gutter="16">
                <a-col :span="8">
                  <div class="risk-metric">
                    <div class="risk-icon high-risk">
                      <ExclamationCircleOutlined />
                    </div>
                    <div class="risk-content">
                      <div class="risk-label">高风险行为</div>
                      <div class="risk-count">{{ riskBehaviors.high }}</div>
                    </div>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="risk-metric">
                    <div class="risk-icon medium-risk">
                      <WarningOutlined />
                    </div>
                    <div class="risk-content">
                      <div class="risk-label">中风险行为</div>
                      <div class="risk-count">{{ riskBehaviors.medium }}</div>
                    </div>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="risk-metric">
                    <div class="risk-icon low-risk">
                      <InfoCircleOutlined />
                    </div>
                    <div class="risk-content">
                      <div class="risk-label">低风险行为</div>
                      <div class="risk-count">{{ riskBehaviors.low }}</div>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
            <div ref="riskChart" class="chart-container-small"></div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 实时行为监控 -->
        <a-card title="实时行为监控" class="analysis-card">
          <div class="real-time-monitor">
            <div v-for="event in realtimeEvents" :key="event.id" class="event-item">
              <div class="event-time">{{ event.time }}</div>
              <div class="event-content">
                <div class="event-type" :class="getEventClass(event.type)">
                  {{ getEventTypeText(event.type) }}
                </div>
                <div class="event-description">{{ event.description }}</div>
                <div class="event-customer">客户：{{ event.customerName }}</div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 行为偏好分析 -->
        <a-card title="行为偏好分析" class="analysis-card">
          <div class="preference-analysis">
            <div v-for="preference in behaviorPreferences" :key="preference.type" class="preference-item">
              <div class="preference-header">
                <span class="preference-name">{{ preference.name }}</span>
                <span class="preference-percentage">{{ preference.percentage }}%</span>
              </div>
              <a-progress 
                :percent="preference.percentage" 
                :stroke-color="preference.color"
                size="small"
              />
            </div>
          </div>
        </a-card>

        <!-- 异常检测 -->
        <a-card title="异常检测" class="analysis-card">
          <template #extra>
            <a-badge :count="anomalies.filter(a => !a.handled).length" />
          </template>
          <div class="anomaly-detection">
            <div v-for="anomaly in anomalies" :key="anomaly.id" class="anomaly-item">
              <div class="anomaly-header">
                <div class="anomaly-severity" :class="getSeverityClass(anomaly.severity)">
                  {{ getSeverityText(anomaly.severity) }}
                </div>
                <div class="anomaly-time">{{ anomaly.time }}</div>
              </div>
              <div class="anomaly-description">{{ anomaly.description }}</div>
              <div class="anomaly-customer">{{ anomaly.customerName }}</div>
              <div class="anomaly-actions">
                <a-button 
                  v-if="!anomaly.handled" 
                  type="link" 
                  size="small" 
                  @click="handleAnomaly(anomaly)"
                >
                  处理
                </a-button>
                <a-button type="link" size="small" @click="viewAnomalyDetail(anomaly)">
                  详情
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 分析配置 -->
        <a-card title="分析配置" class="analysis-card">
          <div class="analysis-config">
            <a-form layout="vertical" size="small">
              <a-form-item label="分析维度">
                <a-checkbox-group v-model:value="analysisConfig.dimensions">
                  <a-checkbox value="temporal">时间维度</a-checkbox>
                  <a-checkbox value="frequency">频率维度</a-checkbox>
                  <a-checkbox value="sequence">序列维度</a-checkbox>
                  <a-checkbox value="interaction">交互维度</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="检测算法">
                <a-select v-model:value="analysisConfig.algorithm">
                  <a-select-option value="kmeans">K-Means聚类</a-select-option>
                  <a-select-option value="dbscan">DBSCAN密度聚类</a-select-option>
                  <a-select-option value="isolation">孤立森林</a-select-option>
                  <a-select-option value="lstm">LSTM神经网络</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="敏感度">
                <a-slider 
                  v-model:value="analysisConfig.sensitivity" 
                  :min="1" 
                  :max="10"
                  :marks="{ 1: '低', 5: '中', 10: '高' }"
                />
              </a-form-item>
              <a-button type="primary" block @click="applyConfig">
                应用配置
              </a-button>
            </a-form>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 分析模态框 -->
    <a-modal
      v-model:open="showAnalysisModal"
      title="启动行为分析"
      width="600px"
      @ok="startAnalysis"
    >
      <a-form layout="vertical">
        <a-form-item label="分析范围">
          <a-radio-group v-model:value="analysisForm.scope">
            <a-radio value="all">全部客户</a-radio>
            <a-radio value="segment">指定客群</a-radio>
            <a-radio value="individual">单个客户</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="analysisForm.scope === 'segment'" label="客户群体">
          <a-select v-model:value="analysisForm.segment" placeholder="选择客户群体">
            <a-select-option value="high-value">高价值客户</a-select-option>
            <a-select-option value="risk-customer">风险客户</a-select-option>
            <a-select-option value="new-customer">新客户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时间范围">
          <a-range-picker v-model:value="analysisForm.timeRange" />
        </a-form-item>
        <a-form-item label="分析类型">
          <a-checkbox-group v-model:value="analysisForm.analysisTypes">
            <a-checkbox value="pattern">模式识别</a-checkbox>
            <a-checkbox value="anomaly">异常检测</a-checkbox>
            <a-checkbox value="preference">偏好分析</a-checkbox>
            <a-checkbox value="risk">风险评估</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  FundViewOutlined,
  UserOutlined,
  NodeIndexOutlined,
  WarningOutlined,
  TrophyOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showAnalysisModal = ref(false)
const selectedTimeRange = ref('30d')
const selectedCustomerGroup = ref('all')

// 图表引用
const trajectoryChart = ref(null)
const patternChart = ref(null)
const riskChart = ref(null)

// 分析统计
const analysisStats = reactive({
  totalCustomers: 5648,
  behaviorPatterns: 28,
  anomalies: 156,
  accuracy: 92.3
})

// 行为模式
const behaviorPatterns = ref([
  {
    id: 1,
    name: '延迟还款模式',
    type: '风险',
    description: '客户倾向于在还款日后1-3天内还款',
    affectedCustomers: 245,
    confidence: 87
  },
  {
    id: 2,
    name: '主动沟通模式',
    type: '正常',
    description: '客户主动联系客服，积极配合处理',
    affectedCustomers: 189,
    confidence: 94
  },
  {
    id: 3,
    name: '躲避催收模式',
    type: '风险',
    description: '客户避免接听电话，不回复短信',
    affectedCustomers: 156,
    confidence: 89
  }
])

// 风险行为统计
const riskBehaviors = reactive({
  high: 23,
  medium: 156,
  low: 89
})

// 实时事件
const realtimeEvents = ref([
  {
    id: 1,
    type: 'risk',
    time: '2分钟前',
    description: '检测到异常登录行为',
    customerName: '张三'
  },
  {
    id: 2,
    type: 'normal',
    time: '5分钟前',
    description: '完成在线还款',
    customerName: '李四'
  },
  {
    id: 3,
    type: 'warning',
    time: '8分钟前',
    description: '连续3次催收未接听',
    customerName: '王五'
  },
  {
    id: 4,
    type: 'normal',
    time: '12分钟前',
    description: '主动查询账单信息',
    customerName: '赵六'
  }
])

// 行为偏好
const behaviorPreferences = ref([
  { name: '线上操作偏好', percentage: 78, color: '#1890ff' },
  { name: '主动沟通偏好', percentage: 65, color: '#52c41a' },
  { name: '移动端使用偏好', percentage: 82, color: '#faad14' },
  { name: '自助服务偏好', percentage: 71, color: '#722ed1' }
])

// 异常检测
const anomalies = ref([
  {
    id: 1,
    severity: 'high',
    time: '10分钟前',
    description: '深夜频繁查询账户信息',
    customerName: '张三',
    handled: false
  },
  {
    id: 2,
    severity: 'medium',
    time: '30分钟前',
    description: '短时间内多次修改联系方式',
    customerName: '李四',
    handled: false
  },
  {
    id: 3,
    severity: 'low',
    time: '1小时前',
    description: '跨地区登录异常',
    customerName: '王五',
    handled: true
  }
])

// 分析配置
const analysisConfig = reactive({
  dimensions: ['temporal', 'frequency'],
  algorithm: 'kmeans',
  sensitivity: 5
})

// 分析表单
const analysisForm = reactive({
  scope: 'segment',
  segment: 'high-value',
  timeRange: null,
  analysisTypes: ['pattern', 'anomaly']
})

// 方法定义
const getPatternColor = (type) => {
  const colors = {
    '风险': 'red',
    '正常': 'green',
    '警告': 'orange'
  }
  return colors[type] || 'default'
}

const getEventClass = (type) => {
  return `event-${type}`
}

const getEventTypeText = (type) => {
  const texts = {
    risk: '风险事件',
    warning: '警告事件',
    normal: '正常事件'
  }
  return texts[type] || type
}

const getSeverityClass = (severity) => {
  return `severity-${severity}`
}

const getSeverityText = (severity) => {
  const texts = {
    high: '严重',
    medium: '中等',
    low: '轻微'
  }
  return texts[severity] || severity
}

const refreshAnalysis = () => {
  console.log('刷新行为分析')
  initCharts()
}

const exportReport = () => {
  console.log('导出行为分析报告')
}

const updateTrajectoryChart = () => {
  console.log('更新轨迹图表', selectedTimeRange.value, selectedCustomerGroup.value)
  initCharts()
}

const handleAnomaly = (anomaly) => {
  anomaly.handled = true
  console.log('处理异常:', anomaly)
}

const viewAnomalyDetail = (anomaly) => {
  console.log('查看异常详情:', anomaly)
}

const applyConfig = () => {
  console.log('应用分析配置:', analysisConfig)
  initCharts()
}

const startAnalysis = () => {
  console.log('开始行为分析:', analysisForm)
  showAnalysisModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 行为轨迹图
    if (trajectoryChart.value) {
      const chart1 = echarts.init(trajectoryChart.value)
      chart1.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['登录频次', '操作频次', '查询频次', '还款频次'] },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '登录频次',
            type: 'line',
            data: [120, 132, 101, 134, 90, 230, 210],
            smooth: true
          },
          {
            name: '操作频次',
            type: 'line',
            data: [220, 182, 191, 234, 290, 330, 310],
            smooth: true
          },
          {
            name: '查询频次',
            type: 'line',
            data: [150, 232, 201, 154, 190, 330, 410],
            smooth: true
          },
          {
            name: '还款频次',
            type: 'line',
            data: [320, 332, 301, 334, 390, 330, 320],
            smooth: true
          }
        ]
      })
    }

    // 模式分布图
    if (patternChart.value) {
      const chart2 = echarts.init(patternChart.value)
      chart2.setOption({
        title: { text: '行为模式分布', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 245, name: '延迟还款模式' },
            { value: 189, name: '主动沟通模式' },
            { value: 156, name: '躲避催收模式' },
            { value: 98, name: '正常还款模式' }
          ]
        }]
      })
    }

    // 风险趋势图
    if (riskChart.value) {
      const chart3 = echarts.init(riskChart.value)
      chart3.setOption({
        title: { text: '风险行为趋势', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['高风险', '中风险', '低风险'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '高风险',
            type: 'bar',
            data: [12, 15, 18, 23, 20, 23],
            itemStyle: { color: '#ff4d4f' }
          },
          {
            name: '中风险',
            type: 'bar',
            data: [89, 95, 112, 156, 145, 156],
            itemStyle: { color: '#faad14' }
          },
          {
            name: '低风险',
            type: 'bar',
            data: [65, 72, 78, 89, 85, 89],
            itemStyle: { color: '#52c41a' }
          }
        ]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshAnalysis()
})
</script>

<style scoped>
.behavior-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.analysis-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.analysis-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.analysis-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container {
  height: 350px;
  width: 100%;
}

.chart-container-small {
  height: 250px;
  width: 100%;
}

.pattern-analysis {
  padding: 8px 0;
}

.pattern-list {
  max-height: 250px;
  overflow-y: auto;
  padding-left: 16px;
}

.pattern-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.pattern-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.pattern-header h4 {
  margin: 0;
  color: #262626;
}

.pattern-description {
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.pattern-metrics {
  display: flex;
  gap: 16px;
}

.metric-item {
  font-size: 12px;
}

.metric-label {
  color: #999;
}

.metric-value {
  color: #262626;
  font-weight: 500;
}

.risk-behavior-analysis {
  padding: 8px 0;
}

.risk-summary {
  margin-bottom: 16px;
}

.risk-metric {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.risk-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 16px;
}

.risk-icon.high-risk {
  background: #fff2f0;
  color: #ff4d4f;
}

.risk-icon.medium-risk {
  background: #fff7e6;
  color: #faad14;
}

.risk-icon.low-risk {
  background: #f6f8ff;
  color: #1890ff;
}

.risk-content {
  flex: 1;
}

.risk-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.risk-count {
  font-size: 18px;
  font-weight: bold;
  color: #262626;
}

.real-time-monitor {
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.event-item:last-child {
  border-bottom: none;
}

.event-time {
  font-size: 11px;
  color: #999;
  white-space: nowrap;
  min-width: 60px;
}

.event-content {
  flex: 1;
}

.event-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-bottom: 4px;
  display: inline-block;
}

.event-risk {
  background: #fff2f0;
  color: #ff4d4f;
}

.event-warning {
  background: #fff7e6;
  color: #faad14;
}

.event-normal {
  background: #f6ffed;
  color: #52c41a;
}

.event-description {
  font-size: 13px;
  color: #262626;
  margin-bottom: 2px;
}

.event-customer {
  font-size: 11px;
  color: #999;
}

.preference-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preference-item {
  padding: 8px 0;
}

.preference-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.preference-name {
  font-size: 13px;
  color: #262626;
}

.preference-percentage {
  font-size: 12px;
  color: #999;
}

.anomaly-detection {
  max-height: 300px;
  overflow-y: auto;
}

.anomaly-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.anomaly-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.anomaly-severity {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.severity-high {
  background: #fff2f0;
  color: #ff4d4f;
}

.severity-medium {
  background: #fff7e6;
  color: #faad14;
}

.severity-low {
  background: #f6f8ff;
  color: #1890ff;
}

.anomaly-time {
  font-size: 11px;
  color: #999;
}

.anomaly-description {
  font-size: 13px;
  color: #262626;
  margin-bottom: 4px;
}

.anomaly-customer {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.anomaly-actions {
  display: flex;
  gap: 8px;
}

.analysis-config {
  padding: 8px 0;

  .ant-btn {
    height: 36px;
    margin-top: 16px;
    font-size: 14px;
  }
}
</style>