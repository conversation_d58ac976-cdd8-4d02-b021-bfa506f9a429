<template>
  <div class="strategy-recommendation">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="strategy-info">
          <h2>策略推荐</h2>
          <p class="strategy-desc">基于AI智能分析，提供个性化催收策略推荐</p>
        </div>
        <div class="header-actions">
          <a-button @click="refreshStrategies">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportReport">
            <template #icon><DownloadOutlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="showStrategyModal = true">
            <template #icon><BulbOutlined /></template>
            生成策略
          </a-button>
        </div>
      </div>
    </div>

    <!-- 策略统计 -->
    <div class="strategy-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="策略总数" 
              :value="strategyStats.total" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><BulbOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="活跃策略" 
              :value="strategyStats.active" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="平均成功率" 
              :value="strategyStats.successRate" 
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic 
              title="今日推荐" 
              :value="strategyStats.todayRecommendations" 
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><RocketOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="16">
      <!-- 左侧内容 -->
      <a-col :span="16">
        <!-- 智能推荐 -->
        <a-card title="智能推荐" class="strategy-card">
          <template #extra>
            <a-space>
              <a-select 
                v-model:value="selectedCustomerType" 
                style="width: 150px"
                @change="updateRecommendations"
              >
                <a-select-option value="all">全部客户</a-select-option>
                <a-select-option value="high-risk">高风险客户</a-select-option>
                <a-select-option value="overdue">逾期客户</a-select-option>
                <a-select-option value="new">新客户</a-select-option>
              </a-select>
              <a-button @click="generateRecommendations">
                <template #icon><SyncOutlined /></template>
                重新生成
              </a-button>
            </a-space>
          </template>
          
          <div class="recommendation-list">
            <div v-for="recommendation in recommendations" :key="recommendation.id" class="recommendation-item">
              <div class="recommendation-header">
                <div class="recommendation-title">
                  <h4>{{ recommendation.title }}</h4>
                  <a-tag :color="getStrategyColor(recommendation.type)">{{ recommendation.type }}</a-tag>
                </div>
                <div class="recommendation-score">
                  <span class="score-label">推荐度：</span>
                  <a-rate :value="recommendation.score" disabled allow-half />
                  <span class="score-value">{{ recommendation.score }}/5</span>
                </div>
              </div>
              <div class="recommendation-description">{{ recommendation.description }}</div>
              <div class="recommendation-metrics">
                <div class="metric-item">
                  <span class="metric-label">预期成功率：</span>
                  <span class="metric-value">{{ recommendation.expectedSuccessRate }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">适用客户：</span>
                  <span class="metric-value">{{ recommendation.applicableCustomers }}人</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">执行难度：</span>
                  <a-tag :color="getDifficultyColor(recommendation.difficulty)">
                    {{ getDifficultyText(recommendation.difficulty) }}
                  </a-tag>
                </div>
              </div>
              <div class="recommendation-actions">
                <a-button type="primary" size="small" @click="applyStrategy(recommendation)">
                  应用策略
                </a-button>
                <a-button size="small" @click="viewDetails(recommendation)">
                  查看详情
                </a-button>
                <a-button size="small" @click="addToFavorites(recommendation)">
                  收藏
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 策略效果分析 -->
        <a-card title="策略效果分析" class="strategy-card">
          <template #extra>
            <a-select v-model:value="selectedTimeRange" style="width: 120px" @change="updateEffectChart">
              <a-select-option value="7d">近7天</a-select-option>
              <a-select-option value="30d">近30天</a-select-option>
              <a-select-option value="90d">近90天</a-select-option>
            </a-select>
          </template>
          
          <div ref="effectChart" class="chart-container"></div>
        </a-card>

        <!-- 策略配置 -->
        <a-card title="策略配置库" class="strategy-card">
          <template #extra>
            <a-input-search 
              v-model:value="strategySearchKeyword" 
              placeholder="搜索策略" 
              style="width: 200px"
              @search="searchStrategies"
            />
          </template>
          
          <a-table 
            :columns="strategyColumns" 
            :data-source="filteredStrategies"
            :pagination="{ pageSize: 8 }"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="strategy-name-cell">
                  <div class="strategy-icon">
                    <component :is="getStrategyIcon(record.type)" :style="{ color: getStrategyColor(record.type) }" />
                  </div>
                  <div class="strategy-details">
                    <div class="strategy-name">{{ record.name }}</div>
                    <div class="strategy-description">{{ record.description }}</div>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'successRate'">
                <div class="success-rate-cell">
                  <a-progress 
                    :percent="record.successRate" 
                    size="small"
                    :stroke-color="getSuccessRateColor(record.successRate)"
                  />
                  <span class="rate-text">{{ record.successRate }}%</span>
                </div>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="editStrategy(record)">
                    编辑
                  </a-button>
                  <a-button type="link" size="small" @click="testStrategy(record)">
                    测试
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多 <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="cloneStrategy(record)">复制</a-menu-item>
                        <a-menu-item @click="exportStrategy(record)">导出</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteStrategy(record)" danger>删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 右侧内容 -->
      <a-col :span="8">
        <!-- 策略生成器 -->
        <a-card title="策略生成器" class="strategy-card">
          <div class="strategy-generator">
            <a-form layout="vertical" size="small">
              <a-form-item label="客户群体">
                <a-select v-model:value="generatorConfig.customerSegment" placeholder="选择客户群体">
                  <a-select-option value="high-risk">高风险客户</a-select-option>
                  <a-select-option value="overdue">逾期客户</a-select-option>
                  <a-select-option value="new">新客户</a-select-option>
                  <a-select-option value="vip">VIP客户</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="策略类型">
                <a-checkbox-group v-model:value="generatorConfig.strategyTypes">
                  <a-checkbox value="communication">沟通策略</a-checkbox>
                  <a-checkbox value="payment">还款策略</a-checkbox>
                  <a-checkbox value="legal">法务策略</a-checkbox>
                  <a-checkbox value="incentive">激励策略</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="优化目标">
                <a-radio-group v-model:value="generatorConfig.objective">
                  <a-radio value="success-rate">成功率</a-radio>
                  <a-radio value="efficiency">效率</a-radio>
                  <a-radio value="cost">成本</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="AI模型">
                <a-select v-model:value="generatorConfig.aiModel">
                  <a-select-option value="basic">基础模型</a-select-option>
                  <a-select-option value="advanced">高级模型</a-select-option>
                  <a-select-option value="expert">专家模型</a-select-option>
                </a-select>
              </a-form-item>
              <a-button type="primary" block @click="generateCustomStrategy">
                <template #icon><RobotOutlined /></template>
                生成策略
              </a-button>
            </a-form>
          </div>
        </a-card>

        <!-- 策略执行状态 -->
        <a-card title="执行状态" class="strategy-card">
          <div class="execution-status">
            <div v-for="execution in executionStatus" :key="execution.id" class="execution-item">
              <div class="execution-header">
                <span class="execution-strategy">{{ execution.strategyName }}</span>
                <a-tag :color="getExecutionStatusColor(execution.status)">
                  {{ getExecutionStatusText(execution.status) }}
                </a-tag>
              </div>
              <div class="execution-progress">
                <div class="progress-info">
                  <span>进度：{{ execution.progress }}%</span>
                  <span>{{ execution.completedTasks }}/{{ execution.totalTasks }}</span>
                </div>
                <a-progress :percent="execution.progress" size="small" />
              </div>
              <div class="execution-metrics">
                <div class="metric-row">
                  <span>成功率：</span>
                  <span>{{ execution.successRate }}%</span>
                </div>
                <div class="metric-row">
                  <span>执行时间：</span>
                  <span>{{ execution.executionTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 策略收藏夹 -->
        <a-card title="策略收藏夹" class="strategy-card">
          <div class="favorites-list">
            <div v-for="favorite in favoriteStrategies" :key="favorite.id" class="favorite-item">
              <div class="favorite-header">
                <span class="favorite-name">{{ favorite.name }}</span>
                <a-button type="link" size="small" @click="removeFavorite(favorite)">
                  <DeleteOutlined />
                </a-button>
              </div>
              <div class="favorite-description">{{ favorite.description }}</div>
              <div class="favorite-metrics">
                <span class="metric-tag">成功率: {{ favorite.successRate }}%</span>
                <span class="metric-tag">使用: {{ favorite.usageCount }}次</span>
              </div>
            </div>
            <a-empty v-if="favoriteStrategies.length === 0" description="暂无收藏策略" size="small" />
          </div>
        </a-card>

        <!-- 策略优化建议 -->
        <a-card title="优化建议" class="strategy-card">
          <div class="optimization-suggestions">
            <div v-for="suggestion in optimizationSuggestions" :key="suggestion.id" class="suggestion-item">
              <div class="suggestion-header">
                <div class="suggestion-type" :class="getSuggestionClass(suggestion.type)">
                  {{ getSuggestionTypeText(suggestion.type) }}
                </div>
                <div class="suggestion-priority">
                  <a-tag :color="getPriorityColor(suggestion.priority)">
                    {{ getPriorityText(suggestion.priority) }}
                  </a-tag>
                </div>
              </div>
              <div class="suggestion-content">{{ suggestion.content }}</div>
              <div class="suggestion-impact">
                <span class="impact-label">预期提升：</span>
                <span class="impact-value">{{ suggestion.expectedImprovement }}%</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 策略生成模态框 -->
    <a-modal
      v-model:open="showStrategyModal"
      title="生成策略"
      width="600px"
      @ok="generateStrategy"
    >
      <a-form layout="vertical">
        <a-form-item label="策略名称" required>
          <a-input v-model:value="strategyForm.name" placeholder="请输入策略名称" />
        </a-form-item>
        <a-form-item label="目标客户群">
          <a-select v-model:value="strategyForm.targetGroup" placeholder="选择目标客户群">
            <a-select-option value="all">全部客户</a-select-option>
            <a-select-option value="high-risk">高风险客户</a-select-option>
            <a-select-option value="overdue">逾期客户</a-select-option>
            <a-select-option value="new">新客户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="策略类型">
          <a-select v-model:value="strategyForm.type" placeholder="选择策略类型">
            <a-select-option value="communication">沟通策略</a-select-option>
            <a-select-option value="payment">还款策略</a-select-option>
            <a-select-option value="legal">法务策略</a-select-option>
            <a-select-option value="incentive">激励策略</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="策略描述">
          <a-textarea v-model:value="strategyForm.description" placeholder="策略描述" :rows="3" />
        </a-form-item>
        <a-form-item label="执行条件">
          <a-textarea v-model:value="strategyForm.conditions" placeholder="执行条件" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  BulbOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  RocketOutlined,
  SyncOutlined,
  DownOutlined,
  RobotOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showStrategyModal = ref(false)
const selectedCustomerType = ref('all')
const selectedTimeRange = ref('30d')
const strategySearchKeyword = ref('')

// 图表引用
const effectChart = ref(null)

// 策略统计
const strategyStats = reactive({
  total: 45,
  active: 23,
  successRate: 76.8,
  todayRecommendations: 156
})

// 推荐策略
const recommendations = ref([
  {
    id: 1,
    title: '温和沟通策略',
    type: '沟通策略',
    score: 4.5,
    description: '针对新客户采用温和友善的沟通方式，建立信任关系',
    expectedSuccessRate: 78,
    applicableCustomers: 245,
    difficulty: 'easy'
  },
  {
    id: 2,
    title: '分期还款策略',
    type: '还款策略',
    score: 4.2,
    description: '为逾期客户制定个性化分期还款计划，降低还款压力',
    expectedSuccessRate: 85,
    applicableCustomers: 189,
    difficulty: 'medium'
  },
  {
    id: 3,
    title: '激励奖励策略',
    type: '激励策略',
    score: 3.8,
    description: '通过积分奖励、优惠券等激励手段促进主动还款',
    expectedSuccessRate: 72,
    applicableCustomers: 356,
    difficulty: 'easy'
  },
  {
    id: 4,
    title: '法务威慑策略',
    type: '法务策略',
    score: 4.0,
    description: '对恶意逾期客户进行法务威慑，提高还款紧迫感',
    expectedSuccessRate: 68,
    applicableCustomers: 98,
    difficulty: 'hard'
  }
])

// 策略配置列表
const strategyList = ref([
  {
    id: 1,
    name: '温和沟通策略',
    type: 'communication',
    description: '友善沟通建立信任',
    status: 'active',
    successRate: 78,
    usageCount: 245,
    createTime: '2024-01-15'
  },
  {
    id: 2,
    name: '分期还款策略',
    type: 'payment',
    description: '个性化分期方案',
    status: 'active',
    successRate: 85,
    usageCount: 189,
    createTime: '2024-01-12'
  },
  {
    id: 3,
    name: '激励奖励策略',
    type: 'incentive',
    description: '积分奖励促进还款',
    status: 'testing',
    successRate: 72,
    usageCount: 156,
    createTime: '2024-01-10'
  },
  {
    id: 4,
    name: '法务威慑策略',
    type: 'legal',
    description: '法务手段威慑逾期',
    status: 'inactive',
    successRate: 68,
    usageCount: 98,
    createTime: '2024-01-08'
  }
])

// 表格列定义
const strategyColumns = [
  {
    title: '策略名称',
    key: 'name',
    width: '40%'
  },
  {
    title: '状态',
    key: 'status',
    width: '15%'
  },
  {
    title: '成功率',
    key: 'successRate',
    width: '20%'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: '15%'
  },
  {
    title: '操作',
    key: 'actions',
    width: '10%'
  }
]

// 生成器配置
const generatorConfig = reactive({
  customerSegment: '',
  strategyTypes: [],
  objective: 'success-rate',
  aiModel: 'advanced'
})

// 执行状态
const executionStatus = ref([
  {
    id: 1,
    strategyName: '温和沟通策略',
    status: 'running',
    progress: 75,
    completedTasks: 15,
    totalTasks: 20,
    successRate: 78,
    executionTime: '2小时30分'
  },
  {
    id: 2,
    strategyName: '分期还款策略',
    status: 'completed',
    progress: 100,
    completedTasks: 25,
    totalTasks: 25,
    successRate: 85,
    executionTime: '4小时15分'
  },
  {
    id: 3,
    strategyName: '激励奖励策略',
    status: 'pending',
    progress: 0,
    completedTasks: 0,
    totalTasks: 18,
    successRate: 0,
    executionTime: '待执行'
  }
])

// 收藏策略
const favoriteStrategies = ref([
  {
    id: 1,
    name: '温和沟通策略',
    description: '友善沟通建立信任',
    successRate: 78,
    usageCount: 12
  },
  {
    id: 2,
    name: '分期还款策略',
    description: '个性化分期方案',
    successRate: 85,
    usageCount: 8
  }
])

// 优化建议
const optimizationSuggestions = ref([
  {
    id: 1,
    type: 'performance',
    priority: 'high',
    content: '建议增加客户行为分析维度，可提升策略匹配精准度',
    expectedImprovement: 15
  },
  {
    id: 2,
    type: 'efficiency',
    priority: 'medium',
    content: '优化沟通时段选择，建议在客户活跃时间进行联系',
    expectedImprovement: 12
  },
  {
    id: 3,
    type: 'cost',
    priority: 'low',
    content: '整合多种沟通渠道，降低单次联系成本',
    expectedImprovement: 8
  }
])

// 策略表单
const strategyForm = reactive({
  name: '',
  targetGroup: '',
  type: '',
  description: '',
  conditions: ''
})

// 计算属性
const filteredStrategies = computed(() => {
  if (!strategySearchKeyword.value) return strategyList.value
  return strategyList.value.filter(strategy => 
    strategy.name.includes(strategySearchKeyword.value) ||
    strategy.description.includes(strategySearchKeyword.value)
  )
})

// 方法定义
const getStrategyColor = (type) => {
  const colors = {
    '沟通策略': 'blue',
    '还款策略': 'green',
    '激励策略': 'orange',
    '法务策略': 'red',
    'communication': 'blue',
    'payment': 'green',
    'incentive': 'orange',
    'legal': 'red'
  }
  return colors[type] || 'default'
}

const getStrategyIcon = (type) => {
  const icons = {
    'communication': 'MessageOutlined',
    'payment': 'MoneyCollectOutlined',
    'incentive': 'GiftOutlined',
    'legal': 'SafetyCertificateOutlined'
  }
  return icons[type] || 'BulbOutlined'
}

const getDifficultyColor = (difficulty) => {
  const colors = {
    easy: 'green',
    medium: 'orange',
    hard: 'red'
  }
  return colors[difficulty] || 'default'
}

const getDifficultyText = (difficulty) => {
  const texts = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return texts[difficulty] || difficulty
}

const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    testing: 'blue',
    inactive: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '激活',
    testing: '测试中',
    inactive: '未激活'
  }
  return texts[status] || status
}

const getSuccessRateColor = (rate) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  return '#ff4d4f'
}

const getExecutionStatusColor = (status) => {
  const colors = {
    running: 'blue',
    completed: 'green',
    pending: 'orange',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getExecutionStatusText = (status) => {
  const texts = {
    running: '执行中',
    completed: '已完成',
    pending: '待执行',
    failed: '失败'
  }
  return texts[status] || status
}

const getSuggestionClass = (type) => {
  return `suggestion-${type}`
}

const getSuggestionTypeText = (type) => {
  const texts = {
    performance: '性能优化',
    efficiency: '效率提升',
    cost: '成本控制'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const refreshStrategies = () => {
  console.log('刷新策略')
  initCharts()
}

const exportReport = () => {
  console.log('导出策略报告')
}

const updateRecommendations = () => {
  console.log('更新推荐策略:', selectedCustomerType.value)
}

const generateRecommendations = () => {
  console.log('重新生成推荐')
}

const applyStrategy = (strategy) => {
  console.log('应用策略:', strategy)
}

const viewDetails = (strategy) => {
  console.log('查看策略详情:', strategy)
}

const addToFavorites = (strategy) => {
  favoriteStrategies.value.push({
    id: Date.now(),
    name: strategy.title,
    description: strategy.description,
    successRate: strategy.expectedSuccessRate,
    usageCount: 0
  })
  console.log('添加到收藏:', strategy)
}

const updateEffectChart = () => {
  console.log('更新效果图表:', selectedTimeRange.value)
  initCharts()
}

const searchStrategies = () => {
  console.log('搜索策略:', strategySearchKeyword.value)
}

const editStrategy = (strategy) => {
  console.log('编辑策略:', strategy)
}

const testStrategy = (strategy) => {
  console.log('测试策略:', strategy)
}

const cloneStrategy = (strategy) => {
  console.log('复制策略:', strategy)
}

const exportStrategy = (strategy) => {
  console.log('导出策略:', strategy)
}

const deleteStrategy = (strategy) => {
  const index = strategyList.value.findIndex(s => s.id === strategy.id)
  if (index > -1) {
    strategyList.value.splice(index, 1)
  }
  console.log('删除策略:', strategy)
}

const generateCustomStrategy = () => {
  console.log('生成自定义策略:', generatorConfig)
}

const removeFavorite = (favorite) => {
  const index = favoriteStrategies.value.findIndex(f => f.id === favorite.id)
  if (index > -1) {
    favoriteStrategies.value.splice(index, 1)
  }
}

const generateStrategy = () => {
  console.log('生成策略:', strategyForm)
  showStrategyModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 策略效果分析图
    if (effectChart.value) {
      const chart = echarts.init(effectChart.value)
      chart.setOption({
        title: { text: '策略执行效果对比', left: 'center', textStyle: { fontSize: 14 } },
        tooltip: { trigger: 'axis' },
        legend: { data: ['成功率', '执行效率', '客户满意度'], bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['温和沟通', '分期还款', '激励奖励', '法务威慑', '个性定制']
        },
        yAxis: { type: 'value', min: 0, max: 100 },
        series: [
          {
            name: '成功率',
            type: 'bar',
            data: [78, 85, 72, 68, 82],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '执行效率',
            type: 'bar',
            data: [85, 72, 90, 65, 78],
            itemStyle: { color: '#52c41a' }
          },
          {
            name: '客户满意度',
            type: 'bar',
            data: [92, 78, 88, 45, 85],
            itemStyle: { color: '#faad14' }
          }
        ]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshStrategies()
})
</script>

<style scoped>
.strategy-recommendation {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strategy-info h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.strategy-desc {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.strategy-stats {
  margin-bottom: 16px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.strategy-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.recommendation-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendation-title h4 {
  margin: 0;
  color: #262626;
}

.recommendation-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-label {
  font-size: 12px;
  color: #666;
}

.score-value {
  font-size: 12px;
  color: #999;
}

.recommendation-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.recommendation-metrics {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.metric-item {
  font-size: 13px;
}

.metric-label {
  color: #999;
}

.metric-value {
  color: #262626;
  font-weight: 500;
}

.recommendation-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  height: 350px;
  width: 100%;
}

.strategy-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.strategy-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f6f8ff;
  font-size: 14px;
}

.strategy-details {
  flex: 1;
}

.strategy-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.strategy-description {
  font-size: 12px;
  color: #999;
}

.success-rate-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rate-text {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

.strategy-generator {
  padding: 8px 0;

  .ant-btn {
    height: 36px;
    margin-top: 16px;
    font-size: 14px;
  }
}

.execution-status {
  max-height: 300px;
  overflow-y: auto;
}

.execution-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.execution-strategy {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.execution-progress {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.execution-metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #999;
}

.favorites-list {
  max-height: 300px;
  overflow-y: auto;
}

.favorite-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.favorite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.favorite-name {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.favorite-description {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.favorite-metrics {
  display: flex;
  gap: 8px;
}

.metric-tag {
  font-size: 11px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.optimization-suggestions {
  max-height: 350px;
  overflow-y: auto;
}

.suggestion-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-type {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.suggestion-performance {
  background: #f6ffed;
  color: #52c41a;
}

.suggestion-efficiency {
  background: #fff7e6;
  color: #faad14;
}

.suggestion-cost {
  background: #f6f8ff;
  color: #1890ff;
}

.suggestion-content {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 6px;
}

.suggestion-impact {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.impact-label {
  color: #999;
}

.impact-value {
  color: #52c41a;
  font-weight: 500;
}
</style>