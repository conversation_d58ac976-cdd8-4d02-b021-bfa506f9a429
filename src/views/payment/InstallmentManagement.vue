<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>分期管理</h2>
      
      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="总分期数" 
              :value="stats.totalInstallments"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <WalletOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>较上月 <span class="stat-change up">+12.5%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="进行中" 
              :value="stats.activeInstallments"
              :value-style="{ color: '#52c41a' }" 
            >
              <template #prefix>
                <SyncOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>占比 <span class="stat-percent">67.5%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="逾期分期" 
              :value="stats.overdueInstallments" 
              suffix="笔"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <WarningOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>逾期率 <span class="stat-change down">8.6%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="今日到期" 
              :value="stats.todayDue" 
              suffix="笔"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>待收金额 <span class="stat-amount">¥152,000</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索和操作区域 -->
      <a-card class="search-card enhanced-search">
        <template #title>
          <div class="search-header">
            <span>分期管理搜索</span>
            <div class="search-stats">
              <a-statistic
                title="今日到期"
                :value="stats.todayDue"
                :value-style="{ color: '#fa8c16', fontSize: '16px' }"
              />
            </div>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="toggleAdvanced">
              {{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}
              <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
            </a-button>
            <a-button type="primary" @click="handleQuickSearch">
              <template #icon><SearchOutlined /></template>
              快速搜索
            </a-button>
          </a-space>
        </template>

        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input v-model:value="searchForm.customerName" placeholder="请输入客户姓名" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input v-model:value="searchForm.caseNumber" placeholder="请输入案件编号" allow-clear>
                  <template #prefix><FileTextOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="分期状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="active">
                    <a-tag color="green">进行中</a-tag>
                  </a-select-option>
                  <a-select-option value="completed">
                    <a-tag color="blue">已完成</a-tag>
                  </a-select-option>
                  <a-select-option value="overdue">
                    <a-tag color="red">逾期</a-tag>
                  </a-select-option>
                  <a-select-option value="cancelled">
                    <a-tag color="gray">已取消</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="到期时间">
                <a-range-picker v-model:value="searchForm.dueDateRange" allow-clear :presets="timePresets" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    搜索
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
              </div>
            </a-col>
          </a-row>
        </a-form>

        <div class="action-buttons" style="margin-top: 16px;">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal">
              <plus-outlined />
              创建分期计划
            </a-button>
            <a-button @click="showImportModal">
              <UploadOutlined />
              批量导入
            </a-button>
            <a-button @click="handleBatchOperation">
              <file-text-outlined />
              批量操作
            </a-button>
            <a-button @click="showAnalysisModal">
              <LineChartOutlined />
              数据分析
            </a-button>
            <a-button @click="showRecommendModal">
              <RobotOutlined />
              智能推荐
            </a-button>
            <a-button @click="exportData">
              <download-outlined />
              导出数据
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 分期列表 -->
      <a-card>
        <a-table 
          :columns="columns" 
          :data-source="installmentList" 
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'customerInfo'">
              <div>
                <div class="customer-name">{{ record.customerName }}</div>
                <div class="customer-phone">{{ record.customerPhone }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'installmentInfo'">
              <div>
                <div>总期数：{{ record.totalPeriods }}期</div>
                <div>已还期数：{{ record.paidPeriods }}期</div>
                <div>剩余期数：{{ record.remainingPeriods }}期</div>
              </div>
            </template>
            
            <template v-if="column.key === 'amountInfo'">
              <div>
                <div>总金额：¥{{ record.totalAmount.toLocaleString() }}</div>
                <div>已还金额：¥{{ record.paidAmount.toLocaleString() }}</div>
                <div>剩余金额：¥{{ record.remainingAmount.toLocaleString() }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'nextDueDate'">
              <div :class="{ 'overdue': isOverdue(record.nextDueDate) }">
                {{ record.nextDueDate }}
              </div>
            </template>
            
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskColor(record.riskLevel)">
                {{ getRiskText(record.riskLevel) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">
                  <EyeOutlined />
                  详情
                </a-button>
                <a-button type="link" size="small" @click="recordPayment(record)">
                  <DollarOutlined />
                  还款登记
                </a-button>
                <a-button type="link" size="small" @click="adjustPlan(record)" :disabled="record.status === 'completed'">
                  <SettingOutlined />
                  调整计划
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="sendReminder(record)" :disabled="record.status !== 'active'">
                        <BellOutlined />
                        发送提醒
                      </a-menu-item>
                      <a-menu-item @click="viewPaymentHistory(record)">
                        <HistoryOutlined />
                        还款历史
                      </a-menu-item>
                      <a-menu-item @click="generateContract(record)">
                        <FileTextOutlined />
                        生成合同
                      </a-menu-item>
                      <a-menu-item @click="showEarlySettlementModal(record)" v-if="record.status === 'active'">
                        <CheckCircleOutlined />
                        提前结清
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="cancelInstallment(record)" style="color: #ff4d4f;" :disabled="record.status === 'completed'">
                        <StopOutlined />
                        取消分期
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 创建分期计划弹窗 -->
      <a-modal
        v-model:open="createModalVisible"
        title="创建分期计划"
        width="800px"
        @ok="handleCreateSubmit"
        @cancel="createModalVisible = false"
      >
        <a-form ref="createFormRef" :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="客户信息" name="customerId" :rules="[{ required: true, message: '请选择客户' }]">
            <a-select
              v-model:value="createForm.customerId"
              placeholder="请选择客户"
              show-search
              :filter-option="filterCustomerOption"
            >
              <a-select-option v-for="customer in customers" :key="customer.id" :value="customer.id">
                {{ customer.name }} - {{ customer.phone }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="关联案件" name="caseId" :rules="[{ required: true, message: '请选择案件' }]">
            <a-select
              v-model:value="createForm.caseId"
              placeholder="请选择案件"
              :disabled="!createForm.customerId"
            >
              <a-select-option v-for="caseItem in customerCases" :key="caseItem.id" :value="caseItem.id">
                {{ caseItem.caseNumber }} - ¥{{ caseItem.amount.toLocaleString() }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="分期总金额" name="totalAmount" :rules="[{ required: true, message: '请输入分期总金额' }]">
            <a-input-number
              v-model:value="createForm.totalAmount"
              :min="0"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="请输入分期总金额"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="分期期数" name="totalPeriods" :rules="[{ required: true, message: '请选择分期期数' }]">
            <a-select v-model:value="createForm.totalPeriods" placeholder="请选择分期期数">
              <a-select-option :value="3">3期</a-select-option>
              <a-select-option :value="6">6期</a-select-option>
              <a-select-option :value="12">12期</a-select-option>
              <a-select-option :value="24">24期</a-select-option>
              <a-select-option :value="36">36期</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="每期金额" name="periodAmount">
            <a-input-number
              v-model:value="createForm.periodAmount"
              :min="0"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="自动计算"
              disabled
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="首期还款日期" name="firstDueDate" :rules="[{ required: true, message: '请选择首期还款日期' }]">
            <a-date-picker v-model:value="createForm.firstDueDate" placeholder="请选择首期还款日期" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="还款周期" name="paymentCycle" :rules="[{ required: true, message: '请选择还款周期' }]">
            <a-select v-model:value="createForm.paymentCycle" placeholder="请选择还款周期">
              <a-select-option value="monthly">按月</a-select-option>
              <a-select-option value="weekly">按周</a-select-option>
              <a-select-option value="biweekly">双周</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="利率设置" name="interestRate">
            <a-input-number
              v-model:value="createForm.interestRate"
              :min="0"
              :max="100"
              :precision="2"
              addon-after="%"
              placeholder="年利率"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="备注说明" name="remark">
            <a-textarea v-model:value="createForm.remark" :rows="3" placeholder="请输入备注说明" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 分期详情弹窗 -->
      <a-modal
        v-model:open="detailModalVisible"
        title="分期详情"
        width="1000px"
        :footer="null"
      >
        <div v-if="currentInstallment">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-descriptions title="基本信息" bordered size="small">
                <a-descriptions-item label="客户姓名">{{ currentInstallment.customerName }}</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ currentInstallment.customerPhone }}</a-descriptions-item>
                <a-descriptions-item label="案件编号">{{ currentInstallment.caseNumber }}</a-descriptions-item>
                <a-descriptions-item label="分期状态">
                  <a-tag :color="getStatusColor(currentInstallment.status)">
                    {{ getStatusText(currentInstallment.status) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="风险等级">
                  <a-tag :color="getRiskColor(currentInstallment.riskLevel)">
                    {{ getRiskText(currentInstallment.riskLevel) }}
                  </a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-col>
            <a-col :span="12">
              <a-descriptions title="金额信息" bordered size="small">
                <a-descriptions-item label="总金额">¥{{ currentInstallment.totalAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="已还金额">¥{{ currentInstallment.paidAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="剩余金额">¥{{ currentInstallment.remainingAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="总期数">{{ currentInstallment.totalPeriods }}期</a-descriptions-item>
                <a-descriptions-item label="已还期数">{{ currentInstallment.paidPeriods }}期</a-descriptions-item>
                <a-descriptions-item label="剩余期数">{{ currentInstallment.remainingPeriods }}期</a-descriptions-item>
              </a-descriptions>
            </a-col>
          </a-row>
          
          <!-- 分期明细表 -->
          <a-divider>分期明细</a-divider>
          <a-table
            :columns="periodColumns"
            :data-source="currentInstallment.periods"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getPeriodStatusColor(record.status)">
                  {{ getPeriodStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'amount'">
                ¥{{ record.amount?.toLocaleString() }}
              </template>
              <template v-if="column.key === 'paidAmount'">
                ¥{{ record.paidAmount?.toLocaleString() }}
              </template>
            </template>
          </a-table>
        </div>
      </a-modal>

      <!-- 还款登记弹窗 -->
      <a-modal
        v-model:open="paymentModalVisible"
        title="还款登记"
        @ok="handlePaymentSubmit"
        @cancel="paymentModalVisible = false"
      >
        <a-form ref="paymentFormRef" :model="paymentForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="还款金额" name="amount" :rules="[{ required: true, message: '请输入还款金额' }]">
            <a-input-number
              v-model:value="paymentForm.amount"
              :min="0"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="请输入还款金额"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="还款方式" name="paymentMethod" :rules="[{ required: true, message: '请选择还款方式' }]">
            <a-select v-model:value="paymentForm.paymentMethod" placeholder="请选择还款方式">
              <a-select-option value="cash">现金</a-select-option>
              <a-select-option value="bank_transfer">银行转账</a-select-option>
              <a-select-option value="alipay">支付宝</a-select-option>
              <a-select-option value="wechat">微信支付</a-select-option>
              <a-select-option value="pos">POS机</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="还款日期" name="paymentDate" :rules="[{ required: true, message: '请选择还款日期' }]">
            <a-date-picker v-model:value="paymentForm.paymentDate" placeholder="请选择还款日期" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="凭证上传" name="voucher">
            <a-upload
              v-model:file-list="paymentForm.voucherList"
              :before-upload="() => false"
              list-type="picture-card"
            >
              <div>
                <plus-outlined />
                <div style="margin-top: 8px">上传凭证</div>
              </div>
            </a-upload>
          </a-form-item>
          
          <a-form-item label="备注说明" name="remark">
            <a-textarea v-model:value="paymentForm.remark" :rows="3" placeholder="请输入备注说明" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 计划调整弹窗 -->
      <a-modal
        v-model:open="adjustModalVisible"
        title="调整分期计划"
        width="700px"
        @ok="handleAdjustSubmit"
        @cancel="adjustModalVisible = false"
      >
        <a-form ref="adjustFormRef" :model="adjustForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="调整类型" name="adjustType" :rules="[{ required: true, message: '请选择调整类型' }]">
            <a-radio-group v-model:value="adjustForm.adjustType">
              <a-radio value="amount">调整金额</a-radio>
              <a-radio value="period">调整期数</a-radio>
              <a-radio value="date">调整时间</a-radio>
              <a-radio value="suspend">暂停计划</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <div v-if="adjustForm.adjustType === 'amount'">
            <a-form-item label="新的总金额" name="newTotalAmount" :rules="[{ required: true, message: '请输入新的总金额' }]">
              <a-input-number
                v-model:value="adjustForm.newTotalAmount"
                :min="0"
                :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                placeholder="请输入新的总金额"
                style="width: 100%"
              />
            </a-form-item>
          </div>
          
          <div v-if="adjustForm.adjustType === 'period'">
            <a-form-item label="新的期数" name="newPeriods" :rules="[{ required: true, message: '请选择新的期数' }]">
              <a-select v-model:value="adjustForm.newPeriods" placeholder="请选择新的期数">
                <a-select-option :value="6">6期</a-select-option>
                <a-select-option :value="12">12期</a-select-option>
                <a-select-option :value="18">18期</a-select-option>
                <a-select-option :value="24">24期</a-select-option>
                <a-select-option :value="36">36期</a-select-option>
              </a-select>
            </a-form-item>
          </div>
          
          <div v-if="adjustForm.adjustType === 'date'">
            <a-form-item label="新的还款日" name="newPaymentDay" :rules="[{ required: true, message: '请选择新的还款日' }]">
              <a-select v-model:value="adjustForm.newPaymentDay" placeholder="请选择新的还款日">
                <a-select-option v-for="day in 28" :key="day" :value="day">{{ day }}日</a-select-option>
              </a-select>
            </a-form-item>
          </div>
          
          <div v-if="adjustForm.adjustType === 'suspend'">
            <a-form-item label="暂停期限" name="suspendDays" :rules="[{ required: true, message: '请输入暂停天数' }]">
              <a-input-number
                v-model:value="adjustForm.suspendDays"
                :min="1"
                :max="365"
                placeholder="请输入暂停天数"
                style="width: 100%"
              />
            </a-form-item>
          </div>
          
          <a-form-item label="调整原因" name="adjustReason" :rules="[{ required: true, message: '请输入调整原因' }]">
            <a-textarea v-model:value="adjustForm.adjustReason" :rows="3" placeholder="请输入调整原因" />
          </a-form-item>
          
          <a-form-item label="审批人" name="approver">
            <a-select v-model:value="adjustForm.approver" placeholder="请选择审批人">
              <a-select-option value="manager1">张经理</a-select-option>
              <a-select-option value="manager2">李经理</a-select-option>
              <a-select-option value="manager3">王经理</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 提前结清弹窗 -->
      <a-modal
        v-model:open="settlementModalVisible"
        title="提前结清"
        width="600px"
        @ok="handleSettlementSubmit"
        @cancel="settlementModalVisible = false"
      >
        <a-form ref="settlementFormRef" :model="settlementForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="剩余本金">
            <span class="settlement-amount">¥{{ currentInstallment?.remainingAmount?.toLocaleString() }}</span>
          </a-form-item>
          
          <a-form-item label="结清方式" name="settlementType" :rules="[{ required: true, message: '请选择结清方式' }]">
            <a-radio-group v-model:value="settlementForm.settlementType">
              <a-radio value="full">全额结清</a-radio>
              <a-radio value="discount">优惠结清</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="优惠金额" name="discountAmount" v-if="settlementForm.settlementType === 'discount'">
            <a-input-number
              v-model:value="settlementForm.discountAmount"
              :min="0"
              :max="currentInstallment?.remainingAmount"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="请输入优惠金额"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="实际结清金额">
            <span class="settlement-amount">¥{{ actualSettlementAmount?.toLocaleString() }}</span>
          </a-form-item>
          
          <a-form-item label="结清日期" name="settlementDate" :rules="[{ required: true, message: '请选择结清日期' }]">
            <a-date-picker v-model:value="settlementForm.settlementDate" placeholder="请选择结清日期" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="备注说明" name="remark">
            <a-textarea v-model:value="settlementForm.remark" :rows="3" placeholder="请输入备注说明" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 还款历史弹窗 -->
      <a-modal
        v-model:open="historyModalVisible"
        title="还款历史"
        width="1000px"
        :footer="null"
      >
        <a-table
          :columns="historyColumns"
          :data-source="paymentHistory"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              ¥{{ record.amount?.toLocaleString() }}
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getPaymentStatusColor(record.status)">
                {{ getPaymentStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'paymentMethod'">
              <a-tag>{{ getPaymentMethodText(record.paymentMethod) }}</a-tag>
            </template>
          </template>
        </a-table>
      </a-modal>

      <!-- 批量操作弹窗 -->
      <a-modal
        v-model:open="batchModalVisible"
        title="批量操作"
        width="600px"
        @ok="handleBatchSubmit"
        @cancel="batchModalVisible = false"
      >
        <a-form ref="batchFormRef" :model="batchForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选中数量">
            <span>已选择 {{ selectedRowKeys.length }} 个分期计划</span>
          </a-form-item>
          
          <a-form-item label="操作类型" name="operationType" :rules="[{ required: true, message: '请选择操作类型' }]">
            <a-select v-model:value="batchForm.operationType" placeholder="请选择操作类型">
              <a-select-option value="remind">批量发送提醒</a-select-option>
              <a-select-option value="adjust_date">批量调整还款日</a-select-option>
              <a-select-option value="suspend">批量暂停</a-select-option>
              <a-select-option value="export">批量导出</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="新还款日" name="newPaymentDay" v-if="batchForm.operationType === 'adjust_date'">
            <a-select v-model:value="batchForm.newPaymentDay" placeholder="请选择新的还款日">
              <a-select-option v-for="day in 28" :key="day" :value="day">{{ day }}日</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="暂停天数" name="suspendDays" v-if="batchForm.operationType === 'suspend'">
            <a-input-number
              v-model:value="batchForm.suspendDays"
              :min="1"
              :max="365"
              placeholder="请输入暂停天数"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="操作原因" name="reason" v-if="['adjust_date', 'suspend'].includes(batchForm.operationType)">
            <a-textarea v-model:value="batchForm.reason" :rows="3" placeholder="请输入操作原因" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量导入弹窗 -->
      <a-modal
        v-model:open="importModalVisible"
        title="批量导入分期计划"
        width="600px"
        @ok="handleImportSubmit"
        @cancel="importModalVisible = false"
      >
        <a-form :model="importForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="上传文件">
            <a-upload-dragger
              v-model:file-list="importForm.fileList"
              :max-count="1"
              :before-upload="beforeImportUpload"
              @change="handleImportChange"
            >
              <p class="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持Excel文件，单次最多导入1000条记录
              </p>
            </a-upload-dragger>
          </a-form-item>
          
          <a-form-item label="导入选项">
            <a-checkbox-group v-model:value="importForm.options">
              <a-checkbox value="validate">数据验证</a-checkbox>
              <a-checkbox value="duplicate">去重处理</a-checkbox>
              <a-checkbox value="notification">发送通知</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="模板下载">
            <a-button type="link" @click="downloadTemplate">
              <DownloadOutlined />
              下载导入模板
            </a-button>
          </a-form-item>
          
          <div v-if="importResult.show" class="import-result">
            <a-alert
              :type="importResult.type"
              :message="importResult.message"
              :description="importResult.description"
              show-icon
              closable
            />
          </div>
        </a-form>
      </a-modal>

      <!-- 数据分析弹窗 -->
      <a-modal
        v-model:open="analysisModalVisible"
        title="分期数据分析"
        width="1200px"
        :footer="null"
      >
        <a-tabs v-model:activeKey="analysisActiveKey">
          <a-tab-pane key="overview" tab="总体概览">
            <a-row :gutter="16">
              <a-col :span="12">
                <div id="installment-trend-chart" style="height: 350px;"></div>
              </a-col>
              <a-col :span="12">
                <div id="status-distribution-chart" style="height: 350px;"></div>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <div id="amount-range-chart" style="height: 350px;"></div>
              </a-col>
              <a-col :span="12">
                <div id="period-analysis-chart" style="height: 350px;"></div>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="risk" tab="风险分析">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="风险分布">
                  <div id="risk-distribution-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
              <a-col :span="16">
                <a-card title="逾期趋势">
                  <div id="overdue-trend-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="24">
                <a-card title="高风险分期列表">
                  <a-table
                    :columns="riskColumns"
                    :data-source="riskInstallments"
                    :pagination="false"
                    size="small"
                  />
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="performance" tab="绩效分析">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="还款率趋势">
                  <div id="payment-rate-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="回收金额统计">
                  <div id="recovery-amount-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 智能推荐弹窗 -->
      <a-modal
        v-model:open="recommendModalVisible"
        title="智能分期推荐"
        width="1000px"
        :footer="null"
      >
        <a-tabs v-model:activeKey="recommendActiveKey">
          <a-tab-pane key="strategy" tab="策略推荐">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="AI分期策略推荐" :bordered="false">
                  <a-list
                    :data-source="strategyRecommendations"
                    :split="false"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: item.color }">
                              <template #icon>
                                <component :is="item.icon" />
                              </template>
                            </a-avatar>
                          </template>
                          <template #title>
                            {{ item.title }}
                            <a-tag :color="item.tagColor" style="margin-left: 8px;">{{ item.tag }}</a-tag>
                          </template>
                          <template #description>
                            <div>{{ item.description }}</div>
                            <div class="strategy-metrics">
                              <span>预期成功率: <strong>{{ item.successRate }}%</strong></span>
                              <span>推荐指数: <a-rate :value="item.rating" disabled /></span>
                            </div>
                          </template>
                        </a-list-item-meta>
                        <template #actions>
                          <a-button type="link" @click="applyStrategy(item)">应用策略</a-button>
                        </template>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="分期计算器" :bordered="false">
                  <a-form :model="calculator" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                    <a-form-item label="债务总额">
                      <a-input-number
                        v-model:value="calculator.totalAmount"
                        :min="0"
                        :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                        style="width: 100%"
                        @change="calculateInstallment"
                      />
                    </a-form-item>
                    <a-form-item label="分期期数">
                      <a-select v-model:value="calculator.periods" @change="calculateInstallment">
                        <a-select-option :value="3">3期</a-select-option>
                        <a-select-option :value="6">6期</a-select-option>
                        <a-select-option :value="12">12期</a-select-option>
                        <a-select-option :value="24">24期</a-select-option>
                        <a-select-option :value="36">36期</a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item label="年化利率">
                      <a-input-number
                        v-model:value="calculator.interestRate"
                        :min="0"
                        :max="36"
                        :precision="2"
                        :formatter="value => `${value}%`"
                        :parser="value => value.replace('%', '')"
                        style="width: 100%"
                        @change="calculateInstallment"
                      />
                    </a-form-item>
                    <a-divider />
                    <a-form-item label="每期还款额">
                      <span class="calc-result">¥{{ calculator.monthlyPayment.toLocaleString() }}</span>
                    </a-form-item>
                    <a-form-item label="总利息">
                      <span class="calc-result">¥{{ calculator.totalInterest.toLocaleString() }}</span>
                    </a-form-item>
                    <a-form-item label="还款总额">
                      <span class="calc-result">¥{{ calculator.totalPayment.toLocaleString() }}</span>
                    </a-form-item>
                  </a-form>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="customer" tab="客户推荐">
            <a-card title="适合分期的客户" :bordered="false">
              <a-table
                :columns="recommendCustomerColumns"
                :data-source="recommendCustomers"
                :pagination="false"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'score'">
                    <a-progress 
                      :percent="record.score" 
                      :stroke-color="getScoreColor(record.score)"
                      size="small"
                    />
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="createPlanForCustomer(record)">
                        创建分期
                      </a-button>
                      <a-button type="link" size="small" @click="viewCustomerDetail(record)">
                        查看详情
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 还款提醒设置弹窗 -->
      <a-modal
        v-model:open="reminderSettingVisible"
        title="还款提醒设置"
        width="700px"
        @ok="handleReminderSettingSubmit"
        @cancel="reminderSettingVisible = false"
      >
        <a-form :model="reminderSetting" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="提醒方式">
            <a-checkbox-group v-model:value="reminderSetting.methods">
              <a-checkbox value="sms"><MessageOutlined /> 短信提醒</a-checkbox>
              <a-checkbox value="phone"><PhoneOutlined /> 电话提醒</a-checkbox>
              <a-checkbox value="email"><MailOutlined /> 邮件提醒</a-checkbox>
              <a-checkbox value="app">APP推送</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="提醒时机">
            <a-checkbox-group v-model:value="reminderSetting.timing">
              <a-row>
                <a-col :span="12">
                  <a-checkbox value="7days">到期前7天</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="3days">到期前3天</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="1day">到期前1天</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="dueday">到期当天</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="overdue1">逾期1天</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="overdue3">逾期3天</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
          
          <a-form-item label="提醒模板">
            <a-select v-model:value="reminderSetting.templateId" placeholder="请选择提醒模板">
              <a-select-option value="1">标准提醒模板</a-select-option>
              <a-select-option value="2">友好提醒模板</a-select-option>
              <a-select-option value="3">正式提醒模板</a-select-option>
              <a-select-option value="4">紧急提醒模板</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="发送时间">
            <a-time-picker v-model:value="reminderSetting.sendTime" format="HH:mm" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="重复提醒">
            <a-switch v-model:checked="reminderSetting.repeat" />
            <span style="margin-left: 8px;">{{ reminderSetting.repeat ? '开启' : '关闭' }}</span>
          </a-form-item>
          
          <div v-if="reminderSetting.repeat">
            <a-form-item label="重复间隔">
              <a-input-number 
                v-model:value="reminderSetting.repeatInterval" 
                :min="1" 
                :max="7"
                style="width: 200px;"
              />
              <span style="margin-left: 8px;">天</span>
            </a-form-item>
            
            <a-form-item label="最大次数">
              <a-input-number 
                v-model:value="reminderSetting.maxRepeat" 
                :min="1" 
                :max="10"
                style="width: 200px;"
              />
              <span style="margin-left: 8px;">次</span>
            </a-form-item>
          </div>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  FileTextOutlined,
  DownloadOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DollarOutlined,
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  HistoryOutlined,
  CheckCircleOutlined,
  StopOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  CalendarOutlined,
  LineChartOutlined,
  RobotOutlined,
  CalculatorOutlined,
  UploadOutlined,
  ExportOutlined,
  SafetyOutlined,
  PrinterOutlined,
  MailOutlined,
  MessageOutlined,
  PhoneOutlined,
  TeamOutlined,
  CheckOutlined,
  CloseOutlined,
  SyncOutlined,
  FileDoneOutlined,
  FileProtectOutlined,
  ThunderboltOutlined,
  AuditOutlined,
  FundOutlined,
  BankOutlined,
  WalletOutlined,
  CreditCardOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const showAdvanced = ref(false)

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 统计数据
const stats = reactive({
  totalInstallments: 486,
  activeInstallments: 328,
  overdueInstallments: 42,
  todayDue: 15
})

// 搜索表单
const searchForm = reactive({
  customerName: '',
  caseNumber: '',
  status: undefined,
  dueDateRange: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 486,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '客户信息',
    key: 'customerInfo',
    width: 150
  },
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120
  },
  {
    title: '分期信息',
    key: 'installmentInfo',
    width: 120
  },
  {
    title: '金额信息',
    key: 'amountInfo',
    width: 160
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '下期到期日',
    key: 'nextDueDate',
    width: 100
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 分期明细列配置
const periodColumns = [
  {
    title: '期数',
    dataIndex: 'period',
    key: 'period',
    width: 60
  },
  {
    title: '应还金额',
    key: 'amount',
    width: 100
  },
  {
    title: '已还金额',
    key: 'paidAmount',
    width: 100
  },
  {
    title: '到期日期',
    dataIndex: 'dueDate',
    key: 'dueDate',
    width: 100
  },
  {
    title: '还款日期',
    dataIndex: 'paymentDate',
    key: 'paymentDate',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  }
]

// 模拟数据
const installmentList = ref([
  {
    id: 1,
    customerName: '张三',
    customerPhone: '***********',
    caseNumber: 'CS2024001',
    totalPeriods: 12,
    paidPeriods: 6,
    remainingPeriods: 6,
    totalAmount: 120000,
    paidAmount: 60000,
    remainingAmount: 60000,
    status: 'active',
    nextDueDate: '2024-01-15',
    riskLevel: 'low',
    createTime: '2023-07-01',
    periods: [
      { period: 1, amount: 10000, paidAmount: 10000, dueDate: '2023-08-01', paymentDate: '2023-08-01', status: 'paid' },
      { period: 2, amount: 10000, paidAmount: 10000, dueDate: '2023-09-01', paymentDate: '2023-09-01', status: 'paid' },
      { period: 3, amount: 10000, paidAmount: 10000, dueDate: '2023-10-01', paymentDate: '2023-10-01', status: 'paid' },
      { period: 4, amount: 10000, paidAmount: 10000, dueDate: '2023-11-01', paymentDate: '2023-11-01', status: 'paid' },
      { period: 5, amount: 10000, paidAmount: 10000, dueDate: '2023-12-01', paymentDate: '2023-12-01', status: 'paid' },
      { period: 6, amount: 10000, paidAmount: 10000, dueDate: '2024-01-01', paymentDate: '2024-01-01', status: 'paid' },
      { period: 7, amount: 10000, paidAmount: 0, dueDate: '2024-01-15', paymentDate: '', status: 'pending' },
      { period: 8, amount: 10000, paidAmount: 0, dueDate: '2024-02-01', paymentDate: '', status: 'pending' },
      { period: 9, amount: 10000, paidAmount: 0, dueDate: '2024-03-01', paymentDate: '', status: 'pending' },
      { period: 10, amount: 10000, paidAmount: 0, dueDate: '2024-04-01', paymentDate: '', status: 'pending' },
      { period: 11, amount: 10000, paidAmount: 0, dueDate: '2024-05-01', paymentDate: '', status: 'pending' },
      { period: 12, amount: 10000, paidAmount: 0, dueDate: '2024-06-01', paymentDate: '', status: 'pending' }
    ]
  },
  {
    id: 2,
    customerName: '李四',
    customerPhone: '***********',
    caseNumber: 'CS2024002',
    totalPeriods: 24,
    paidPeriods: 10,
    remainingPeriods: 14,
    totalAmount: 240000,
    paidAmount: 100000,
    remainingAmount: 140000,
    status: 'overdue',
    nextDueDate: '2024-01-10',
    riskLevel: 'high',
    createTime: '2023-06-15',
    periods: []
  }
])

// 弹窗状态
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const paymentModalVisible = ref(false)
const adjustModalVisible = ref(false)
const settlementModalVisible = ref(false)
const historyModalVisible = ref(false)
const batchModalVisible = ref(false)
const importModalVisible = ref(false)
const analysisModalVisible = ref(false)
const recommendModalVisible = ref(false)
const reminderSettingVisible = ref(false)

// Tab激活键
const analysisActiveKey = ref('overview')
const recommendActiveKey = ref('strategy')

// 当前选中的分期
const currentInstallment = ref(null)

// 创建表单
const createForm = reactive({
  customerId: undefined,
  caseId: undefined,
  totalAmount: undefined,
  totalPeriods: undefined,
  periodAmount: undefined,
  firstDueDate: undefined,
  paymentCycle: 'monthly',
  interestRate: 0,
  remark: ''
})

// 还款表单
const paymentForm = reactive({
  amount: undefined,
  paymentMethod: undefined,
  paymentDate: undefined,
  voucherList: [],
  remark: ''
})

// 调整表单
const adjustForm = reactive({
  adjustType: undefined,
  newTotalAmount: undefined,
  newPeriods: undefined,
  newPaymentDay: undefined,
  suspendDays: undefined,
  adjustReason: '',
  approver: undefined
})

// 结清表单
const settlementForm = reactive({
  settlementType: 'full',
  discountAmount: 0,
  settlementDate: undefined,
  remark: ''
})

// 批量操作表单
const batchForm = reactive({
  operationType: undefined,
  newPaymentDay: undefined,
  suspendDays: undefined,
  reason: ''
})

// 导入表单
const importForm = reactive({
  fileList: [],
  options: ['validate', 'duplicate']
})

// 导入结果
const importResult = reactive({
  show: false,
  type: 'success',
  message: '',
  description: ''
})

// 提醒设置
const reminderSetting = reactive({
  methods: ['sms'],
  timing: ['3days', 'dueday'],
  templateId: '1',
  sendTime: null,
  repeat: false,
  repeatInterval: 3,
  maxRepeat: 3
})

// 分期计算器
const calculator = reactive({
  totalAmount: 100000,
  periods: 12,
  interestRate: 12,
  monthlyPayment: 0,
  totalInterest: 0,
  totalPayment: 0
})

// 策略推荐数据
const strategyRecommendations = ref([
  {
    id: 1,
    title: '低风险长期分期方案',
    description: '适用于信用良好、有稳定收入的客户，可提供较长分期期限和较低利率',
    tag: '推荐',
    tagColor: 'green',
    color: '#52c41a',
    icon: 'SafetyOutlined',
    successRate: 92,
    rating: 5
  },
  {
    id: 2,
    title: '中风险平衡方案',
    description: '适用于一般信用状况的客户，平衡风险与收益，中等期限和利率',
    tag: '适中',
    tagColor: 'blue',
    color: '#1890ff',
    icon: 'BankOutlined',
    successRate: 85,
    rating: 4
  },
  {
    id: 3,
    title: '高风险短期方案',
    description: '适用于信用较差但有还款意愿的客户，短期高频还款，降低违约风险',
    tag: '谨慎',
    tagColor: 'orange',
    color: '#fa8c16',
    icon: 'ThunderboltOutlined',
    successRate: 78,
    rating: 3
  }
])

// 推荐客户数据
const recommendCustomers = ref([
  {
    id: 1,
    name: '张三',
    phone: '138****8001',
    caseNumber: 'CS2024001',
    debtAmount: 120000,
    score: 85,
    reason: '有稳定收入，历史还款记录良好'
  },
  {
    id: 2,
    name: '李四',
    phone: '139****8002',
    caseNumber: 'CS2024002',
    debtAmount: 80000,
    score: 72,
    reason: '主动联系要求分期，有还款意愿'
  },
  {
    id: 3,
    name: '王五',
    phone: '136****8003',
    caseNumber: 'CS2024003',
    debtAmount: 150000,
    score: 68,
    reason: '近期收入恢复，可承担分期还款'
  }
])

// 推荐客户列配置
const recommendCustomerColumns = [
  {
    title: '客户姓名',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone'
  },
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber'
  },
  {
    title: '欠款金额',
    dataIndex: 'debtAmount',
    key: 'debtAmount',
    customRender: ({ text }) => `¥${text.toLocaleString()}`
  },
  {
    title: '推荐度',
    key: 'score',
    width: 150
  },
  {
    title: '推荐理由',
    dataIndex: 'reason',
    key: 'reason'
  },
  {
    title: '操作',
    key: 'action',
    width: 160
  }
]

// 风险分期列表
const riskInstallments = ref([
  {
    id: 1,
    customerName: '赵六',
    caseNumber: 'CS2024010',
    totalAmount: 200000,
    overdueCount: 3,
    riskLevel: 'high',
    riskScore: 85,
    suggestion: '建议电话催收，必要时上门'
  },
  {
    id: 2,
    customerName: '钱七',
    caseNumber: 'CS2024011',
    totalAmount: 150000,
    overdueCount: 2,
    riskLevel: 'medium',
    riskScore: 65,
    suggestion: '加强提醒频率，关注还款情况'
  }
])

// 风险列配置
const riskColumns = [
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName'
  },
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber'
  },
  {
    title: '分期金额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    customRender: ({ text }) => `¥${text.toLocaleString()}`
  },
  {
    title: '逾期次数',
    dataIndex: 'overdueCount',
    key: 'overdueCount'
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    key: 'riskLevel',
    customRender: ({ text }) => {
      const colorMap = { high: 'red', medium: 'orange', low: 'green' }
      const textMap = { high: '高风险', medium: '中风险', low: '低风险' }
      return h('a-tag', { color: colorMap[text] }, textMap[text])
    }
  },
  {
    title: '风险评分',
    dataIndex: 'riskScore',
    key: 'riskScore'
  },
  {
    title: '处置建议',
    dataIndex: 'suggestion',
    key: 'suggestion'
  }
]

// 客户列表
const customers = ref([
  { id: 1, name: '张三', phone: '***********' },
  { id: 2, name: '李四', phone: '***********' },
  { id: 3, name: '王五', phone: '***********' }
])

// 案件列表
const customerCases = ref([])

// 还款历史数据
const paymentHistory = ref([
  {
    id: 1,
    period: 1,
    amount: 10000,
    paymentDate: '2023-08-01',
    paymentMethod: 'bank_transfer',
    status: 'success',
    operator: '张三',
    remark: '按时还款'
  },
  {
    id: 2,
    period: 2,
    amount: 10000,
    paymentDate: '2023-09-01',
    paymentMethod: 'alipay',
    status: 'success',
    operator: '张三',
    remark: ''
  }
])

// 还款历史表格列
const historyColumns = [
  {
    title: '期数',
    dataIndex: 'period',
    key: 'period',
    width: 60
  },
  {
    title: '还款金额',
    key: 'amount',
    width: 100
  },
  {
    title: '还款日期',
    dataIndex: 'paymentDate',
    key: 'paymentDate',
    width: 100
  },
  {
    title: '还款方式',
    key: 'paymentMethod',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 80
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark'
  }
]

// 计算实际结清金额
const actualSettlementAmount = computed(() => {
  if (!currentInstallment.value) return 0
  const remainingAmount = currentInstallment.value.remainingAmount || 0
  const discountAmount = settlementForm.discountAmount || 0
  return remainingAmount - discountAmount
})

// 监听客户选择
watch(() => createForm.customerId, (customerId) => {
  if (customerId) {
    // 模拟获取客户的案件列表
    customerCases.value = [
      { id: 1, caseNumber: 'CS2024001', amount: 120000 },
      { id: 2, caseNumber: 'CS2024002', amount: 80000 }
    ]
  } else {
    customerCases.value = []
  }
  createForm.caseId = undefined
})

// 监听金额和期数变化，自动计算每期金额
watch([() => createForm.totalAmount, () => createForm.totalPeriods], ([amount, periods]) => {
  if (amount && periods) {
    createForm.periodAmount = Math.round(amount / periods)
  } else {
    createForm.periodAmount = undefined
  }
})

// 状态相关方法
const getStatusColor = (status) => {
  const colors = {
    active: 'blue',
    completed: 'green',
    overdue: 'red',
    cancelled: 'gray'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    completed: '已完成',
    overdue: '逾期',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getRiskColor = (risk) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colors[risk] || 'default'
}

const getRiskText = (risk) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[risk] || '未知'
}

const getPeriodStatusColor = (status) => {
  const colors = {
    paid: 'green',
    pending: 'blue',
    overdue: 'red'
  }
  return colors[status] || 'default'
}

const getPeriodStatusText = (status) => {
  const texts = {
    paid: '已还款',
    pending: '待还款',
    overdue: '逾期'
  }
  return texts[status] || '未知'
}

const isOverdue = (dateStr) => {
  return new Date(dateStr) < new Date()
}

// 还款状态相关
const getPaymentStatusColor = (status) => {
  const colors = {
    success: 'green',
    failed: 'red',
    pending: 'orange',
    processing: 'blue'
  }
  return colors[status] || 'default'
}

const getPaymentStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    pending: '待处理',
    processing: '处理中'
  }
  return texts[status] || '未知'
}

const getPaymentMethodText = (method) => {
  const texts = {
    cash: '现金',
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat: '微信支付',
    pos: 'POS机'
  }
  return texts[method] || '其他'
}

// 事件处理方法
const handleSearch = () => {
  console.log('搜索参数:', searchForm)
  message.success('搜索完成')
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 快速搜索
const handleQuickSearch = () => {
  handleSearch()
}

const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const showCreateModal = () => {
  createModalVisible.value = true
}

const handleCreateSubmit = () => {
  console.log('创建分期计划:', createForm)
  message.success('分期计划创建成功')
  createModalVisible.value = false
}

const viewDetails = (record) => {
  currentInstallment.value = record
  detailModalVisible.value = true
}

const recordPayment = (record) => {
  currentInstallment.value = record
  paymentModalVisible.value = true
}

const handlePaymentSubmit = () => {
  console.log('还款登记:', paymentForm)
  message.success('还款登记成功')
  paymentModalVisible.value = false
}

const adjustPlan = (record) => {
  currentInstallment.value = record
  adjustModalVisible.value = true
}

const sendReminder = (record) => {
  message.success(`已向 ${record.customerName} 发送还款提醒`)
}

const viewPaymentHistory = (record) => {
  currentInstallment.value = record
  historyModalVisible.value = true
}

const generateContract = (record) => {
  message.success('正在生成分期合同...')
}

const cancelInstallment = (record) => {
  message.success('分期计划已取消')
}

const handleBatchOperation = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的分期')
    return
  }
  batchModalVisible.value = true
}

const exportData = () => {
  message.success('数据导出成功')
}

const filterCustomerOption = (input, option) => {
  return option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 新增事件处理函数
const handleAdjustSubmit = () => {
  console.log('调整计划:', adjustForm)
  message.success('分期计划调整成功')
  adjustModalVisible.value = false
}

const showEarlySettlementModal = (record) => {
  currentInstallment.value = record
  settlementModalVisible.value = true
}

const handleSettlementSubmit = () => {
  console.log('提前结清:', settlementForm)
  message.success('提前结清申请已提交')
  settlementModalVisible.value = false
}

const handleBatchSubmit = () => {
  console.log('批量操作:', batchForm)
  const operationTexts = {
    remind: '批量发送提醒',
    adjust_date: '批量调整还款日',
    suspend: '批量暂停',
    export: '批量导出'
  }
  message.success(`${operationTexts[batchForm.operationType]}操作成功`)
  batchModalVisible.value = false
}

// 新增方法
const showImportModal = () => {
  importModalVisible.value = true
}

const showAnalysisModal = () => {
  analysisModalVisible.value = true
  setTimeout(() => {
    initAnalysisCharts()
  }, 100)
}

const showRecommendModal = () => {
  recommendModalVisible.value = true
  calculateInstallment()
}

const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel' ||
                  file.type === 'text/csv'
  if (!isExcel) {
    message.error('只能上传Excel文件！')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！')
    return false
  }
  return false
}

const handleImportChange = ({ file }) => {
  if (file.status === 'done') {
    importResult.show = true
    importResult.type = 'success'
    importResult.message = '文件上传成功'
    importResult.description = `文件 ${file.name} 已准备就绪，点击确定开始导入`
  }
}

const handleImportSubmit = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    importResult.show = true
    importResult.type = 'success'
    importResult.message = '导入成功'
    importResult.description = '成功导入 156 条分期记录，失败 3 条，重复 2 条'
    importModalVisible.value = false
    handleSearch()
  }, 2000)
}

const downloadTemplate = () => {
  message.success('正在下载模板...')
  // 实际实现时创建并下载Excel文件
}

const calculateInstallment = () => {
  const { totalAmount, periods, interestRate } = calculator
  const monthlyRate = interestRate / 100 / 12
  const monthlyPayment = totalAmount * (monthlyRate * Math.pow(1 + monthlyRate, periods)) / (Math.pow(1 + monthlyRate, periods) - 1)
  
  calculator.monthlyPayment = Math.round(monthlyPayment)
  calculator.totalPayment = Math.round(monthlyPayment * periods)
  calculator.totalInterest = calculator.totalPayment - totalAmount
}

const applyStrategy = (strategy) => {
  message.success(`已应用策略：${strategy.title}`)
  recommendModalVisible.value = false
  // 根据策略自动填充创建表单
  createModalVisible.value = true
}

const getScoreColor = (score) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#1890ff'
  return '#fa8c16'
}

const createPlanForCustomer = (customer) => {
  createForm.customerId = customer.id
  createForm.totalAmount = customer.debtAmount
  recommendModalVisible.value = false
  createModalVisible.value = true
}

const viewCustomerDetail = (customer) => {
  message.info('查看客户详情功能开发中')
}

const handleReminderSettingSubmit = () => {
  console.log('提醒设置:', reminderSetting)
  message.success('提醒设置已保存')
  reminderSettingVisible.value = false
}

// 初始化分析图表
const initAnalysisCharts = () => {
  // 分期趋势图
  const trendChart = echarts.init(document.getElementById('installment-trend-chart'))
  const trendOption = {
    title: {
      text: '分期趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增分期', '完成分期', '逾期分期'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 80,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [
      {
        name: '新增分期',
        type: 'line',
        data: [120, 132, 145, 154, 168, 180],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '完成分期',
        type: 'line',
        data: [80, 85, 90, 95, 102, 110],
        smooth: true,
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '逾期分期',
        type: 'line',
        data: [8, 10, 12, 15, 18, 20],
        smooth: true,
        itemStyle: { color: '#ff4d4f' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 状态分布图
  const statusChart = echarts.init(document.getElementById('status-distribution-chart'))
  const statusOption = {
    title: {
      text: '分期状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 60
    },
    series: [
      {
        name: '分期状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 328, name: '进行中', itemStyle: { color: '#1890ff' } },
          { value: 98, name: '已完成', itemStyle: { color: '#52c41a' } },
          { value: 42, name: '逾期', itemStyle: { color: '#ff4d4f' } },
          { value: 18, name: '已取消', itemStyle: { color: '#d9d9d9' } }
        ]
      }
    ]
  }
  statusChart.setOption(statusOption)

  // 金额区间分析
  const amountChart = echarts.init(document.getElementById('amount-range-chart'))
  const amountOption = {
    title: {
      text: '分期金额区间分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 60,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-5万', '5-10万', '10-20万', '20-50万', '50万以上']
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [
      {
        name: '分期数量',
        type: 'bar',
        data: [45, 120, 180, 95, 46],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#1890ff' },
            { offset: 1, color: '#69c0ff' }
          ])
        }
      }
    ]
  }
  amountChart.setOption(amountOption)

  // 期数分析
  const periodChart = echarts.init(document.getElementById('period-analysis-chart'))
  const periodOption = {
    title: {
      text: '分期期数选择分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '期数选择',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 68, name: '3期', itemStyle: { color: '#13c2c2' } },
          { value: 125, name: '6期', itemStyle: { color: '#1890ff' } },
          { value: 186, name: '12期', itemStyle: { color: '#52c41a' } },
          { value: 82, name: '24期', itemStyle: { color: '#fa8c16' } },
          { value: 25, name: '36期', itemStyle: { color: '#722ed1' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  periodChart.setOption(periodOption)

  // 风险分布图
  const riskChart = echarts.init(document.getElementById('risk-distribution-chart'))
  const riskOption = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 5,
        itemStyle: {
          color: '#ff4d4f'
        },
        progress: {
          show: true,
          roundCap: true,
          width: 18
        },
        pointer: {
          length: '60%',
          width: 8,
          offsetCenter: [0, '5%']
        },
        axisLine: {
          lineStyle: {
            width: 18
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          fontSize: 24,
          offsetCenter: [0, '35%'],
          valueAnimation: true,
          formatter: '{value}%',
          color: '#ff4d4f'
        },
        data: [
          {
            value: 8.6,
            name: '逾期率'
          }
        ]
      }
    ]
  }
  riskChart.setOption(riskOption)

  // 逾期趋势图
  const overdueChart = echarts.init(document.getElementById('overdue-trend-chart'))
  const overdueOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['逾期金额', '逾期笔数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '笔数',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '逾期金额',
        type: 'bar',
        data: [32, 35, 38, 41, 45, 48, 52],
        itemStyle: { color: '#ff4d4f' }
      },
      {
        name: '逾期笔数',
        type: 'line',
        yAxisIndex: 1,
        data: [8, 9, 10, 11, 12, 13, 15],
        itemStyle: { color: '#fa8c16' }
      }
    ]
  }
  overdueChart.setOption(overdueOption)

  // 还款率趋势
  const paymentRateChart = echarts.init(document.getElementById('payment-rate-chart'))
  const paymentRateOption = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '还款率（%）',
      min: 80,
      max: 100
    },
    series: [
      {
        type: 'line',
        data: [92, 93, 91, 94, 95, 96],
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#52c41a' },
            { offset: 1, color: '#52c41a20' }
          ])
        },
        itemStyle: { color: '#52c41a' }
      }
    ]
  }
  paymentRateChart.setOption(paymentRateOption)

  // 回收金额统计
  const recoveryChart = echarts.init(document.getElementById('recovery-amount-chart'))
  const recoveryOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['目标金额', '实际回收']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '金额（万元）'
    },
    yAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    series: [
      {
        name: '目标金额',
        type: 'bar',
        data: [500, 520, 540, 560, 580, 600],
        itemStyle: { color: '#1890ff40' }
      },
      {
        name: '实际回收',
        type: 'bar',
        data: [480, 505, 515, 545, 565, 582],
        itemStyle: { color: '#1890ff' }
      }
    ]
  }
  recoveryChart.setOption(recoveryOption)

  // 响应式调整
  window.addEventListener('resize', () => {
    trendChart.resize()
    statusChart.resize()
    amountChart.resize()
    periodChart.resize()
    riskChart.resize()
    overdueChart.resize()
    paymentRateChart.resize()
    recoveryChart.resize()
  })
}

// 初始化
onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.customer-name {
  font-weight: 500;
  color: #1890ff;
}

.customer-phone {
  font-size: 12px;
  color: #666;
}

.overdue {
  color: #ff4d4f;
  font-weight: 500;
}

.ant-table {
  background: #fff;
}

.ant-descriptions-title {
  font-weight: 500;
}

.settlement-amount {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

/* 模态框样式优化 */
.ant-modal-body {
  padding: 20px;
}

.ant-form-item {
  margin-bottom: 16px;
}

/* 表格内容样式 */
.ant-table-tbody > tr > td {
  padding: 8px 12px;
}

/* 操作按钮样式 */
.ant-btn-link {
  padding: 0 4px;
  height: auto;
}

/* 状态标签样式 */
.ant-tag {
  margin-right: 0;
}

/* 统计卡片增强样式 */
.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-change {
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-change.down {
  color: #ff4d4f;
}

.stat-percent {
  color: #1890ff;
  font-weight: 500;
}

.stat-amount {
  color: #fa8c16;
  font-weight: 500;
}

/* 导入结果样式 */
.import-result {
  margin-top: 16px;
}

/* 策略推荐样式 */
.strategy-metrics {
  margin-top: 8px;
  display: flex;
  gap: 24px;
  font-size: 12px;
  color: #666;
}

.strategy-metrics strong {
  color: #1890ff;
}

/* 计算器结果样式 */
.calc-result {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-wrapper {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .search-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-buttons,
  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .stats-cards .ant-col {
    margin-bottom: 16px;
  }
}
/* 增强搜索样式 */
.enhanced-search {
  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-stats {
      display: flex;
      gap: 16px;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
  }

  .action-buttons {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
