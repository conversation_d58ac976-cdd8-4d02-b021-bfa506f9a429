<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>减免管理</h2>
      
      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="减免申请数" 
              :value="stats.totalApplications"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>较上月 <span class="stat-change up">+23.5%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="待审批" 
              :value="stats.pendingApprovals" 
              suffix="笔"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>平均处理时间 <span class="stat-time">2.5天</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已批准" 
              :value="stats.approvedApplications" 
              suffix="笔"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>批准率 <span class="stat-percent">57.1%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="减免金额" 
              :value="stats.totalReductionAmount" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <DollarCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>平均减免率 <span class="stat-percent">31.2%</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索和操作区域 -->
      <a-card class="search-card enhanced-search">
        <template #title>
          <div class="search-header">
            <span>减免管理搜索</span>
            <div class="search-stats">
              <a-statistic
                title="待审批"
                :value="stats.pendingApprovals"
                :value-style="{ color: '#fa8c16', fontSize: '16px' }"
              />
            </div>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="toggleAdvanced">
              {{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}
              <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
            </a-button>
            <a-button type="primary" @click="handleQuickSearch">
              <template #icon><SearchOutlined /></template>
              快速搜索
            </a-button>
          </a-space>
        </template>

        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input v-model:value="searchForm.customerName" placeholder="请输入客户姓名" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input v-model:value="searchForm.caseNumber" placeholder="请输入案件编号" allow-clear>
                  <template #prefix><FileTextOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="申请状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="pending">
                    <a-tag color="orange">待审批</a-tag>
                  </a-select-option>
                  <a-select-option value="approved">
                    <a-tag color="green">已批准</a-tag>
                  </a-select-option>
                  <a-select-option value="rejected">
                    <a-tag color="red">已拒绝</a-tag>
                  </a-select-option>
                  <a-select-option value="executed">
                    <a-tag color="blue">已执行</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="减免类型">
                <a-select v-model:value="searchForm.reductionType" placeholder="请选择类型" allow-clear>
                  <a-select-option value="principal">
                    <a-tag color="blue">本金减免</a-tag>
                  </a-select-option>
                  <a-select-option value="interest">
                    <a-tag color="green">利息减免</a-tag>
                  </a-select-option>
                  <a-select-option value="penalty">
                    <a-tag color="orange">违约金减免</a-tag>
                  </a-select-option>
                  <a-select-option value="total">
                    <a-tag color="purple">全额减免</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" v-if="showAdvanced" style="margin-top: 16px;">
            <a-col :span="6">
              <a-form-item label="申请时间">
                <a-range-picker v-model:value="searchForm.applicationDateRange" allow-clear :presets="timePresets" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="减免金额">
                <a-input-group compact>
                  <a-input-number
                    v-model:value="searchForm.amountMin"
                    placeholder="最小金额"
                    :min="0"
                    style="width: 50%"
                  />
                  <a-input-number
                    v-model:value="searchForm.amountMax"
                    placeholder="最大金额"
                    :min="0"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="申请人">
                <a-input v-model:value="searchForm.applicant" placeholder="请输入申请人" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="审批人">
                <a-input v-model:value="searchForm.approver" placeholder="请输入审批人" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    搜索
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
              </div>
            </a-col>
          </a-row>
        </a-form>

        <div class="action-buttons" style="margin-top: 16px;">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal">
              <plus-outlined />
              创建减免申请
            </a-button>
            <a-button @click="handleBatchApproval">
              <check-outlined />
              批量审批
            </a-button>
            <a-button @click="showAnalysisModal">
              <BarChartOutlined />
              效果分析
            </a-button>
            <a-button @click="showTemplateModal">
              <FileTextOutlined />
              减免模板
            </a-button>
            <a-button @click="showAIRecommendModal">
              <RobotOutlined />
              AI减免建议
            </a-button>
            <a-button @click="showRuleConfigModal">
              <ApiOutlined />
              规则配置
            </a-button>
            <a-button @click="exportData">
              <download-outlined />
              导出数据
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 减免申请列表 -->
      <a-card>
        <a-table 
          :columns="columns" 
          :data-source="reductionList" 
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'customerInfo'">
              <div>
                <div class="customer-name">{{ record.customerName }}</div>
                <div class="customer-phone">{{ record.customerPhone }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'caseInfo'">
              <div>
                <div>{{ record.caseNumber }}</div>
                <div class="case-amount">原金额：¥{{ record.originalAmount?.toLocaleString() }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'reductionInfo'">
              <div>
                <div>
                  <a-tag :color="getReductionTypeColor(record.reductionType)">
                    {{ getReductionTypeText(record.reductionType) }}
                  </a-tag>
                </div>
                <div class="reduction-amount">减免：¥{{ record.reductionAmount?.toLocaleString() }}</div>
                <div class="reduction-ratio">比例：{{ record.reductionRatio }}%</div>
              </div>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'urgency'">
              <a-tag :color="getUrgencyColor(record.urgency)">
                {{ getUrgencyText(record.urgency) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button 
                  v-if="record.status === 'pending'" 
                  type="link" 
                  size="small" 
                  @click="approveApplication(record)"
                >
                  审批
                </a-button>
                <a-button 
                  v-if="record.status === 'approved'" 
                  type="link" 
                  size="small" 
                  @click="executeReduction(record)"
                >
                  执行
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewApprovalHistory(record)">审批历史</a-menu-item>
                      <a-menu-item @click="generateContract(record)">生成协议</a-menu-item>
                      <a-menu-item @click="printCertificate(record)">打印凭证</a-menu-item>
                      <a-menu-item 
                        v-if="record.status === 'pending'" 
                        @click="withdrawApplication(record)"
                      >
                        撤回申请
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 创建减免申请弹窗 -->
      <a-modal
        v-model:open="createModalVisible"
        title="创建减免申请"
        width="800px"
        @ok="handleCreateSubmit"
        @cancel="createModalVisible = false"
      >
        <a-form ref="createFormRef" :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="客户信息" name="customerId" :rules="[{ required: true, message: '请选择客户' }]">
            <a-select
              v-model:value="createForm.customerId"
              placeholder="请选择客户"
              show-search
              :filter-option="filterCustomerOption"
            >
              <a-select-option v-for="customer in customers" :key="customer.id" :value="customer.id">
                {{ customer.name }} - {{ customer.phone }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="关联案件" name="caseId" :rules="[{ required: true, message: '请选择案件' }]">
            <a-select
              v-model:value="createForm.caseId"
              placeholder="请选择案件"
              :disabled="!createForm.customerId"
              @change="onCaseSelect"
            >
              <a-select-option v-for="caseItem in customerCases" :key="caseItem.id" :value="caseItem.id">
                {{ caseItem.caseNumber }} - ¥{{ caseItem.amount.toLocaleString() }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="原始金额" name="originalAmount">
            <a-input-number
              v-model:value="createForm.originalAmount"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="自动获取"
              disabled
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="减免类型" name="reductionType" :rules="[{ required: true, message: '请选择减免类型' }]">
            <a-select v-model:value="createForm.reductionType" placeholder="请选择减免类型">
              <a-select-option value="principal">本金减免</a-select-option>
              <a-select-option value="interest">利息减免</a-select-option>
              <a-select-option value="penalty">违约金减免</a-select-option>
              <a-select-option value="total">全额减免</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="减免金额" name="reductionAmount" :rules="[{ required: true, message: '请输入减免金额' }]">
            <a-input-number
              v-model:value="createForm.reductionAmount"
              :min="0"
              :max="createForm.originalAmount"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="请输入减免金额"
              style="width: 100%"
              @change="calculateReductionRatio"
            />
          </a-form-item>
          
          <a-form-item label="减免比例" name="reductionRatio">
            <a-input-number
              v-model:value="createForm.reductionRatio"
              :min="0"
              :max="100"
              :precision="2"
              addon-after="%"
              placeholder="自动计算"
              disabled
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="紧急程度" name="urgency" :rules="[{ required: true, message: '请选择紧急程度' }]">
            <a-select v-model:value="createForm.urgency" placeholder="请选择紧急程度">
              <a-select-option value="low">一般</a-select-option>
              <a-select-option value="medium">紧急</a-select-option>
              <a-select-option value="high">特急</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="申请原因" name="reason" :rules="[{ required: true, message: '请输入申请原因' }]">
            <a-textarea v-model:value="createForm.reason" :rows="4" placeholder="请详细说明减免申请的原因" />
          </a-form-item>
          
          <a-form-item label="支撑材料" name="attachments">
            <a-upload
              v-model:file-list="createForm.attachmentList"
              :before-upload="() => false"
              multiple
            >
              <a-button>
                <upload-outlined />
                上传文件
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 减免详情弹窗 -->
      <a-modal
        v-model:open="detailModalVisible"
        title="减免申请详情"
        width="1000px"
        :footer="null"
      >
        <div v-if="currentReduction">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-descriptions title="申请信息" bordered size="small">
                <a-descriptions-item label="申请编号">{{ currentReduction.applicationId }}</a-descriptions-item>
                <a-descriptions-item label="客户姓名">{{ currentReduction.customerName }}</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ currentReduction.customerPhone }}</a-descriptions-item>
                <a-descriptions-item label="案件编号">{{ currentReduction.caseNumber }}</a-descriptions-item>
                <a-descriptions-item label="申请状态">
                  <a-tag :color="getStatusColor(currentReduction.status)">
                    {{ getStatusText(currentReduction.status) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="紧急程度">
                  <a-tag :color="getUrgencyColor(currentReduction.urgency)">
                    {{ getUrgencyText(currentReduction.urgency) }}
                  </a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-col>
            <a-col :span="12">
              <a-descriptions title="减免信息" bordered size="small">
                <a-descriptions-item label="减免类型">
                  <a-tag :color="getReductionTypeColor(currentReduction.reductionType)">
                    {{ getReductionTypeText(currentReduction.reductionType) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="原始金额">¥{{ currentReduction.originalAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="减免金额">¥{{ currentReduction.reductionAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="减免比例">{{ currentReduction.reductionRatio }}%</a-descriptions-item>
                <a-descriptions-item label="剩余金额">¥{{ (currentReduction.originalAmount - currentReduction.reductionAmount)?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="申请时间">{{ currentReduction.applicationDate }}</a-descriptions-item>
              </a-descriptions>
            </a-col>
          </a-row>
          
          <a-divider>申请原因</a-divider>
          <p>{{ currentReduction.reason }}</p>
          
          <a-divider>支撑材料</a-divider>
          <div v-if="currentReduction.attachments && currentReduction.attachments.length > 0">
            <a-list
              :data-source="currentReduction.attachments"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a @click="downloadFile(item)">{{ item.name }}</a>
                    </template>
                    <template #description>
                      {{ item.size }} | {{ item.uploadTime }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <a-empty v-else description="暂无支撑材料" size="small" />
          
          <!-- 审批历史 -->
          <a-divider>审批历史</a-divider>
          <a-timeline>
            <a-timeline-item 
              v-for="history in currentReduction.approvalHistory" 
              :key="history.id"
              :color="getHistoryColor(history.action)"
            >
              <p><strong>{{ history.action }}</strong> - {{ history.approver }}</p>
              <p>{{ history.comment }}</p>
              <p class="history-time">{{ history.time }}</p>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-modal>

      <!-- 审批弹窗 -->
      <a-modal
        v-model:open="approvalModalVisible"
        title="减免申请审批"
        @ok="handleApprovalSubmit"
        @cancel="approvalModalVisible = false"
      >
        <a-form ref="approvalFormRef" :model="approvalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="审批结果" name="result" :rules="[{ required: true, message: '请选择审批结果' }]">
            <a-radio-group v-model:value="approvalForm.result">
              <a-radio value="approved">批准</a-radio>
              <a-radio value="rejected">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="approvalForm.result === 'approved'"
            label="调整减免金额" 
            name="adjustedAmount"
          >
            <a-input-number
              v-model:value="approvalForm.adjustedAmount"
              :min="0"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="可调整减免金额"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="审批意见" name="comment" :rules="[{ required: true, message: '请输入审批意见' }]">
            <a-textarea v-model:value="approvalForm.comment" :rows="4" placeholder="请输入审批意见" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量审批弹窗 -->
      <a-modal
        v-model:open="batchApprovalModalVisible"
        title="批量审批"
        width="700px"
        @ok="handleBatchApprovalSubmit"
        @cancel="batchApprovalModalVisible = false"
      >
        <a-form ref="batchApprovalFormRef" :model="batchApprovalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选中数量">
            <span>已选择 {{ selectedRowKeys.length }} 个减免申请</span>
          </a-form-item>
          
          <a-form-item label="批量操作" name="batchAction" :rules="[{ required: true, message: '请选择批量操作' }]">
            <a-radio-group v-model:value="batchApprovalForm.batchAction">
              <a-radio value="approve">批量批准</a-radio>
              <a-radio value="reject">批量拒绝</a-radio>
              <a-radio value="transfer">批量转审</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="batchApprovalForm.batchAction === 'approve'"
            label="统一减免比例"
            name="uniformRatio"
          >
            <a-input-number
              v-model:value="batchApprovalForm.uniformRatio"
              :min="0"
              :max="100"
              :precision="2"
              addon-after="%"
              placeholder="可设置统一减免比例"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item 
            v-if="batchApprovalForm.batchAction === 'transfer'"
            label="转审人员"
            name="transferTo"
            :rules="[{ required: true, message: '请选择转审人员' }]"
          >
            <a-select v-model:value="batchApprovalForm.transferTo" placeholder="请选择转审人员">
              <a-select-option value="manager1">张经理</a-select-option>
              <a-select-option value="manager2">李经理</a-select-option>
              <a-select-option value="manager3">王经理</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="批量意见" name="batchComment" :rules="[{ required: true, message: '请输入批量意见' }]">
            <a-textarea v-model:value="batchApprovalForm.batchComment" :rows="4" placeholder="请输入批量处理意见" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 减免效果分析弹窗 -->
      <a-modal
        v-model:open="analysisModalVisible"
        title="减免效果分析"
        width="1200px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="overview" tab="总体概览">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card>
                  <a-statistic title="总减免金额" :value="analysisData.totalReductionAmount" precision="2" prefix="¥" />
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card>
                  <a-statistic title="平均减免率" :value="analysisData.avgReductionRate" precision="2" suffix="%" />
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card>
                  <a-statistic title="回收率提升" :value="analysisData.recoveryRateImprovement" precision="2" suffix="%" />
                </a-card>
              </a-col>
            </a-row>
            
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <a-card title="减免类型分布">
                  <div id="reduction-type-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="月度减免趋势">
                  <div id="reduction-trend-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="efficiency" tab="效率分析">
            <a-table
              :columns="efficiencyColumns"
              :data-source="efficiencyData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'reductionRate'">
                  <a-progress :percent="record.reductionRate" size="small" />
                </template>
                <template v-if="column.key === 'recoveryRate'">
                  <a-progress :percent="record.recoveryRate" size="small" />
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="risk" tab="风险评估">
            <a-alert 
              message="减免风险提醒" 
              description="当前减免率较高，请注意控制减免风险，避免影响整体回收效果。"
              type="warning" 
              show-icon 
              style="margin-bottom: 16px;"
            />
            
            <a-descriptions title="风险指标" bordered>
              <a-descriptions-item label="高风险减免数">{{ analysisData.highRiskCount }}</a-descriptions-item>
              <a-descriptions-item label="异常减免率">{{ analysisData.abnormalRateCount }}</a-descriptions-item>
              <a-descriptions-item label="风险等级">
                <a-tag color="orange">中等风险</a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane key="comparison" tab="同比分析">
            <a-row :gutter="16">
              <a-col :span="12">
                <div id="reduction-comparison-chart" style="height: 350px;"></div>
              </a-col>
              <a-col :span="12">
                <div id="recovery-comparison-chart" style="height: 350px;"></div>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 减免模板弹窗 -->
      <a-modal
        v-model:open="templateModalVisible"
        title="减免模板管理"
        width="800px"
        @ok="handleTemplateSubmit"
        @cancel="templateModalVisible = false"
      >
        <a-table
          :columns="templateColumns"
          :data-source="templateList"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'conditions'">
              <a-tag v-for="condition in record.conditions" :key="condition">{{ condition }}</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editTemplate(record)">编辑</a-button>
                <a-button type="link" size="small" @click="applyTemplate(record)">应用</a-button>
                <a-button type="link" size="small" danger @click="deleteTemplate(record)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        
        <a-divider>新建模板</a-divider>
        <a-form :model="newTemplate" layout="inline">
          <a-form-item label="模板名称">
            <a-input v-model:value="newTemplate.name" placeholder="请输入模板名称" />
          </a-form-item>
          <a-form-item label="减免类型">
            <a-select v-model:value="newTemplate.type" placeholder="请选择类型">
              <a-select-option value="principal">本金减免</a-select-option>
              <a-select-option value="interest">利息减免</a-select-option>
              <a-select-option value="penalty">违约金减免</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="减免比例">
            <a-input-number v-model:value="newTemplate.ratio" :min="0" :max="100" addon-after="%" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="addTemplate">添加模板</a-button>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- AI减免建议弹窗 -->
      <a-modal
        v-model:open="aiRecommendModalVisible"
        title="AI减免建议"
        width="1200px"
        :footer="null"
      >
        <a-tabs v-model:activeKey="aiActiveKey">
          <a-tab-pane key="analysis" tab="智能分析">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="客户画像分析" :bordered="false">
                  <div class="customer-profile">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="信用评分">
                        <a-progress 
                          :percent="aiAnalysis.creditScore" 
                          :stroke-color="getCreditColor(aiAnalysis.creditScore)"
                          size="small"
                        />
                      </a-descriptions-item>
                      <a-descriptions-item label="还款能力">
                        <a-rate :value="aiAnalysis.paymentAbility" disabled />
                      </a-descriptions-item>
                      <a-descriptions-item label="还款意愿">
                        <a-rate :value="aiAnalysis.paymentWillingness" disabled />
                      </a-descriptions-item>
                      <a-descriptions-item label="历史表现">
                        <a-tag :color="aiAnalysis.historyPerformance.color">
                          {{ aiAnalysis.historyPerformance.text }}
                        </a-tag>
                      </a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="16">
                <a-card title="AI减免方案推荐" :bordered="false">
                  <a-list
                    :data-source="aiRecommendations"
                    :split="false"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: item.color }">
                              <template #icon>
                                <component :is="item.icon" />
                              </template>
                            </a-avatar>
                          </template>
                          <template #title>
                            {{ item.title }}
                            <a-tag :color="item.tagColor" style="margin-left: 8px;">{{ item.tag }}</a-tag>
                          </template>
                          <template #description>
                            <div>{{ item.description }}</div>
                            <div class="recommendation-metrics">
                              <span>减免比例: <strong>{{ item.reductionRate }}%</strong></span>
                              <span>预计回收率: <strong>{{ item.expectedRecovery }}%</strong></span>
                              <span>风险等级: <a-tag :color="item.riskColor">{{ item.riskLevel }}</a-tag></span>
                            </div>
                          </template>
                        </a-list-item-meta>
                        <template #actions>
                          <a-button type="link" @click="applyAIRecommendation(item)">应用方案</a-button>
                        </template>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="simulator" tab="减免模拟器">
            <a-row :gutter="16">
              <a-col :span="10">
                <a-card title="参数设置" :bordered="false">
                  <a-form :model="simulator" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                    <a-form-item label="原始金额">
                      <a-input-number
                        v-model:value="simulator.originalAmount"
                        :min="0"
                        :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                        style="width: 100%"
                        @change="simulateReduction"
                      />
                    </a-form-item>
                    <a-form-item label="减免类型">
                      <a-select v-model:value="simulator.reductionType" @change="simulateReduction">
                        <a-select-option value="principal">本金减免</a-select-option>
                        <a-select-option value="interest">利息减免</a-select-option>
                        <a-select-option value="penalty">违约金减免</a-select-option>
                        <a-select-option value="total">全额减免</a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item label="减免比例">
                      <a-slider
                        v-model:value="simulator.reductionRate"
                        :min="0"
                        :max="100"
                        :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
                        @change="simulateReduction"
                      />
                    </a-form-item>
                    <a-form-item label="还款方式">
                      <a-radio-group v-model:value="simulator.paymentMethod" @change="simulateReduction">
                        <a-radio value="lump">一次性</a-radio>
                        <a-radio value="installment">分期</a-radio>
                      </a-radio-group>
                    </a-form-item>
                    <div v-if="simulator.paymentMethod === 'installment'">
                      <a-form-item label="分期期数">
                        <a-select v-model:value="simulator.installmentPeriods" @change="simulateReduction">
                          <a-select-option :value="3">3期</a-select-option>
                          <a-select-option :value="6">6期</a-select-option>
                          <a-select-option :value="12">12期</a-select-option>
                          <a-select-option :value="24">24期</a-select-option>
                        </a-select>
                      </a-form-item>
                    </div>
                  </a-form>
                </a-card>
              </a-col>
              <a-col :span="14">
                <a-card title="模拟结果" :bordered="false">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-statistic
                        title="减免后金额"
                        :value="simulationResult.finalAmount"
                        :precision="2"
                        prefix="¥"
                        :value-style="{ color: '#52c41a' }"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="减免金额"
                        :value="simulationResult.reductionAmount"
                        :precision="2"
                        prefix="¥"
                        :value-style="{ color: '#ff4d4f' }"
                      />
                    </a-col>
                  </a-row>
                  <a-divider />
                  <div v-if="simulator.paymentMethod === 'installment'">
                    <h4>分期还款计划</h4>
                    <a-table
                      :columns="simulationPlanColumns"
                      :data-source="simulationResult.installmentPlan"
                      :pagination="false"
                      size="small"
                    />
                  </div>
                  <div class="impact-analysis">
                    <h4>影响分析</h4>
                    <a-descriptions :column="2" size="small">
                      <a-descriptions-item label="预计回收率">
                        <a-progress :percent="simulationResult.expectedRecoveryRate" size="small" />
                      </a-descriptions-item>
                      <a-descriptions-item label="风险等级">
                        <a-tag :color="simulationResult.riskLevel.color">{{ simulationResult.riskLevel.text }}</a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="利润影响">
                        <span :style="{ color: simulationResult.profitImpact < 0 ? '#ff4d4f' : '#52c41a' }">
                          {{ simulationResult.profitImpact > 0 ? '+' : '' }}{{ simulationResult.profitImpact }}%
                        </span>
                      </a-descriptions-item>
                      <a-descriptions-item label="建议等级">
                        <a-rate :value="simulationResult.recommendLevel" disabled />
                      </a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="batch" tab="批量减免">
            <a-card title="批量减免客户推荐" :bordered="false">
              <div class="batch-filter">
                <a-space>
                  <a-select 
                    v-model:value="batchFilter.type" 
                    placeholder="选择推荐类型"
                    style="width: 200px;"
                  >
                    <a-select-option value="highValue">高价值客户</a-select-option>
                    <a-select-option value="longOverdue">长期逾期</a-select-option>
                    <a-select-option value="willingToPay">有还款意愿</a-select-option>
                    <a-select-option value="economicHardship">经济困难</a-select-option>
                  </a-select>
                  <a-button type="primary" @click="refreshBatchRecommendations">
                    <SyncOutlined />
                    刷新推荐
                  </a-button>
                </a-space>
              </div>
              <a-table
                :columns="batchRecommendColumns"
                :data-source="batchRecommendations"
                :row-selection="{ selectedRowKeys: batchSelectedKeys, onChange: onBatchSelectChange }"
                :pagination="false"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'recommendScore'">
                    <a-progress 
                      :percent="record.recommendScore" 
                      :stroke-color="getScoreColor(record.recommendScore)"
                      size="small"
                    />
                  </template>
                  <template v-if="column.key === 'suggestedReduction'">
                    <a-tag color="green">{{ record.suggestedReduction }}%</a-tag>
                  </template>
                </template>
              </a-table>
              <div class="batch-actions">
                <a-button type="primary" @click="applyBatchReduction" :disabled="batchSelectedKeys.length === 0">
                  <CheckOutlined />
                  应用批量减免
                </a-button>
                <span style="margin-left: 16px;">已选择 {{ batchSelectedKeys.length }} 个客户</span>
              </div>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 规则配置弹窗 -->
      <a-modal
        v-model:open="ruleConfigModalVisible"
        title="减免规则配置"
        width="1000px"
        @ok="handleRuleConfigSubmit"
        @cancel="ruleConfigModalVisible = false"
      >
        <a-tabs>
          <a-tab-pane key="basic" tab="基本规则">
            <a-form :model="ruleConfig" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-item label="最大减免比例">
                <a-input-number
                  v-model:value="ruleConfig.maxReductionRate"
                  :min="0"
                  :max="100"
                  addon-after="%"
                  style="width: 200px;"
                />
              </a-form-item>
              <a-form-item label="单笔最大减免金额">
                <a-input-number
                  v-model:value="ruleConfig.maxReductionAmount"
                  :min="0"
                  :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                  :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                  style="width: 200px;"
                />
              </a-form-item>
              <a-form-item label="最小逾期天数">
                <a-input-number
                  v-model:value="ruleConfig.minOverdueDays"
                  :min="0"
                  addon-after="天"
                  style="width: 200px;"
                />
              </a-form-item>
              <a-form-item label="审批级别">
                <a-checkbox-group v-model:value="ruleConfig.approvalLevels">
                  <a-checkbox value="level1">一级审批 (≤¥5万)</a-checkbox>
                  <a-checkbox value="level2">二级审批 (¥5-20万)</a-checkbox>
                  <a-checkbox value="level3">三级审批 (>¥20万)</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-form>
          </a-tab-pane>
          
          <a-tab-pane key="advanced" tab="高级规则">
            <a-card title="条件规则编辑器" :bordered="false">
              <div class="rule-editor">
                <a-space direction="vertical" style="width: 100%;">
                  <div v-for="(rule, index) in advancedRules" :key="index" class="rule-item">
                    <a-row :gutter="8" align="middle">
                      <a-col :span="4">
                        <a-select v-model:value="rule.field" placeholder="选择字段">
                          <a-select-option value="overdueDays">逾期天数</a-select-option>
                          <a-select-option value="debtAmount">债务金额</a-select-option>
                          <a-select-option value="creditScore">信用评分</a-select-option>
                          <a-select-option value="paymentHistory">还款历史</a-select-option>
                        </a-select>
                      </a-col>
                      <a-col :span="4">
                        <a-select v-model:value="rule.operator" placeholder="操作符">
                          <a-select-option value=">">大于</a-select-option>
                          <a-select-option value="<">小于</a-select-option>
                          <a-select-option value="=">等于</a-select-option>
                          <a-select-option value="between">介于</a-select-option>
                        </a-select>
                      </a-col>
                      <a-col :span="6">
                        <a-input v-model:value="rule.value" placeholder="值" />
                      </a-col>
                      <a-col :span="4">
                        <a-select v-model:value="rule.action" placeholder="执行动作">
                          <a-select-option value="approve">自动批准</a-select-option>
                          <a-select-option value="reject">自动拒绝</a-select-option>
                          <a-select-option value="manual">人工审核</a-select-option>
                        </a-select>
                      </a-col>
                      <a-col :span="4">
                        <a-input-number 
                          v-model:value="rule.reductionRate" 
                          :min="0" 
                          :max="100" 
                          addon-after="%"
                          placeholder="减免率"
                        />
                      </a-col>
                      <a-col :span="2">
                        <a-button type="text" danger @click="removeRule(index)">
                          删除
                        </a-button>
                      </a-col>
                    </a-row>
                  </div>
                  <a-button type="dashed" block @click="addRule">
                    <PlusOutlined />
                    添加规则
                  </a-button>
                </a-space>
              </div>
            </a-card>
          </a-tab-pane>
          
          <a-tab-pane key="notification" tab="通知设置">
            <a-form :model="notificationConfig" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-item label="通知方式">
                <a-checkbox-group v-model:value="notificationConfig.methods">
                  <a-checkbox value="sms"><MessageOutlined /> 短信通知</a-checkbox>
                  <a-checkbox value="email"><MailOutlined /> 邮件通知</a-checkbox>
                  <a-checkbox value="app">APP推送</a-checkbox>
                  <a-checkbox value="wechat">微信通知</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="通知时机">
                <a-checkbox-group v-model:value="notificationConfig.timing">
                  <a-row>
                    <a-col :span="12">
                      <a-checkbox value="submit">申请提交时</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="approve">审批通过时</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="reject">审批拒绝时</a-checkbox>
                    </a-col>
                    <a-col :span="12">
                      <a-checkbox value="execute">执行完成时</a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item label="通知对象">
                <a-checkbox-group v-model:value="notificationConfig.recipients">
                  <a-checkbox value="customer">客户</a-checkbox>
                  <a-checkbox value="collector">催收员</a-checkbox>
                  <a-checkbox value="manager">主管</a-checkbox>
                  <a-checkbox value="finance">财务</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, h, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  CheckOutlined,
  DownloadOutlined,
  DownOutlined,
  UpOutlined,
  UploadOutlined,
  SearchOutlined,
  ReloadOutlined,
  BarChartOutlined,
  FileTextOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  DollarCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  PercentageOutlined,
  RobotOutlined,
  BulbOutlined,
  LineChartOutlined,
  SafetyOutlined,
  FundOutlined,
  FileDoneOutlined,
  AlertOutlined,
  AuditOutlined,
  TeamOutlined,
  HistoryOutlined,
  PrinterOutlined,
  MailOutlined,
  MessageOutlined,
  PhoneOutlined,
  ApiOutlined,
  CalculatorOutlined,
  ThunderboltOutlined,
  RiseOutlined,
  FallOutlined,
  SyncOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const showAdvanced = ref(false)

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 统计数据
const stats = reactive({
  totalApplications: 156,
  pendingApprovals: 23,
  approvedApplications: 89,
  totalReductionAmount: 2458600
})

// 搜索表单
const searchForm = reactive({
  customerName: '',
  caseNumber: '',
  status: undefined,
  reductionType: undefined,
  applicationDateRange: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '申请编号',
    dataIndex: 'applicationId',
    key: 'applicationId',
    width: 120
  },
  {
    title: '客户信息',
    key: 'customerInfo',
    width: 150
  },
  {
    title: '案件信息',
    key: 'caseInfo',
    width: 150
  },
  {
    title: '减免信息',
    key: 'reductionInfo',
    width: 180
  },
  {
    title: '申请状态',
    key: 'status',
    width: 100
  },
  {
    title: '紧急程度',
    key: 'urgency',
    width: 80
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    key: 'applicant',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'applicationDate',
    key: 'applicationDate',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模拟数据
const reductionList = ref([
  {
    id: 1,
    applicationId: 'RA2024001',
    customerName: '张三',
    customerPhone: '13800138001',
    caseNumber: '*********',
    originalAmount: 100000,
    reductionAmount: 30000,
    reductionRatio: 30,
    reductionType: 'principal',
    status: 'pending',
    urgency: 'high',
    applicant: '李催收',
    applicationDate: '2024-01-15',
    reason: '客户经济困难，家庭收入骤减，诚意还款但能力有限，申请减免部分本金。',
    attachments: [
      { name: '收入证明.pdf', size: '2.3MB', uploadTime: '2024-01-15 09:30' },
      { name: '医疗费用单据.jpg', size: '1.8MB', uploadTime: '2024-01-15 09:35' }
    ],
    approvalHistory: [
      { id: 1, action: '提交申请', approver: '李催收', comment: '客户主动申请减免', time: '2024-01-15 09:00' }
    ]
  },
  {
    id: 2,
    applicationId: 'RA2024002',
    customerName: '李四',
    customerPhone: '13800138002',
    caseNumber: '*********',
    originalAmount: 80000,
    reductionAmount: 20000,
    reductionRatio: 25,
    reductionType: 'interest',
    status: 'approved',
    urgency: 'medium',
    applicant: '王催收',
    applicationDate: '2024-01-10',
    reason: '客户积极配合催收工作，同意按照减免后金额一次性还款。',
    attachments: [],
    approvalHistory: [
      { id: 1, action: '提交申请', approver: '王催收', comment: '客户主动申请减免', time: '2024-01-10 14:00' },
      { id: 2, action: '初审通过', approver: '张主管', comment: '符合减免条件', time: '2024-01-11 10:00' },
      { id: 3, action: '终审批准', approver: '刘经理', comment: '同意减免申请', time: '2024-01-12 16:00' }
    ]
  }
])

// 弹窗状态
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const approvalModalVisible = ref(false)
const batchApprovalModalVisible = ref(false)
const analysisModalVisible = ref(false)
const templateModalVisible = ref(false)
const aiRecommendModalVisible = ref(false)
const ruleConfigModalVisible = ref(false)

// Tab激活键
const aiActiveKey = ref('analysis')

// 当前选中的减免申请
const currentReduction = ref(null)

// 创建表单
const createForm = reactive({
  customerId: undefined,
  caseId: undefined,
  originalAmount: undefined,
  reductionType: undefined,
  reductionAmount: undefined,
  reductionRatio: undefined,
  urgency: undefined,
  reason: '',
  attachmentList: []
})

// 审批表单
const approvalForm = reactive({
  result: undefined,
  adjustedAmount: undefined,
  comment: ''
})

// 批量审批表单
const batchApprovalForm = reactive({
  batchAction: undefined,
  uniformRatio: undefined,
  transferTo: undefined,
  batchComment: ''
})

// 分析数据
const analysisData = reactive({
  totalReductionAmount: 2458600,
  avgReductionRate: 28.5,
  recoveryRateImprovement: 15.2,
  highRiskCount: 5,
  abnormalRateCount: 3
})

// 效率分析数据
const efficiencyData = ref([
  { period: '2024年1月', reductionCount: 45, reductionAmount: 856000, reductionRate: 32, recoveryRate: 78 },
  { period: '2024年2月', reductionCount: 38, reductionAmount: 672000, reductionRate: 28, recoveryRate: 82 },
  { period: '2024年3月', reductionCount: 52, reductionAmount: 930000, reductionRate: 35, recoveryRate: 75 }
])

const efficiencyColumns = [
  { title: '时间周期', dataIndex: 'period', key: 'period' },
  { title: '减免笔数', dataIndex: 'reductionCount', key: 'reductionCount' },
  { title: '减免金额', dataIndex: 'reductionAmount', key: 'reductionAmount' },
  { title: '减免率', key: 'reductionRate' },
  { title: '回收率', key: 'recoveryRate' }
]

// 模板数据
const templateList = ref([
  {
    id: 1,
    name: '困难客户减免',
    type: 'principal',
    ratio: 30,
    conditions: ['逾期>90天', '金额<10万', '有收入证明']
  },
  {
    id: 2,
    name: '快速结清减免',
    type: 'interest',
    ratio: 50,
    conditions: ['一次性还款', '金额>5万']
  }
])

const templateColumns = [
  { title: '模板名称', dataIndex: 'name', key: 'name' },
  { title: '减免类型', dataIndex: 'type', key: 'type' },
  { title: '减免比例', dataIndex: 'ratio', key: 'ratio' },
  { title: '适用条件', key: 'conditions' },
  { title: '操作', key: 'action' }
]

const newTemplate = reactive({
  name: '',
  type: undefined,
  ratio: undefined
})

// AI分析数据
const aiAnalysis = reactive({
  creditScore: 75,
  paymentAbility: 3,
  paymentWillingness: 4,
  historyPerformance: { text: '良好', color: 'green' }
})

// AI推荐方案
const aiRecommendations = ref([
  {
    id: 1,
    title: '标准减免方案',
    description: '基于客户信用良好且有稳定还款记录，建议给予适度减免以促进快速回收',
    tag: 'AI推荐',
    tagColor: 'green',
    color: '#52c41a',
    icon: 'SafetyOutlined',
    reductionRate: 30,
    expectedRecovery: 85,
    riskLevel: '低风险',
    riskColor: 'green'
  },
  {
    id: 2,
    title: '激进减免方案',
    description: '考虑到客户确实存在经济困难，建议给予较大幅度减免以实现部分回收',
    tag: '高风险',
    tagColor: 'orange',
    color: '#fa8c16',
    icon: 'ThunderboltOutlined',
    reductionRate: 50,
    expectedRecovery: 70,
    riskLevel: '中风险',
    riskColor: 'orange'
  },
  {
    id: 3,
    title: '保守减免方案',
    description: '维持较低减免比例，通过分期等方式逐步回收，最大化回收金额',
    tag: '稳健',
    tagColor: 'blue',
    color: '#1890ff',
    icon: 'BulbOutlined',
    reductionRate: 15,
    expectedRecovery: 95,
    riskLevel: '极低风险',
    riskColor: 'blue'
  }
])

// 减免模拟器
const simulator = reactive({
  originalAmount: 100000,
  reductionType: 'principal',
  reductionRate: 30,
  paymentMethod: 'lump',
  installmentPeriods: 12
})

// 模拟结果
const simulationResult = reactive({
  finalAmount: 70000,
  reductionAmount: 30000,
  expectedRecoveryRate: 85,
  riskLevel: { text: '低风险', color: 'green' },
  profitImpact: -15,
  recommendLevel: 4,
  installmentPlan: []
})

// 模拟计划列
const simulationPlanColumns = [
  { title: '期数', dataIndex: 'period', key: 'period', width: 60 },
  { title: '应还金额', dataIndex: 'amount', key: 'amount' },
  { title: '到期日期', dataIndex: 'dueDate', key: 'dueDate' },
  { title: '累计还款', dataIndex: 'cumulative', key: 'cumulative' }
]

// 批量筛选
const batchFilter = reactive({
  type: 'highValue'
})

// 批量选中
const batchSelectedKeys = ref([])

// 批量推荐客户
const batchRecommendations = ref([
  {
    id: 1,
    customerName: '张三',
    caseNumber: '*********',
    debtAmount: 150000,
    overdueDays: 180,
    recommendScore: 85,
    suggestedReduction: 30,
    reason: '有稳定收入，历史还款记录良好'
  },
  {
    id: 2,
    customerName: '李四',
    caseNumber: '*********',
    debtAmount: 80000,
    overdueDays: 120,
    recommendScore: 72,
    suggestedReduction: 25,
    reason: '主动联系要求减免，有还款意愿'
  },
  {
    id: 3,
    customerName: '王五',
    caseNumber: 'CS2024003',
    debtAmount: 200000,
    overdueDays: 360,
    recommendScore: 68,
    suggestedReduction: 40,
    reason: '长期逾期但近期收入恢复'
  }
])

// 批量推荐列
const batchRecommendColumns = [
  { title: '客户姓名', dataIndex: 'customerName', key: 'customerName' },
  { title: '案件编号', dataIndex: 'caseNumber', key: 'caseNumber' },
  { title: '债务金额', dataIndex: 'debtAmount', key: 'debtAmount', customRender: ({ text }) => `¥${text.toLocaleString()}` },
  { title: '逾期天数', dataIndex: 'overdueDays', key: 'overdueDays' },
  { title: '推荐度', key: 'recommendScore', width: 150 },
  { title: '建议减免', key: 'suggestedReduction' },
  { title: '推荐理由', dataIndex: 'reason', key: 'reason' }
]

// 规则配置
const ruleConfig = reactive({
  maxReductionRate: 50,
  maxReductionAmount: 100000,
  minOverdueDays: 90,
  approvalLevels: ['level1', 'level2']
})

// 高级规则
const advancedRules = ref([
  {
    field: 'overdueDays',
    operator: '>',
    value: '180',
    action: 'manual',
    reductionRate: 30
  },
  {
    field: 'creditScore',
    operator: '>',
    value: '700',
    action: 'approve',
    reductionRate: 25
  }
])

// 通知配置
const notificationConfig = reactive({
  methods: ['sms', 'app'],
  timing: ['submit', 'approve'],
  recipients: ['customer', 'collector']
})

// 客户列表
const customers = ref([
  { id: 1, name: '张三', phone: '13800138001' },
  { id: 2, name: '李四', phone: '13800138002' },
  { id: 3, name: '王五', phone: '13800138003' }
])

// 案件列表
const customerCases = ref([])

// 监听客户选择
watch(() => createForm.customerId, (customerId) => {
  if (customerId) {
    customerCases.value = [
      { id: 1, caseNumber: '*********', amount: 100000 },
      { id: 2, caseNumber: '*********', amount: 80000 }
    ]
  } else {
    customerCases.value = []
  }
  createForm.caseId = undefined
  createForm.originalAmount = undefined
})

// 监听减免金额变化，自动计算减免比例
watch(() => createForm.reductionAmount, () => {
  calculateReductionRatio()
})

// 状态相关方法
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    approved: 'green',
    rejected: 'red',
    executed: 'blue'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    executed: '已执行'
  }
  return texts[status] || '未知'
}

const getReductionTypeColor = (type) => {
  const colors = {
    principal: 'blue',
    interest: 'green',
    penalty: 'orange',
    total: 'red'
  }
  return colors[type] || 'default'
}

const getReductionTypeText = (type) => {
  const texts = {
    principal: '本金减免',
    interest: '利息减免',
    penalty: '违约金减免',
    total: '全额减免'
  }
  return texts[type] || '未知'
}

const getUrgencyColor = (urgency) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colors[urgency] || 'default'
}

const getUrgencyText = (urgency) => {
  const texts = {
    low: '一般',
    medium: '紧急',
    high: '特急'
  }
  return texts[urgency] || '未知'
}

const getHistoryColor = (action) => {
  const colors = {
    '提交申请': 'blue',
    '初审通过': 'green',
    '终审批准': 'green',
    '审批拒绝': 'red',
    '执行完成': 'purple'
  }
  return colors[action] || 'default'
}

// 事件处理方法
const handleSearch = () => {
  console.log('搜索参数:', searchForm)
  message.success('搜索完成')
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 快速搜索
const handleQuickSearch = () => {
  handleSearch()
}

const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const showCreateModal = () => {
  createModalVisible.value = true
}

const onCaseSelect = (caseId) => {
  const selectedCase = customerCases.value.find(c => c.id === caseId)
  if (selectedCase) {
    createForm.originalAmount = selectedCase.amount
  }
}

const calculateReductionRatio = () => {
  if (createForm.originalAmount && createForm.reductionAmount) {
    createForm.reductionRatio = ((createForm.reductionAmount / createForm.originalAmount) * 100).toFixed(2)
  } else {
    createForm.reductionRatio = undefined
  }
}

const handleCreateSubmit = () => {
  console.log('创建减免申请:', createForm)
  message.success('减免申请创建成功')
  createModalVisible.value = false
}

const viewDetails = (record) => {
  currentReduction.value = record
  detailModalVisible.value = true
}

const approveApplication = (record) => {
  currentReduction.value = record
  approvalForm.adjustedAmount = record.reductionAmount
  approvalModalVisible.value = true
}

const handleApprovalSubmit = () => {
  console.log('审批结果:', approvalForm)
  message.success('审批完成')
  approvalModalVisible.value = false
}

const executeReduction = (record) => {
  message.success(`减免申请 ${record.applicationId} 执行成功`)
}

const viewApprovalHistory = (record) => {
  currentReduction.value = record
  detailModalVisible.value = true
}

const generateContract = (record) => {
  message.success('正在生成减免协议...')
}

const printCertificate = (record) => {
  message.success('正在打印减免凭证...')
}

const withdrawApplication = (record) => {
  message.success(`减免申请 ${record.applicationId} 已撤回`)
}

const handleBatchApproval = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要审批的申请')
    return
  }
  batchApprovalModalVisible.value = true
}

const handleBatchApprovalSubmit = () => {
  console.log('批量审批:', batchApprovalForm)
  message.success(`已${batchApprovalForm.batchAction === 'approve' ? '批准' : batchApprovalForm.batchAction === 'reject' ? '拒绝' : '转审'} ${selectedRowKeys.value.length} 个申请`)
  batchApprovalModalVisible.value = false
  selectedRowKeys.value = []
}


const showTemplateModal = () => {
  templateModalVisible.value = true
}

const handleTemplateSubmit = () => {
  templateModalVisible.value = false
}

const editTemplate = (record) => {
  message.info(`编辑模板: ${record.name}`)
}

const applyTemplate = (record) => {
  message.success(`已应用模板: ${record.name}`)
}

const deleteTemplate = (record) => {
  message.success(`已删除模板: ${record.name}`)
}

const addTemplate = () => {
  if (!newTemplate.name || !newTemplate.type || !newTemplate.ratio) {
    message.warning('请填写完整的模板信息')
    return
  }
  message.success('模板添加成功')
  Object.keys(newTemplate).forEach(key => {
    newTemplate[key] = key === 'ratio' ? undefined : ''
  })
}

const exportData = () => {
  message.success('数据导出成功')
}

const downloadFile = (file) => {
  message.success(`正在下载 ${file.name}`)
}

const filterCustomerOption = (input, option) => {
  return option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 新增方法
const showAIRecommendModal = () => {
  aiRecommendModalVisible.value = true
}

const showRuleConfigModal = () => {
  ruleConfigModalVisible.value = true
}

const getCreditColor = (score) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#1890ff'
  if (score >= 40) return '#fa8c16'
  return '#ff4d4f'
}

const applyAIRecommendation = (recommendation) => {
  createForm.reductionRatio = recommendation.reductionRate
  createForm.reductionType = 'principal'
  aiRecommendModalVisible.value = false
  createModalVisible.value = true
  message.success(`已应用AI推荐方案：${recommendation.title}`)
}

const simulateReduction = () => {
  const { originalAmount, reductionRate, paymentMethod, installmentPeriods } = simulator
  const reductionAmount = originalAmount * reductionRate / 100
  const finalAmount = originalAmount - reductionAmount
  
  simulationResult.reductionAmount = reductionAmount
  simulationResult.finalAmount = finalAmount
  
  // 计算预期回收率
  if (reductionRate <= 20) {
    simulationResult.expectedRecoveryRate = 90 + Math.random() * 10
  } else if (reductionRate <= 40) {
    simulationResult.expectedRecoveryRate = 75 + Math.random() * 15
  } else {
    simulationResult.expectedRecoveryRate = 60 + Math.random() * 15
  }
  
  // 计算风险等级
  if (reductionRate <= 20) {
    simulationResult.riskLevel = { text: '低风险', color: 'green' }
  } else if (reductionRate <= 40) {
    simulationResult.riskLevel = { text: '中风险', color: 'orange' }
  } else {
    simulationResult.riskLevel = { text: '高风险', color: 'red' }
  }
  
  // 计算利润影响
  simulationResult.profitImpact = -reductionRate * 0.5
  
  // 计算推荐等级
  simulationResult.recommendLevel = Math.max(1, Math.min(5, Math.round(5 - reductionRate / 20)))
  
  // 生成分期计划
  if (paymentMethod === 'installment') {
    simulationResult.installmentPlan = []
    const monthlyAmount = finalAmount / installmentPeriods
    let cumulative = 0
    
    for (let i = 1; i <= installmentPeriods; i++) {
      cumulative += monthlyAmount
      const dueDate = new Date()
      dueDate.setMonth(dueDate.getMonth() + i)
      
      simulationResult.installmentPlan.push({
        period: i,
        amount: `¥${monthlyAmount.toFixed(2)}`,
        dueDate: dueDate.toISOString().split('T')[0],
        cumulative: `¥${cumulative.toFixed(2)}`
      })
    }
  }
}

const getScoreColor = (score) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#1890ff'
  return '#fa8c16'
}

const onBatchSelectChange = (selectedKeys) => {
  batchSelectedKeys.value = selectedKeys
}

const refreshBatchRecommendations = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('推荐列表已刷新')
  }, 1000)
}

const applyBatchReduction = () => {
  const selectedCount = batchSelectedKeys.value.length
  Modal.confirm({
    title: '批量减免确认',
    icon: h(ExclamationCircleOutlined),
    content: `确定要对选中的 ${selectedCount} 个客户应用批量减免吗？`,
    onOk() {
      message.success(`已对 ${selectedCount} 个客户创建减免申请`)
      batchSelectedKeys.value = []
      aiRecommendModalVisible.value = false
    }
  })
}

const handleRuleConfigSubmit = () => {
  message.success('规则配置已保存')
  ruleConfigModalVisible.value = false
}

const addRule = () => {
  advancedRules.value.push({
    field: undefined,
    operator: undefined,
    value: '',
    action: undefined,
    reductionRate: undefined
  })
}

const removeRule = (index) => {
  advancedRules.value.splice(index, 1)
}

const showAnalysisModal = () => {
  analysisModalVisible.value = true
  setTimeout(() => {
    initAnalysisCharts()
  }, 100)
}

// 初始化分析图表
const initAnalysisCharts = () => {
  // 减免类型分布图
  const typeChart = document.getElementById('reduction-type-chart')
  if (typeChart) {
    const chart = echarts.init(typeChart)
    const option = {
      title: {
        text: '减免类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 60
      },
      series: [
        {
          name: '减免类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 45, name: '本金减免', itemStyle: { color: '#1890ff' } },
            { value: 32, name: '利息减免', itemStyle: { color: '#52c41a' } },
            { value: 18, name: '违约金减免', itemStyle: { color: '#fa8c16' } },
            { value: 5, name: '全额减免', itemStyle: { color: '#ff4d4f' } }
          ]
        }
      ]
    }
    chart.setOption(option)
  }

  // 月度减免趋势图
  const trendChart = document.getElementById('reduction-trend-chart')
  if (trendChart) {
    const chart = echarts.init(trendChart)
    const option = {
      title: {
        text: '月度减免趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['申请数量', '批准数量', '减免金额'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: 80,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: [
        {
          type: 'value',
          name: '数量',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '金额（万元）',
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: '申请数量',
          type: 'bar',
          data: [30, 35, 42, 38, 45, 52],
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '批准数量',
          type: 'bar',
          data: [18, 21, 25, 23, 27, 31],
          itemStyle: { color: '#52c41a' }
        },
        {
          name: '减免金额',
          type: 'line',
          yAxisIndex: 1,
          data: [120, 145, 168, 152, 180, 205],
          smooth: true,
          itemStyle: { color: '#fa8c16' }
        }
      ]
    }
    chart.setOption(option)
  }

  // 同比分析图表
  const comparisonChart = document.getElementById('reduction-comparison-chart')
  if (comparisonChart) {
    const chart = echarts.init(comparisonChart)
    const option = {
      title: {
        text: '减免同比分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['去年同期', '今年'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: 80,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['Q1', 'Q2', 'Q3', 'Q4']
      },
      yAxis: {
        type: 'value',
        name: '减免金额（万元）'
      },
      series: [
        {
          name: '去年同期',
          type: 'bar',
          data: [320, 380, 350, 400],
          itemStyle: { color: '#91d5ff' }
        },
        {
          name: '今年',
          type: 'bar',
          data: [380, 420, 390, 450],
          itemStyle: { color: '#1890ff' }
        }
      ]
    }
    chart.setOption(option)
  }

  // 回收对比图
  const recoveryChart = document.getElementById('recovery-comparison-chart')
  if (recoveryChart) {
    const chart = echarts.init(recoveryChart)
    const option = {
      title: {
        text: '减免前后回收率对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: 60,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['无减免', '10-20%', '20-30%', '30-40%', '40-50%', '>50%']
      },
      yAxis: {
        type: 'value',
        name: '回收率（%）',
        min: 0,
        max: 100
      },
      series: [
        {
          name: '回收率',
          type: 'line',
          data: [45, 65, 78, 82, 75, 68],
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#52c41a' },
              { offset: 1, color: '#52c41a20' }
            ])
          },
          itemStyle: { color: '#52c41a' }
        }
      ]
    }
    chart.setOption(option)
  }

  // 响应式调整
  window.addEventListener('resize', () => {
    const charts = [
      document.getElementById('reduction-type-chart'),
      document.getElementById('reduction-trend-chart'),
      document.getElementById('reduction-comparison-chart'),
      document.getElementById('recovery-comparison-chart')
    ]
    charts.forEach(chartDom => {
      if (chartDom) {
        const chart = echarts.getInstanceByDom(chartDom)
        if (chart) {
          chart.resize()
        }
      }
    })
  })
}

// 初始化
onMounted(() => {
  handleSearch()
  simulateReduction()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.customer-name {
  font-weight: 500;
  color: #1890ff;
}

.customer-phone {
  font-size: 12px;
  color: #666;
}

.case-amount {
  font-size: 12px;
  color: #666;
}

.reduction-amount {
  color: #52c41a;
  font-weight: 500;
}

.reduction-ratio {
  font-size: 12px;
  color: #666;
}

.history-time {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.ant-table {
  background: #fff;
}

.ant-descriptions-title {
  font-weight: 500;
}

/* 统计卡片增强样式 */
.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-change {
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-change.down {
  color: #ff4d4f;
}

.stat-percent {
  color: #1890ff;
  font-weight: 500;
}

.stat-time {
  color: #fa8c16;
  font-weight: 500;
}

/* AI推荐样式 */
.customer-profile {
  padding: 16px;
}

.recommendation-metrics {
  margin-top: 8px;
  display: flex;
  gap: 24px;
  font-size: 12px;
  color: #666;
}

.recommendation-metrics strong {
  color: #1890ff;
}

/* 影响分析样式 */
.impact-analysis {
  margin-top: 16px;
}

.impact-analysis h4 {
  margin-bottom: 12px;
  color: #333;
}

/* 批量操作样式 */
.batch-filter {
  margin-bottom: 16px;
}

.batch-actions {
  margin-top: 16px;
  text-align: center;
}

/* 规则编辑器样式 */
.rule-editor {
  max-height: 400px;
  overflow-y: auto;
}

.rule-item {
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-buttons,
  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
}
/* 增强搜索样式 */
.enhanced-search {
  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-stats {
      display: flex;
      gap: 16px;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
  }

  .action-buttons {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
