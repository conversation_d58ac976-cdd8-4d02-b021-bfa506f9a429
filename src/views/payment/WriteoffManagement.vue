<template>
  <div class="page-container">
    <div class="content-wrapper">
      <h2>核销管理</h2>
      
      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="核销申请数" 
              :value="stats.totalApplications"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>较上月 <span class="stat-change up">+18.5%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="待审批" 
              :value="stats.pendingApprovals" 
              suffix="笔"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>平均审批时间 <span class="stat-time">3.8天</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已核销" 
              :value="stats.approvedApplications" 
              suffix="笔"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>通过率 <span class="stat-percent">62.9%</span></span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="核销金额" 
              :value="stats.totalWriteoffAmount" 
              :precision="2" 
              prefix="¥"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <DollarCircleOutlined />
              </template>
            </a-statistic>
            <div class="stat-footer">
              <span>平均核销率 <span class="stat-percent">78.3%</span></span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索和操作区域 -->
      <a-card class="search-card enhanced-search">
        <template #title>
          <div class="search-header">
            <span>核销管理搜索</span>
            <div class="search-stats">
              <a-statistic
                title="待审批"
                :value="stats.pendingApprovals"
                :value-style="{ color: '#fa8c16', fontSize: '16px' }"
              />
            </div>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="toggleAdvanced">
              {{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}
              <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
            </a-button>
            <a-button type="primary" @click="handleQuickSearch">
              <template #icon><SearchOutlined /></template>
              快速搜索
            </a-button>
          </a-space>
        </template>

        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input v-model:value="searchForm.customerName" placeholder="请输入客户姓名" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input v-model:value="searchForm.caseNumber" placeholder="请输入案件编号" allow-clear>
                  <template #prefix><FileTextOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="申请状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option value="pending">
                    <a-tag color="orange">待审批</a-tag>
                  </a-select-option>
                  <a-select-option value="approved">
                    <a-tag color="green">已批准</a-tag>
                  </a-select-option>
                  <a-select-option value="rejected">
                    <a-tag color="red">已拒绝</a-tag>
                  </a-select-option>
                  <a-select-option value="executed">
                    <a-tag color="blue">已执行</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="核销类型">
                <a-select v-model:value="searchForm.writeoffType" placeholder="请选择类型" allow-clear>
                  <a-select-option value="bad_debt">
                    <a-tag color="red">坏账核销</a-tag>
                  </a-select-option>
                  <a-select-option value="uncollectable">
                    <a-tag color="orange">无法催收</a-tag>
                  </a-select-option>
                  <a-select-option value="legal_limit">
                    <a-tag color="purple">法律时效</a-tag>
                  </a-select-option>
                  <a-select-option value="business">
                    <a-tag color="blue">商业核销</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" v-if="showAdvanced" style="margin-top: 16px;">
            <a-col :span="6">
              <a-form-item label="申请时间">
                <a-range-picker v-model:value="searchForm.applicationDateRange" allow-clear :presets="timePresets" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="核销金额">
                <a-input-group compact>
                  <a-input-number
                    v-model:value="searchForm.amountMin"
                    placeholder="最小金额"
                    :min="0"
                    style="width: 50%"
                  />
                  <a-input-number
                    v-model:value="searchForm.amountMax"
                    placeholder="最大金额"
                    :min="0"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="申请人">
                <a-input v-model:value="searchForm.applicant" placeholder="请输入申请人" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="审批人">
                <a-input v-model:value="searchForm.approver" placeholder="请输入审批人" allow-clear>
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <div class="search-actions">
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <search-outlined />
                    搜索
                  </a-button>
                  <a-button @click="resetSearch">
                    <reload-outlined />
                    重置
                  </a-button>
                </a-space>
              </div>
            </a-col>
          </a-row>
        </a-form>

        <div class="action-buttons" style="margin-top: 16px;">
          <a-space wrap>
            <a-button type="primary" @click="showCreateModal">
              <plus-outlined />
              创建核销申请
            </a-button>
            <a-button @click="handleBatchApproval">
              <check-outlined />
              批量审批
            </a-button>
            <a-button @click="showRiskAssessmentModal">
              <safety-outlined />
              风险评估
            </a-button>
            <a-button @click="showReportModal">
              <bar-chart-outlined />
              统计分析
            </a-button>
            <a-button @click="showAIAnalysisModal">
              <RobotOutlined />
              AI核销分析
            </a-button>
            <a-button @click="showPolicyModal">
              <BookOutlined />
              政策管理
            </a-button>
            <a-button @click="exportData">
              <download-outlined />
              导出数据
            </a-button>
            <a-button @click="generateReport">
              <file-text-outlined />
              生成报告
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 核销申请列表 -->
      <a-card>
        <a-table 
          :columns="columns" 
          :data-source="writeoffList" 
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'customerInfo'">
              <div>
                <div class="customer-name">{{ record.customerName }}</div>
                <div class="customer-phone">{{ record.customerPhone }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'caseInfo'">
              <div>
                <div>{{ record.caseNumber }}</div>
                <div class="case-amount">原金额：¥{{ record.originalAmount?.toLocaleString() }}</div>
                <div class="case-overdue">逾期：{{ record.overdueDays }}天</div>
              </div>
            </template>
            
            <template v-if="column.key === 'writeoffInfo'">
              <div>
                <div>
                  <a-tag :color="getWriteoffTypeColor(record.writeoffType)">
                    {{ getWriteoffTypeText(record.writeoffType) }}
                  </a-tag>
                </div>
                <div class="writeoff-amount">核销：¥{{ record.writeoffAmount?.toLocaleString() }}</div>
                <div class="writeoff-ratio">比例：{{ record.writeoffRatio }}%</div>
              </div>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'riskLevel'">
              <a-tag :color="getRiskColor(record.riskLevel)">
                {{ getRiskText(record.riskLevel) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button 
                  v-if="record.status === 'pending'" 
                  type="link" 
                  size="small" 
                  @click="approveApplication(record)"
                >
                  审批
                </a-button>
                <a-button 
                  v-if="record.status === 'approved'" 
                  type="link" 
                  size="small" 
                  @click="executeWriteoff(record)"
                >
                  执行
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewApprovalHistory(record)">审批历史</a-menu-item>
                      <a-menu-item @click="generateCertificate(record)">生成凭证</a-menu-item>
                      <a-menu-item @click="printDocument(record)">打印单据</a-menu-item>
                      <a-menu-item 
                        v-if="record.status === 'pending'" 
                        @click="withdrawApplication(record)"
                      >
                        撤回申请
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 创建核销申请弹窗 -->
      <a-modal
        v-model:open="createModalVisible"
        title="创建核销申请"
        width="800px"
        @ok="handleCreateSubmit"
        @cancel="createModalVisible = false"
      >
        <a-form ref="createFormRef" :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="客户信息" name="customerId" :rules="[{ required: true, message: '请选择客户' }]">
            <a-select
              v-model:value="createForm.customerId"
              placeholder="请选择客户"
              show-search
              :filter-option="filterCustomerOption"
            >
              <a-select-option v-for="customer in customers" :key="customer.id" :value="customer.id">
                {{ customer.name }} - {{ customer.phone }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="关联案件" name="caseId" :rules="[{ required: true, message: '请选择案件' }]">
            <a-select
              v-model:value="createForm.caseId"
              placeholder="请选择案件"
              :disabled="!createForm.customerId"
              @change="onCaseSelect"
            >
              <a-select-option v-for="caseItem in customerCases" :key="caseItem.id" :value="caseItem.id">
                {{ caseItem.caseNumber }} - ¥{{ caseItem.amount.toLocaleString() }} (逾期{{ caseItem.overdueDays }}天)
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="案件信息">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="原始金额" name="originalAmount">
                  <a-input-number
                    v-model:value="createForm.originalAmount"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                    placeholder="自动获取"
                    disabled
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="逾期天数" name="overdueDays">
                  <a-input-number
                    v-model:value="createForm.overdueDays"
                    placeholder="自动获取"
                    disabled
                    style="width: 100%"
                    suffix="天"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>
          
          <a-form-item label="核销类型" name="writeoffType" :rules="[{ required: true, message: '请选择核销类型' }]">
            <a-select v-model:value="createForm.writeoffType" placeholder="请选择核销类型">
              <a-select-option value="bad_debt">坏账核销</a-select-option>
              <a-select-option value="uncollectable">无法催收</a-select-option>
              <a-select-option value="legal_limit">法律时效</a-select-option>
              <a-select-option value="business">商业核销</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="核销金额" name="writeoffAmount" :rules="[{ required: true, message: '请输入核销金额' }]">
            <a-input-number
              v-model:value="createForm.writeoffAmount"
              :min="0"
              :max="createForm.originalAmount"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="请输入核销金额"
              style="width: 100%"
              @change="calculateWriteoffRatio"
            />
          </a-form-item>
          
          <a-form-item label="核销比例" name="writeoffRatio">
            <a-input-number
              v-model:value="createForm.writeoffRatio"
              :min="0"
              :max="100"
              :precision="2"
              addon-after="%"
              placeholder="自动计算"
              disabled
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="风险等级" name="riskLevel" :rules="[{ required: true, message: '请选择风险等级' }]">
            <a-select v-model:value="createForm.riskLevel" placeholder="请选择风险等级">
              <a-select-option value="low">低风险</a-select-option>
              <a-select-option value="medium">中风险</a-select-option>
              <a-select-option value="high">高风险</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="申请原因" name="reason" :rules="[{ required: true, message: '请输入申请原因' }]">
            <a-textarea v-model:value="createForm.reason" :rows="4" placeholder="请详细说明核销申请的原因和依据" />
          </a-form-item>
          
          <a-form-item label="催收情况" name="collectionSummary" :rules="[{ required: true, message: '请输入催收情况说明' }]">
            <a-textarea v-model:value="createForm.collectionSummary" :rows="3" placeholder="请说明已进行的催收措施和效果" />
          </a-form-item>
          
          <a-form-item label="支撑材料" name="attachments">
            <a-upload
              v-model:file-list="createForm.attachmentList"
              :before-upload="() => false"
              multiple
            >
              <a-button>
                <upload-outlined />
                上传文件
              </a-button>
            </a-upload>
            <div class="upload-tip">
              支持上传：催收记录、法律文书、客户资产证明等相关材料
            </div>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 核销详情弹窗 -->
      <a-modal
        v-model:open="detailModalVisible"
        title="核销申请详情"
        width="1000px"
        :footer="null"
      >
        <div v-if="currentWriteoff">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-descriptions title="申请信息" bordered size="small">
                <a-descriptions-item label="申请编号">{{ currentWriteoff.applicationId }}</a-descriptions-item>
                <a-descriptions-item label="客户姓名">{{ currentWriteoff.customerName }}</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ currentWriteoff.customerPhone }}</a-descriptions-item>
                <a-descriptions-item label="案件编号">{{ currentWriteoff.caseNumber }}</a-descriptions-item>
                <a-descriptions-item label="申请状态">
                  <a-tag :color="getStatusColor(currentWriteoff.status)">
                    {{ getStatusText(currentWriteoff.status) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="风险等级">
                  <a-tag :color="getRiskColor(currentWriteoff.riskLevel)">
                    {{ getRiskText(currentWriteoff.riskLevel) }}
                  </a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-col>
            <a-col :span="12">
              <a-descriptions title="核销信息" bordered size="small">
                <a-descriptions-item label="核销类型">
                  <a-tag :color="getWriteoffTypeColor(currentWriteoff.writeoffType)">
                    {{ getWriteoffTypeText(currentWriteoff.writeoffType) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="原始金额">¥{{ currentWriteoff.originalAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="核销金额">¥{{ currentWriteoff.writeoffAmount?.toLocaleString() }}</a-descriptions-item>
                <a-descriptions-item label="核销比例">{{ currentWriteoff.writeoffRatio }}%</a-descriptions-item>
                <a-descriptions-item label="逾期天数">{{ currentWriteoff.overdueDays }}天</a-descriptions-item>
                <a-descriptions-item label="申请时间">{{ currentWriteoff.applicationDate }}</a-descriptions-item>
              </a-descriptions>
            </a-col>
          </a-row>
          
          <a-divider>申请原因</a-divider>
          <p>{{ currentWriteoff.reason }}</p>
          
          <a-divider>催收情况说明</a-divider>
          <p>{{ currentWriteoff.collectionSummary }}</p>
          
          <a-divider>支撑材料</a-divider>
          <div v-if="currentWriteoff.attachments && currentWriteoff.attachments.length > 0">
            <a-list
              :data-source="currentWriteoff.attachments"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a @click="downloadFile(item)">{{ item.name }}</a>
                    </template>
                    <template #description>
                      {{ item.size }} | {{ item.uploadTime }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <a-empty v-else description="暂无支撑材料" size="small" />
          
          <!-- 审批历史 -->
          <a-divider>审批历史</a-divider>
          <a-timeline>
            <a-timeline-item 
              v-for="history in currentWriteoff.approvalHistory" 
              :key="history.id"
              :color="getHistoryColor(history.action)"
            >
              <p><strong>{{ history.action }}</strong> - {{ history.approver }}</p>
              <p>{{ history.comment }}</p>
              <p class="history-time">{{ history.time }}</p>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-modal>

      <!-- 审批弹窗 -->
      <a-modal
        v-model:open="approvalModalVisible"
        title="核销申请审批"
        @ok="handleApprovalSubmit"
        @cancel="approvalModalVisible = false"
      >
        <a-form ref="approvalFormRef" :model="approvalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="审批结果" name="result" :rules="[{ required: true, message: '请选择审批结果' }]">
            <a-radio-group v-model:value="approvalForm.result">
              <a-radio value="approved">批准</a-radio>
              <a-radio value="rejected">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="approvalForm.result === 'approved'"
            label="调整核销金额" 
            name="adjustedAmount"
          >
            <a-input-number
              v-model:value="approvalForm.adjustedAmount"
              :min="0"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              placeholder="可调整核销金额"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item 
            v-if="approvalForm.result === 'approved'"
            label="生效日期" 
            name="effectiveDate"
            :rules="[{ required: true, message: '请选择生效日期' }]"
          >
            <a-date-picker v-model:value="approvalForm.effectiveDate" placeholder="请选择生效日期" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="审批意见" name="comment" :rules="[{ required: true, message: '请输入审批意见' }]">
            <a-textarea v-model:value="approvalForm.comment" :rows="4" placeholder="请输入审批意见" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量审批弹窗 -->
      <a-modal
        v-model:open="batchApprovalModalVisible"
        title="批量审批核销申请"
        width="700px"
        @ok="handleBatchApprovalSubmit"
        @cancel="batchApprovalModalVisible = false"
      >
        <a-form ref="batchApprovalFormRef" :model="batchApprovalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="选中数量">
            <span>已选择 {{ selectedRowKeys.length }} 个核销申请</span>
          </a-form-item>
          
          <a-form-item label="批量操作" name="batchAction" :rules="[{ required: true, message: '请选择批量操作' }]">
            <a-radio-group v-model:value="batchApprovalForm.batchAction">
              <a-radio value="approve">批量批准</a-radio>
              <a-radio value="reject">批量拒绝</a-radio>
              <a-radio value="transfer">批量转审</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="batchApprovalForm.batchAction === 'approve'"
            label="统一核销比例"
            name="uniformRatio"
          >
            <a-input-number
              v-model:value="batchApprovalForm.uniformRatio"
              :min="0"
              :max="100"
              :precision="2"
              addon-after="%"
              placeholder="可设置统一核销比例"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item 
            v-if="batchApprovalForm.batchAction === 'transfer'"
            label="转审人员"
            name="transferTo"
            :rules="[{ required: true, message: '请选择转审人员' }]"
          >
            <a-select v-model:value="batchApprovalForm.transferTo" placeholder="请选择转审人员">
              <a-select-option value="manager1">张经理</a-select-option>
              <a-select-option value="manager2">李经理</a-select-option>
              <a-select-option value="manager3">王经理</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="生效日期" name="effectiveDate" v-if="batchApprovalForm.batchAction === 'approve'">
            <a-date-picker v-model:value="batchApprovalForm.effectiveDate" placeholder="请选择生效日期" style="width: 100%" />
          </a-form-item>
          
          <a-form-item label="批量意见" name="batchComment" :rules="[{ required: true, message: '请输入批量意见' }]">
            <a-textarea v-model:value="batchApprovalForm.batchComment" :rows="4" placeholder="请输入批量处理意见" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 风险评估弹窗 -->
      <a-modal
        v-model:open="riskAssessmentModalVisible"
        title="核销风险评估"
        width="1000px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="overview" tab="风险概览">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card>
                  <a-statistic title="高风险核销" :value="riskData.highRiskCount" suffix="笔" valueStyle="{ color: '#cf1322' }" />
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card>
                  <a-statistic title="风险金额" :value="riskData.riskAmount" precision="2" prefix="¥" valueStyle="{ color: '#fa541c' }" />
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card>
                  <a-statistic title="风险比例" :value="riskData.riskRatio" precision="2" suffix="%" valueStyle="{ color: '#faad14' }" />
                </a-card>
              </a-col>
            </a-row>
            
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <a-card title="风险等级分布">
                  <div id="risk-distribution-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="月度风险趋势">
                  <div id="risk-trend-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="analysis" tab="风险分析">
            <a-table
              :columns="riskAnalysisColumns"
              :data-source="riskAnalysisData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'riskScore'">
                  <a-progress :percent="record.riskScore" size="small" :status="record.riskScore > 70 ? 'exception' : 'normal'" />
                </template>
                <template v-if="column.key === 'riskLevel'">
                  <a-tag :color="getRiskColor(record.riskLevel)">{{ getRiskText(record.riskLevel) }}</a-tag>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="prevention" tab="风险预防">
            <a-alert 
              message="风险预防建议" 
              description="基于当前核销数据分析，建议加强以下方面的风险控制措施。"
              type="info" 
              show-icon 
              style="margin-bottom: 16px;"
            />
            
            <a-list
              :data-source="riskPreventionList"
              item-layout="horizontal"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.title }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                  <a-tag :color="item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'green'">
                    {{ item.priority === 'high' ? '高优先级' : item.priority === 'medium' ? '中优先级' : '低优先级' }}
                  </a-tag>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 统计分析弹窗 -->
      <a-modal
        v-model:open="reportModalVisible"
        title="核销统计分析"
        width="1200px"
        :footer="null"
      >
        <a-tabs>
          <a-tab-pane key="statistics" tab="统计数据">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-card>
                  <a-statistic title="本月核销" :value="reportData.monthlyWriteoff" suffix="笔" />
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card>
                  <a-statistic title="本月金额" :value="reportData.monthlyAmount" precision="2" prefix="¥" />
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card>
                  <a-statistic title="平均核销率" :value="reportData.avgWriteoffRate" precision="2" suffix="%" />
                </a-card>
              </a-col>
              <a-col :span="6">
                <a-card>
                  <a-statistic title="审批效率" :value="reportData.approvalEfficiency" precision="1" suffix="天" />
                </a-card>
              </a-col>
            </a-row>
            
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <a-card title="核销类型统计">
                  <div id="writeoff-type-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="月度核销趋势">
                  <div id="writeoff-trend-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="efficiency" tab="效率分析">
            <a-table
              :columns="efficiencyAnalysisColumns"
              :data-source="efficiencyAnalysisData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'approvalRate'">
                  <a-progress :percent="record.approvalRate" size="small" />
                </template>
                <template v-if="column.key === 'avgDays'">
                  <span :style="{ color: record.avgDays > 7 ? '#ff4d4f' : '#52c41a' }">{{ record.avgDays }}天</span>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="comparison" tab="对比分析">
            <a-descriptions title="同期对比" bordered>
              <a-descriptions-item label="核销数量同比">+15.2%</a-descriptions-item>
              <a-descriptions-item label="核销金额同比">****%</a-descriptions-item>
              <a-descriptions-item label="审批效率同比">-2.1天</a-descriptions-item>
              <a-descriptions-item label="风险控制同比">+12.5%</a-descriptions-item>
            </a-descriptions>
            
            <div style="margin-top: 24px;">
              <a-card title="年度核销趋势对比">
                <div id="yearly-comparison-chart" style="height: 300px;"></div>
              </a-card>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- AI核销分析弹窗 -->
      <a-modal
        v-model:open="aiAnalysisModalVisible"
        title="AI核销分析"
        width="1200px"
        :footer="null"
      >
        <a-tabs v-model:activeKey="aiActiveKey">
          <a-tab-pane key="prediction" tab="风险预测">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="AI风险评分" :bordered="false">
                  <div class="ai-score-card">
                    <div class="score-value">{{ aiPrediction.riskScore }}</div>
                    <div class="score-label">风险评分</div>
                    <a-progress 
                      :percent="aiPrediction.riskScore" 
                      :stroke-color="getAIScoreColor(aiPrediction.riskScore)"
                      :show-info="false"
                    />
                    <div class="score-desc">{{ aiPrediction.riskDescription }}</div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="16">
                <a-card title="风险因素分析" :bordered="false">
                  <div id="risk-factors-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="24">
                <a-card title="AI建议" :bordered="false">
                  <a-list
                    :data-source="aiRecommendations"
                    :split="false"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar :style="{ backgroundColor: item.color }">
                              <template #icon>
                                <component :is="item.icon" />
                              </template>
                            </a-avatar>
                          </template>
                          <template #title>
                            {{ item.title }}
                            <a-tag :color="item.tagColor" style="margin-left: 8px;">{{ item.tag }}</a-tag>
                          </template>
                          <template #description>
                            <div>{{ item.description }}</div>
                            <div class="recommendation-actions">
                              <a-button type="link" size="small" @click="applyRecommendation(item)">
                                <CheckOutlined /> 应用建议
                              </a-button>
                              <a-button type="link" size="small" @click="viewRecommendationDetail(item)">
                                <InfoCircleOutlined /> 详细信息
                              </a-button>
                            </div>
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="pattern" tab="模式识别">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="核销模式分析" :bordered="false">
                  <div id="pattern-analysis-chart" style="height: 350px;"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="异常检测" :bordered="false">
                  <a-alert
                    v-if="anomalyDetection.hasAnomaly"
                    :message="anomalyDetection.message"
                    :description="anomalyDetection.description"
                    type="warning"
                    show-icon
                    closable
                    style="margin-bottom: 16px;"
                  />
                  <a-table
                    :columns="anomalyColumns"
                    :data-source="anomalyDetection.items"
                    :pagination="false"
                    size="small"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.key === 'severity'">
                        <a-tag :color="getSeverityColor(record.severity)">{{ record.severity }}</a-tag>
                      </template>
                    </template>
                  </a-table>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          
          <a-tab-pane key="optimization" tab="优化建议">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="流程优化" :bordered="false">
                  <a-timeline>
                    <a-timeline-item v-for="item in processOptimization" :key="item.id" :color="item.color">
                      <p><strong>{{ item.title }}</strong></p>
                      <p>{{ item.description }}</p>
                      <p class="optimization-impact">{{ item.impact }}</p>
                    </a-timeline-item>
                  </a-timeline>
                </a-card>
              </a-col>
              <a-col :span="16">
                <a-card title="效果预测" :bordered="false">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-statistic
                        title="预计降低损失"
                        :value="optimizationPrediction.reducedLoss"
                        :precision="2"
                        prefix="¥"
                        suffix="万"
                        :value-style="{ color: '#52c41a' }"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="预计提升效率"
                        :value="optimizationPrediction.improvedEfficiency"
                        :precision="1"
                        suffix="%"
                        :value-style="{ color: '#1890ff' }"
                      />
                    </a-col>
                  </a-row>
                  <a-divider />
                  <div id="optimization-impact-chart" style="height: 300px;"></div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 政策管理弹窗 -->
      <a-modal
        v-model:open="policyModalVisible"
        title="核销政策管理"
        width="1000px"
        @ok="handlePolicySubmit"
        @cancel="policyModalVisible = false"
      >
        <a-tabs v-model:activeKey="policyActiveKey">
          <a-tab-pane key="current" tab="现行政策">
            <a-table
              :columns="policyColumns"
              :data-source="currentPolicies"
              :pagination="false"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 'active' ? 'green' : 'orange'">
                    {{ record.status === 'active' ? '生效中' : '待生效' }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewPolicy(record)">查看</a-button>
                    <a-button type="link" size="small" @click="editPolicy(record)">编辑</a-button>
                    <a-button type="link" size="small" danger @click="disablePolicy(record)">停用</a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="create" tab="创建政策">
            <a-form :model="policyForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-item label="政策名称" name="name" :rules="[{ required: true, message: '请输入政策名称' }]">
                <a-input v-model:value="policyForm.name" placeholder="请输入政策名称" />
              </a-form-item>
              
              <a-form-item label="政策类型" name="type" :rules="[{ required: true, message: '请选择政策类型' }]">
                <a-select v-model:value="policyForm.type" placeholder="请选择政策类型">
                  <a-select-option value="threshold">金额阈值</a-select-option>
                  <a-select-option value="period">逾期时间</a-select-option>
                  <a-select-option value="rate">核销比例</a-select-option>
                  <a-select-option value="approval">审批流程</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="适用范围" name="scope">
                <a-checkbox-group v-model:value="policyForm.scope">
                  <a-checkbox value="bad_debt">坏账核销</a-checkbox>
                  <a-checkbox value="uncollectable">无法催收</a-checkbox>
                  <a-checkbox value="legal_limit">法律时效</a-checkbox>
                  <a-checkbox value="business">商业核销</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              
              <a-form-item label="规则设置">
                <div class="rule-builder">
                  <a-space direction="vertical" style="width: 100%;">
                    <div v-for="(rule, index) in policyForm.rules" :key="index" class="rule-item">
                      <a-row :gutter="8" align="middle">
                        <a-col :span="6">
                          <a-select v-model:value="rule.field" placeholder="选择字段">
                            <a-select-option value="amount">金额</a-select-option>
                            <a-select-option value="overdueDays">逾期天数</a-select-option>
                            <a-select-option value="writeoffRate">核销率</a-select-option>
                          </a-select>
                        </a-col>
                        <a-col :span="4">
                          <a-select v-model:value="rule.operator" placeholder="操作符">
                            <a-select-option value=">">大于</a-select-option>
                            <a-select-option value="<">小于</a-select-option>
                            <a-select-option value="=">等于</a-select-option>
                            <a-select-option value="between">介于</a-select-option>
                          </a-select>
                        </a-col>
                        <a-col :span="8">
                          <a-input v-model:value="rule.value" placeholder="值" />
                        </a-col>
                        <a-col :span="4">
                          <a-select v-model:value="rule.action" placeholder="动作">
                            <a-select-option value="approve">自动批准</a-select-option>
                            <a-select-option value="reject">自动拒绝</a-select-option>
                            <a-select-option value="manual">人工审核</a-select-option>
                          </a-select>
                        </a-col>
                        <a-col :span="2">
                          <a-button type="text" danger @click="removeRule(index)">
                            删除
                          </a-button>
                        </a-col>
                      </a-row>
                    </div>
                    <a-button type="dashed" block @click="addRule">
                      <PlusOutlined />
                      添加规则
                    </a-button>
                  </a-space>
                </div>
              </a-form-item>
              
              <a-form-item label="生效日期" name="effectiveDate">
                <a-date-picker v-model:value="policyForm.effectiveDate" style="width: 100%;" />
              </a-form-item>
              
              <a-form-item label="备注说明" name="description">
                <a-textarea v-model:value="policyForm.description" :rows="3" placeholder="请输入备注说明" />
              </a-form-item>
            </a-form>
          </a-tab-pane>
          
          <a-tab-pane key="history" tab="历史政策">
            <a-timeline>
              <a-timeline-item v-for="item in policyHistory" :key="item.id" :color="item.color">
                <template #dot>
                  <component :is="item.icon" />
                </template>
                <div class="policy-history-item">
                  <div class="policy-title">{{ item.name }}</div>
                  <div class="policy-meta">
                    <span>{{ item.date }}</span>
                    <a-divider type="vertical" />
                    <span>{{ item.operator }}</span>
                    <a-divider type="vertical" />
                    <a-tag :color="item.statusColor">{{ item.status }}</a-tag>
                  </div>
                  <div class="policy-desc">{{ item.description }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, h, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  CheckOutlined,
  DownloadOutlined,
  DownOutlined,
  UpOutlined,
  UploadOutlined,
  FileTextOutlined,
  SearchOutlined,
  ReloadOutlined,
  SafetyOutlined,
  UserOutlined,
  BarChartOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  DollarCircleOutlined,
  PercentageOutlined,
  RobotOutlined,
  BulbOutlined,
  LineChartOutlined,
  FundOutlined,
  FileDoneOutlined,
  AlertOutlined,
  AuditOutlined,
  TeamOutlined,
  HistoryOutlined,
  PrinterOutlined,
  MailOutlined,
  MessageOutlined,
  PhoneOutlined,
  ApiOutlined,
  CalculatorOutlined,
  ThunderboltOutlined,
  RiseOutlined,
  FallOutlined,
  SyncOutlined,
  DatabaseOutlined,
  FileProtectOutlined,
  BookOutlined,
  FieldTimeOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedRowKeys = ref([])
const showAdvanced = ref(false)

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 统计数据
const stats = reactive({
  totalApplications: 89,
  pendingApprovals: 12,
  approvedApplications: 56,
  totalWriteoffAmount: 3456700
})

// 搜索表单
const searchForm = reactive({
  customerName: '',
  caseNumber: '',
  status: undefined,
  writeoffType: undefined,
  applicationDateRange: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 89,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '申请编号',
    dataIndex: 'applicationId',
    key: 'applicationId',
    width: 120
  },
  {
    title: '客户信息',
    key: 'customerInfo',
    width: 150
  },
  {
    title: '案件信息',
    key: 'caseInfo',
    width: 150
  },
  {
    title: '核销信息',
    key: 'writeoffInfo',
    width: 180
  },
  {
    title: '申请状态',
    key: 'status',
    width: 100
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 80
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    key: 'applicant',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'applicationDate',
    key: 'applicationDate',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 模拟数据
const writeoffList = ref([
  {
    id: 1,
    applicationId: 'WO2024001',
    customerName: '张三',
    customerPhone: '13800138001',
    caseNumber: '*********',
    originalAmount: 100000,
    writeoffAmount: 100000,
    writeoffRatio: 100,
    writeoffType: 'bad_debt',
    status: 'pending',
    riskLevel: 'high',
    overdueDays: 365,
    applicant: '李催收',
    applicationDate: '2024-01-15',
    reason: '客户已失联超过一年，多方查找无果，经法律途径追讨无效，债权无法收回。',
    collectionSummary: '已进行电话催收50余次，上门走访3次，发送催收函件5次，委托第三方催收机构催收6个月，均无效果。客户已更换联系方式，原住址搬离。',
    attachments: [
      { name: '催收记录汇总.pdf', size: '3.2MB', uploadTime: '2024-01-15 09:30' },
      { name: '法律意见书.pdf', size: '1.5MB', uploadTime: '2024-01-15 09:35' },
      { name: '失联证明.jpg', size: '0.8MB', uploadTime: '2024-01-15 09:40' }
    ],
    approvalHistory: [
      { id: 1, action: '提交申请', approver: '李催收', comment: '客户失联，债权无法收回', time: '2024-01-15 09:00' }
    ]
  },
  {
    id: 2,
    applicationId: 'WO2024002',
    customerName: '李四',
    customerPhone: '13800138002',
    caseNumber: 'CS2024002',
    originalAmount: 80000,
    writeoffAmount: 60000,
    writeoffRatio: 75,
    writeoffType: 'uncollectable',
    status: 'approved',
    riskLevel: 'medium',
    overdueDays: 180,
    applicant: '王催收',
    applicationDate: '2024-01-10',
    reason: '客户因重大疾病丧失劳动能力，家庭经济困难，经评估确实无偿还能力。',
    collectionSummary: '客户配合催收工作，提供了完整的医疗证明和收入证明，确认无偿还能力。',
    attachments: [
      { name: '医疗诊断证明.pdf', size: '2.1MB', uploadTime: '2024-01-10 14:30' },
      { name: '收入证明.pdf', size: '1.2MB', uploadTime: '2024-01-10 14:35' }
    ],
    approvalHistory: [
      { id: 1, action: '提交申请', approver: '王催收', comment: '客户疾病导致无偿还能力', time: '2024-01-10 14:00' },
      { id: 2, action: '初审通过', approver: '张主管', comment: '材料齐全，符合核销条件', time: '2024-01-11 10:00' },
      { id: 3, action: '终审批准', approver: '刘经理', comment: '同意核销申请', time: '2024-01-12 16:00' }
    ]
  }
])

// 弹窗状态
const createModalVisible = ref(false)
const detailModalVisible = ref(false)
const approvalModalVisible = ref(false)
const batchApprovalModalVisible = ref(false)
const riskAssessmentModalVisible = ref(false)
const reportModalVisible = ref(false)
const aiAnalysisModalVisible = ref(false)
const policyModalVisible = ref(false)

// Tab激活键
const aiActiveKey = ref('prediction')
const policyActiveKey = ref('current')

// 当前选中的核销申请
const currentWriteoff = ref(null)

// 创建表单
const createForm = reactive({
  customerId: undefined,
  caseId: undefined,
  originalAmount: undefined,
  overdueDays: undefined,
  writeoffType: undefined,
  writeoffAmount: undefined,
  writeoffRatio: undefined,
  riskLevel: undefined,
  reason: '',
  collectionSummary: '',
  attachmentList: []
})

// 审批表单
const approvalForm = reactive({
  result: undefined,
  adjustedAmount: undefined,
  effectiveDate: undefined,
  comment: ''
})

// 批量审批表单
const batchApprovalForm = reactive({
  batchAction: undefined,
  uniformRatio: undefined,
  transferTo: undefined,
  effectiveDate: undefined,
  batchComment: ''
})

// 风险评估数据
const riskData = reactive({
  highRiskCount: 15,
  riskAmount: 1250000,
  riskRatio: 36.2
})

const riskAnalysisData = ref([
  { type: '坏账核销', count: 25, amount: 1200000, riskScore: 85, riskLevel: 'high' },
  { type: '无法催收', count: 18, amount: 850000, riskScore: 72, riskLevel: 'high' },
  { type: '法律时效', count: 12, amount: 600000, riskScore: 45, riskLevel: 'medium' },
  { type: '商业核销', count: 8, amount: 350000, riskScore: 25, riskLevel: 'low' }
])

const riskAnalysisColumns = [
  { title: '核销类型', dataIndex: 'type', key: 'type' },
  { title: '数量', dataIndex: 'count', key: 'count' },
  { title: '金额', dataIndex: 'amount', key: 'amount' },
  { title: '风险评分', key: 'riskScore' },
  { title: '风险等级', key: 'riskLevel' }
]

const riskPreventionList = ref([
  {
    title: '完善核销审批流程',
    description: '建立更严格的核销审批机制，对高风险核销申请进行多级审批',
    priority: 'high'
  },
  {
    title: '加强贷前风险评估',
    description: '在放贷前进行更全面的风险评估，减少后期核销风险',
    priority: 'high'
  },
  {
    title: '定期风险监控',
    description: '建立定期风险监控机制，及时发现和处理潜在风险',
    priority: 'medium'
  },
  {
    title: '核销数据分析',
    description: '定期分析核销数据趋势，识别风险模式和预警信号',
    priority: 'medium'
  }
])

// 统计报告数据
const reportData = reactive({
  monthlyWriteoff: 45,
  monthlyAmount: 2100000,
  avgWriteoffRate: 68.5,
  approvalEfficiency: 5.2
})

const efficiencyAnalysisData = ref([
  { department: '风控部', processedCount: 35, approvalRate: 78, avgDays: 4.2 },
  { department: '法务部', processedCount: 28, approvalRate: 65, avgDays: 6.8 },
  { department: '催收部', processedCount: 42, approvalRate: 82, avgDays: 3.5 }
])

const efficiencyAnalysisColumns = [
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '处理数量', dataIndex: 'processedCount', key: 'processedCount' },
  { title: '通过率', key: 'approvalRate' },
  { title: '平均用时', key: 'avgDays' }
]

// AI预测数据
const aiPrediction = reactive({
  riskScore: 72,
  riskDescription: '基于历史数据和客户行为分析，该批次核销申请整体风险处于中等水平'
})

// AI推荐
const aiRecommendations = ref([
  {
    id: 1,
    title: '加强尽职调查',
    description: '建议对金额超过10万的核销申请进行更详细的资产调查和催收措施验证',
    tag: '高优先级',
    tagColor: 'red',
    color: '#ff4d4f',
    icon: 'AuditOutlined'
  },
  {
    id: 2,
    title: '优化审批流程',
    description: 'AI检测到审批流程存在瓶颈，建议设置分级审批权限，提高效率',
    tag: '中优先级',
    tagColor: 'orange',
    color: '#fa8c16',
    icon: 'SyncOutlined'
  },
  {
    id: 3,
    title: '风险预警设置',
    description: '建议对逾期超过180天且金额大于5万的案件设置自动预警机制',
    tag: '建议项',
    tagColor: 'blue',
    color: '#1890ff',
    icon: 'AlertOutlined'
  }
])

// 异常检测
const anomalyDetection = reactive({
  hasAnomaly: true,
  message: '检测到异常核销模式',
  description: '发现3笔核销申请存在异常特征，建议重点审查',
  items: [
    { id: 1, caseNumber: 'CS2024015', anomalyType: '金额异常', severity: '高', description: '核销金额远超历史平均值' },
    { id: 2, caseNumber: 'CS2024028', anomalyType: '时间异常', severity: '中', description: '逾期时间较短即申请核销' },
    { id: 3, caseNumber: 'CS2024032', anomalyType: '催收异常', severity: '低', description: '催收记录不完整' }
  ]
})

// 异常列配置
const anomalyColumns = [
  { title: '案件编号', dataIndex: 'caseNumber', key: 'caseNumber' },
  { title: '异常类型', dataIndex: 'anomalyType', key: 'anomalyType' },
  { title: '严重程度', key: 'severity' },
  { title: '异常描述', dataIndex: 'description', key: 'description' }
]

// 流程优化建议
const processOptimization = ref([
  {
    id: 1,
    title: '简化小额核销审批',
    description: '5万以下核销申请可设置快速审批通道',
    impact: '预计减少60%审批时间',
    color: 'green'
  },
  {
    id: 2,
    title: '建立核销模型',
    description: '基于AI建立自动化核销评估模型',
    impact: '提升风险识别准确率30%',
    color: 'blue'
  },
  {
    id: 3,
    title: '完善催收记录',
    description: '强制要求核销前完成标准化催收流程',
    impact: '降低合规风险',
    color: 'orange'
  }
])

// 优化预测
const optimizationPrediction = reactive({
  reducedLoss: 156.8,
  improvedEfficiency: 42.5
})

// 当前政策
const currentPolicies = ref([
  {
    id: 1,
    name: '小额快速核销政策',
    type: 'threshold',
    scope: ['bad_debt', 'uncollectable'],
    status: 'active',
    effectiveDate: '2024-01-01',
    description: '对于金额5万以下、逾期超过180天的案件，简化核销流程'
  },
  {
    id: 2,
    name: '坏账核销标准',
    type: 'period',
    scope: ['bad_debt'],
    status: 'active',
    effectiveDate: '2023-06-01',
    description: '逾期超过365天且催收无效的案件可申请坏账核销'
  },
  {
    id: 3,
    name: '分级审批制度',
    type: 'approval',
    scope: ['bad_debt', 'uncollectable', 'legal_limit', 'business'],
    status: 'active',
    effectiveDate: '2023-01-01',
    description: '根据核销金额设置不同级别的审批权限'
  }
])

// 政策列配置
const policyColumns = [
  { title: '政策名称', dataIndex: 'name', key: 'name' },
  { title: '政策类型', dataIndex: 'type', key: 'type' },
  { title: '适用范围', dataIndex: 'scope', key: 'scope', customRender: ({ text }) => text.join('、') },
  { title: '状态', key: 'status' },
  { title: '生效日期', dataIndex: 'effectiveDate', key: 'effectiveDate' },
  { title: '操作', key: 'action', width: 180 }
]

// 政策表单
const policyForm = reactive({
  name: '',
  type: undefined,
  scope: [],
  rules: [
    { field: undefined, operator: undefined, value: '', action: undefined }
  ],
  effectiveDate: undefined,
  description: ''
})

// 政策历史
const policyHistory = ref([
  {
    id: 1,
    name: '调整核销审批流程',
    date: '2024-01-15',
    operator: '系统管理员',
    status: '已执行',
    statusColor: 'green',
    icon: 'CheckCircleOutlined',
    color: 'green',
    description: '简化小额核销审批流程，提高处理效率'
  },
  {
    id: 2,
    name: '更新坏账认定标准',
    date: '2023-12-01',
    operator: '风控经理',
    status: '已执行',
    statusColor: 'green',
    icon: 'EditOutlined',
    color: 'blue',
    description: '将坏账认定的逾期天数从300天调整为365天'
  },
  {
    id: 3,
    name: '新增商业核销类型',
    date: '2023-10-15',
    operator: '财务总监',
    status: '已执行',
    statusColor: 'green',
    icon: 'PlusOutlined',
    color: 'purple',
    description: '增加商业核销类型，适用于战略性债务重组'
  }
])

// 客户列表
const customers = ref([
  { id: 1, name: '张三', phone: '13800138001' },
  { id: 2, name: '李四', phone: '13800138002' },
  { id: 3, name: '王五', phone: '13800138003' }
])

// 案件列表
const customerCases = ref([])

// 监听客户选择
watch(() => createForm.customerId, (customerId) => {
  if (customerId) {
    customerCases.value = [
      { id: 1, caseNumber: '*********', amount: 100000, overdueDays: 365 },
      { id: 2, caseNumber: 'CS2024002', amount: 80000, overdueDays: 180 }
    ]
  } else {
    customerCases.value = []
  }
  createForm.caseId = undefined
  createForm.originalAmount = undefined
  createForm.overdueDays = undefined
})

// 监听核销金额变化，自动计算核销比例
watch(() => createForm.writeoffAmount, () => {
  calculateWriteoffRatio()
})

// 状态相关方法
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    approved: 'green',
    rejected: 'red',
    executed: 'blue'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    executed: '已执行'
  }
  return texts[status] || '未知'
}

const getWriteoffTypeColor = (type) => {
  const colors = {
    bad_debt: 'red',
    uncollectable: 'orange',
    legal_limit: 'blue',
    business: 'green'
  }
  return colors[type] || 'default'
}

const getWriteoffTypeText = (type) => {
  const texts = {
    bad_debt: '坏账核销',
    uncollectable: '无法催收',
    legal_limit: '法律时效',
    business: '商业核销'
  }
  return texts[type] || '未知'
}

const getRiskColor = (risk) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colors[risk] || 'default'
}

const getRiskText = (risk) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[risk] || '未知'
}

const getHistoryColor = (action) => {
  const colors = {
    '提交申请': 'blue',
    '初审通过': 'green',
    '终审批准': 'green',
    '审批拒绝': 'red',
    '执行完成': 'purple'
  }
  return colors[action] || 'default'
}

// 事件处理方法
const handleSearch = () => {
  console.log('搜索参数:', searchForm)
  message.success('搜索完成')
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  handleSearch()
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 快速搜索
const handleQuickSearch = () => {
  handleSearch()
}

const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  handleSearch()
}

const showCreateModal = () => {
  createModalVisible.value = true
}

const onCaseSelect = (caseId) => {
  const selectedCase = customerCases.value.find(c => c.id === caseId)
  if (selectedCase) {
    createForm.originalAmount = selectedCase.amount
    createForm.overdueDays = selectedCase.overdueDays
  }
}

const calculateWriteoffRatio = () => {
  if (createForm.originalAmount && createForm.writeoffAmount) {
    createForm.writeoffRatio = ((createForm.writeoffAmount / createForm.originalAmount) * 100).toFixed(2)
  } else {
    createForm.writeoffRatio = undefined
  }
}

const handleCreateSubmit = () => {
  console.log('创建核销申请:', createForm)
  message.success('核销申请创建成功')
  createModalVisible.value = false
}

const viewDetails = (record) => {
  currentWriteoff.value = record
  detailModalVisible.value = true
}

const approveApplication = (record) => {
  currentWriteoff.value = record
  approvalForm.adjustedAmount = record.writeoffAmount
  approvalModalVisible.value = true
}

const handleApprovalSubmit = () => {
  console.log('审批结果:', approvalForm)
  message.success('审批完成')
  approvalModalVisible.value = false
}

const executeWriteoff = (record) => {
  message.success(`核销申请 ${record.applicationId} 执行成功`)
}

const viewApprovalHistory = (record) => {
  currentWriteoff.value = record
  detailModalVisible.value = true
}

const generateCertificate = (record) => {
  message.success('正在生成核销凭证...')
}

const printDocument = (record) => {
  message.success('正在打印核销单据...')
}

const withdrawApplication = (record) => {
  message.success(`核销申请 ${record.applicationId} 已撤回`)
}

const handleBatchApproval = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要审批的申请')
    return
  }
  batchApprovalModalVisible.value = true
}

const handleBatchApprovalSubmit = () => {
  console.log('批量审批:', batchApprovalForm)
  message.success(`已${batchApprovalForm.batchAction === 'approve' ? '批准' : batchApprovalForm.batchAction === 'reject' ? '拒绝' : '转审'} ${selectedRowKeys.value.length} 个申请`)
  batchApprovalModalVisible.value = false
  selectedRowKeys.value = []
}

const showRiskAssessmentModal = () => {
  riskAssessmentModalVisible.value = true
}

const showReportModal = () => {
  reportModalVisible.value = true
}

const exportData = () => {
  message.success('数据导出成功')
}

const generateReport = () => {
  message.success('正在生成核销报告...')
}

const downloadFile = (file) => {
  message.success(`正在下载 ${file.name}`)
}

const filterCustomerOption = (input, option) => {
  return option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// AI分析结果数据
const aiAnalysisResult = ref({})

// AI预测数据扩展
const aiRecommendationsData = ref([
  {
    id: 1,
    title: '加强尽职调查',
    description: '建议对金额超过10万的核销申请进行更详细的资产调查和催收措施验证',
    action: 'strengthen_review',
    priority: 'high',
    impact: '降低风险15%'
  },
  {
    id: 2,
    title: '优化审批流程',
    description: 'AI检测到审批流程存在瓶颈，建议设置分级审批权限，提高效率',
    action: 'optimize_process',
    priority: 'medium',
    impact: '提升效率20%'
  },
  {
    id: 3,
    title: '风险预警设置',
    description: '建议对逾期超过180天且金额大于5万的案件设置自动预警机制',
    action: 'setup_alert',
    priority: 'low',
    impact: '预防损失10%'
  }
])

// 异常检测结果
const anomalyDetectionResult = ref([])

// 政策管理数据
const policies = ref([
  {
    id: 1,
    name: '小额快速核销',
    type: 'auto',
    conditions: [
      { field: 'amount', operator: '<', value: '50000' },
      { field: 'age', operator: '>', value: '180' }
    ],
    actions: {
      writeoffRatio: 100,
      requireApproval: false,
      notifyRoles: ['manager']
    },
    priority: 1,
    status: 'active',
    createTime: '2024-01-01'
  }
])

// AI相关方法
const showAIAnalysisModal = () => {
  aiAnalysisModalVisible.value = true
  // 模拟AI分析数据
  aiAnalysisResult.value = {
    riskScore: 85,
    prediction: {
      probability: 0.75,
      factors: [
        { factor: '账龄', weight: 0.35, impact: 'high' },
        { factor: '还款历史', weight: 0.25, impact: 'medium' },
        { factor: '客户类型', weight: 0.20, impact: 'low' },
        { factor: '金额大小', weight: 0.20, impact: 'medium' }
      ]
    },
    patterns: [
      {
        pattern: '高账龄案件集中',
        description: '超过180天的案件占比达到65%',
        suggestion: '建议加快处理流程'
      },
      {
        pattern: '小额案件占比高',
        description: '5000元以下案件占比45%',
        suggestion: '可以考虑批量核销'
      }
    ],
    optimization: {
      currentEfficiency: 68,
      potentialEfficiency: 85,
      suggestions: [
        '优化审批流程，减少等待时间',
        '建立自动化核销规则',
        '加强风险预警机制'
      ]
    }
  }
  initAICharts()
}

const handleAIRecommendation = (recommendation) => {
  message.info(`采纳建议: ${recommendation.action}`)
  aiAnalysisModalVisible.value = false
}

const detectAnomalies = () => {
  // 模拟异常检测
  const anomalies = [
    { case_id: '*********', type: '金额异常', description: '核销金额超过债务总额', risk: 'high' },
    { case_id: '*********', type: '时间异常', description: '核销申请频率异常', risk: 'medium' },
    { case_id: '*********', type: '审批异常', description: '审批时间过短', risk: 'low' }
  ]
  anomalyDetectionResult.value = anomalies
  message.success(`检测完成，发现 ${anomalies.length} 个异常`)
}

// 政策管理方法
const showPolicyModal = () => {
  policyModalVisible.value = true
}

const addPolicy = () => {
  const newPolicy = {
    id: policies.value.length + 1,
    name: '',
    type: 'auto',
    conditions: [],
    actions: { writeoffRatio: 100, requireApproval: true, notifyRoles: [] },
    priority: 1,
    status: 'active',
    createTime: new Date().toISOString().split('T')[0]
  }
  policies.value.push(newPolicy)
}

const removePolicy = (index) => {
  policies.value.splice(index, 1)
}

const addCondition = (policy) => {
  policy.conditions.push({
    field: 'age',
    operator: '>',
    value: ''
  })
}

const removeCondition = (policy, index) => {
  policy.conditions.splice(index, 1)
}

const savePolicy = () => {
  message.success('政策保存成功')
  policyModalVisible.value = false
}

// 获取AI评分颜色
const getAIScoreColor = (score) => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#fa8c16'
  return '#52c41a'
}

// 获取严重程度颜色
const getSeverityColor = (severity) => {
  const colors = {
    '高': 'red',
    '中': 'orange',
    '低': 'green'
  }
  return colors[severity] || 'default'
}

// 应用推荐建议
const applyRecommendation = (item) => {
  message.success(`正在应用建议: ${item.title}`)
}

// 查看推荐详情
const viewRecommendationDetail = (item) => {
  message.info(`查看详情: ${item.title}`)
}

// 图表初始化
const initCharts = () => {
  // 风险分布图
  setTimeout(() => {
    const riskDistributionEl = document.getElementById('riskDistributionChart')
    if (riskDistributionEl) {
      const riskChart = echarts.init(riskDistributionEl)
      riskChart.setOption({
        tooltip: { trigger: 'item' },
        legend: { bottom: '5%' },
        series: [{
          name: '风险等级',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 35, name: '低风险' },
            { value: 45, name: '中风险' },
            { value: 20, name: '高风险' }
          ]
        }]
      })
    }
  }, 100)

  // 核销趋势图
  setTimeout(() => {
    const writeoffTrendEl = document.getElementById('writeoffTrendChart')
    if (writeoffTrendEl) {
      const trendChart = echarts.init(writeoffTrendEl)
      trendChart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '核销金额',
            type: 'line',
            data: [120, 200, 150, 180, 220, 250],
            smooth: true
          },
          {
            name: '核销数量',
            type: 'bar',
            data: [30, 45, 35, 40, 50, 55]
          }
        ]
      })
    }
  }, 150)

  // 核销类型分布
  setTimeout(() => {
    const writeoffTypeEl = document.getElementById('writeoffTypeChart')
    if (writeoffTypeEl) {
      const typeChart = echarts.init(writeoffTypeEl)
      typeChart.setOption({
        tooltip: { trigger: 'axis' },
        radar: {
          indicator: [
            { name: '坏账核销', max: 100 },
            { name: '政策核销', max: 100 },
            { name: '减免核销', max: 100 },
            { name: '和解核销', max: 100 },
            { name: '破产核销', max: 100 }
          ]
        },
        series: [{
          type: 'radar',
          data: [{
            value: [80, 65, 45, 70, 30],
            name: '核销类型分布'
          }]
        }]
      })
    }
  }, 200)

  // 月度核销趋势
  setTimeout(() => {
    const monthlyTrendEl = document.getElementById('monthlyTrendChart')
    if (monthlyTrendEl) {
      const monthlyChart = echarts.init(monthlyTrendEl)
      monthlyChart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: Array.from({ length: 30 }, (_, i) => `${i + 1}日`)
        },
        yAxis: { type: 'value' },
        series: [{
          name: '日核销金额',
          type: 'line',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 100) + 50),
          areaStyle: {},
          smooth: true
        }]
      })
    }
  }, 250)

  // 年度对比图
  setTimeout(() => {
    const yearComparisonEl = document.getElementById('yearComparisonChart')
    if (yearComparisonEl) {
      const yearChart = echarts.init(yearComparisonEl)
      yearChart.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['2023年', '2024年'] },
        xAxis: {
          type: 'category',
          data: ['Q1', 'Q2', 'Q3', 'Q4']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '2023年',
            type: 'bar',
            data: [320, 380, 420, 480]
          },
          {
            name: '2024年',
            type: 'bar',
            data: [380, 420, 460, 520]
          }
        ]
      })
    }
  }, 300)

  // 风险因素分析
  setTimeout(() => {
    const riskFactorEl = document.getElementById('riskFactorChart')
    if (riskFactorEl) {
      const factorChart = echarts.init(riskFactorEl)
      factorChart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['账龄', '金额', '客户类型', '还款历史', '地区']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '风险权重',
          type: 'bar',
          data: [85, 65, 45, 75, 55],
          itemStyle: {
            color: (params) => {
              const colors = ['#ff4d4f', '#ff7a45', '#ffa940', '#ffc53d', '#ffec3d']
              return colors[params.dataIndex]
            }
          }
        }]
      })
    }
  }, 350)
}

// AI分析图表初始化
const initAICharts = () => {
  // 风险预测图
  setTimeout(() => {
    const riskPredictionEl = document.getElementById('riskPredictionChart')
    if (riskPredictionEl) {
      const predictionChart = echarts.init(riskPredictionEl)
      predictionChart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['当前', '1个月后', '3个月后', '6个月后']
        },
        yAxis: { type: 'value', max: 100 },
        series: [{
          name: '风险值',
          type: 'line',
          data: [85, 88, 92, 95],
          markLine: {
            data: [{ type: 'average', name: '平均值' }]
          }
        }]
      })
    }
  }, 100)

  // 模式分析图
  setTimeout(() => {
    const patternAnalysisEl = document.getElementById('patternAnalysisChart')
    if (patternAnalysisEl) {
      const patternChart = echarts.init(patternAnalysisEl)
      patternChart.setOption({
        tooltip: { trigger: 'item' },
        series: [{
          type: 'treemap',
          data: [
            { name: '高账龄案件', value: 65 },
            { name: '小额案件', value: 45 },
            { name: '重复核销', value: 25 },
            { name: '异常核销', value: 15 }
          ]
        }]
      })
    }
  }, 150)

  // 优化效果图
  setTimeout(() => {
    const optimizationEl = document.getElementById('optimizationChart')
    if (optimizationEl) {
      const optimizationChart = echarts.init(optimizationEl)
      optimizationChart.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['当前效率', '优化后效率'] },
        radar: {
          indicator: [
            { name: '处理速度', max: 100 },
            { name: '准确率', max: 100 },
            { name: '风险控制', max: 100 },
            { name: '成本效益', max: 100 },
            { name: '合规性', max: 100 }
          ]
        },
        series: [{
          type: 'radar',
          data: [
            {
              value: [68, 75, 70, 65, 80],
              name: '当前效率'
            },
            {
              value: [85, 90, 88, 82, 95],
              name: '优化后效率'
            }
          ]
        }]
      })
    }
  }, 200)
}

// 初始化
onMounted(() => {
  handleSearch()
  initCharts()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.customer-name {
  font-weight: 500;
  color: #1890ff;
}

.customer-phone {
  font-size: 12px;
  color: #666;
}

.case-amount {
  font-size: 12px;
  color: #666;
}

.case-overdue {
  font-size: 12px;
  color: #ff4d4f;
}

.writeoff-amount {
  color: #ff4d4f;
  font-weight: 500;
}

.writeoff-ratio {
  font-size: 12px;
  color: #666;
}

.upload-tip {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.history-time {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.ant-table {
  background: #fff;
}

.ant-descriptions-title {
  font-weight: 500;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  color: #999;
  font-size: 16px;
}

/* 新增样式 */
.stat-footer {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.stat-change {
  font-weight: 500;
}

.stat-change.up {
  color: #52c41a;
}

.stat-change.down {
  color: #ff4d4f;
}

.stat-time,
.stat-percent {
  color: #1890ff;
  font-weight: 500;
}

.ai-score-card {
  text-align: center;
  padding: 20px;
}

.score-value {
  font-size: 48px;
  font-weight: bold;
  color: #ff4d4f;
  line-height: 1;
}

.score-label {
  font-size: 14px;
  color: #666;
  margin: 8px 0;
}

.score-desc {
  font-size: 12px;
  color: #999;
  margin-top: 12px;
}

.recommendation-actions {
  margin-top: 8px;
}

.optimization-impact {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

.policy-history-item {
  margin-bottom: 16px;
}

.policy-title {
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 4px;
}

.policy-meta {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.policy-desc {
  font-size: 13px;
  color: #666;
}

.rule-builder {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.rule-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}
/* 增强搜索样式 */
.enhanced-search {
  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-stats {
      display: flex;
      gap: 16px;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
  }

  .action-buttons {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
