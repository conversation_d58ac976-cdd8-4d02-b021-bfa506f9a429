<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>还款计划</h1>
      <p>管理客户还款计划，包括计划制定、执行跟踪、调整管理等功能</p>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card enhanced-search">
      <template #title>
        <div class="search-header">
          <span>还款计划搜索</span>
          <div class="search-stats">
            <a-statistic
              title="今日新增"
              :value="todayNewPlans"
              :value-style="{ color: '#1890ff', fontSize: '16px' }"
            />
          </div>
        </div>
      </template>
      <template #extra>
        <a-space>
          <a-button @click="toggleAdvanced">
            <span>{{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}</span>
            <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
          </a-button>
          <a-button type="primary" @click="handleQuickSearch">
            <template #icon><SearchOutlined /></template>
            快速搜索
          </a-button>
        </a-space>
      </template>

      <a-form :model="searchForm" @submit="handleSearch">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="案件编号">
              <a-input
                v-model:value="searchForm.caseNumber"
                placeholder="请输入案件编号"
                allow-clear
              >
                <template #prefix><FileTextOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户姓名">
              <a-input
                v-model:value="searchForm.customerName"
                placeholder="请输入客户姓名"
                allow-clear
              >
                <template #prefix><UserOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="计划状态">
              <a-select
                v-model:value="searchForm.status"
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option value="draft">
                  <a-tag color="gray">草稿</a-tag>
                </a-select-option>
                <a-select-option value="pending">
                  <a-tag color="orange">待审批</a-tag>
                </a-select-option>
                <a-select-option value="approved">
                  <a-tag color="blue">已批准</a-tag>
                </a-select-option>
                <a-select-option value="active">
                  <a-tag color="green">执行中</a-tag>
                </a-select-option>
                <a-select-option value="completed">
                  <a-tag color="cyan">已完成</a-tag>
                </a-select-option>
                <a-select-option value="overdue">
                  <a-tag color="red">逾期</a-tag>
                </a-select-option>
                <a-select-option value="terminated">
                  <a-tag color="purple">已终止</a-tag>
                </a-select-option>
                <a-select-option value="suspended">
                  <a-tag color="volcano">已暂停</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="计划类型">
              <a-select
                v-model:value="searchForm.planType"
                placeholder="请选择类型"
                allow-clear
              >
                <a-select-option value="full">
                  <a-tag color="blue">一次性还款</a-tag>
                </a-select-option>
                <a-select-option value="installment">
                  <a-tag color="green">分期还款</a-tag>
                </a-select-option>
                <a-select-option value="reduction">
                  <a-tag color="orange">减免计划</a-tag>
                </a-select-option>
                <a-select-option value="extension">
                  <a-tag color="purple">延期计划</a-tag>
                </a-select-option>
                <a-select-option value="settlement">
                  <a-tag color="cyan">和解计划</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="showAdvanced" style="margin-top: 16px;">
          <a-col :span="6">
            <a-form-item label="创建时间">
              <a-range-picker
                v-model:value="searchForm.createTimeRange"
                format="YYYY-MM-DD"
                style="width: 100%"
                :presets="timePresets"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="计划金额">
              <a-input-group compact>
                <a-input-number
                  v-model:value="searchForm.amountMin"
                  placeholder="最小金额"
                  :min="0"
                  style="width: 50%"
                />
                <a-input-number
                  v-model:value="searchForm.amountMax"
                  placeholder="最大金额"
                  :min="0"
                  style="width: 50%"
                />
              </a-input-group>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="协商人">
              <a-input
                v-model:value="searchForm.negotiator"
                placeholder="请输入协商人"
                allow-clear
              >
                <template #prefix><UserOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="计划期数">
              <a-input-number
                v-model:value="searchForm.installmentCount"
                placeholder="期数"
                :min="1"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="showAdvanced" style="margin-top: 16px;">
          <a-col :span="6">
            <a-form-item label="首期时间">
              <a-range-picker
                v-model:value="searchForm.firstPaymentRange"
                format="YYYY-MM-DD"
                style="width: 100%"
                :presets="timePresets"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="最后期限">
              <a-range-picker
                v-model:value="searchForm.finalPaymentRange"
                format="YYYY-MM-DD"
                style="width: 100%"
                :presets="timePresets"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="还款进度">
              <a-select
                v-model:value="searchForm.progress"
                placeholder="请选择进度"
                allow-clear
              >
                <a-select-option value="not_started">
                  <a-tag color="gray">未开始</a-tag>
                </a-select-option>
                <a-select-option value="in_progress">
                  <a-tag color="blue">进行中</a-tag>
                </a-select-option>
                <a-select-option value="delayed">
                  <a-tag color="orange">延期</a-tag>
                </a-select-option>
                <a-select-option value="completed">
                  <a-tag color="green">已完成</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="风险等级">
              <a-select
                v-model:value="searchForm.riskLevel"
                placeholder="请选择风险等级"
                allow-clear
              >
                <a-select-option value="low">
                  <a-tag color="green">低风险</a-tag>
                </a-select-option>
                <a-select-option value="medium">
                  <a-tag color="orange">中风险</a-tag>
                </a-select-option>
                <a-select-option value="high">
                  <a-tag color="red">高风险</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="24">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" html-type="submit">
                  <SearchOutlined />
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  <ReloadOutlined />
                  重置
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>

      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="活跃计划"
              :value="statistics.activePlans"
              suffix="个"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总计划金额"
              :value="statistics.totalAmount"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="已收金额"
              :value="statistics.paidAmount"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <MoneyCollectOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="执行率"
              :value="statistics.executionRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <a-space>
        <a-button type="primary" @click="showCreateModal = true">
          <PlusOutlined />
          新建计划
        </a-button>
        <a-button @click="showBatchModal = true">
          <GroupOutlined />
          批量审批
        </a-button>
        <a-button @click="showTemplateModal = true">
          <FileTextOutlined />
          计划模板
        </a-button>
        <a-button @click="showAnalysisModal = true">
          <BarChartOutlined />
          执行分析
        </a-button>
        <a-button @click="handleExport">
          <DownloadOutlined />
          导出计划
        </a-button>
        <a-button @click="handleRefresh">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag 
              :color="getStatusColor(record.status)"
              class="status-tag"
            >
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'planType'">
            <a-tag :color="getTypeColor(record.planType)">
              {{ getTypeText(record.planType) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'progress'">
            <a-progress 
              :percent="record.progressPercent" 
              :size="'small'"
              :status="getProgressStatus(record.progressPercent, record.status)"
            />
          </template>
          
          <template v-if="column.key === 'riskLevel'">
            <a-tag :color="getRiskColor(record.riskLevel)">
              {{ getRiskText(record.riskLevel) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'nextPayment'">
            <div v-if="record.nextPaymentDate" class="next-payment">
              <div class="payment-date">{{ record.nextPaymentDate }}</div>
              <div class="payment-amount">¥{{ record.nextPaymentAmount?.toLocaleString() }}</div>
            </div>
            <span v-else class="no-payment">无</span>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleEdit(record)"
                :disabled="record.status === 'completed'"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handlePayment(record)">
                <DollarOutlined />
                还款
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleApprove(record)" v-if="record.status === 'pending'">
                      <CheckOutlined />
                      审批
                    </a-menu-item>
                    <a-menu-item @click="handleAdjust(record)" v-if="['active', 'overdue'].includes(record.status)">
                      <SettingOutlined />
                      调整
                    </a-menu-item>
                    <a-menu-item @click="handleSuspend(record)" v-if="record.status === 'active'">
                      <PauseOutlined />
                      暂停
                    </a-menu-item>
                    <a-menu-item @click="handleTerminate(record)" v-if="['active', 'suspended', 'overdue'].includes(record.status)">
                      <StopOutlined />
                      终止
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDelete(record)" style="color: #ff4d4f;">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新建/编辑计划模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingRecord ? '编辑还款计划' : '新建还款计划'"
      width="1000px"
      @ok="handleCreateSave"
      @cancel="handleCreateCancel"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-tabs v-model:activeKey="formActiveKey">
          <a-tab-pane key="basic" tab="基本信息">
            <a-form-item label="案件编号" required>
              <a-input 
                v-model:value="createForm.caseNumber" 
                placeholder="请输入案件编号"
                @blur="loadCaseInfo"
              />
            </a-form-item>
            
            <a-form-item label="客户信息" required>
              <a-input v-model:value="createForm.customerName" placeholder="请输入客户姓名" />
            </a-form-item>
            
            <a-form-item label="计划类型" required>
              <a-select v-model:value="createForm.planType" placeholder="请选择计划类型" @change="handlePlanTypeChange">
                <a-select-option value="full">一次性还款</a-select-option>
                <a-select-option value="installment">分期还款</a-select-option>
                <a-select-option value="reduction">减免计划</a-select-option>
                <a-select-option value="extension">延期计划</a-select-option>
                <a-select-option value="settlement">和解计划</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="总欠款金额" required>
              <a-input-number
                v-model:value="createForm.totalAmount"
                placeholder="请输入总欠款金额"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="计划金额" required>
              <a-input-number
                v-model:value="createForm.planAmount"
                placeholder="请输入计划金额"
                :min="0"
                :max="createForm.totalAmount"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="计划描述">
              <a-textarea
                v-model:value="createForm.description"
                :rows="3"
                placeholder="请输入计划描述"
              />
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="schedule" tab="还款计划">
            <div v-if="createForm.planType === 'full'">
              <a-form-item label="还款日期" required>
                <a-date-picker
                  v-model:value="createForm.paymentDate"
                  placeholder="选择还款日期"
                  style="width: 100%"
                />
              </a-form-item>
            </div>
            
            <div v-if="createForm.planType === 'installment'">
              <a-form-item label="分期数量" required>
                <a-input-number
                  v-model:value="createForm.installmentCount"
                  placeholder="请输入分期数量"
                  :min="2"
                  :max="36"
                  style="width: 100%"
                  @change="generateInstallments"
                />
              </a-form-item>
              
              <a-form-item label="首期日期" required>
                <a-date-picker
                  v-model:value="createForm.firstPaymentDate"
                  placeholder="选择首期还款日期"
                  style="width: 100%"
                  @change="generateInstallments"
                />
              </a-form-item>
              
              <a-form-item label="分期间隔">
                <a-radio-group v-model:value="createForm.paymentInterval" @change="generateInstallments">
                  <a-radio value="monthly">月付</a-radio>
                  <a-radio value="quarterly">季付</a-radio>
                  <a-radio value="semi-annually">半年付</a-radio>
                </a-radio-group>
              </a-form-item>
              
              <a-form-item label="分期明细">
                <a-table
                  :columns="installmentColumns"
                  :data-source="createForm.installments"
                  :pagination="false"
                  size="small"
                  bordered
                >
                  <template #bodyCell="{ column, record, index }">
                    <template v-if="column.key === 'amount'">
                      <a-input-number
                        v-model:value="record.amount"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        @change="updateTotalInstallmentAmount"
                      />
                    </template>
                    <template v-if="column.key === 'dueDate'">
                      <a-date-picker
                        v-model:value="record.dueDate"
                        style="width: 100%"
                      />
                    </template>
                    <template v-if="column.key === 'action'">
                      <a-button 
                        type="link" 
                        size="small" 
                        danger
                        @click="removeInstallment(index)"
                        v-if="createForm.installments.length > 1"
                      >
                        删除
                      </a-button>
                    </template>
                  </template>
                </a-table>
                <a-button 
                  type="dashed" 
                  @click="addInstallment"
                  style="width: 100%; margin-top: 8px;"
                >
                  <PlusOutlined />
                  添加分期
                </a-button>
              </a-form-item>
            </div>
            
            <div v-if="createForm.planType === 'reduction'">
              <a-form-item label="减免金额" required>
                <a-input-number
                  v-model:value="createForm.reductionAmount"
                  placeholder="请输入减免金额"
                  :min="0"
                  :max="createForm.totalAmount"
                  :precision="2"
                  style="width: 100%"
                >
                  <template #addonAfter>元</template>
                </a-input-number>
              </a-form-item>
              
              <a-form-item label="减免原因" required>
                <a-textarea
                  v-model:value="createForm.reductionReason"
                  :rows="3"
                  placeholder="请输入减免原因"
                />
              </a-form-item>
              
              <a-form-item label="剩余还款日期" required>
                <a-date-picker
                  v-model:value="createForm.remainingPaymentDate"
                  placeholder="选择剩余金额还款日期"
                  style="width: 100%"
                />
              </a-form-item>
            </div>
            
            <div v-if="createForm.planType === 'extension'">
              <a-form-item label="延期时间" required>
                <a-input-number
                  v-model:value="createForm.extensionDays"
                  placeholder="请输入延期天数"
                  :min="1"
                  :max="365"
                  style="width: 100%"
                >
                  <template #addonAfter>天</template>
                </a-input-number>
              </a-form-item>
              
              <a-form-item label="新还款日期" required>
                <a-date-picker
                  v-model:value="createForm.newPaymentDate"
                  placeholder="选择新的还款日期"
                  style="width: 100%"
                />
              </a-form-item>
              
              <a-form-item label="延期费用">
                <a-input-number
                  v-model:value="createForm.extensionFee"
                  placeholder="请输入延期费用"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                >
                  <template #addonAfter>元</template>
                </a-input-number>
              </a-form-item>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="terms" tab="条款设置">
            <a-form-item label="特殊条款">
              <a-checkbox-group v-model:value="createForm.specialTerms">
                <a-checkbox value="early_payment_discount">提前还款优惠</a-checkbox>
                <a-checkbox value="late_payment_penalty">逾期罚息</a-checkbox>
                <a-checkbox value="auto_extension">自动延期</a-checkbox>
                <a-checkbox value="partial_payment">允许部分还款</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="提前还款优惠" v-if="createForm.specialTerms.includes('early_payment_discount')">
              <a-input-number
                v-model:value="createForm.earlyPaymentDiscount"
                placeholder="请输入优惠率"
                :min="0"
                :max="50"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>%</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="逾期罚息率" v-if="createForm.specialTerms.includes('late_payment_penalty')">
              <a-input-number
                v-model:value="createForm.latePenaltyRate"
                placeholder="请输入罚息率"
                :min="0"
                :max="30"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>%</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="最小还款金额" v-if="createForm.specialTerms.includes('partial_payment')">
              <a-input-number
                v-model:value="createForm.minPaymentAmount"
                placeholder="请输入最小还款金额"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="备注信息">
              <a-textarea
                v-model:value="createForm.remarks"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </a-form-item>
            
            <a-form-item label="附件文件">
              <a-upload
                v-model:file-list="createForm.attachments"
                :before-upload="beforeUpload"
                multiple
              >
                <a-button>
                  <UploadOutlined />
                  上传附件
                </a-button>
              </a-upload>
            </a-form-item>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-modal>

    <!-- 计划详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="还款计划详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="案件编号">{{ selectedRecord.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedRecord.customerName }}</a-descriptions-item>
          <a-descriptions-item label="计划类型">
            <a-tag :color="getTypeColor(selectedRecord.planType)">
              {{ getTypeText(selectedRecord.planType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="计划状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="总欠款金额">¥{{ selectedRecord.totalAmount?.toLocaleString() }}</a-descriptions-item>
          <a-descriptions-item label="计划金额">¥{{ selectedRecord.planAmount?.toLocaleString() }}</a-descriptions-item>
          <a-descriptions-item label="已还金额">¥{{ selectedRecord.paidAmount?.toLocaleString() }}</a-descriptions-item>
          <a-descriptions-item label="剩余金额">¥{{ selectedRecord.remainingAmount?.toLocaleString() }}</a-descriptions-item>
          <a-descriptions-item label="执行进度">
            <a-progress :percent="selectedRecord.progressPercent" />
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ selectedRecord.createTime }}</a-descriptions-item>
          <a-descriptions-item label="协商人">{{ selectedRecord.negotiator }}</a-descriptions-item>
          <a-descriptions-item label="风险等级">
            <a-tag :color="getRiskColor(selectedRecord.riskLevel)">
              {{ getRiskText(selectedRecord.riskLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="计划描述" :span="3">
            <div class="description-content">{{ selectedRecord.description }}</div>
          </a-descriptions-item>
        </a-descriptions>
        
        <!-- 还款明细 -->
        <a-card title="还款明细" style="margin-top: 16px;">
          <a-table
            :columns="paymentDetailColumns"
            :data-source="selectedRecord.paymentDetails"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getPaymentStatusColor(record.status)">
                  {{ getPaymentStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'amount'">
                ¥{{ record.amount?.toLocaleString() }}
              </template>
            </template>
          </a-table>
        </a-card>
        
        <!-- 操作历史 -->
        <a-card title="操作历史" style="margin-top: 16px;">
          <a-timeline>
            <a-timeline-item 
              v-for="(history, index) in selectedRecord.operationHistory" 
              :key="index"
              :color="getHistoryColor(history.type)"
            >
              <div class="history-content">
                <div class="history-header">
                  <span class="history-title">{{ history.title }}</span>
                  <span class="history-time">{{ history.time }}</span>
                </div>
                <div class="history-detail">{{ history.detail }}</div>
                <div class="history-operator">操作人：{{ history.operator }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </div>
    </a-modal>

    <!-- 批量审批模态框 -->
    <a-modal
      v-model:open="showBatchModal"
      title="批量审批"
      width="800px"
      @ok="handleBatchApprove"
    >
      <div class="batch-approval-section">
        <a-alert
          message="批量审批提示"
          description="请仔细审核每个计划的详细信息，审批后将无法撤销。"
          type="warning"
          show-icon
          style="margin-bottom: 16px;"
        />
        
        <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="审批结果" required>
            <a-radio-group v-model:value="batchApprovalForm.result">
              <a-radio value="approve">批准</a-radio>
              <a-radio value="reject">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="审批意见">
            <a-textarea
              v-model:value="batchApprovalForm.opinion"
              :rows="4"
              placeholder="请输入审批意见"
            />
          </a-form-item>
        </a-form>
        
        <a-divider>待审批计划列表</a-divider>
        
        <a-table
          :columns="batchApprovalColumns"
          :data-source="selectedPlansForApproval"
          :pagination="false"
          size="small"
          :scroll="{ y: 300 }"
        />
        
        <div class="batch-summary" style="margin-top: 16px;">
          <a-statistic-group>
            <a-statistic 
              title="选中计划" 
              :value="selectedPlansForApproval.length" 
              suffix="个" 
            />
            <a-statistic 
              title="总金额" 
              :value="selectedPlansForApproval.reduce((sum, plan) => sum + plan.planAmount, 0)"
              prefix="¥"
              :precision="2"
            />
          </a-statistic-group>
        </div>
      </div>
    </a-modal>
    
    <!-- 计划模板管理模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="计划模板管理"
      width="1200px"
      :footer="null"
    >
      <a-tabs v-model:activeKey="templateActiveKey">
        <a-tab-pane key="list" tab="模板列表">
          <div class="template-actions">
            <a-space>
              <a-input-search
                v-model:value="templateSearch"
                placeholder="搜索模板"
                style="width: 300px;"
              />
              <a-button type="primary" @click="showCreateTemplate = true">
                <PlusOutlined />
                新建模板
              </a-button>
            </a-space>
          </div>
          
          <a-table
            :columns="templateColumns"
            :data-source="filteredTemplates"
            :pagination="false"
            size="small"
            style="margin-top: 16px;"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'planType'">
                <a-tag :color="getTypeColor(record.planType)">
                  {{ getTypeText(record.planType) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleUseTemplate(record)">
                    <PlayCircleOutlined />
                    使用
                  </a-button>
                  <a-button type="link" size="small" @click="handleEditTemplate(record)">
                    <EditOutlined />
                    编辑
                  </a-button>
                  <a-button type="link" size="small" @click="handleCopyTemplate(record)">
                    <CopyOutlined />
                    复制
                  </a-button>
                  <a-popconfirm title="确定删除？" @confirm="handleDeleteTemplate(record)">
                    <a-button type="link" size="small" danger>
                      <DeleteOutlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="create" tab="创建模板">
          <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <a-form-item label="模板名称" required>
              <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
            </a-form-item>
            
            <a-form-item label="适用场景" required>
              <a-textarea 
                v-model:value="templateForm.scenario" 
                :rows="2"
                placeholder="请输入适用场景描述" 
              />
            </a-form-item>
            
            <a-form-item label="计划类型" required>
              <a-select v-model:value="templateForm.planType" placeholder="请选择计划类型">
                <a-select-option value="full">一次性还款</a-select-option>
                <a-select-option value="installment">分期还款</a-select-option>
                <a-select-option value="reduction">减免计划</a-select-option>
                <a-select-option value="extension">延期计划</a-select-option>
                <a-select-option value="settlement">和解计划</a-select-option>
              </a-select>
            </a-form-item>
            
            <div v-if="templateForm.planType === 'installment'">
              <a-form-item label="默认期数">
                <a-input-number
                  v-model:value="templateForm.config.periods"
                  :min="2"
                  :max="36"
                  style="width: 100%"
                />
              </a-form-item>
              
              <a-form-item label="首付比例">
                <a-input-number
                  v-model:value="templateForm.config.downPaymentRate"
                  :min="0"
                  :max="50"
                  :precision="2"
                  style="width: 100%"
                >
                  <template #addonAfter>%</template>
                </a-input-number>
              </a-form-item>
              
              <a-form-item label="还款间隔">
                <a-select v-model:value="templateForm.config.interval">
                  <a-select-option value="monthly">月付</a-select-option>
                  <a-select-option value="quarterly">季付</a-select-option>
                  <a-select-option value="semi-annually">半年付</a-select-option>
                </a-select>
              </a-form-item>
            </div>
            
            <div v-if="templateForm.planType === 'reduction'">
              <a-form-item label="减免比例">
                <a-input-number
                  v-model:value="templateForm.config.reductionRate"
                  :min="0"
                  :max="50"
                  :precision="2"
                  style="width: 100%"
                >
                  <template #addonAfter>%</template>
                </a-input-number>
              </a-form-item>
              
              <a-form-item label="还款期限">
                <a-input-number
                  v-model:value="templateForm.config.paymentDays"
                  :min="7"
                  :max="365"
                  style="width: 100%"
                >
                  <template #addonAfter>天</template>
                </a-input-number>
              </a-form-item>
            </div>
            
            <a-form-item :wrapper-col="{ offset: 4 }">
              <a-space>
                <a-button type="primary" @click="handleSaveTemplate">
                  保存模板
                </a-button>
                <a-button @click="handleCancelTemplate">
                  取消
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
    
    <!-- 执行分析模态框 -->
    <a-modal
      v-model:open="showAnalysisModal"
      title="执行分析"
      width="1400px"
      :footer="null"
    >
      <a-tabs v-model:activeKey="analysisActiveKey">
        <a-tab-pane key="overview" tab="总体分析">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="计划类型分布">
                <div id="typeChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="执行状态分布">
                <div id="statusChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
          
          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <a-card title="执行趋势分析">
                <div id="trendChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
          
          <div class="ai-analysis" style="margin-top: 16px;">
            <a-card title="AI 智能分析">
              <a-space>
                <a-button type="primary" @click="performAIAnalysis">
                  <ThunderboltOutlined />
                  生成分析报告
                </a-button>
                <a-button @click="exportAnalysis">
                  <DownloadOutlined />
                  导出分析
                </a-button>
              </a-space>
            </a-card>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="performance" tab="执行绩效">
          <a-card title="协商人员绩效">
            <a-table
              :columns="performanceColumns"
              :data-source="performanceData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'completionRate'">
                  <a-progress :percent="record.completionRate" size="small" />
                </template>
                <template v-if="column.key === 'onTimeRate'">
                  <a-progress :percent="record.onTimeRate" size="small" />
                </template>
              </template>
            </a-table>
          </a-card>
          
          <a-card title="月度执行对比" style="margin-top: 16px;">
            <div id="performanceChart" style="height: 400px;"></div>
          </a-card>
        </a-tab-pane>
        
        <a-tab-pane key="risk" tab="风险分析">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="风险等级分布">
                <div id="riskChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="逾期风险预警">
                <a-list
                  size="small"
                  :data-source="riskWarnings"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <a-avatar :style="{ backgroundColor: getRiskColor(item.level) }">
                            {{ item.level === 'high' ? 'H' : item.level === 'medium' ? 'M' : 'L' }}
                          </a-avatar>
                        </template>
                        <template #title>
                          <span>{{ item.caseNumber }} - {{ item.customerName }}</span>
                        </template>
                        <template #description>
                          {{ item.description }}
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
    
    <!-- 还款模态框 -->
    <a-modal
      v-model:open="showPaymentModal"
      title="还款操作"
      width="800px"
      @ok="handlePaymentSave"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="期数" required>
          <a-select v-model:value="paymentForm.period" placeholder="请选择还款期数">
            <a-select-option 
              v-for="detail in selectedRecord?.paymentDetails?.filter(d => d.status === 'pending')"
              :key="detail.period"
              :value="detail.period"
            >
              第{{ detail.period }}期 - ¥{{ detail.amount?.toLocaleString() }} ({{ detail.dueDate }})
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="应还金额">
          <a-input-number
            v-model:value="paymentForm.dueAmount"
            :precision="2"
            disabled
            style="width: 100%"
          >
            <template #addonAfter>元</template>
          </a-input-number>
        </a-form-item>
        
        <a-form-item label="实际金额" required>
          <a-input-number
            v-model:value="paymentForm.actualAmount"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入实际还款金额"
          >
            <template #addonAfter>元</template>
          </a-input-number>
        </a-form-item>
        
        <a-form-item label="还款日期" required>
          <a-date-picker
            v-model:value="paymentForm.paymentDate"
            placeholder="选择还款日期"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="还款方式">
          <a-select v-model:value="paymentForm.paymentMethod" placeholder="请选择还款方式">
            <a-select-option value="bank_transfer">银行转账</a-select-option>
            <a-select-option value="cash">现金</a-select-option>
            <a-select-option value="check">支票</a-select-option>
            <a-select-option value="online">线上支付</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="收据编号">
          <a-input
            v-model:value="paymentForm.receiptNumber"
            placeholder="请输入收据编号"
          />
        </a-form-item>
        
        <a-form-item label="备注">
          <a-textarea
            v-model:value="paymentForm.remarks"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 计划调整模态框 -->
    <a-modal
      v-model:open="showAdjustModal"
      title="计划调整"
      width="1000px"
      @ok="handleAdjustSave"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="调整类型" required>
          <a-radio-group v-model:value="adjustForm.type">
            <a-radio value="amount">金额调整</a-radio>
            <a-radio value="date">日期调整</a-radio>
            <a-radio value="both">金额和日期</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="调整原因" required>
          <a-textarea
            v-model:value="adjustForm.reason"
            :rows="3"
            placeholder="请输入调整原因"
          />
        </a-form-item>
        
        <a-form-item label="调整明细">
          <a-table
            :columns="adjustColumns"
            :data-source="adjustForm.details"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'newAmount'">
                <a-input-number
                  v-model:value="record.newAmount"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  :disabled="!['amount', 'both'].includes(adjustForm.type)"
                />
              </template>
              <template v-if="column.key === 'newDate'">
                <a-date-picker
                  v-model:value="record.newDate"
                  style="width: 100%"
                  :disabled="!['date', 'both'].includes(adjustForm.type)"
                />
              </template>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  UpOutlined,
  PlusOutlined,
  GroupOutlined,
  FileTextOutlined,
  BarChartOutlined,
  DownloadOutlined,
  CalendarOutlined,
  UserOutlined,
  DollarOutlined,
  MoneyCollectOutlined,
  TrophyOutlined,
  EyeOutlined,
  EditOutlined,
  CheckOutlined,
  SettingOutlined,
  PauseOutlined,
  StopOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const showAdvanced = ref(false)

// 今日新增计划数
const todayNewPlans = ref(23)

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 模态框显示状态
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showBatchModal = ref(false)
const showTemplateModal = ref(false)
const showAnalysisModal = ref(false)
const showPaymentModal = ref(false)
const showAdjustModal = ref(false)
const showCreateTemplate = ref(false)

// 编辑状态
const editingRecord = ref(null)
const selectedRecord = ref(null)

// Tab激活键
const formActiveKey = ref('basic')
const templateActiveKey = ref('list')
const analysisActiveKey = ref('overview')

// 表单数据
const searchForm = reactive({
  caseNumber: '',
  customerName: '',
  status: undefined,
  planType: undefined,
  createTimeRange: [],
  amountMin: undefined,
  amountMax: undefined,
  negotiator: '',
  installmentCount: undefined,
  firstPaymentRange: [],
  finalPaymentRange: [],
  progress: undefined,
  riskLevel: undefined
})

const createForm = reactive({
  caseNumber: '',
  customerName: '',
  planType: '',
  totalAmount: 0,
  planAmount: 0,
  description: '',
  // 一次性还款
  paymentDate: null,
  // 分期还款
  installmentCount: 3,
  firstPaymentDate: null,
  paymentInterval: 'monthly',
  installments: [],
  // 减免计划
  reductionAmount: 0,
  reductionReason: '',
  remainingPaymentDate: null,
  // 延期计划
  extensionDays: 30,
  newPaymentDate: null,
  extensionFee: 0,
  // 特殊条款
  specialTerms: [],
  earlyPaymentDiscount: 0,
  latePenaltyRate: 0,
  minPaymentAmount: 0,
  remarks: '',
  attachments: []
})

// 统计数据
const statistics = reactive({
  activePlans: 128,
  totalAmount: 2456789.00,
  paidAmount: 1876543.00,
  executionRate: 76.4
})

// 表格列定义
const columns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    sorter: true
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '计划类型',
    dataIndex: 'planType',
    key: 'planType',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '草稿', value: 'draft' },
      { text: '待审批', value: 'pending' },
      { text: '已批准', value: 'approved' },
      { text: '执行中', value: 'active' },
      { text: '已完成', value: 'completed' },
      { text: '逾期', value: 'overdue' },
      { text: '已终止', value: 'terminated' },
      { text: '已暂停', value: 'suspended' }
    ]
  },
  {
    title: '计划金额',
    dataIndex: 'planAmount',
    key: 'planAmount',
    width: 120,
    sorter: true,
    customRender: ({ text }) => `¥${text?.toLocaleString()}`
  },
  {
    title: '已还金额',
    dataIndex: 'paidAmount',
    key: 'paidAmount',
    width: 120,
    sorter: true,
    customRender: ({ text }) => `¥${text?.toLocaleString()}`
  },
  {
    title: '执行进度',
    key: 'progress',
    width: 120
  },
  {
    title: '下次还款',
    key: 'nextPayment',
    width: 150
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 分期表格列
const installmentColumns = [
  {
    title: '期数',
    dataIndex: 'period',
    key: 'period',
    width: 80
  },
  {
    title: '应还金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 150
  },
  {
    title: '到期日期',
    dataIndex: 'dueDate',
    key: 'dueDate',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 还款明细表格列
const paymentDetailColumns = [
  {
    title: '期数',
    dataIndex: 'period',
    key: 'period',
    width: 80
  },
  {
    title: '应还金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120
  },
  {
    title: '到期日期',
    dataIndex: 'dueDate',
    key: 'dueDate',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '实际还款日期',
    dataIndex: 'actualPayDate',
    key: 'actualPayDate',
    width: 140
  },
  {
    title: '实际还款金额',
    dataIndex: 'actualAmount',
    key: 'actualAmount',
    width: 140,
    customRender: ({ text }) => text ? `¥${text.toLocaleString()}` : '-'
  }
]

// 表格数据
const tableData = ref([
  {
    key: '1',
    caseNumber: 'CS202401001',
    customerName: '张三',
    planType: 'installment',
    status: 'active',
    totalAmount: 100000,
    planAmount: 100000,
    paidAmount: 30000,
    remainingAmount: 70000,
    progressPercent: 30,
    nextPaymentDate: '2024-02-15',
    nextPaymentAmount: 20000,
    riskLevel: 'low',
    createTime: '2024-01-10 10:30:00',
    negotiator: '李四',
    description: '分期还款计划，共5期还清',
    paymentDetails: [
      {
        period: 1,
        amount: 20000,
        dueDate: '2024-01-15',
        status: 'paid',
        actualPayDate: '2024-01-15',
        actualAmount: 20000
      },
      {
        period: 2,
        amount: 20000,
        dueDate: '2024-02-15',
        status: 'pending',
        actualPayDate: null,
        actualAmount: null
      },
      {
        period: 3,
        amount: 20000,
        dueDate: '2024-03-15',
        status: 'pending',
        actualPayDate: null,
        actualAmount: null
      },
      {
        period: 4,
        amount: 20000,
        dueDate: '2024-04-15',
        status: 'pending',
        actualPayDate: null,
        actualAmount: null
      },
      {
        period: 5,
        amount: 20000,
        dueDate: '2024-05-15',
        status: 'pending',
        actualPayDate: null,
        actualAmount: null
      }
    ],
    operationHistory: [
      {
        type: 'create',
        title: '创建计划',
        time: '2024-01-10 10:30:00',
        detail: '创建分期还款计划，共5期',
        operator: '李四'
      },
      {
        type: 'approve',
        title: '审批通过',
        time: '2024-01-11 09:00:00',
        detail: '计划审批通过，开始执行',
        operator: '王五'
      },
      {
        type: 'payment',
        title: '首期还款',
        time: '2024-01-15 14:20:00',
        detail: '客户完成首期还款20000元',
        operator: '系统'
      }
    ]
  },
  {
    key: '2',
    caseNumber: 'CS202401002',
    customerName: '李四',
    planType: 'reduction',
    status: 'pending',
    totalAmount: 80000,
    planAmount: 60000,
    paidAmount: 0,
    remainingAmount: 60000,
    progressPercent: 0,
    nextPaymentDate: '2024-02-20',
    nextPaymentAmount: 60000,
    riskLevel: 'medium',
    createTime: '2024-01-12 15:45:00',
    negotiator: '赵六',
    description: '减免20000元，一次性还60000元',
    paymentDetails: [
      {
        period: 1,
        amount: 60000,
        dueDate: '2024-02-20',
        status: 'pending',
        actualPayDate: null,
        actualAmount: null
      }
    ],
    operationHistory: [
      {
        type: 'create',
        title: '创建计划',
        time: '2024-01-12 15:45:00',
        detail: '创建减免计划，减免20000元',
        operator: '赵六'
      }
    ]
  },
  {
    key: '3',
    caseNumber: 'CS202401003',
    customerName: '王五',
    planType: 'full',
    status: 'completed',
    totalAmount: 50000,
    planAmount: 50000,
    paidAmount: 50000,
    remainingAmount: 0,
    progressPercent: 100,
    nextPaymentDate: null,
    nextPaymentAmount: null,
    riskLevel: 'low',
    createTime: '2024-01-08 09:20:00',
    negotiator: '孙七',
    description: '一次性还清全部欠款',
    paymentDetails: [
      {
        period: 1,
        amount: 50000,
        dueDate: '2024-01-20',
        status: 'paid',
        actualPayDate: '2024-01-18',
        actualAmount: 50000
      }
    ],
    operationHistory: [
      {
        type: 'create',
        title: '创建计划',
        time: '2024-01-08 09:20:00',
        detail: '创建一次性还款计划',
        operator: '孙七'
      },
      {
        type: 'approve',
        title: '审批通过',
        time: '2024-01-08 16:00:00',
        detail: '计划审批通过',
        operator: '李四'
      },
      {
        type: 'payment',
        title: '完成还款',
        time: '2024-01-18 11:30:00',
        detail: '客户提前完成还款50000元',
        operator: '系统'
      },
      {
        type: 'complete',
        title: '计划完成',
        time: '2024-01-18 11:31:00',
        detail: '计划执行完成',
        operator: '系统'
      }
    ]
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    draft: 'default',
    pending: 'orange',
    approved: 'blue',
    active: 'green',
    completed: 'success',
    overdue: 'red',
    terminated: 'error',
    suspended: 'warning'
  }
  return colorMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    draft: '草稿',
    pending: '待审批',
    approved: '已批准',
    active: '执行中',
    completed: '已完成',
    overdue: '逾期',
    terminated: '已终止',
    suspended: '已暂停'
  }
  return textMap[status] || status
}

// 类型颜色映射
const getTypeColor = (type) => {
  const colorMap = {
    full: 'blue',
    installment: 'green',
    reduction: 'orange',
    extension: 'purple',
    settlement: 'cyan'
  }
  return colorMap[type] || 'default'
}

// 类型文本映射
const getTypeText = (type) => {
  const textMap = {
    full: '一次性还款',
    installment: '分期还款',
    reduction: '减免计划',
    extension: '延期计划',
    settlement: '和解计划'
  }
  return textMap[type] || type
}

// 风险颜色映射
const getRiskColor = (risk) => {
  const colorMap = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colorMap[risk] || 'default'
}

// 风险文本映射
const getRiskText = (risk) => {
  const textMap = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return textMap[risk] || risk
}

// 进度状态
const getProgressStatus = (percent, status) => {
  if (status === 'completed') return 'success'
  if (status === 'overdue') return 'exception'
  if (percent > 80) return 'success'
  if (percent > 50) return 'active'
  return 'normal'
}

// 还款状态颜色
const getPaymentStatusColor = (status) => {
  const colorMap = {
    pending: 'orange',
    paid: 'green',
    overdue: 'red',
    partial: 'blue'
  }
  return colorMap[status] || 'default'
}

// 还款状态文本
const getPaymentStatusText = (status) => {
  const textMap = {
    pending: '待还款',
    paid: '已还款',
    overdue: '逾期',
    partial: '部分还款'
  }
  return textMap[status] || status
}

// 历史颜色
const getHistoryColor = (type) => {
  const colorMap = {
    create: 'blue',
    approve: 'green',
    payment: 'purple',
    adjust: 'orange',
    suspend: 'yellow',
    terminate: 'red',
    complete: 'success'
  }
  return colorMap[type] || 'default'
}

// 事件处理函数
const handleSearch = () => {
  loading.value = true
  console.log('搜索条件:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  message.success('已重置搜索条件')
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 快速搜索
const handleQuickSearch = () => {
  handleSearch()
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 800)
}

const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变化:', pag, filters, sorter)
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleView = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleEdit = (record) => {
  editingRecord.value = record
  Object.assign(createForm, {
    caseNumber: record.caseNumber,
    customerName: record.customerName,
    planType: record.planType,
    totalAmount: record.totalAmount,
    planAmount: record.planAmount,
    description: record.description,
    // 清空其他字段
    paymentDate: null,
    installmentCount: 3,
    firstPaymentDate: null,
    paymentInterval: 'monthly',
    installments: [],
    reductionAmount: 0,
    reductionReason: '',
    remainingPaymentDate: null,
    extensionDays: 30,
    newPaymentDate: null,
    extensionFee: 0,
    specialTerms: [],
    earlyPaymentDiscount: 0,
    latePenaltyRate: 0,
    minPaymentAmount: 0,
    remarks: '',
    attachments: []
  })
  showCreateModal.value = true
}

const handlePayment = (record) => {
  selectedRecord.value = record
  const pendingPayments = record.paymentDetails?.filter(d => d.status === 'pending')
  if (pendingPayments && pendingPayments.length > 0) {
    const firstPending = pendingPayments[0]
    paymentForm.period = firstPending.period
    paymentForm.dueAmount = firstPending.amount
    paymentForm.actualAmount = firstPending.amount
    paymentForm.paymentDate = null
  }
  showPaymentModal.value = true
}

const handleApprove = (record) => {
  Modal.confirm({
    title: '审批确认',
    content: `确定要批准案件 ${record.caseNumber} 的还款计划吗？`,
    onOk() {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        const index = tableData.value.findIndex(item => item.key === record.key)
        if (index !== -1) {
          tableData.value[index].status = 'approved'
          tableData.value[index].operationHistory.push({
            type: 'approve',
            title: '审批通过',
            time: new Date().toLocaleString(),
            detail: '计划审批通过，开始执行',
            operator: '当前用户'
          })
        }
        message.success('计划已审批通过')
      }, 1000)
    }
  })
}

const handleAdjust = (record) => {
  selectedRecord.value = record
  adjustForm.type = 'amount'
  adjustForm.reason = ''
  adjustForm.details = record.paymentDetails
    ?.filter(d => d.status === 'pending')
    ?.map(d => ({
      ...d,
      newAmount: d.amount,
      newDate: d.dueDate
    })) || []
  showAdjustModal.value = true
}

const handleSuspend = (record) => {
  Modal.confirm({
    title: '暂停确认',
    content: `确定要暂停案件 ${record.caseNumber} 的还款计划吗？`,
    onOk() {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        const index = tableData.value.findIndex(item => item.key === record.key)
        if (index !== -1) {
          tableData.value[index].status = 'suspended'
          tableData.value[index].operationHistory.push({
            type: 'suspend',
            title: '计划暂停',
            time: new Date().toLocaleString(),
            detail: '计划暂停执行',
            operator: '当前用户'
          })
        }
        message.success('计划已暂停')
      }, 1000)
    }
  })
}

const handleTerminate = (record) => {
  Modal.confirm({
    title: '终止确认',
    content: `确定要终止案件 ${record.caseNumber} 的还款计划吗？此操作不可撤销。`,
    onOk() {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        const index = tableData.value.findIndex(item => item.key === record.key)
        if (index !== -1) {
          tableData.value[index].status = 'terminated'
          tableData.value[index].operationHistory.push({
            type: 'terminate',
            title: '计划终止',
            time: new Date().toLocaleString(),
            detail: '计划强制终止',
            operator: '当前用户'
          })
        }
        message.success('计划已终止')
      }, 1000)
    }
  })
}

const handleDelete = (record) => {
  Modal.confirm({
    title: '删除确认',
    content: `确定要删除案件 ${record.caseNumber} 的还款计划吗？此操作不可撤销。`,
    onOk() {
      const index = tableData.value.findIndex(item => item.key === record.key)
      if (index !== -1) {
        tableData.value.splice(index, 1)
        pagination.total--
        message.success('计划已删除')
      }
    }
  })
}

const handleCreateSave = () => {
  console.log('保存计划:', createForm)
  message.success('还款计划已保存')
  showCreateModal.value = false
  handleCreateCancel()
}

const handleCreateCancel = () => {
  editingRecord.value = null
  Object.assign(createForm, {
    caseNumber: '',
    customerName: '',
    planType: '',
    totalAmount: 0,
    planAmount: 0,
    description: '',
    paymentDate: null,
    installmentCount: 3,
    firstPaymentDate: null,
    paymentInterval: 'monthly',
    installments: [],
    reductionAmount: 0,
    reductionReason: '',
    remainingPaymentDate: null,
    extensionDays: 30,
    newPaymentDate: null,
    extensionFee: 0,
    specialTerms: [],
    earlyPaymentDiscount: 0,
    latePenaltyRate: 0,
    minPaymentAmount: 0,
    remarks: '',
    attachments: []
  })
  formActiveKey.value = 'basic'
}

const loadCaseInfo = () => {
  // 加载案件信息自动填充
  console.log('加载案件信息:', createForm.caseNumber)
  if (createForm.caseNumber) {
    // 模拟加载案件信息
    createForm.customerName = '张三'
    createForm.totalAmount = 100000
    createForm.planAmount = 100000
  }
}

const handlePlanTypeChange = (value) => {
  // 清空相关字段
  if (value === 'installment') {
    generateInstallments()
  }
}

const generateInstallments = () => {
  if (!createForm.installmentCount || !createForm.firstPaymentDate || !createForm.planAmount) {
    return
  }
  
  const installments = []
  const averageAmount = Math.floor(createForm.planAmount / createForm.installmentCount)
  const remainder = createForm.planAmount % createForm.installmentCount
  
  for (let i = 0; i < createForm.installmentCount; i++) {
    let amount = averageAmount
    if (i === createForm.installmentCount - 1) {
      amount += remainder // 最后一期加上余数
    }
    
    // 计算到期日期
    let dueDate = new Date(createForm.firstPaymentDate)
    if (createForm.paymentInterval === 'monthly') {
      dueDate.setMonth(dueDate.getMonth() + i)
    } else if (createForm.paymentInterval === 'quarterly') {
      dueDate.setMonth(dueDate.getMonth() + i * 3)
    } else if (createForm.paymentInterval === 'semi-annually') {
      dueDate.setMonth(dueDate.getMonth() + i * 6)
    }
    
    installments.push({
      period: i + 1,
      amount: amount,
      dueDate: dueDate
    })
  }
  
  createForm.installments = installments
}

const addInstallment = () => {
  const lastPeriod = createForm.installments.length > 0 
    ? createForm.installments[createForm.installments.length - 1].period 
    : 0
  
  createForm.installments.push({
    period: lastPeriod + 1,
    amount: 0,
    dueDate: null
  })
  
  createForm.installmentCount = createForm.installments.length
}

const removeInstallment = (index) => {
  createForm.installments.splice(index, 1)
  // 重新计算期数
  createForm.installments.forEach((item, idx) => {
    item.period = idx + 1
  })
  createForm.installmentCount = createForm.installments.length
}

const updateTotalInstallmentAmount = () => {
  const total = createForm.installments.reduce((sum, item) => sum + (item.amount || 0), 0)
  if (total !== createForm.planAmount) {
    message.warning(`分期总金额(${total})与计划金额(${createForm.planAmount})不一致`)
  }
}

const beforeUpload = (file) => {
  createForm.attachments.push(file)
  return false
}

const handleExport = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    // 模拟下载文件
    const link = document.createElement('a')
    const headers = '案件编号,客户姓名,计划类型,状态,计划金额,已还金额,执行进度,创建时间\n'
    const data = tableData.value.map(record => 
      `${record.caseNumber},${record.customerName},${getTypeText(record.planType)},${getStatusText(record.status)},${record.planAmount},${record.paidAmount},${record.progressPercent}%,${record.createTime}`
    ).join('\n')
    
    link.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(headers + data)
    link.download = `还款计划_${new Date().toLocaleDateString().replace(/\//g, '-')}.csv`
    link.click()
    
    message.success('导出成功')
  }, 1000)
}

// 生成还款明细
const generatePaymentDetails = () => {
  const details = []
  
  if (createForm.planType === 'full') {
    details.push({
      period: 1,
      amount: createForm.planAmount,
      dueDate: createForm.paymentDate?.format('YYYY-MM-DD') || '',
      status: 'pending',
      actualPayDate: null,
      actualAmount: null
    })
  } else if (createForm.planType === 'installment') {
    createForm.installments.forEach(item => {
      details.push({
        period: item.period,
        amount: item.amount,
        dueDate: item.dueDate?.format ? item.dueDate.format('YYYY-MM-DD') : item.dueDate,
        status: 'pending',
        actualPayDate: null,
        actualAmount: null
      })
    })
  } else if (createForm.planType === 'reduction') {
    details.push({
      period: 1,
      amount: createForm.planAmount - createForm.reductionAmount,
      dueDate: createForm.remainingPaymentDate?.format('YYYY-MM-DD') || '',
      status: 'pending',
      actualPayDate: null,
      actualAmount: null
    })
  } else if (createForm.planType === 'extension') {
    details.push({
      period: 1,
      amount: createForm.planAmount + (createForm.extensionFee || 0),
      dueDate: createForm.newPaymentDate?.format('YYYY-MM-DD') || '',
      status: 'pending',
      actualPayDate: null,
      actualAmount: null
    })
  }
  
  return details
}

// 获取下次还款日期
const getNextPaymentDate = () => {
  const details = generatePaymentDetails()
  const nextPayment = details.find(d => d.status === 'pending')
  return nextPayment?.dueDate || null
}

// 获取下次还款金额
const getNextPaymentAmount = () => {
  const details = generatePaymentDetails()
  const nextPayment = details.find(d => d.status === 'pending')
  return nextPayment?.amount || 0
}

// 导入必要模块
import { Modal } from 'ant-design-vue'
import { h, watch } from 'vue'
import * as echarts from 'echarts'

// 还款表单
const paymentForm = reactive({
  period: 1,
  dueAmount: 0,
  actualAmount: 0,
  paymentDate: null,
  paymentMethod: 'bank_transfer',
  receiptNumber: '',
  remarks: ''
})

// 处理还款保存
const handlePaymentSave = () => {
  if (!paymentForm.actualAmount || paymentForm.actualAmount <= 0) {
    message.error('请输入正确的还款金额')
    return
  }
  if (!paymentForm.paymentDate) {
    message.error('请选择还款日期')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    const record = selectedRecord.value
    const index = tableData.value.findIndex(item => item.key === record.key)
    if (index !== -1) {
      // 更新还款明细
      const detailIndex = tableData.value[index].paymentDetails.findIndex(d => d.period === paymentForm.period)
      if (detailIndex !== -1) {
        tableData.value[index].paymentDetails[detailIndex] = {
          ...tableData.value[index].paymentDetails[detailIndex],
          status: 'paid',
          actualPayDate: paymentForm.paymentDate.format('YYYY-MM-DD'),
          actualAmount: paymentForm.actualAmount
        }
      }
      
      // 更新总体进度
      const paidAmount = tableData.value[index].paymentDetails
        .filter(d => d.status === 'paid')
        .reduce((sum, d) => sum + (d.actualAmount || 0), 0)
      
      tableData.value[index].paidAmount = paidAmount
      tableData.value[index].remainingAmount = tableData.value[index].planAmount - paidAmount
      tableData.value[index].progressPercent = Math.round(paidAmount / tableData.value[index].planAmount * 100)
      
      // 更新下次还款信息
      const nextPayment = tableData.value[index].paymentDetails.find(d => d.status === 'pending')
      if (nextPayment) {
        tableData.value[index].nextPaymentDate = nextPayment.dueDate
        tableData.value[index].nextPaymentAmount = nextPayment.amount
      } else {
        tableData.value[index].nextPaymentDate = null
        tableData.value[index].nextPaymentAmount = null
        tableData.value[index].status = 'completed'
      }
      
      // 添加操作历史
      tableData.value[index].operationHistory.push({
        type: 'payment',
        title: '还款操作',
        time: new Date().toLocaleString(),
        detail: `第${paymentForm.period}期还款${paymentForm.actualAmount}元`,
        operator: '当前用户'
      })
    }
    
    message.success('还款记录已保存')
    showPaymentModal.value = false
    
    // 清空表单
    Object.assign(paymentForm, {
      period: 1,
      dueAmount: 0,
      actualAmount: 0,
      paymentDate: null,
      paymentMethod: 'bank_transfer',
      receiptNumber: '',
      remarks: ''
    })
  }, 1000)
}

// 调整表单
const adjustForm = reactive({
  type: 'amount',
  reason: '',
  details: []
})

// 调整表格列
const adjustColumns = [
  {
    title: '期数',
    dataIndex: 'period',
    key: 'period',
    width: 80
  },
  {
    title: '原金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }) => `¥${text?.toLocaleString()}`
  },
  {
    title: '新金额',
    dataIndex: 'newAmount',
    key: 'newAmount',
    width: 150
  },
  {
    title: '原日期',
    dataIndex: 'dueDate',
    key: 'dueDate',
    width: 120
  },
  {
    title: '新日期',
    dataIndex: 'newDate',
    key: 'newDate',
    width: 150
  }
]

// 处理调整保存
const handleAdjustSave = () => {
  if (!adjustForm.reason) {
    message.error('请输入调整原因')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('计划调整已保存')
    showAdjustModal.value = false
    
    // 清空表单
    adjustForm.type = 'amount'
    adjustForm.reason = ''
    adjustForm.details = []
  }, 1000)
}

// 批量审批表单
const batchApprovalForm = reactive({
  opinion: '',
  result: 'approve'
})

// 批量审批列
const batchApprovalColumns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber'
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName'
  },
  {
    title: '计划类型',
    dataIndex: 'planType',
    key: 'planType',
    customRender: ({ text }) => getTypeText(text)
  },
  {
    title: '计划金额',
    dataIndex: 'planAmount',
    key: 'planAmount',
    customRender: ({ text }) => `¥${text?.toLocaleString()}`
  }
]

// 待审批计划
const selectedPlansForApproval = computed(() => {
  return tableData.value.filter(item => 
    selectedRowKeys.value.includes(item.key) && item.status === 'pending'
  )
})

// 处理批量审批
const handleBatchApprove = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    const status = batchApprovalForm.result === 'approve' ? 'approved' : 'rejected'
    selectedPlansForApproval.value.forEach(plan => {
      const index = tableData.value.findIndex(item => item.key === plan.key)
      if (index !== -1) {
        tableData.value[index].status = status
      }
    })
    
    message.success(`批量${batchApprovalForm.result === 'approve' ? '通过' : '拒绝'}成功`)
    showBatchModal.value = false
    selectedRowKeys.value = []
    
    // 清空表单
    batchApprovalForm.opinion = ''
    batchApprovalForm.result = 'approve'
  }, 1000)
}

// 模板表单
const templateForm = reactive({
  name: '',
  scenario: '',
  planType: '',
  config: {
    periods: 6,
    downPaymentRate: 20,
    interval: 'monthly',
    reductionRate: 20,
    paymentDays: 30
  }
})

// 模板列表
const templateList = ref([
  {
    id: '1',
    name: '标准分期模板',
    scenario: '适用于一般客户的6期还款',
    planType: 'installment',
    config: {
      periods: 6,
      downPaymentRate: 20,
      interval: 'monthly'
    },
    createTime: '2024-01-01',
    usageCount: 45
  },
  {
    id: '2',
    name: '困难减免模板',
    scenario: '适用于经济困难客户',
    planType: 'reduction',
    config: {
      reductionRate: 30,
      paymentDays: 60
    },
    createTime: '2024-01-05',
    usageCount: 12
  }
])

// 模板列
const templateColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '适用场景',
    dataIndex: 'scenario',
    key: 'scenario'
  },
  {
    title: '计划类型',
    dataIndex: 'planType',
    key: 'planType',
    customRender: ({ text }) => getTypeText(text)
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'action',
    width: 180
  }
]

// 模板操作
const handleUseTemplate = (template) => {
  // 填充创建表单
  createForm.planType = template.planType
  if (template.planType === 'installment') {
    createForm.installmentCount = template.config.periods
    createForm.paymentInterval = template.config.interval
  } else if (template.planType === 'reduction') {
    createForm.reductionAmount = createForm.totalAmount * template.config.reductionRate / 100
  }
  
  showTemplateModal.value = false
  showCreateModal.value = true
  message.success('已应用模板')
}

const handleEditTemplate = (template) => {
  Object.assign(templateForm, template)
  showCreateTemplate.value = true
  templateActiveKey.value = 'create'
}

const handleDeleteTemplate = (template) => {
  Modal.confirm({
    title: '删除确认',
    content: `确定要删除模板"${template.name}"吗？`,
    onOk() {
      const index = templateList.value.findIndex(t => t.id === template.id)
      if (index !== -1) {
        templateList.value.splice(index, 1)
        message.success('模板已删除')
      }
    }
  })
}

const handleSaveTemplate = () => {
  if (!templateForm.name) {
    message.error('请输入模板名称')
    return
  }
  if (!templateForm.planType) {
    message.error('请选择计划类型')
    return
  }
  
  const newTemplate = {
    ...templateForm,
    id: Date.now().toString(),
    createTime: new Date().toLocaleDateString(),
    usageCount: 0
  }
  
  templateList.value.unshift(newTemplate)
  message.success('模板保存成功')
  
  // 清空表单
  Object.assign(templateForm, {
    name: '',
    scenario: '',
    planType: '',
    config: {
      periods: 6,
      downPaymentRate: 20,
      interval: 'monthly',
      reductionRate: 20,
      paymentDays: 30
    }
  })
  
  showCreateTemplate.value = false
  templateActiveKey.value = 'list'
}

// 执行绩效列
const performanceColumns = [
  {
    title: '协商人',
    dataIndex: 'negotiator',
    key: 'negotiator'
  },
  {
    title: '计划数',
    dataIndex: 'planCount',
    key: 'planCount'
  },
  {
    title: '完成率',
    dataIndex: 'completionRate',
    key: 'completionRate'
  },
  {
    title: '准时率',
    dataIndex: 'onTimeRate',
    key: 'onTimeRate'
  },
  {
    title: '回款金额',
    dataIndex: 'collectedAmount',
    key: 'collectedAmount',
    customRender: ({ text }) => `¥${text?.toLocaleString()}`
  }
]

// 执行绩效数据
const performanceData = ref([
  {
    negotiator: '李四',
    planCount: 35,
    completionRate: 85.7,
    onTimeRate: 92.3,
    collectedAmount: 1250000
  },
  {
    negotiator: '赵六',
    planCount: 28,
    completionRate: 78.6,
    onTimeRate: 85.7,
    collectedAmount: 980000
  },
  {
    negotiator: '孙七',
    planCount: 42,
    completionRate: 90.5,
    onTimeRate: 95.2,
    collectedAmount: 1680000
  }
])

// 初始化图表
const initCharts = () => {
  setTimeout(() => {
    if (showAnalysisModal.value) {
      initTypeChart()
      initStatusChart()
      initTrendChart()
    }
  }, 100)
}

// 计划类型分布图
const initTypeChart = () => {
  const chartDom = document.getElementById('typeChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '计划类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 68, name: '分期还款' },
          { value: 25, name: '减免计划' },
          { value: 15, name: '一次性还款' },
          { value: 12, name: '延期计划' },
          { value: 8, name: '和解计划' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}

// 执行状态分布图
const initStatusChart = () => {
  const chartDom = document.getElementById('statusChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '执行状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 85, name: '执行中' },
          { value: 45, name: '已完成' },
          { value: 15, name: '逾期' },
          { value: 8, name: '已暂停' },
          { value: 5, name: '已终止' }
        ]
      }
    ]
  }
  myChart.setOption(option)
}

// 执行趋势图
const initTrendChart = () => {
  const chartDom = document.getElementById('trendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增计划', '完成计划', '回款金额']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        max: 150,
        interval: 30
      },
      {
        type: 'value',
        name: '金额(万)',
        min: 0,
        max: 300,
        interval: 60
      }
    ],
    series: [
      {
        name: '新增计划',
        type: 'line',
        data: [40, 52, 61, 74, 80, 95, 110]
      },
      {
        name: '完成计划',
        type: 'line',
        data: [32, 42, 51, 64, 70, 82, 96]
      },
      {
        name: '回款金额',
        type: 'line',
        yAxisIndex: 1,
        data: [120, 150, 180, 210, 245, 265, 280]
      }
    ]
  }
  myChart.setOption(option)
}

// 风险分析图
const initRiskChart = () => {
  const chartDom = document.getElementById('riskChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '风险等级',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 25, name: '低风险' },
          { value: 45, name: '中风险' },
          { value: 30, name: '高风险' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}

// 执行绩效对比图
const initPerformanceChart = () => {
  const chartDom = document.getElementById('performanceChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['计划数', '完成率', '准时率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: performanceData.value.map(item => item.negotiator)
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        max: 50,
        interval: 10
      },
      {
        type: 'value',
        name: '比率',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '计划数',
        type: 'bar',
        data: performanceData.value.map(item => item.planCount)
      },
      {
        name: '完成率',
        type: 'line',
        yAxisIndex: 1,
        data: performanceData.value.map(item => item.completionRate)
      },
      {
        name: '准时率',
        type: 'line',
        yAxisIndex: 1,
        data: performanceData.value.map(item => item.onTimeRate)
      }
    ]
  }
  myChart.setOption(option)
}

// AI分析
const performAIAnalysis = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    Modal.info({
      title: 'AI 智能分析结果',
      width: 800,
      content: h('div', [
        h('div', { class: 'analysis-section' }, [
          h('h4', { style: 'color: #1890ff; margin-bottom: 12px;' }, '📊 数据概览'),
          h('div', { style: 'background: #f0f9ff; padding: 12px; border-radius: 6px; margin-bottom: 16px;' }, [
            h('p', { style: 'margin: 4px 0;' }, `• 总计划数：${tableData.value.length}个`),
            h('p', { style: 'margin: 4px 0;' }, `• 执行中计划：${tableData.value.filter(p => p.status === 'active').length}个`),
            h('p', { style: 'margin: 4px 0;' }, `• 平均执行率：${(tableData.value.reduce((sum, p) => sum + p.progressPercent, 0) / tableData.value.length).toFixed(1)}%`)
          ])
        ]),
        h('div', { class: 'analysis-section' }, [
          h('h4', { style: 'color: #f5222d; margin-bottom: 12px;' }, '⚠️ 风险评估'),
          h('div', { style: 'background: #fff2f0; padding: 12px; border-radius: 6px; margin-bottom: 16px;' }, [
            h('p', { style: 'margin: 4px 0;' }, '• 当前有15%的计划存在高逾期风险，建议提前介入'),
            h('p', { style: 'margin: 4px 0;' }, '• 3个计划即将到期，需要重点关注'),
            h('p', { style: 'margin: 4px 0;' }, '• 高风险客户主要集中在分期还款类型')
          ])
        ]),
        h('div', { class: 'analysis-section' }, [
          h('h4', { style: 'color: #52c41a; margin-bottom: 12px;' }, '💡 优化建议'),
          h('div', { style: 'background: #f6ffed; padding: 12px; border-radius: 6px; margin-bottom: 16px;' }, [
            h('ul', { style: 'margin: 0; padding-left: 20px;' }, [
              h('li', { style: 'margin: 8px 0;' }, '建议为高风险客户提供更灵活的还款选项'),
              h('li', { style: 'margin: 8px 0;' }, '分期计划的首付比例可适当降低至15%'),
              h('li', { style: 'margin: 8px 0;' }, '建立提前还款激励机制，给予2-5%的减免优惠'),
              h('li', { style: 'margin: 8px 0;' }, '加强逾期预警，提前7天发送提醒通知')
            ])
          ])
        ]),
        h('div', { class: 'analysis-section' }, [
          h('h4', { style: 'color: #722ed1; margin-bottom: 12px;' }, '🔮 预测分析'),
          h('div', { style: 'background: #f9f0ff; padding: 12px; border-radius: 6px;' }, [
            h('p', { style: 'margin: 4px 0;' }, '• 预计本月回款率将达到85%，较上月提升7个百分点'),
            h('p', { style: 'margin: 4px 0;' }, '• 分期还款计划的完成率预计为78%'),
            h('p', { style: 'margin: 4px 0;' }, '• 建议重点关注减免计划的执行效果')
          ])
        ])
      ])
    })
  }, 2000)
}

// 监听分析模态框
watch(showAnalysisModal, (newVal) => {
  if (newVal) {
    initCharts()
  }
})

// 监听调整模态框
watch(showAdjustModal, (newVal) => {
  if (newVal && selectedRecord.value) {
    // 初始化调整详情
    adjustForm.details = selectedRecord.value.paymentDetails
      .filter(d => d.status === 'pending')
      .map(d => ({
        ...d,
        newAmount: d.amount,
        newDate: d.dueDate
      }))
  }
})

// 组件挂载后初始化
onMounted(() => {
  handleRefresh()
  
  // 模拟定时更新统计数据
  setInterval(() => {
    statistics.activePlans = Math.floor(Math.random() * 20) + 120
    statistics.totalAmount = (Math.random() * 500000 + 2000000).toFixed(2)
    statistics.paidAmount = (Math.random() * 400000 + 1500000).toFixed(2)
    statistics.executionRate = (Math.random() * 10 + 70).toFixed(1)
  }, 5000)
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 16px;
}

.search-section .ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

/* 统计卡片样式 */
.statistics-section {
  margin-bottom: 16px;
}

.statistics-section .ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 操作按钮样式 */
.action-section {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 表格样式 */
.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-tag {
  margin: 0;
}

.next-payment {
  text-align: center;
}

.payment-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.payment-amount {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.no-payment {
  color: #999;
  font-size: 12px;
}

/* 详情样式 */
.description-content {
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 4px;
  color: #666;
}

/* 历史记录样式 */
.history-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-title {
  font-weight: 500;
  color: #262626;
}

.history-time {
  font-size: 12px;
  color: #999;
}

.history-detail {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .statistics-section .ant-col {
    margin-bottom: 16px;
  }
  
  .action-section .ant-space {
    flex-wrap: wrap;
  }
  
  .action-section .ant-btn {
    margin-bottom: 8px;
  }
  
  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 48px;
  color: #bbb;
  margin-bottom: 16px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}
/* 增强搜索样式 */
.enhanced-search {
  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    > span {
      font-weight: 500;
      font-size: 16px;
    }

    .search-stats {
      display: flex;
      gap: 16px;
      align-items: center;
    }
  }

  .ant-card-extra {
    .ant-space {
      .ant-btn {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
  }

  .action-buttons {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
