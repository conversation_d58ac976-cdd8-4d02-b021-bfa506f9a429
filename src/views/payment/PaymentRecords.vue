<template>
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>还款记录</h1>
        <p>管理所有还款记录，包括记录查询、统计分析、凭证管理等功能</p>
      </div>

      <!-- 搜索筛选区域 -->
      <a-card class="search-card">
        <a-form :model="searchForm" @submit="handleSearch">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="案件编号">
                <a-input 
                  v-model:value="searchForm.caseNumber" 
                  placeholder="请输入案件编号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="客户姓名">
                <a-input 
                  v-model:value="searchForm.customerName" 
                  placeholder="请输入客户姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="还款状态">
                <a-select 
                  v-model:value="searchForm.status" 
                  placeholder="请选择状态"
                  allow-clear
                >
                  <a-select-option value="success">成功</a-select-option>
                  <a-select-option value="failed">失败</a-select-option>
                  <a-select-option value="processing">处理中</a-select-option>
                  <a-select-option value="pending">待审核</a-select-option>
                  <a-select-option value="refunded">已退款</a-select-option>
                  <a-select-option value="partial">部分还款</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="还款方式">
                <a-select 
                  v-model:value="searchForm.paymentMethod" 
                  placeholder="请选择方式"
                  allow-clear
                >
                  <a-select-option value="bank_transfer">银行转账</a-select-option>
                  <a-select-option value="alipay">支付宝</a-select-option>
                  <a-select-option value="wechat">微信支付</a-select-option>
                  <a-select-option value="cash">现金</a-select-option>
                  <a-select-option value="check">支票</a-select-option>
                  <a-select-option value="pos">POS机</a-select-option>
                  <a-select-option value="other">其他</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="还款时间">
                <a-range-picker 
                  v-model:value="searchForm.paymentTimeRange"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="还款金额">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.amountMin"
                    placeholder="最小金额"
                    :min="0"
                    style="width: 50%"
                  />
                  <a-input-number 
                    v-model:value="searchForm.amountMax"
                    placeholder="最大金额"
                    :min="0"
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="操作人">
                <a-input 
                  v-model:value="searchForm.operator" 
                  placeholder="请输入操作人"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    搜索
                  </a-button>
                  <a-button @click="handleReset">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button 
                    :class="{ 'expand-btn-active': searchExpanded }"
                    @click="searchExpanded = !searchExpanded"
                  >
                    {{ searchExpanded ? '收起' : '展开' }}
                    <DownOutlined :class="{ 'expand-icon-active': searchExpanded }" />
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 展开的搜索条件 -->
          <div v-show="searchExpanded">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="收据编号">
                  <a-input 
                    v-model:value="searchForm.receiptNumber"
                    placeholder="请输入收据编号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="银行流水号">
                  <a-input 
                    v-model:value="searchForm.transactionId"
                    placeholder="请输入银行流水号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="还款类型">
                  <a-select 
                    v-model:value="searchForm.paymentType" 
                    placeholder="请选择类型"
                    allow-clear
                  >
                    <a-select-option value="normal">正常还款</a-select-option>
                    <a-select-option value="advance">提前还款</a-select-option>
                    <a-select-option value="overdue">逾期还款</a-select-option>
                    <a-select-option value="partial">部分还款</a-select-option>
                    <a-select-option value="settlement">一次性结清</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="记录来源">
                  <a-select 
                    v-model:value="searchForm.source" 
                    placeholder="请选择来源"
                    allow-clear
                  >
                    <a-select-option value="manual">手动录入</a-select-option>
                    <a-select-option value="import">批量导入</a-select-option>
                    <a-select-option value="auto">自动识别</a-select-option>
                    <a-select-option value="api">接口同步</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-cards">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日还款"
              :value="statistics.todayPayments"
              suffix="笔"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日金额"
              :value="statistics.todayAmount"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <MoneyCollectOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="本月还款"
              :value="statistics.monthlyPayments"
              suffix="笔"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="statistics.successRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>

      <!-- 操作按钮区域 -->
      <a-card class="action-card">
        <div class="action-buttons">
          <a-space>
            <a-button type="primary" @click="showCreateModal = true">
              <PlusOutlined />
              新增记录
            </a-button>
            <a-button @click="showImportModal = true">
              <UploadOutlined />
              批量导入
            </a-button>
            <a-button @click="showReceiptModal = true">
              <FileTextOutlined />
              凭证管理
            </a-button>
            <a-button @click="showStatisticsModal = true">
              <BarChartOutlined />
              统计分析
            </a-button>
            <a-button @click="handleExport">
              <DownloadOutlined />
              导出记录
            </a-button>
            <a-button @click="handleRefresh">
              <ReloadOutlined />
              刷新
            </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 还款记录列表 -->
      <a-card>
        <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          @change="handleTableChange"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag 
                :color="getStatusColor(record.status)"
                class="status-tag"
              >
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'paymentMethod'">
              <a-tag :color="getMethodColor(record.paymentMethod)">
                {{ getMethodText(record.paymentMethod) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'paymentType'">
              <a-tag :color="getTypeColor(record.paymentType)">
                {{ getTypeText(record.paymentType) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'amount'">
              <span class="amount-text">¥{{ record.amount?.toLocaleString() }}</span>
            </template>
            
            <template v-if="column.key === 'receipt'">
              <a-space v-if="record.receiptUrl">
                <a-button type="link" size="small" @click="handleViewReceipt(record)">
                  <EyeOutlined />
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleDownloadReceipt(record)">
                  <DownloadOutlined />
                  下载
                </a-button>
              </a-space>
              <span v-else class="no-receipt">暂无凭证</span>
            </template>
            
            <template v-if="column.key === 'verification'">
              <a-space>
                <a-tooltip :title="record.verified ? '已核实' : '未核实'">
                  <CheckCircleOutlined 
                    :style="{ 
                      color: record.verified ? '#52c41a' : '#d9d9d9',
                      fontSize: '16px'
                    }"
                  />
                </a-tooltip>
                <a-tooltip :title="record.bankVerified ? '银行已核实' : '银行未核实'">
                  <BankOutlined 
                    :style="{ 
                      color: record.bankVerified ? '#1890ff' : '#d9d9d9',
                      fontSize: '16px'
                    }"
                  />
                </a-tooltip>
              </a-space>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleView(record)">
                  <EyeOutlined />
                  查看
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="handleEdit(record)"
                  :disabled="record.status === 'success' && record.verified"
                >
                  <EditOutlined />
                  编辑
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="handleVerify(record)"
                  v-if="!record.verified"
                >
                  <CheckOutlined />
                  核实
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleRefund(record)" v-if="record.status === 'success'">
                        <UndoOutlined />
                        退款
                      </a-menu-item>
                      <a-menu-item @click="handleSplit(record)" v-if="record.status === 'success'">
                        <SplitCellsOutlined />
                        拆分
                      </a-menu-item>
                      <a-menu-item @click="handlePrint(record)">
                        <PrinterOutlined />
                        打印
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="handleDelete(record)" style="color: #ff4d4f;">
                        <DeleteOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新增/编辑记录模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingRecord ? '编辑还款记录' : '新增还款记录'"
      width="900px"
      @ok="handleCreateSave"
      @cancel="handleCreateCancel"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-tabs v-model:activeKey="createActiveKey">
          <a-tab-pane key="basic" tab="基本信息">
            <a-form-item label="案件编号" required>
              <a-input 
                v-model:value="createForm.caseNumber" 
                placeholder="请输入案件编号"
                @blur="loadCaseInfo"
              />
            </a-form-item>
            
            <a-form-item label="客户信息" required>
              <a-input v-model:value="createForm.customerName" placeholder="客户姓名" readonly />
            </a-form-item>
            
            <a-form-item label="还款金额" required>
              <a-input-number
                v-model:value="createForm.amount"
                placeholder="请输入还款金额"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="还款时间" required>
              <a-date-picker
                v-model:value="createForm.paymentTime"
                show-time
                placeholder="选择还款时间"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item label="还款方式" required>
              <a-select v-model:value="createForm.paymentMethod" placeholder="请选择还款方式">
                <a-select-option value="bank_transfer">银行转账</a-select-option>
                <a-select-option value="alipay">支付宝</a-select-option>
                <a-select-option value="wechat">微信支付</a-select-option>
                <a-select-option value="cash">现金</a-select-option>
                <a-select-option value="check">支票</a-select-option>
                <a-select-option value="pos">POS机</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="还款类型" required>
              <a-select v-model:value="createForm.paymentType" placeholder="请选择还款类型">
                <a-select-option value="normal">正常还款</a-select-option>
                <a-select-option value="advance">提前还款</a-select-option>
                <a-select-option value="overdue">逾期还款</a-select-option>
                <a-select-option value="partial">部分还款</a-select-option>
                <a-select-option value="settlement">一次性结清</a-select-option>
              </a-select>
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="detail" tab="详细信息">
            <a-form-item label="收据编号">
              <a-input v-model:value="createForm.receiptNumber" placeholder="请输入收据编号" />
            </a-form-item>
            
            <a-form-item label="银行流水号">
              <a-input v-model:value="createForm.transactionId" placeholder="请输入银行流水号" />
            </a-form-item>
            
            <a-form-item label="付款账户">
              <a-input v-model:value="createForm.payerAccount" placeholder="请输入付款账户信息" />
            </a-form-item>
            
            <a-form-item label="收款账户">
              <a-input v-model:value="createForm.receiverAccount" placeholder="请输入收款账户信息" />
            </a-form-item>
            
            <a-form-item label="手续费">
              <a-input-number
                v-model:value="createForm.fee"
                placeholder="请输入手续费"
                :min="0"
                :precision="2"
                style="width: 100%"
              >
                <template #addonAfter>元</template>
              </a-input-number>
            </a-form-item>
            
            <a-form-item label="汇率">
              <a-input-number
                v-model:value="createForm.exchangeRate"
                placeholder="请输入汇率（外币时填写）"
                :min="0"
                :precision="4"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item label="备注信息">
              <a-textarea
                v-model:value="createForm.remarks"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </a-form-item>
          </a-tab-pane>
          
          <a-tab-pane key="receipt" tab="凭证管理">
            <a-form-item label="上传凭证">
              <a-upload
                v-model:file-list="createForm.receipts"
                :before-upload="beforeUpload"
                list-type="picture-card"
                :multiple="true"
              >
                <div v-if="createForm.receipts.length < 5">
                  <PlusOutlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </a-upload>
              <div class="upload-tip">
                支持JPG、PNG、PDF格式，最多上传5个文件，单个文件不超过10MB
              </div>
            </a-form-item>
            
            <a-form-item label="核实状态">
              <a-checkbox-group v-model:value="createForm.verificationStatus">
                <a-checkbox value="manual">人工核实</a-checkbox>
                <a-checkbox value="bank">银行核实</a-checkbox>
                <a-checkbox value="system">系统核实</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="核实说明">
              <a-textarea
                v-model:value="createForm.verificationRemarks"
                :rows="3"
                placeholder="请输入核实说明"
              />
            </a-form-item>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-modal>

    <!-- 记录详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="还款记录详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="记录编号">{{ selectedRecord.id }}</a-descriptions-item>
          <a-descriptions-item label="案件编号">{{ selectedRecord.caseNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedRecord.customerName }}</a-descriptions-item>
          <a-descriptions-item label="还款金额">¥{{ selectedRecord.amount?.toLocaleString() }}</a-descriptions-item>
          <a-descriptions-item label="还款时间">{{ selectedRecord.paymentTime }}</a-descriptions-item>
          <a-descriptions-item label="还款方式">
            <a-tag :color="getMethodColor(selectedRecord.paymentMethod)">
              {{ getMethodText(selectedRecord.paymentMethod) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="还款类型">
            <a-tag :color="getTypeColor(selectedRecord.paymentType)">
              {{ getTypeText(selectedRecord.paymentType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="收据编号">{{ selectedRecord.receiptNumber || '无' }}</a-descriptions-item>
          <a-descriptions-item label="银行流水号">{{ selectedRecord.transactionId || '无' }}</a-descriptions-item>
          <a-descriptions-item label="操作人">{{ selectedRecord.operator }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ selectedRecord.createTime }}</a-descriptions-item>
          <a-descriptions-item label="备注信息" :span="2">
            <div class="remarks-content">{{ selectedRecord.remarks || '无' }}</div>
          </a-descriptions-item>
        </a-descriptions>
        
        <!-- 凭证信息 -->
        <a-card title="还款凭证" style="margin-top: 16px;" v-if="selectedRecord.receipts">
          <a-row :gutter="16">
            <a-col :span="6" v-for="(receipt, index) in selectedRecord.receipts" :key="index">
              <div class="receipt-item">
                <img v-if="receipt.type === 'image'" :src="receipt.url" alt="凭证" class="receipt-image" />
                <div v-else class="receipt-file">
                  <FileTextOutlined style="font-size: 48px; color: #1890ff;" />
                  <div>{{ receipt.name }}</div>
                </div>
                <div class="receipt-actions">
                  <a-button type="link" size="small" @click="handlePreviewReceipt(receipt)">
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="handleDownloadReceipt(receipt)">
                    下载
                  </a-button>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-card>
        
        <!-- 核实记录 -->
        <a-card title="核实记录" style="margin-top: 16px;">
          <a-timeline>
            <a-timeline-item 
              v-for="(verification, index) in selectedRecord.verificationHistory" 
              :key="index"
              :color="getVerificationColor(verification.type)"
            >
              <div class="verification-content">
                <div class="verification-header">
                  <span class="verification-title">{{ verification.title }}</span>
                  <span class="verification-time">{{ verification.time }}</span>
                </div>
                <div class="verification-detail">{{ verification.detail }}</div>
                <div class="verification-operator">核实人：{{ verification.operator }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </div>
    </a-modal>

    <!-- 统计分析模态框 -->
    <a-modal
      v-model:open="showStatisticsModal"
      title="还款统计分析"
      width="1200px"
      :footer="null"
    >
      <a-tabs v-model:activeKey="statisticsActiveKey">
        <a-tab-pane key="overview" tab="总体统计">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="还款方式分布">
                <div id="methodChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="还款状态分布">
                <div id="statusChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
          
          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="24">
              <a-card title="还款趋势分析">
                <div id="trendChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="amount" tab="金额分析">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="金额区间分布">
                <div id="amountChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="月度回款统计">
                <div id="monthlyChart" style="height: 300px;"></div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="performance" tab="绩效分析">
          <a-card title="操作人员绩效">
            <a-table
              :columns="performanceColumns"
              :data-source="performanceData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'successRate'">
                  <a-progress :percent="record.successRate" size="small" />
                </template>
                <template v-if="column.key === 'amount'">
                  ¥{{ record.amount?.toLocaleString() }}
                </template>
              </template>
            </a-table>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 批量导入模态框 -->
    <a-modal
      v-model:open="showImportModal"
      title="批量导入还款记录"
      width="800px"
      @ok="handleImportSave"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="选择文件" required>
          <a-upload
            :before-upload="beforeImportUpload"
            accept=".xlsx,.xls,.csv"
            :file-list="importFile"
          >
            <a-button>
              <UploadOutlined />
              选择Excel文件
            </a-button>
          </a-upload>
          <div class="import-tip">
            支持Excel、CSV格式，需包含案件编号、还款金额、还款时间等必要字段
          </div>
        </a-form-item>
        
        <a-form-item label="导入选项">
          <a-checkbox-group v-model:value="importOptions">
            <a-checkbox value="skipExists">跳过已存在记录</a-checkbox>
            <a-checkbox value="validateAmount">验证金额范围</a-checkbox>
            <a-checkbox value="autoVerify">自动核实</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="预览数据" v-if="importPreview.length > 0">
          <a-table
            :columns="importColumns"
            :data-source="importPreview"
            :pagination="false"
            size="small"
            :scroll="{ y: 200 }"
          />
          <div class="import-summary">
            共 {{ importPreview.length }} 条记录，有效 {{ validImportCount }} 条，无效 {{ importPreview.length - validImportCount }} 条
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 凭证管理模态框 -->
    <a-modal
      v-model:open="showReceiptModal"
      title="凭证管理"
      width="1000px"
      :footer="null"
    >
      <div class="receipt-management">
        <div class="receipt-actions">
          <a-space>
            <a-input-search
              v-model:value="receiptSearch"
              placeholder="搜索凭证"
              style="width: 300px;"
            />
            <a-select v-model:value="receiptFilter" placeholder="筛选类型" style="width: 150px;" allow-clear>
              <a-select-option value="image">图片</a-select-option>
              <a-select-option value="pdf">PDF</a-select-option>
              <a-select-option value="verified">已核实</a-select-option>
              <a-select-option value="pending">待核实</a-select-option>
            </a-select>
          </a-space>
        </div>
        
        <div class="receipt-grid">
          <div 
            v-for="receipt in filteredReceipts" 
            :key="receipt.id"
            class="receipt-card"
            @click="handleSelectReceipt(receipt)"
            :class="{ active: selectedReceipts.includes(receipt.id) }"
          >
            <div class="receipt-preview">
              <img v-if="receipt.type === 'image'" :src="receipt.thumbnail" alt="凭证缩略图" />
              <div v-else class="receipt-icon">
                <FileTextOutlined style="font-size: 32px;" />
              </div>
            </div>
            <div class="receipt-info">
              <div class="receipt-name">{{ receipt.name }}</div>
              <div class="receipt-meta">
                <span>{{ receipt.size }}</span>
                <span>{{ receipt.uploadTime }}</span>
              </div>
              <div class="receipt-status">
                <a-tag :color="receipt.verified ? 'green' : 'orange'">
                  {{ receipt.verified ? '已核实' : '待核实' }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
        
        <div class="receipt-operations">
          <a-space>
            <a-button @click="handleBatchVerifyReceipts">批量核实</a-button>
            <a-button @click="handleBatchDownloadReceipts">批量下载</a-button>
            <a-button danger @click="handleBatchDeleteReceipts">批量删除</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  PlusOutlined,
  UploadOutlined,
  FileTextOutlined,
  BarChartOutlined,
  DownloadOutlined,
  DollarOutlined,
  MoneyCollectOutlined,
  CalendarOutlined,
  TrophyOutlined,
  EyeOutlined,
  EditOutlined,
  CheckOutlined,
  DeleteOutlined,
  UndoOutlined,
  SplitCellsOutlined,
  PrinterOutlined,
  CheckCircleOutlined,
  BankOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const searchExpanded = ref(false)

// 模态框显示状态
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showStatisticsModal = ref(false)
const showImportModal = ref(false)
const showReceiptModal = ref(false)

// 编辑状态
const editingRecord = ref(null)
const selectedRecord = ref(null)

// Tab激活键
const createActiveKey = ref('basic')
const statisticsActiveKey = ref('overview')

// 表单数据
const searchForm = reactive({
  caseNumber: '',
  customerName: '',
  status: undefined,
  paymentMethod: undefined,
  paymentTimeRange: [],
  amountMin: undefined,
  amountMax: undefined,
  operator: '',
  receiptNumber: '',
  transactionId: '',
  paymentType: undefined,
  source: undefined
})

const createForm = reactive({
  caseNumber: '',
  customerName: '',
  amount: 0,
  paymentTime: null,
  paymentMethod: '',
  paymentType: '',
  receiptNumber: '',
  transactionId: '',
  payerAccount: '',
  receiverAccount: '',
  fee: 0,
  exchangeRate: 1,
  remarks: '',
  receipts: [],
  verificationStatus: [],
  verificationRemarks: ''
})

// 统计数据
const statistics = reactive({
  todayPayments: 42,
  todayAmount: 586789.50,
  monthlyPayments: 1256,
  successRate: 94.2
})

// 表格列定义
const columns = [
  {
    title: '记录编号',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    sorter: true
  },
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber',
    width: 120,
    sorter: true
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 100
  },
  {
    title: '还款金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    sorter: true
  },
  {
    title: '还款时间',
    dataIndex: 'paymentTime',
    key: 'paymentTime',
    width: 150,
    sorter: true
  },
  {
    title: '还款方式',
    dataIndex: 'paymentMethod',
    key: 'paymentMethod',
    width: 100
  },
  {
    title: '还款类型',
    dataIndex: 'paymentType',
    key: 'paymentType',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '成功', value: 'success' },
      { text: '失败', value: 'failed' },
      { text: '处理中', value: 'processing' },
      { text: '待审核', value: 'pending' },
      { text: '已退款', value: 'refunded' },
      { text: '部分还款', value: 'partial' }
    ]
  },
  {
    title: '核实状态',
    key: 'verification',
    width: 100
  },
  {
    title: '凭证',
    key: 'receipt',
    width: 100
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 绩效分析列
const performanceColumns = [
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator'
  },
  {
    title: '处理笔数',
    dataIndex: 'count',
    key: 'count',
    sorter: true
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate',
    sorter: true
  },
  {
    title: '处理金额',
    dataIndex: 'amount',
    key: 'amount',
    sorter: true
  },
  {
    title: '平均用时',
    dataIndex: 'avgTime',
    key: 'avgTime'
  }
]

// 导入列
const importColumns = [
  {
    title: '案件编号',
    dataIndex: 'caseNumber',
    key: 'caseNumber'
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName'
  },
  {
    title: '还款金额',
    dataIndex: 'amount',
    key: 'amount'
  },
  {
    title: '还款时间',
    dataIndex: 'paymentTime',
    key: 'paymentTime'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  }
]

// 表格数据
const tableData = ref([
  {
    key: '1',
    id: 'PR202401001',
    caseNumber: 'CS202401001',
    customerName: '张三',
    amount: 50000,
    paymentTime: '2024-01-15 14:30:00',
    paymentMethod: 'bank_transfer',
    paymentType: 'normal',
    status: 'success',
    receiptNumber: 'RC202401001',
    transactionId: 'TXN20240115001',
    verified: true,
    bankVerified: true,
    operator: '李四',
    createTime: '2024-01-15 14:35:00',
    remarks: '客户主动还款，银行转账成功',
    receiptUrl: '/uploads/receipts/receipt1.jpg',
    receipts: [
      {
        id: '1',
        name: '银行转账凭证.jpg',
        type: 'image',
        url: '/uploads/receipts/receipt1.jpg',
        thumbnail: '/uploads/receipts/receipt1_thumb.jpg',
        size: '1.2MB'
      }
    ],
    verificationHistory: [
      {
        type: 'create',
        title: '创建记录',
        time: '2024-01-15 14:35:00',
        detail: '系统创建还款记录',
        operator: '李四'
      },
      {
        type: 'verify',
        title: '人工核实',
        time: '2024-01-15 15:00:00',
        detail: '核实银行转账凭证，确认到账',
        operator: '王五'
      },
      {
        type: 'bank_verify',
        title: '银行核实',
        time: '2024-01-15 15:30:00',
        detail: '银行系统确认交易成功',
        operator: '系统'
      }
    ]
  },
  {
    key: '2',
    id: 'PR202401002',
    caseNumber: 'CS202401002',
    customerName: '李四',
    amount: 25000,
    paymentTime: '2024-01-16 10:15:00',
    paymentMethod: 'alipay',
    paymentType: 'partial',
    status: 'success',
    receiptNumber: 'RC202401002',
    transactionId: 'ALIPAY20240116001',
    verified: true,
    bankVerified: false,
    operator: '张三',
    createTime: '2024-01-16 10:20:00',
    remarks: '部分还款，支付宝到账',
    receiptUrl: '/uploads/receipts/receipt2.jpg',
    receipts: [
      {
        id: '2',
        name: '支付宝付款截图.jpg',
        type: 'image',
        url: '/uploads/receipts/receipt2.jpg',
        thumbnail: '/uploads/receipts/receipt2_thumb.jpg',
        size: '856KB'
      }
    ],
    verificationHistory: [
      {
        type: 'create',
        title: '创建记录',
        time: '2024-01-16 10:20:00',
        detail: '客户上传支付宝付款截图',
        operator: '张三'
      },
      {
        type: 'verify',
        title: '人工核实',
        time: '2024-01-16 11:00:00',
        detail: '核实支付宝交易记录，确认到账',
        operator: '赵六'
      }
    ]
  },
  {
    key: '3',
    id: 'PR202401003',
    caseNumber: 'CS202401003',
    customerName: '王五',
    amount: 80000,
    paymentTime: '2024-01-17 16:45:00',
    paymentMethod: 'cash',
    paymentType: 'advance',
    status: 'success',
    receiptNumber: 'RC202401003',
    transactionId: '',
    verified: true,
    bankVerified: false,
    operator: '孙七',
    createTime: '2024-01-17 16:50:00',
    remarks: '客户现金还款，提前结清',
    receiptUrl: '/uploads/receipts/receipt3.pdf',
    receipts: [
      {
        id: '3',
        name: '现金收据.pdf',
        type: 'pdf',
        url: '/uploads/receipts/receipt3.pdf',
        size: '2.1MB'
      }
    ],
    verificationHistory: [
      {
        type: 'create',
        title: '创建记录',
        time: '2024-01-17 16:50:00',
        detail: '客户现场现金还款',
        operator: '孙七'
      },
      {
        type: 'verify',
        title: '现场核实',
        time: '2024-01-17 16:50:00',
        detail: '现场收取现金，开具收据',
        operator: '孙七'
      }
    ]
  }
])

// 绩效数据
const performanceData = ref([
  {
    operator: '李四',
    count: 45,
    successRate: 96.8,
    amount: 1250000,
    avgTime: '2.5小时'
  },
  {
    operator: '张三',
    count: 38,
    successRate: 94.2,
    amount: 980000,
    avgTime: '3.1小时'
  },
  {
    operator: '王五',
    count: 52,
    successRate: 98.1,
    amount: 1680000,
    avgTime: '2.2小时'
  }
])

// 导入相关
const importFile = ref([])
const importOptions = ref(['skipExists'])
const importPreview = ref([])
const validImportCount = computed(() => {
  return importPreview.value.filter(item => item.status === 'valid').length
})

// 凭证管理
const receiptSearch = ref('')
const receiptFilter = ref('')
const selectedReceipts = ref([])
const receiptList = ref([
  {
    id: '1',
    name: '银行转账凭证_张三.jpg',
    type: 'image',
    url: '/uploads/receipts/receipt1.jpg',
    thumbnail: '/uploads/receipts/receipt1_thumb.jpg',
    size: '1.2MB',
    uploadTime: '2024-01-15 14:35:00',
    verified: true,
    caseNumber: 'CS202401001'
  },
  {
    id: '2',
    name: '支付宝付款截图_李四.jpg',
    type: 'image',
    url: '/uploads/receipts/receipt2.jpg',
    thumbnail: '/uploads/receipts/receipt2_thumb.jpg',
    size: '856KB',
    uploadTime: '2024-01-16 10:20:00',
    verified: true,
    caseNumber: 'CS202401002'
  },
  {
    id: '3',
    name: '现金收据_王五.pdf',
    type: 'pdf',
    url: '/uploads/receipts/receipt3.pdf',
    size: '2.1MB',
    uploadTime: '2024-01-17 16:50:00',
    verified: true,
    caseNumber: 'CS202401003'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 行选择配置
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const filteredReceipts = computed(() => {
  let filtered = receiptList.value
  
  if (receiptSearch.value) {
    filtered = filtered.filter(receipt => 
      receipt.name.includes(receiptSearch.value) ||
      receipt.caseNumber.includes(receiptSearch.value)
    )
  }
  
  if (receiptFilter.value) {
    if (receiptFilter.value === 'verified') {
      filtered = filtered.filter(receipt => receipt.verified)
    } else if (receiptFilter.value === 'pending') {
      filtered = filtered.filter(receipt => !receipt.verified)
    } else {
      filtered = filtered.filter(receipt => receipt.type === receiptFilter.value)
    }
  }
  
  return filtered
})

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    success: 'green',
    failed: 'red',
    processing: 'blue',
    pending: 'orange',
    refunded: 'purple',
    partial: 'cyan'
  }
  return colorMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    success: '成功',
    failed: '失败',
    processing: '处理中',
    pending: '待审核',
    refunded: '已退款',
    partial: '部分还款'
  }
  return textMap[status] || status
}

// 方式颜色映射
const getMethodColor = (method) => {
  const colorMap = {
    bank_transfer: 'blue',
    alipay: 'cyan',
    wechat: 'green',
    cash: 'orange',
    check: 'purple',
    pos: 'magenta',
    other: 'default'
  }
  return colorMap[method] || 'default'
}

// 方式文本映射
const getMethodText = (method) => {
  const textMap = {
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat: '微信支付',
    cash: '现金',
    check: '支票',
    pos: 'POS机',
    other: '其他'
  }
  return textMap[method] || method
}

// 类型颜色映射
const getTypeColor = (type) => {
  const colorMap = {
    normal: 'blue',
    advance: 'green',
    overdue: 'red',
    partial: 'orange',
    settlement: 'purple'
  }
  return colorMap[type] || 'default'
}

// 类型文本映射
const getTypeText = (type) => {
  const textMap = {
    normal: '正常还款',
    advance: '提前还款',
    overdue: '逾期还款',
    partial: '部分还款',
    settlement: '一次性结清'
  }
  return textMap[type] || type
}

// 核实颜色映射
const getVerificationColor = (type) => {
  const colorMap = {
    create: 'blue',
    verify: 'green',
    bank_verify: 'purple',
    reject: 'red'
  }
  return colorMap[type] || 'default'
}

// 事件处理函数
const handleSearch = () => {
  loading.value = true
  console.log('搜索条件:', searchForm)
  setTimeout(() => {
    loading.value = false
    message.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = undefined
    }
  })
  message.success('已重置搜索条件')
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 800)
}

const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变化:', pag, filters, sorter)
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleView = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

const handleEdit = (record) => {
  editingRecord.value = record
  Object.assign(createForm, {
    caseNumber: record.caseNumber,
    customerName: record.customerName,
    amount: record.amount,
    paymentTime: record.paymentTime,
    paymentMethod: record.paymentMethod,
    paymentType: record.paymentType,
    receiptNumber: record.receiptNumber,
    transactionId: record.transactionId,
    remarks: record.remarks,
    receipts: record.receipts || [],
    verificationStatus: [],
    verificationRemarks: ''
  })
  showCreateModal.value = true
}

const handleVerify = (record) => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    const index = tableData.value.findIndex(item => item.key === record.key)
    if (index !== -1) {
      tableData.value[index].verified = true
      tableData.value[index].verificationHistory.push({
        type: 'verify',
        title: '人工核实',
        time: new Date().toLocaleString(),
        detail: '核实完成，确认记录真实有效',
        operator: '当前用户'
      })
    }
    message.success('核实完成')
  }, 1000)
}

const handleRefund = (record) => {
  console.log('退款操作:', record)
  message.info('退款功能开发中')
}

const handleSplit = (record) => {
  console.log('拆分操作:', record)
  message.info('拆分功能开发中')
}

const handlePrint = (record) => {
  console.log('打印操作:', record)
  message.success('正在准备打印...')
}

const handleDelete = (record) => {
  const index = tableData.value.findIndex(item => item.key === record.key)
  if (index !== -1) {
    tableData.value.splice(index, 1)
    pagination.total--
    message.success('记录已删除')
  }
}

const handleCreateSave = () => {
  // 验证必填字段
  if (!createForm.caseNumber) {
    message.error('请输入案件编号')
    return
  }
  if (!createForm.amount || createForm.amount <= 0) {
    message.error('请输入有效的还款金额')
    return
  }
  if (!createForm.paymentTime) {
    message.error('请选择还款时间')
    return
  }
  if (!createForm.paymentMethod) {
    message.error('请选择还款方式')
    return
  }
  if (!createForm.paymentType) {
    message.error('请选择还款类型')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    const newRecord = {
      key: Date.now().toString(),
      id: 'PR' + Date.now(),
      caseNumber: createForm.caseNumber,
      customerName: createForm.customerName,
      amount: createForm.amount,
      paymentTime: createForm.paymentTime.format('YYYY-MM-DD HH:mm:ss'),
      paymentMethod: createForm.paymentMethod,
      paymentType: createForm.paymentType,
      status: 'success',
      receiptNumber: createForm.receiptNumber,
      transactionId: createForm.transactionId,
      verified: createForm.verificationStatus.length > 0,
      bankVerified: createForm.verificationStatus.includes('bank'),
      operator: '当前用户',
      createTime: new Date().toLocaleString(),
      remarks: createForm.remarks,
      receipts: createForm.receipts,
      verificationHistory: [
        {
          type: 'create',
          title: '创建记录',
          time: new Date().toLocaleString(),
          detail: '新增还款记录',
          operator: '当前用户'
        }
      ]
    }
    
    if (editingRecord.value) {
      // 编辑模式
      const index = tableData.value.findIndex(item => item.key === editingRecord.value.key)
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...newRecord,
          key: editingRecord.value.key,
          id: editingRecord.value.id,
          createTime: editingRecord.value.createTime
        }
        tableData.value[index].verificationHistory.push({
          type: 'edit',
          title: '编辑记录',
          time: new Date().toLocaleString(),
          detail: '记录信息已更新',
          operator: '当前用户'
        })
      }
      message.success('记录更新成功')
    } else {
      // 新增模式
      tableData.value.unshift(newRecord)
      pagination.total++
      message.success('记录创建成功')
    }
    
    showCreateModal.value = false
    handleCreateCancel()
  }, 1500)
}

const handleCreateCancel = () => {
  editingRecord.value = null
  Object.assign(createForm, {
    caseNumber: '',
    customerName: '',
    amount: 0,
    paymentTime: null,
    paymentMethod: '',
    paymentType: '',
    receiptNumber: '',
    transactionId: '',
    payerAccount: '',
    receiverAccount: '',
    fee: 0,
    exchangeRate: 1,
    remarks: '',
    receipts: [],
    verificationStatus: [],
    verificationRemarks: ''
  })
  createActiveKey.value = 'basic'
}

const loadCaseInfo = () => {
  if (createForm.caseNumber) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      
      // 模拟加载案件信息
      const mockCaseInfo = {
        'CS202401001': { customerName: '张三' },
        'CS202401002': { customerName: '李四' },
        'CS202401003': { customerName: '王五' }
      }
      
      const caseInfo = mockCaseInfo[createForm.caseNumber]
      if (caseInfo) {
        createForm.customerName = caseInfo.customerName
        message.success('已加载案件信息')
      } else {
        message.warning('未找到案件信息')
      }
    }, 500)
  }
}

const beforeUpload = (file) => {
  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  // 检查文件类型
  const allowedTypes = ['jpg', 'jpeg', 'png', 'pdf']
  const fileType = file.name.split('.').pop().toLowerCase()
  if (!allowedTypes.includes(fileType)) {
    message.error(`不支持${fileType}格式的文件!`)
    return false
  }
  
  createForm.receipts.push({
    id: Date.now().toString(),
    name: file.name,
    type: fileType === 'pdf' ? 'pdf' : 'image',
    url: URL.createObjectURL(file),
    size: (file.size / 1024 / 1024).toFixed(2) + 'MB'
  })
  
  return false
}

const beforeImportUpload = (file) => {
  // 检查文件类型
  const fileType = file.name.split('.').pop().toLowerCase()
  if (!['xlsx', 'xls', 'csv'].includes(fileType)) {
    message.error('只支持Excel和CSV格式的文件!')
    return false
  }
  
  // 模拟解析文件
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    // 模拟解析结果
    importPreview.value = [
      {
        caseNumber: 'CS202401004',
        customerName: '赵六',
        amount: 30000,
        paymentTime: '2024-01-18 10:00:00',
        status: 'valid'
      },
      {
        caseNumber: 'CS202401005',
        customerName: '孙七',
        amount: 45000,
        paymentTime: '2024-01-18 14:30:00',
        status: 'valid'
      },
      {
        caseNumber: 'CS202401006',
        customerName: '周八',
        amount: -1000,
        paymentTime: '2024-01-18 16:00:00',
        status: 'invalid'
      }
    ]
    
    message.success(`文件解析成功！共${importPreview.value.length}条记录`)
  }, 1500)
  
  importFile.value = [file]
  return false
}

const handleImportSave = () => {
  if (importPreview.value.length === 0) {
    message.error('请先上传文件')
    return
  }
  
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    const validRecords = importPreview.value.filter(item => item.status === 'valid')
    validRecords.forEach(item => {
      const newRecord = {
        key: Date.now().toString() + Math.random(),
        id: 'PR' + Date.now() + Math.random(),
        ...item,
        paymentMethod: 'bank_transfer',
        paymentType: 'normal',
        status: 'success',
        verified: importOptions.value.includes('autoVerify'),
        bankVerified: false,
        operator: '当前用户',
        createTime: new Date().toLocaleString(),
        verificationHistory: [
          {
            type: 'create',
            title: '批量导入',
            time: new Date().toLocaleString(),
            detail: '通过文件批量导入',
            operator: '当前用户'
          }
        ]
      }
      tableData.value.unshift(newRecord)
    })
    
    pagination.total += validRecords.length
    message.success(`成功导入${validRecords.length}条记录`)
    
    showImportModal.value = false
    importPreview.value = []
    importFile.value = []
  }, 2000)
}

const handleExport = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    
    // 模拟下载文件
    const link = document.createElement('a')
    const headers = '记录编号,案件编号,客户姓名,还款金额,还款时间,还款方式,状态,操作人\n'
    const data = tableData.value.map(record => 
      `${record.id},${record.caseNumber},${record.customerName},${record.amount},${record.paymentTime},${getMethodText(record.paymentMethod)},${getStatusText(record.status)},${record.operator}`
    ).join('\n')
    
    link.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(headers + data)
    link.download = `还款记录_${new Date().toLocaleDateString().replace(/\//g, '-')}.csv`
    link.click()
    
    message.success('导出成功')
  }, 1000)
}

const handleViewReceipt = (record) => {
  if (record.receiptUrl) {
    window.open(record.receiptUrl, '_blank')
  }
}

const handleDownloadReceipt = (record) => {
  if (record.receiptUrl) {
    const link = document.createElement('a')
    link.href = record.receiptUrl
    link.download = record.receiptNumber + '_凭证'
    link.click()
    message.success('下载开始')
  }
}

const handlePreviewReceipt = (receipt) => {
  window.open(receipt.url, '_blank')
}

const handleSelectReceipt = (receipt) => {
  const index = selectedReceipts.value.indexOf(receipt.id)
  if (index > -1) {
    selectedReceipts.value.splice(index, 1)
  } else {
    selectedReceipts.value.push(receipt.id)
  }
}

const handleBatchVerifyReceipts = () => {
  if (selectedReceipts.value.length === 0) {
    message.warning('请先选择凭证')
    return
  }
  
  selectedReceipts.value.forEach(id => {
    const receipt = receiptList.value.find(r => r.id === id)
    if (receipt) {
      receipt.verified = true
    }
  })
  
  message.success(`已批量核实${selectedReceipts.value.length}个凭证`)
  selectedReceipts.value = []
}

const handleBatchDownloadReceipts = () => {
  if (selectedReceipts.value.length === 0) {
    message.warning('请先选择凭证')
    return
  }
  
  message.success(`开始下载${selectedReceipts.value.length}个凭证`)
  selectedReceipts.value = []
}

const handleBatchDeleteReceipts = () => {
  if (selectedReceipts.value.length === 0) {
    message.warning('请先选择凭证')
    return
  }
  
  selectedReceipts.value.forEach(id => {
    const index = receiptList.value.findIndex(r => r.id === id)
    if (index > -1) {
      receiptList.value.splice(index, 1)
    }
  })
  
  message.success(`已删除${selectedReceipts.value.length}个凭证`)
  selectedReceipts.value = []
}

// 初始化图表
const initCharts = () => {
  setTimeout(() => {
    if (showStatisticsModal.value) {
      initMethodChart()
      initStatusChart()
      initTrendChart()
      initAmountChart()
      initMonthlyChart()
    }
  }, 100)
}

// 还款方式分布图
const initMethodChart = () => {
  const chartDom = document.getElementById('methodChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '还款方式',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 45, name: '银行转账' },
          { value: 25, name: '支付宝' },
          { value: 18, name: '微信支付' },
          { value: 8, name: '现金' },
          { value: 4, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}

// 还款状态分布图
const initStatusChart = () => {
  const chartDom = document.getElementById('statusChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '还款状态',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 128, name: '成功' },
          { value: 15, name: '处理中' },
          { value: 8, name: '待审核' },
          { value: 3, name: '失败' },
          { value: 2, name: '已退款' }
        ]
      }
    ]
  }
  myChart.setOption(option)
}

// 还款趋势图
const initTrendChart = () => {
  const chartDom = document.getElementById('trendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['还款笔数', '还款金额']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: [
      {
        type: 'value',
        name: '笔数',
        min: 0,
        max: 200,
        interval: 40
      },
      {
        type: 'value',
        name: '金额(万)',
        min: 0,
        max: 500,
        interval: 100
      }
    ],
    series: [
      {
        name: '还款笔数',
        type: 'line',
        data: [65, 85, 120, 95, 145, 165, 180]
      },
      {
        name: '还款金额',
        type: 'line',
        yAxisIndex: 1,
        data: [180, 250, 320, 280, 380, 420, 450]
      }
    ]
  }
  myChart.setOption(option)
}

// 金额区间分布图
const initAmountChart = () => {
  const chartDom = document.getElementById('amountChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1万以下', '1-5万', '5-10万', '10-50万', '50万以上']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '笔数',
        type: 'bar',
        data: [25, 45, 35, 28, 12],
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
  myChart.setOption(option)
}

// 月度回款统计图
const initMonthlyChart = () => {
  const chartDom = document.getElementById('monthlyChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value',
      name: '金额(万)'
    },
    series: [
      {
        name: '回款金额',
        type: 'bar',
        data: [180, 250, 320, 280, 380, 420, 450, 380, 320, 280, 250, 200],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  myChart.setOption(option)
}

// 监听统计模态框变化
watch(showStatisticsModal, (newVal) => {
  if (newVal) {
    initCharts()
  }
})

// 组件挂载后初始化
onMounted(() => {
  handleRefresh()
  
  // 模拟定时更新统计数据
  setInterval(() => {
    statistics.todayPayments = Math.floor(Math.random() * 10) + 40
    statistics.todayAmount = parseFloat((Math.random() * 100000 + 500000).toFixed(2))
    statistics.monthlyPayments = Math.floor(Math.random() * 200) + 1200
    statistics.successRate = parseFloat((Math.random() * 5 + 92).toFixed(1))
  }, 30000)
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 搜索区域样式 */
.search-card {
  margin-bottom: 16px;
}

.expand-btn-active {
  color: #1890ff;
}

.expand-icon-active {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 16px;
}

.stats-cards .ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 操作按钮样式 */
.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
}

/* 表格样式 */
.status-tag {
  margin: 0;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.no-receipt {
  color: #999;
  font-size: 12px;
}

/* 详情样式 */
.remarks-content {
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 4px;
  color: #666;
}

/* 凭证样式 */
.receipt-item {
  text-align: center;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 16px;
}

.receipt-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
}

.receipt-file {
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #fafafa;
  border-radius: 4px;
}

.receipt-actions {
  margin-top: 8px;
}

/* 核实记录样式 */
.verification-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
}

.verification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.verification-title {
  font-weight: 500;
  color: #262626;
}

.verification-time {
  font-size: 12px;
  color: #999;
}

.verification-detail {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.verification-operator {
  font-size: 12px;
  color: #999;
}

/* 导入提示样式 */
.import-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.import-summary {
  margin-top: 8px;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  font-size: 12px;
  color: #1890ff;
}

/* 凭证管理样式 */
.receipt-management {
  padding: 0;
}

.receipt-actions {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.receipt-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.receipt-card {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.receipt-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.receipt-card.active {
  border-color: #1890ff;
  background: #f0f9ff;
}

.receipt-preview {
  text-align: center;
  margin-bottom: 8px;
}

.receipt-preview img {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
}

.receipt-icon {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 4px;
}

.receipt-info {
  text-align: center;
}

.receipt-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.receipt-meta {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.receipt-meta span {
  margin-right: 8px;
}

.receipt-status {
  text-align: center;
}

.receipt-operations {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .stats-cards .ant-col {
    margin-bottom: 16px;
  }
  
  .action-buttons .ant-space {
    flex-wrap: wrap;
  }
  
  .action-buttons .ant-btn {
    margin-bottom: 8px;
  }
  
  .verification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .receipt-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 48px;
  color: #bbb;
  margin-bottom: 16px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}
</style>