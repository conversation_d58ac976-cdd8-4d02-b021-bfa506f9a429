<template>
  <div class="quality-report">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>质检报告</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="generateReport">
            <template #icon><FileTextOutlined /></template>
            生成报告
          </a-button>
        </div>
      </div>
    </div>

    <!-- 报告筛选 -->
    <div class="filter-section">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="报告类型">
          <a-select v-model:value="filterForm.type" placeholder="选择类型" style="width: 150px" allowClear>
            <a-select-option value="daily">日报</a-select-option>
            <a-select-option value="weekly">周报</a-select-option>
            <a-select-option value="monthly">月报</a-select-option>
            <a-select-option value="quarterly">季报</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="时间范围">
          <a-range-picker v-model:value="filterForm.dateRange" style="width: 240px" />
        </a-form-item>
        
        <a-form-item label="部门">
          <a-select v-model:value="filterForm.department" placeholder="选择部门" style="width: 150px" allowClear>
            <a-select-option value="dept1">催收一部</a-select-option>
            <a-select-option value="dept2">催收二部</a-select-option>
            <a-select-option value="dept3">催收三部</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="质检类型">
          <a-select v-model:value="filterForm.qualityType" placeholder="选择质检类型" style="width: 150px" allowClear>
            <a-select-option value="call">通话质检</a-select-option>
            <a-select-option value="visit">外访质检</a-select-option>
            <a-select-option value="sms">短信质检</a-select-option>
            <a-select-option value="service">服务质检</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="resetFilter">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 报告概览 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="质检总数" 
              :value="overview.totalInspections" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><AuditOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均得分" 
              :value="overview.avgScore" 
              :value-style="{ color: '#52c41a' }"
              suffix="分"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="合格率" 
              :value="overview.passRate" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="问题发现" 
              :value="overview.issuesFound" 
              :value-style="{ color: '#faad14' }"
              suffix="个"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 报告内容 -->
    <a-row :gutter="16">
      <!-- 左侧图表区 -->
      <a-col :span="16">
        <!-- 质检趋势图 -->
        <a-card title="质检趋势分析" class="chart-card">
          <a-tabs v-model:activeKey="chartTab">
            <a-tab-pane key="score" tab="得分趋势">
              <div ref="scoreChart" class="chart-container"></div>
            </a-tab-pane>
            <a-tab-pane key="volume" tab="质检量趋势">
              <div ref="volumeChart" class="chart-container"></div>
            </a-tab-pane>
            <a-tab-pane key="passRate" tab="合格率趋势">
              <div ref="passRateChart" class="chart-container"></div>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 部门对比 -->
        <a-card title="部门质检对比" class="chart-card">
          <div ref="departmentChart" class="chart-container"></div>
        </a-card>

        <!-- 问题分布 -->
        <a-card title="问题分布分析" class="chart-card">
          <div ref="issueChart" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 右侧详情区 -->
      <a-col :span="8">
        <!-- 质检排行 -->
        <a-card title="质检员排行" class="ranking-card">
          <a-list 
            :data-source="inspectorRanking" 
            size="small"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-badge 
                      :count="index + 1" 
                      :number-style="{ backgroundColor: getRankColor(index) }"
                    >
                      <a-avatar :src="item.avatar">{{ item.name.charAt(0) }}</a-avatar>
                    </a-badge>
                  </template>
                  <template #title>{{ item.name }}</template>
                  <template #description>
                    <div class="ranking-info">
                      <div>完成质检: {{ item.completedCount }}次</div>
                      <div>平均得分: {{ item.avgScore }}分</div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>

        <!-- 异常问题汇总 -->
        <a-card title="异常问题汇总" class="issue-card">
          <a-list 
            :data-source="issuesSummary" 
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <div class="issue-header">
                      <span>{{ item.type }}</span>
                      <a-tag :color="getSeverityColor(item.severity)">
                        {{ getSeverityText(item.severity) }}
                      </a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="issue-content">
                      <div>发现次数: {{ item.count }}次</div>
                      <div>影响人员: {{ item.affectedPersons }}人</div>
                      <div class="issue-desc">{{ item.description }}</div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>

        <!-- 改进建议 -->
        <a-card title="改进建议" class="suggestion-card">
          <a-list 
            :data-source="suggestions" 
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar 
                      :style="{ backgroundColor: getPriorityColor(item.priority) }"
                      size="small"
                    >
                      {{ item.priority.charAt(0).toUpperCase() }}
                    </a-avatar>
                  </template>
                  <template #title>{{ item.title }}</template>
                  <template #description>{{ item.content }}</template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <!-- 详细报告表格 -->
    <a-card title="详细质检记录" class="table-card">
      <a-table
        :columns="reportColumns"
        :data-source="reportData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span :class="getScoreClass(record.score)">
              {{ record.score }}分
            </span>
          </template>
          <template v-else-if="column.key === 'result'">
            <a-tag :color="record.result === 'pass' ? 'green' : 'red'">
              {{ record.result === 'pass' ? '合格' : '不合格' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看详情
              </a-button>
              <a-button type="link" size="small" @click="exportRecord(record)">
                导出
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 生成报告模态框 -->
    <a-modal
      v-model:open="showGenerateModal"
      title="生成质检报告"
      width="600px"
      @ok="handleGenerateSubmit"
    >
      <a-form :model="generateForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="报告名称" required>
          <a-input v-model:value="generateForm.name" placeholder="请输入报告名称" />
        </a-form-item>
        
        <a-form-item label="报告类型" required>
          <a-select v-model:value="generateForm.type" placeholder="选择报告类型">
            <a-select-option value="summary">汇总报告</a-select-option>
            <a-select-option value="detail">详细报告</a-select-option>
            <a-select-option value="analysis">分析报告</a-select-option>
            <a-select-option value="trend">趋势报告</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="时间范围" required>
          <a-range-picker 
            v-model:value="generateForm.dateRange" 
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="包含内容">
          <a-checkbox-group v-model:value="generateForm.includes">
            <a-checkbox value="statistics">统计数据</a-checkbox>
            <a-checkbox value="charts">图表分析</a-checkbox>
            <a-checkbox value="issues">问题汇总</a-checkbox>
            <a-checkbox value="suggestions">改进建议</a-checkbox>
            <a-checkbox value="ranking">人员排行</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="导出格式">
          <a-radio-group v-model:value="generateForm.format">
            <a-radio value="pdf">PDF</a-radio>
            <a-radio value="excel">Excel</a-radio>
            <a-radio value="word">Word</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  FileTextOutlined,
  AuditOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const showGenerateModal = ref(false)
const chartTab = ref('score')

// 图表引用
const scoreChart = ref(null)
const volumeChart = ref(null)
const passRateChart = ref(null)
const departmentChart = ref(null)
const issueChart = ref(null)

// 筛选表单
const filterForm = reactive({
  type: 'monthly',
  dateRange: [],
  department: undefined,
  qualityType: undefined
})

// 生成报告表单
const generateForm = reactive({
  name: '',
  type: '',
  dateRange: [],
  includes: ['statistics', 'charts'],
  format: 'pdf'
})

// 概览数据
const overview = reactive({
  totalInspections: 324,
  avgScore: 86.5,
  passRate: 91.2,
  issuesFound: 28
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 324,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 质检员排行数据
const inspectorRanking = ref([
  {
    name: '张质检',
    avatar: '',
    completedCount: 45,
    avgScore: 92.5
  },
  {
    name: '李主管',
    avatar: '',
    completedCount: 38,
    avgScore: 89.3
  },
  {
    name: '王专员',
    avatar: '',
    completedCount: 36,
    avgScore: 87.8
  },
  {
    name: '赵质检',
    avatar: '',
    completedCount: 32,
    avgScore: 85.2
  }
])

// 问题汇总数据
const issuesSummary = ref([
  {
    type: '沟通不规范',
    severity: 'high',
    count: 12,
    affectedPersons: 8,
    description: '催收语言不当，可能引起客户投诉'
  },
  {
    type: '流程不合规',
    severity: 'medium',
    count: 8,
    affectedPersons: 6,
    description: '未按标准流程执行催收操作'
  },
  {
    type: '记录不完整',
    severity: 'low',
    count: 15,
    affectedPersons: 12,
    description: '催收记录填写不完整或不及时'
  }
])

// 改进建议数据
const suggestions = ref([
  {
    priority: 'high',
    title: '加强沟通技巧培训',
    content: '针对沟通不规范问题，建议开展专项培训'
  },
  {
    priority: 'medium',
    title: '完善流程监督',
    content: '建立更严格的流程监督机制'
  },
  {
    priority: 'low',
    title: '优化记录系统',
    content: '简化记录流程，提高填写效率'
  }
])

// 报告表格列配置
const reportColumns = [
  {
    title: '质检编号',
    dataIndex: 'inspectionId',
    key: 'inspectionId',
    width: 120
  },
  {
    title: '质检对象',
    dataIndex: 'targetName',
    key: 'targetName',
    width: 100
  },
  {
    title: '质检员',
    dataIndex: 'inspector',
    key: 'inspector',
    width: 100
  },
  {
    title: '质检类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    width: 80
  },
  {
    title: '结果',
    dataIndex: 'result',
    key: 'result',
    width: 80
  },
  {
    title: '质检时间',
    dataIndex: 'inspectionTime',
    key: 'inspectionTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 报告数据
const reportData = ref([
  {
    id: 1,
    inspectionId: 'QC2024010001',
    targetName: '李催收员',
    inspector: '张质检',
    type: '通话质检',
    score: 92,
    result: 'pass',
    inspectionTime: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    inspectionId: 'QC2024010002',
    targetName: '王催收员',
    inspector: '李主管',
    type: '外访质检',
    score: 85,
    result: 'pass',
    inspectionTime: '2024-01-15 15:20:00'
  },
  {
    id: 3,
    inspectionId: 'QC2024010003',
    targetName: '张催收员',
    inspector: '王专员',
    type: '短信质检',
    score: 68,
    result: 'fail',
    inspectionTime: '2024-01-15 16:10:00'
  }
])

// 方法定义
const getRankColor = (index) => {
  const colors = ['#f5222d', '#fa8c16', '#fadb14', '#52c41a']
  return colors[index] || '#d9d9d9'
}

const getSeverityColor = (severity) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[severity] || 'default'
}

const getSeverityText = (severity) => {
  const texts = {
    high: '严重',
    medium: '中等',
    low: '轻微'
  }
  return texts[severity] || severity
}

const getPriorityColor = (priority) => {
  const colors = {
    high: '#f5222d',
    medium: '#fa8c16',
    low: '#52c41a'
  }
  return colors[priority] || '#d9d9d9'
}

const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 70) return 'score-average'
  return 'score-poor'
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const generateReport = () => {
  showGenerateModal.value = true
}

const handleSearch = () => {
  pagination.current = 1
  refreshData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    type: 'monthly',
    dateRange: [],
    department: undefined,
    qualityType: undefined
  })
  handleSearch()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  refreshData()
}

const viewDetail = (record) => {
  console.log('查看详情:', record)
}

const exportRecord = (record) => {
  console.log('导出记录:', record)
}

const handleGenerateSubmit = () => {
  console.log('生成报告:', generateForm)
  showGenerateModal.value = false
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 得分趋势图
    if (scoreChart.value) {
      const chart1 = echarts.init(scoreChart.value)
      chart1.setOption({
        title: { text: '质检得分趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value', min: 60, max: 100 },
        series: [{
          name: '平均得分',
          type: 'line',
          data: [82.5, 85.3, 87.8, 86.2, 88.9, 86.5],
          smooth: true,
          areaStyle: { opacity: 0.3 }
        }]
      })
    }

    // 质检量趋势图
    if (volumeChart.value) {
      const chart2 = echarts.init(volumeChart.value)
      chart2.setOption({
        title: { text: '质检量趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '质检次数',
          type: 'bar',
          data: [45, 52, 48, 61, 55, 58],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      })
    }

    // 合格率趋势图
    if (passRateChart.value) {
      const chart3 = echarts.init(passRateChart.value)
      chart3.setOption({
        title: { text: '合格率趋势', left: 'center' },
        tooltip: { trigger: 'axis', formatter: '{b}: {c}%' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value', min: 80, max: 100 },
        series: [{
          name: '合格率',
          type: 'line',
          data: [88.5, 91.2, 89.7, 93.1, 90.8, 91.2],
          smooth: true,
          lineStyle: { color: '#52c41a' },
          areaStyle: { color: 'rgba(82, 196, 26, 0.3)' }
        }]
      })
    }

    // 部门对比图
    if (departmentChart.value) {
      const chart4 = echarts.init(departmentChart.value)
      chart4.setOption({
        title: { text: '部门质检对比', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['催收一部', '催收二部', '催收三部']
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '平均得分',
            type: 'bar',
            data: [87.5, 85.2, 89.1],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '合格率(%)',
            type: 'bar',
            data: [92.3, 88.7, 94.5],
            itemStyle: { color: '#52c41a' }
          }
        ]
      })
    }

    // 问题分布图
    if (issueChart.value) {
      const chart5 = echarts.init(issueChart.value)
      chart5.setOption({
        title: { text: '问题类型分布', left: 'center' },
        tooltip: { trigger: 'item' },
        series: [{
          name: '问题分布',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 12, name: '沟通不规范' },
            { value: 8, name: '流程不合规' },
            { value: 15, name: '记录不完整' },
            { value: 5, name: '其他问题' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshData()
  initCharts()
})
</script>

<style scoped>
.quality-report {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.overview-section {
  margin-bottom: 16px;
}

.chart-card,
.ranking-card,
.issue-card,
.suggestion-card,
.table-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.ranking-info {
  font-size: 12px;
  color: #666;
}

.issue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.issue-content {
  font-size: 12px;
  color: #666;
}

.issue-desc {
  margin-top: 4px;
  color: #999;
}

.score-excellent {
  color: #52c41a;
  font-weight: 500;
}

.score-good {
  color: #1890ff;
  font-weight: 500;
}

.score-average {
  color: #faad14;
  font-weight: 500;
}

.score-poor {
  color: #ff4d4f;
  font-weight: 500;
}
</style>