<template>
  <div class="quality-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>质检分析</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="generateAnalysisReport">
            <template #icon><FileTextOutlined /></template>
            生成分析报告
          </a-button>
        </div>
      </div>
    </div>

    <!-- 分析筛选 -->
    <div class="filter-section">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="分析维度">
          <a-select v-model:value="filterForm.dimension" placeholder="选择维度" style="width: 150px">
            <a-select-option value="overall">整体分析</a-select-option>
            <a-select-option value="department">部门分析</a-select-option>
            <a-select-option value="inspector">质检员分析</a-select-option>
            <a-select-option value="type">类型分析</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="时间范围">
          <a-range-picker v-model:value="filterForm.dateRange" style="width: 240px" />
        </a-form-item>
        
        <a-form-item label="对比周期">
          <a-select v-model:value="filterForm.compareType" placeholder="对比周期" style="width: 120px">
            <a-select-option value="none">无对比</a-select-option>
            <a-select-option value="previous">环比</a-select-option>
            <a-select-option value="yearOnYear">同比</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleAnalysis">分析</a-button>
          <a-button style="margin-left: 8px" @click="resetFilter">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 核心指标 -->
    <div class="metrics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="质检覆盖率" 
              :value="metrics.coverageRate" 
              :value-style="{ color: '#1890ff' }"
              suffix="%"
            >
              <template #prefix><PieChartOutlined /></template>
              <template #suffix>
                <a-tooltip title="质检覆盖率 = 已质检数量 / 总业务量 × 100%">
                  <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                </a-tooltip>
              </template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="metrics.coverageTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="metrics.coverageTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(metrics.coverageTrend) }}%
              </span>
              <span class="trend-text">较上期</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="平均质检分" 
              :value="metrics.avgScore" 
              :value-style="{ color: '#52c41a' }"
              suffix="分"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="metrics.scoreTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="metrics.scoreTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(metrics.scoreTrend) }}分
              </span>
              <span class="trend-text">较上期</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="合格率" 
              :value="metrics.passRate" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="metrics.passRateTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="metrics.passRateTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(metrics.passRateTrend) }}%
              </span>
              <span class="trend-text">较上期</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="问题发现率" 
              :value="metrics.issueRate" 
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
            <div class="metric-trend">
              <span :class="metrics.issueRateTrend > 0 ? 'trend-up' : 'trend-down'">
                <CaretUpOutlined v-if="metrics.issueRateTrend > 0" />
                <CaretDownOutlined v-else />
                {{ Math.abs(metrics.issueRateTrend) }}%
              </span>
              <span class="trend-text">较上期</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 分析内容 -->
    <a-row :gutter="16">
      <!-- 左侧分析图表 -->
      <a-col :span="16">
        <!-- 质检趋势分析 -->
        <a-card title="质检趋势分析" class="chart-card">
          <a-tabs v-model:activeKey="trendTab">
            <a-tab-pane key="score" tab="分数趋势">
              <div ref="scoreTrendChart" class="chart-container"></div>
            </a-tab-pane>
            <a-tab-pane key="volume" tab="质检量趋势">
              <div ref="volumeTrendChart" class="chart-container"></div>
            </a-tab-pane>
            <a-tab-pane key="passRate" tab="合格率趋势">
              <div ref="passRateTrendChart" class="chart-container"></div>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 多维度对比分析 -->
        <a-card title="多维度对比分析" class="chart-card">
          <a-tabs v-model:activeKey="compareTab">
            <a-tab-pane key="department" tab="部门对比">
              <div ref="departmentCompareChart" class="chart-container"></div>
            </a-tab-pane>
            <a-tab-pane key="inspector" tab="质检员对比">
              <div ref="inspectorCompareChart" class="chart-container"></div>
            </a-tab-pane>
            <a-tab-pane key="type" tab="类型对比">
              <div ref="typeCompareChart" class="chart-container"></div>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 质量分布分析 -->
        <a-card title="质量分布分析" class="chart-card">
          <div ref="qualityDistributionChart" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 右侧详细分析 -->
      <a-col :span="8">
        <!-- AI智能分析 -->
        <a-card title="AI智能分析" class="analysis-card">
          <div class="ai-analysis">
            <div class="analysis-item">
              <div class="analysis-header">
                <RobotOutlined style="color: #1890ff;" />
                <span>核心发现</span>
              </div>
              <div class="analysis-content">
                <p>• 本周质检覆盖率较上周提升3.2%，达到{{ metrics.coverageRate }}%</p>
                <p>• 平均质检分较上月上升{{ metrics.scoreTrend }}分，整体质量持续改善</p>
                <p>• 沟通类问题占比下降15%，培训效果显著</p>
                <p>• 建议重点关注催收二部的合规问题</p>
              </div>
            </div>
            
            <a-divider />
            
            <div class="analysis-item">
              <div class="analysis-header">
                <BulbOutlined style="color: #faad14;" />
                <span>改进建议</span>
              </div>
              <div class="analysis-content">
                <a-list size="small" :data-source="improvementSuggestions">
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <div class="suggestion-item">
                        <a-tag :color="item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'blue'">
                          {{ item.priority === 'high' ? '高' : item.priority === 'medium' ? '中' : '低' }}
                        </a-tag>
                        <span>{{ item.content }}</span>
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 绩效排行 -->
        <a-card title="绩效排行" class="ranking-card">
          <a-tabs v-model:activeKey="rankingTab" size="small">
            <a-tab-pane key="department" tab="部门排行">
              <a-list size="small" :data-source="departmentRanking">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge 
                          :count="index + 1" 
                          :number-style="{ backgroundColor: getRankColor(index) }"
                        >
                          <a-avatar :style="{ backgroundColor: item.color }">
                            {{ item.name.charAt(0) }}
                          </a-avatar>
                        </a-badge>
                      </template>
                      <template #title>{{ item.name }}</template>
                      <template #description>
                        <div class="ranking-metrics">
                          <div>平均分: {{ item.avgScore }}</div>
                          <div>合格率: {{ item.passRate }}%</div>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-tab-pane>
            <a-tab-pane key="inspector" tab="质检员排行">
              <a-list size="small" :data-source="inspectorRanking">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge 
                          :count="index + 1" 
                          :number-style="{ backgroundColor: getRankColor(index) }"
                        >
                          <a-avatar :src="item.avatar">{{ item.name.charAt(0) }}</a-avatar>
                        </a-badge>
                      </template>
                      <template #title>{{ item.name }}</template>
                      <template #description>
                        <div class="ranking-metrics">
                          <div>质检量: {{ item.inspectionCount }}</div>
                          <div>准确率: {{ item.accuracy }}%</div>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 风险预警 -->
        <a-card title="风险预警" class="warning-card">
          <a-list size="small" :data-source="riskWarnings">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar 
                      :style="{ backgroundColor: getWarningColor(item.level) }"
                      size="small"
                    >
                      <WarningOutlined />
                    </a-avatar>
                  </template>
                  <template #title>
                    <div class="warning-header">
                      <span>{{ item.title }}</span>
                      <a-tag :color="getWarningColor(item.level)" size="small">
                        {{ getWarningText(item.level) }}
                      </a-tag>
                    </div>
                  </template>
                  <template #description>{{ item.description }}</template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>

        <!-- 预测分析 -->
        <a-card title="趋势预测" class="prediction-card">
          <div class="prediction-content">
            <div class="prediction-item">
              <div class="prediction-label">下月预测合格率</div>
              <div class="prediction-value">
                <span class="prediction-number">{{ prediction.nextMonthPassRate }}%</span>
                <span :class="prediction.passRateTrend > 0 ? 'trend-up' : 'trend-down'">
                  <CaretUpOutlined v-if="prediction.passRateTrend > 0" />
                  <CaretDownOutlined v-else />
                  {{ Math.abs(prediction.passRateTrend) }}%
                </span>
              </div>
            </div>
            
            <a-divider />
            
            <div class="prediction-item">
              <div class="prediction-label">问题发现趋势</div>
              <div class="prediction-value">
                <span class="prediction-number">{{ prediction.nextMonthIssueRate }}%</span>
                <span :class="prediction.issueRateTrend < 0 ? 'trend-up' : 'trend-down'">
                  <CaretDownOutlined v-if="prediction.issueRateTrend < 0" />
                  <CaretUpOutlined v-else />
                  {{ Math.abs(prediction.issueRateTrend) }}%
                </span>
              </div>
            </div>
            
            <a-divider />
            
            <div class="prediction-confidence">
              <div class="confidence-label">预测置信度</div>
              <a-progress 
                :percent="prediction.confidence" 
                :show-info="false"
                stroke-color="#52c41a"
              />
              <span class="confidence-text">{{ prediction.confidence }}%</span>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  FileTextOutlined,
  PieChartOutlined,
  QuestionCircleOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  RobotOutlined,
  BulbOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const trendTab = ref('score')
const compareTab = ref('department')
const rankingTab = ref('department')

// 图表引用
const scoreTrendChart = ref(null)
const volumeTrendChart = ref(null)
const passRateTrendChart = ref(null)
const departmentCompareChart = ref(null)
const inspectorCompareChart = ref(null)
const typeCompareChart = ref(null)
const qualityDistributionChart = ref(null)

// 筛选表单
const filterForm = reactive({
  dimension: 'overall',
  dateRange: [],
  compareType: 'previous'
})

// 核心指标
const metrics = reactive({
  coverageRate: 92.5,
  coverageTrend: 3.2,
  avgScore: 86.8,
  scoreTrend: 2.3,
  passRate: 91.2,
  passRateTrend: 1.8,
  issueRate: 12.5,
  issueRateTrend: -2.1
})

// 改进建议
const improvementSuggestions = ref([
  {
    priority: 'high',
    content: '加强催收二部合规培训，重点关注短信催收规范'
  },
  {
    priority: 'medium', 
    content: '建立质检标准化模板，提高质检一致性'
  },
  {
    priority: 'medium',
    content: '增加外访质检频次，确保服务质量'
  },
  {
    priority: 'low',
    content: '优化质检流程，减少重复性工作'
  }
])

// 部门排行
const departmentRanking = ref([
  {
    name: '催收一部',
    avgScore: 89.2,
    passRate: 94.1,
    color: '#1890ff'
  },
  {
    name: '催收三部',
    avgScore: 87.8,
    passRate: 92.3,
    color: '#52c41a'
  },
  {
    name: '催收二部',
    avgScore: 84.5,
    passRate: 88.7,
    color: '#faad14'
  }
])

// 质检员排行
const inspectorRanking = ref([
  {
    name: '张质检',
    inspectionCount: 145,
    accuracy: 96.2,
    avatar: ''
  },
  {
    name: '李主管',
    inspectionCount: 132,
    accuracy: 94.8,
    avatar: ''
  },
  {
    name: '王专员',
    inspectionCount: 118,
    accuracy: 93.5,
    avatar: ''
  },
  {
    name: '赵质检',
    inspectionCount: 98,
    accuracy: 91.2,
    avatar: ''
  }
])

// 风险预警
const riskWarnings = ref([
  {
    level: 'high',
    title: '催收二部合格率下降',
    description: '本月合格率较上月下降8.3%，需要重点关注'
  },
  {
    level: 'medium',
    title: '外访质检覆盖不足',
    description: '外访质检覆盖率仅为65%，建议增加抽检频次'
  },
  {
    level: 'low',
    title: '新员工质检待安排',
    description: '3名新入职员工尚未安排质检，建议尽快排期'
  }
])

// 趋势预测
const prediction = reactive({
  nextMonthPassRate: 93.5,
  passRateTrend: 2.3,
  nextMonthIssueRate: 10.2,
  issueRateTrend: -2.3,
  confidence: 87
})

// 方法定义
const getRankColor = (index) => {
  const colors = ['#f5222d', '#fa8c16', '#fadb14', '#52c41a']
  return colors[index] || '#d9d9d9'
}

const getWarningColor = (level) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#1890ff'
  }
  return colors[level] || '#d9d9d9'
}

const getWarningText = (level) => {
  const texts = {
    high: '严重',
    medium: '警告',
    low: '提醒'
  }
  return texts[level] || level
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const generateAnalysisReport = () => {
  console.log('生成分析报告')
}

const handleAnalysis = () => {
  refreshData()
  initCharts()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    dimension: 'overall',
    dateRange: [],
    compareType: 'previous'
  })
  handleAnalysis()
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 分数趋势图
    if (scoreTrendChart.value) {
      const chart1 = echarts.init(scoreTrendChart.value)
      chart1.setOption({
        title: { text: '质检分数趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value', min: 75, max: 95 },
        series: [
          {
            name: '实际得分',
            type: 'line',
            data: [82.1, 84.5, 86.2, 85.8, 87.3, 86.8],
            smooth: true,
            lineStyle: { color: '#1890ff' },
            areaStyle: { color: 'rgba(24, 144, 255, 0.1)' }
          },
          {
            name: '目标得分',
            type: 'line',
            data: [85, 85, 85, 85, 85, 85],
            lineStyle: { color: '#ff4d4f', type: 'dashed' }
          },
          {
            name: '预测得分',
            type: 'line',
            data: [null, null, null, null, 86.8, 88.2],
            smooth: true,
            lineStyle: { color: '#52c41a', type: 'dotted' }
          }
        ]
      })
    }

    // 质检量趋势图
    if (volumeTrendChart.value) {
      const chart2 = echarts.init(volumeTrendChart.value)
      chart2.setOption({
        title: { text: '质检量趋势', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '质检数量',
          type: 'bar',
          data: [245, 252, 268, 261, 285, 298],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      })
    }

    // 合格率趋势图
    if (passRateTrendChart.value) {
      const chart3 = echarts.init(passRateTrendChart.value)
      chart3.setOption({
        title: { text: '合格率趋势', left: 'center' },
        tooltip: { trigger: 'axis', formatter: '{b}: {c}%' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value', min: 80, max: 100 },
        series: [{
          name: '合格率',
          type: 'line',
          data: [88.5, 89.2, 90.7, 89.4, 91.8, 91.2],
          smooth: true,
          lineStyle: { color: '#52c41a' },
          areaStyle: { color: 'rgba(82, 196, 26, 0.3)' }
        }]
      })
    }

    // 部门对比图
    if (departmentCompareChart.value) {
      const chart4 = echarts.init(departmentCompareChart.value)
      chart4.setOption({
        title: { text: '部门质检对比', left: 'center' },
        tooltip: { trigger: 'axis' },
        legend: { bottom: 0 },
        radar: {
          indicator: [
            { name: '平均得分', max: 100 },
            { name: '合格率', max: 100 },
            { name: '问题发现率', max: 30 },
            { name: '整改完成率', max: 100 },
            { name: '客户满意度', max: 100 }
          ]
        },
        series: [{
          type: 'radar',
          data: [
            {
              value: [89.2, 94.1, 12.3, 95.6, 92.1],
              name: '催收一部',
              areaStyle: { color: 'rgba(24, 144, 255, 0.3)' }
            },
            {
              value: [84.5, 88.7, 18.2, 87.3, 89.5],
              name: '催收二部',
              areaStyle: { color: 'rgba(250, 173, 20, 0.3)' }
            },
            {
              value: [87.8, 92.3, 14.1, 91.8, 90.7],
              name: '催收三部',
              areaStyle: { color: 'rgba(82, 196, 26, 0.3)' }
            }
          ]
        }]
      })
    }

    // 质检员对比图
    if (inspectorCompareChart.value) {
      const chart5 = echarts.init(inspectorCompareChart.value)
      chart5.setOption({
        title: { text: '质检员工作量对比', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['张质检', '李主管', '王专员', '赵质检']
        },
        yAxis: [
          {
            type: 'value',
            name: '质检数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '准确率(%)',
            position: 'right',
            min: 85,
            max: 100
          }
        ],
        series: [
          {
            name: '质检数量',
            type: 'bar',
            data: [145, 132, 118, 98],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '准确率',
            type: 'line',
            yAxisIndex: 1,
            data: [96.2, 94.8, 93.5, 91.2],
            lineStyle: { color: '#52c41a' }
          }
        ]
      })
    }

    // 类型对比图
    if (typeCompareChart.value) {
      const chart6 = echarts.init(typeCompareChart.value)
      chart6.setOption({
        title: { text: '质检类型分布', left: 'center' },
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 145, name: '通话质检' },
            { value: 89, name: '外访质检' },
            { value: 67, name: '短信质检' },
            { value: 45, name: '服务质检' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    }

    // 质量分布图
    if (qualityDistributionChart.value) {
      const chart7 = echarts.init(qualityDistributionChart.value)
      chart7.setOption({
        title: { text: '质检分数分布', left: 'center' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['60-69', '70-79', '80-89', '90-95', '95-100']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '数量',
          type: 'bar',
          data: [5, 18, 89, 132, 54],
          itemStyle: {
            color: function(params) {
              const colors = ['#ff4d4f', '#faad14', '#1890ff', '#52c41a', '#722ed1']
              return colors[params.dataIndex]
            }
          }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  refreshData()
  initCharts()
})
</script>

<style scoped>
.quality-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metrics-section {
  margin-bottom: 16px;
}

.metric-trend {
  margin-top: 8px;
  font-size: 12px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-text {
  color: #8c8c8c;
  margin-left: 4px;
}

.chart-card,
.analysis-card,
.ranking-card,
.warning-card,
.prediction-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.ai-analysis {
  padding: 8px 0;
}

.analysis-item {
  margin-bottom: 16px;
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #262626;
}

.analysis-content p {
  margin: 8px 0;
  color: #595959;
  font-size: 13px;
  line-height: 1.5;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.ranking-metrics {
  font-size: 12px;
  color: #8c8c8c;
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prediction-content {
  padding: 8px 0;
}

.prediction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.prediction-label {
  color: #595959;
  font-size: 13px;
}

.prediction-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.prediction-number {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

.prediction-confidence {
  margin-top: 16px;
}

.confidence-label {
  color: #595959;
  font-size: 13px;
  margin-bottom: 8px;
}

.confidence-text {
  margin-left: 8px;
  color: #52c41a;
  font-weight: 500;
}
</style>