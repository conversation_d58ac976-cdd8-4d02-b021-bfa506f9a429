<template>
  <div class="quality-plan">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>质检计划</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建计划
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="计划总数"
              :value="overview.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><FileTextOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="执行中"
              :value="overview.executing"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="已完成"
              :value="overview.completed"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="今日新增"
              :value="overview.todayNew"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix><PlusOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="4">
          <a-card>
            <a-statistic
              title="完成率"
              :value="overview.completionRate"
              :value-style="{ color: '#faad14' }"
              suffix="%"
            >
              <template #prefix><PercentageOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <a-card class="search-card enhanced-search">
      <template #title>
        质检计划搜索
      </template>

      <a-form :model="filterForm" @submit="handleSearch">
        <!-- 基础搜索 -->
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="计划状态">
              <a-select
                v-model:value="filterForm.status"
                placeholder="请选择状态"
                allow-clear
              >
                <template #suffixIcon><CheckCircleOutlined /></template>
                <a-select-option value="draft">
                  <a-tag color="default">草稿</a-tag>
                </a-select-option>
                <a-select-option value="published">
                  <a-tag color="blue">已发布</a-tag>
                </a-select-option>
                <a-select-option value="executing">
                  <a-tag color="processing">执行中</a-tag>
                </a-select-option>
                <a-select-option value="completed">
                  <a-tag color="success">已完成</a-tag>
                </a-select-option>
                <a-select-option value="paused">
                  <a-tag color="warning">已暂停</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="质检类型">
              <a-select
                v-model:value="filterForm.type"
                placeholder="请选择类型"
                allow-clear
              >
                <template #suffixIcon><AuditOutlined /></template>
                <a-select-option value="call">
                  <a-tag color="blue">通话质检</a-tag>
                </a-select-option>
                <a-select-option value="visit">
                  <a-tag color="green">外访质检</a-tag>
                </a-select-option>
                <a-select-option value="sms">
                  <a-tag color="orange">短信质检</a-tag>
                </a-select-option>
                <a-select-option value="service">
                  <a-tag color="purple">服务质检</a-tag>
                </a-select-option>
                <a-select-option value="comprehensive">
                  <a-tag color="red">综合质检</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="创建人">
              <a-input
                v-model:value="filterForm.creator"
                placeholder="请输入创建人"
                allow-clear
              >
                <template #prefix><UserOutlined /></template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="创建时间">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                :presets="timePresets"
                format="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 高级搜索 -->
        <div v-if="showAdvanced">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="计划周期">
                <a-select
                  v-model:value="filterForm.cycle"
                  placeholder="请选择周期"
                  allow-clear
                >
                  <a-select-option value="daily">每日</a-select-option>
                  <a-select-option value="weekly">每周</a-select-option>
                  <a-select-option value="monthly">每月</a-select-option>
                  <a-select-option value="quarterly">每季度</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="优先级">
                <a-select
                  v-model:value="filterForm.priority"
                  placeholder="请选择优先级"
                  allow-clear
                >
                  <a-select-option value="high">高</a-select-option>
                  <a-select-option value="medium">中</a-select-option>
                  <a-select-option value="low">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="执行部门">
                <a-select
                  v-model:value="filterForm.department"
                  placeholder="请选择部门"
                  allow-clear
                >
                  <a-select-option value="quality">质检部</a-select-option>
                  <a-select-option value="collection">催收部</a-select-option>
                  <a-select-option value="service">客服部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="计划编号">
                <a-input
                  v-model:value="filterForm.planNo"
                  placeholder="请输入计划编号"
                  allow-clear
                >
                  <template #prefix><NumberOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 搜索按钮 -->
        <a-row>
          <a-col :span="24">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetFilter">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
                <a-button @click="toggleAdvanced">
                  <span>{{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}</span>
                  <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
                </a-button>
                <a-button @click="exportPlans">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 计划列表 -->
    <div class="plan-list">
      <a-table
        :columns="columns"
        :data-source="planList"
        :pagination="pagination"
        :loading="loading"
        :scroll="{ x: 1040 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>{{ getTypeText(record.type) }}</span>
          </template>
          <template v-else-if="column.key === 'progress'">
            <a-progress 
              :percent="record.progress" 
              size="small" 
              :status="record.status === 'completed' ? 'success' : 'active'"
            />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space :size="4">
              <a-button type="link" size="small" @click="viewPlan(record)">
                查看
              </a-button>
              <a-button
                v-if="record.status === 'draft'"
                type="link"
                size="small"
                @click="editPlan(record)"
              >
                编辑
              </a-button>
              <a-button
                v-if="record.status === 'published'"
                type="link"
                size="small"
                @click="executePlan(record)"
              >
                执行
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-if="record.status === 'executing'" @click="pausePlan(record)">
                      <PauseCircleOutlined />
                      暂停
                    </a-menu-item>
                    <a-menu-item v-if="record.status === 'paused'" @click="resumePlan(record)">
                      <PlayCircleOutlined />
                      继续
                    </a-menu-item>
                    <a-menu-item @click="copyPlan(record)">
                      <CopyOutlined />
                      复制
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deletePlan(record)" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建计划模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建质检计划"
      width="800px"
      @ok="handleCreateSubmit"
      @cancel="resetCreateForm"
    >
      <a-form :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="计划名称" required>
          <a-input v-model:value="createForm.name" placeholder="请输入计划名称" />
        </a-form-item>
        
        <a-form-item label="质检类型" required>
          <a-select v-model:value="createForm.type" placeholder="请选择质检类型">
            <a-select-option value="call">通话质检</a-select-option>
            <a-select-option value="visit">外访质检</a-select-option>
            <a-select-option value="sms">短信质检</a-select-option>
            <a-select-option value="service">服务质检</a-select-option>
            <a-select-option value="comprehensive">综合质检</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="执行周期" required>
          <a-select v-model:value="createForm.cycle" placeholder="请选择执行周期">
            <a-select-option value="daily">每日</a-select-option>
            <a-select-option value="weekly">每周</a-select-option>
            <a-select-option value="monthly">每月</a-select-option>
            <a-select-option value="quarterly">每季度</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="执行时间" required>
          <a-range-picker 
            v-model:value="createForm.timeRange" 
            show-time 
            style="width: 100%" 
            placeholder="['开始时间', '结束时间']"
          />
        </a-form-item>
        
        <a-form-item label="质检人员" required>
          <a-select 
            v-model:value="createForm.inspectors" 
            mode="multiple" 
            placeholder="请选择质检人员"
            style="width: 100%"
          >
            <a-select-option value="user1">张质检</a-select-option>
            <a-select-option value="user2">李主管</a-select-option>
            <a-select-option value="user3">王经理</a-select-option>
            <a-select-option value="user4">赵专员</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="抽检比例" required>
          <a-slider 
            v-model:value="createForm.sampleRate" 
            :min="1" 
            :max="100" 
            :marks="{ 10: '10%', 30: '30%', 50: '50%', 80: '80%', 100: '100%' }"
          />
        </a-form-item>
        
        <a-form-item label="质检标准">
          <a-select v-model:value="createForm.standard" placeholder="选择质检标准">
            <a-select-option value="standard1">通话规范标准</a-select-option>
            <a-select-option value="standard2">外访服务标准</a-select-option>
            <a-select-option value="standard3">沟通技巧标准</a-select-option>
            <a-select-option value="standard4">合规操作标准</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="计划描述">
          <a-textarea v-model:value="createForm.description" placeholder="请输入计划描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="自动执行">
          <a-switch 
            v-model:checked="createForm.autoExecute" 
            checked-children="开启" 
            un-checked-children="关闭" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 计划详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="质检计划详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentPlan" class="plan-detail">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="计划名称">{{ currentPlan.name }}</a-descriptions-item>
          <a-descriptions-item label="质检类型">{{ getTypeText(currentPlan.type) }}</a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(currentPlan.status)">
              {{ getStatusText(currentPlan.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="执行周期">{{ getCycleText(currentPlan.cycle) }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ currentPlan.creator }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentPlan.createTime }}</a-descriptions-item>
          <a-descriptions-item label="开始时间">{{ currentPlan.startTime }}</a-descriptions-item>
          <a-descriptions-item label="结束时间">{{ currentPlan.endTime }}</a-descriptions-item>
          <a-descriptions-item label="抽检比例">{{ currentPlan.sampleRate }}%</a-descriptions-item>
          <a-descriptions-item label="质检标准">{{ currentPlan.standard }}</a-descriptions-item>
          <a-descriptions-item label="执行进度" :span="2">
            <a-progress :percent="currentPlan.progress" />
          </a-descriptions-item>
          <a-descriptions-item label="计划描述" :span="2">{{ currentPlan.description }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>质检人员</h4>
        <a-list 
          :data-source="currentPlan.inspectorList" 
          size="small"
          :grid="{ gutter: 16, column: 4 }"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-card size="small">
                <a-card-meta>
                  <template #avatar>
                    <a-avatar :src="item.avatar">{{ item.name.charAt(0) }}</a-avatar>
                  </template>
                  <template #title>{{ item.name }}</template>
                  <template #description>{{ item.role }}</template>
                </a-card-meta>
              </a-card>
            </a-list-item>
          </template>
        </a-list>
        
        <a-divider />
        
        <h4>执行统计</h4>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总任务数" :value="currentPlan.stats.totalTasks" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="已完成" :value="currentPlan.stats.completedTasks" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="进行中" :value="currentPlan.stats.ongoingTasks" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均分" :value="currentPlan.stats.avgScore" suffix="分" />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  ReloadOutlined,
  PlusOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  PercentageOutlined,
  PauseCircleOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  UserOutlined,
  AuditOutlined,
  NumberOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const currentPlan = ref(null)
const showAdvanced = ref(false)

// 筛选表单
const filterForm = reactive({
  status: undefined,
  type: undefined,
  creator: '',
  dateRange: [],
  cycle: undefined,
  priority: undefined,
  department: undefined,
  planNo: ''
})

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 创建表单
const createForm = reactive({
  name: '',
  type: '',
  cycle: '',
  timeRange: [],
  inspectors: [],
  sampleRate: 30,
  standard: '',
  description: '',
  autoExecute: false
})

// 统计数据
const overview = reactive({
  total: 28,
  executing: 8,
  completed: 15,
  completionRate: 75.5,
  todayNew: 3
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '计划名称',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 80
  },
  {
    title: '周期',
    dataIndex: 'cycle',
    key: 'cycle',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 100
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 计划列表数据
const planList = ref([
  {
    id: 1,
    name: '2024年第一季度通话质检计划',
    type: 'call',
    cycle: 'monthly',
    status: 'executing',
    progress: 65,
    creator: '张质检',
    createTime: '2024-01-01',
    startTime: '2024-01-01',
    endTime: '2024-03-31',
    sampleRate: 30,
    standard: '通话规范标准',
    description: '针对催收通话的全面质检，重点关注沟通技巧和合规性',
    inspectorList: [
      { name: '张质检', role: '主质检', avatar: '' },
      { name: '李主管', role: '质检组长', avatar: '' },
      { name: '王专员', role: '质检员', avatar: '' }
    ],
    stats: {
      totalTasks: 120,
      completedTasks: 78,
      ongoingTasks: 42,
      avgScore: 87.5
    }
  },
  {
    id: 2,
    name: '外访服务质量检查',
    type: 'visit',
    cycle: 'weekly',
    status: 'published',
    progress: 0,
    creator: '李主管',
    createTime: '2024-01-15',
    startTime: '2024-01-20',
    endTime: '2024-02-20',
    sampleRate: 50,
    standard: '外访服务标准',
    description: '外访催收服务质量检查，确保服务标准和客户体验',
    inspectorList: [
      { name: '李主管', role: '质检组长', avatar: '' },
      { name: '赵专员', role: '质检员', avatar: '' }
    ],
    stats: {
      totalTasks: 0,
      completedTasks: 0,
      ongoingTasks: 0,
      avgScore: 0
    }
  },
  {
    id: 3,
    name: '短信催收合规性检查',
    type: 'sms',
    cycle: 'daily',
    status: 'completed',
    progress: 100,
    creator: '王经理',
    createTime: '2023-12-01',
    startTime: '2023-12-01',
    endTime: '2023-12-31',
    sampleRate: 100,
    standard: '合规操作标准',
    description: '短信催收内容合规性全面检查',
    inspectorList: [
      { name: '王经理', role: '主质检', avatar: '' },
      { name: '张质检', role: '质检员', avatar: '' }
    ],
    stats: {
      totalTasks: 300,
      completedTasks: 300,
      ongoingTasks: 0,
      avgScore: 92.3
    }
  }
])

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    draft: 'default',
    published: 'blue',
    executing: 'green',
    completed: 'purple',
    paused: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    executing: '执行中',
    completed: '已完成',
    paused: '已暂停'
  }
  return texts[status] || status
}

const getTypeText = (type) => {
  const texts = {
    call: '通话质检',
    visit: '外访质检',
    sms: '短信质检',
    service: '服务质检',
    comprehensive: '综合质检'
  }
  return texts[type] || type
}

const getCycleText = (cycle) => {
  const texts = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    quarterly: '每季度',
    custom: '自定义'
  }
  return texts[cycle] || cycle
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleSearch = () => {
  pagination.current = 1
  refreshData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    status: undefined,
    type: undefined,
    creator: '',
    dateRange: [],
    cycle: undefined,
    priority: undefined,
    department: undefined,
    planNo: ''
  })
  handleSearch()
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 导出计划
const exportPlans = () => {
  console.log('导出质检计划')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  refreshData()
}

const viewPlan = (record) => {
  currentPlan.value = record
  showDetailModal.value = true
}

const editPlan = (record) => {
  console.log('编辑计划:', record)
}

const executePlan = (record) => {
  console.log('执行计划:', record)
  record.status = 'executing'
}

const pausePlan = (record) => {
  console.log('暂停计划:', record)
  record.status = 'paused'
}

const resumePlan = (record) => {
  console.log('继续计划:', record)
  record.status = 'executing'
}

const copyPlan = (record) => {
  console.log('复制计划:', record)
}

const deletePlan = (record) => {
  console.log('删除计划:', record)
}

const handleCreateSubmit = () => {
  console.log('创建计划:', createForm)
  showCreateModal.value = false
  resetCreateForm()
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    type: '',
    cycle: '',
    timeRange: [],
    inspectors: [],
    sampleRate: 30,
    standard: '',
    description: '',
    autoExecute: false
  })
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.quality-plan {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.overview-section {
  margin-bottom: 16px;
}

/* 增强搜索样式 */
.enhanced-search {
  margin-bottom: 16px;

  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    > span {
      font-weight: 500;
      font-size: 16px;
    }

    .search-stats {
      display: flex;
      gap: 16px;
      align-items: center;
    }
  }

  .ant-card-extra {
    .ant-space {
      .ant-btn {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

.plan-list {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.plan-detail {
  max-height: 600px;
  overflow-y: auto;
}
</style>