<template>
  <div class="quality-improvement">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>问题整改</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建整改
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计面板 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="待整改" 
              :value="stats.pending" 
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ExclamationCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="整改中" 
              :value="stats.processing" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><SyncOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="已完成" 
              :value="stats.completed" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic 
              title="整改率" 
              :value="stats.completionRate" 
              :value-style="{ color: '#722ed1' }"
              suffix="%"
            >
              <template #prefix><PercentageOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="整改状态">
          <a-select v-model:value="filterForm.status" placeholder="选择状态" style="width: 120px" allowClear>
            <a-select-option value="pending">待整改</a-select-option>
            <a-select-option value="processing">整改中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="overdue">已逾期</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="问题级别">
          <a-select v-model:value="filterForm.severity" placeholder="选择级别" style="width: 120px" allowClear>
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="责任人">
          <a-select v-model:value="filterForm.assignee" placeholder="选择责任人" style="width: 120px" allowClear>
            <a-select-option value="user1">张催收员</a-select-option>
            <a-select-option value="user2">李催收员</a-select-option>
            <a-select-option value="user3">王催收员</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="创建时间">
          <a-range-picker v-model:value="filterForm.dateRange" style="width: 240px" />
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="resetFilter">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 整改列表 -->
    <div class="improvement-list">
      <a-table
        :columns="columns"
        :data-source="improvementList"
        :pagination="pagination"
        :loading="loading"
        :scroll="{ x: 1020 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'severity'">
            <a-tag :color="getSeverityColor(record.severity)">
              {{ getSeverityText(record.severity) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'progress'">
            <a-progress 
              :percent="record.progress" 
              size="small" 
              :status="record.status === 'completed' ? 'success' : record.status === 'overdue' ? 'exception' : 'active'"
            />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space :size="4">
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看详情
              </a-button>
              <a-button
                v-if="record.status === 'pending' || record.status === 'processing'"
                type="link"
                size="small"
                @click="updateProgress(record)"
              >
                更新进度
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="editImprovement(record)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="transferResponsibility(record)">
                      <UserSwitchOutlined />
                      转交
                    </a-menu-item>
                    <a-menu-item @click="extendDeadline(record)">
                      <ClockCircleOutlined />
                      延期
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="closeImprovement(record)" danger>
                      <CloseOutlined />
                      关闭
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建整改模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建整改任务"
      width="800px"
      @ok="handleCreateSubmit"
      @cancel="resetCreateForm"
    >
      <a-form :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="问题标题" required>
          <a-input v-model:value="createForm.title" placeholder="请输入问题标题" />
        </a-form-item>
        
        <a-form-item label="问题类型" required>
          <a-select v-model:value="createForm.type" placeholder="请选择问题类型">
            <a-select-option value="communication">沟通问题</a-select-option>
            <a-select-option value="compliance">合规问题</a-select-option>
            <a-select-option value="service">服务问题</a-select-option>
            <a-select-option value="system">系统问题</a-select-option>
            <a-select-option value="process">流程问题</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="问题级别" required>
          <a-select v-model:value="createForm.severity" placeholder="请选择问题级别">
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="责任人" required>
          <a-select v-model:value="createForm.assignee" placeholder="选择责任人">
            <a-select-option value="user1">张催收员</a-select-option>
            <a-select-option value="user2">李催收员</a-select-option>
            <a-select-option value="user3">王催收员</a-select-option>
            <a-select-option value="user4">赵催收员</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="截止时间" required>
          <a-date-picker 
            v-model:value="createForm.deadline" 
            show-time 
            placeholder="选择截止时间"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="问题描述" required>
          <a-textarea v-model:value="createForm.description" placeholder="请详细描述问题" :rows="4" />
        </a-form-item>
        
        <a-form-item label="整改要求">
          <a-textarea v-model:value="createForm.requirements" placeholder="请输入整改要求" :rows="3" />
        </a-form-item>
        
        <a-form-item label="相关质检">
          <a-select v-model:value="createForm.relatedInspection" placeholder="选择相关质检记录" allowClear>
            <a-select-option value="qc001">QC2024010001 - 李催收员通话质检</a-select-option>
            <a-select-option value="qc002">QC2024010002 - 外访服务质检</a-select-option>
            <a-select-option value="qc003">QC2024010003 - 短信催收质检</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="整改详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentImprovement" class="improvement-detail">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="问题标题">{{ currentImprovement.title }}</a-descriptions-item>
          <a-descriptions-item label="问题类型">{{ getTypeText(currentImprovement.type) }}</a-descriptions-item>
          <a-descriptions-item label="问题级别">
            <a-tag :color="getSeverityColor(currentImprovement.severity)">
              {{ getSeverityText(currentImprovement.severity) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(currentImprovement.status)">
              {{ getStatusText(currentImprovement.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="责任人">{{ currentImprovement.assigneeName }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ currentImprovement.creator }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentImprovement.createTime }}</a-descriptions-item>
          <a-descriptions-item label="截止时间">{{ currentImprovement.deadline }}</a-descriptions-item>
          <a-descriptions-item label="完成时间">{{ currentImprovement.completeTime || '未完成' }}</a-descriptions-item>
          <a-descriptions-item label="整改进度">
            <a-progress :percent="currentImprovement.progress" />
          </a-descriptions-item>
          <a-descriptions-item label="问题描述" :span="2">{{ currentImprovement.description }}</a-descriptions-item>
          <a-descriptions-item label="整改要求" :span="2">{{ currentImprovement.requirements }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>整改进度记录</h4>
        <a-timeline>
          <a-timeline-item 
            v-for="record in currentImprovement.progressRecords" 
            :key="record.id"
            :color="record.type === 'create' ? 'blue' : record.type === 'update' ? 'green' : 'red'"
          >
            <template #dot>
              <PlusCircleOutlined v-if="record.type === 'create'" style="color: #1890ff" />
              <EditOutlined v-else-if="record.type === 'update'" style="color: #52c41a" />
              <CheckCircleOutlined v-else-if="record.type === 'complete'" style="color: #52c41a" />
              <ExclamationCircleOutlined v-else style="color: #faad14" />
            </template>
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="timeline-title">{{ record.title }}</span>
                <span class="timeline-time">{{ record.time }}</span>
              </div>
              <div class="timeline-description">{{ record.description }}</div>
              <div v-if="record.attachments" class="timeline-attachments">
                <a-tag v-for="file in record.attachments" :key="file" color="blue">
                  <PaperClipOutlined />
                  {{ file }}
                </a-tag>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
        
        <a-divider />
        
        <h4>整改效果评估</h4>
        <div v-if="currentImprovement.evaluation">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="整改效果">{{ currentImprovement.evaluation.effect }}</a-descriptions-item>
            <a-descriptions-item label="评估分数">{{ currentImprovement.evaluation.score }}分</a-descriptions-item>
            <a-descriptions-item label="评估人">{{ currentImprovement.evaluation.evaluator }}</a-descriptions-item>
            <a-descriptions-item label="评估时间">{{ currentImprovement.evaluation.time }}</a-descriptions-item>
            <a-descriptions-item label="评估说明" :span="2">{{ currentImprovement.evaluation.comments }}</a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-else class="no-evaluation">
          <a-empty description="暂无评估信息" />
        </div>
      </div>
    </a-modal>

    <!-- 更新进度模态框 -->
    <a-modal
      v-model:open="showProgressModal"
      title="更新整改进度"
      width="600px"
      @ok="handleProgressSubmit"
    >
      <a-form :model="progressForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="进度百分比" required>
          <a-slider 
            v-model:value="progressForm.progress" 
            :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
          />
        </a-form-item>
        
        <a-form-item label="进度说明" required>
          <a-textarea v-model:value="progressForm.description" placeholder="请说明当前进度" :rows="3" />
        </a-form-item>
        
        <a-form-item label="遇到问题">
          <a-textarea v-model:value="progressForm.issues" placeholder="描述遇到的问题" :rows="2" />
        </a-form-item>
        
        <a-form-item label="下步计划">
          <a-textarea v-model:value="progressForm.nextPlan" placeholder="下一步计划" :rows="2" />
        </a-form-item>
        
        <a-form-item label="附件上传">
          <a-upload
            v-model:file-list="progressForm.attachments"
            name="file"
            action="/upload"
            list-type="text"
          >
            <a-button>
              <UploadOutlined />
              上传文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  ReloadOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  PercentageOutlined,
  EditOutlined,
  UserSwitchOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  DownOutlined,
  PlusCircleOutlined,
  PaperClipOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showProgressModal = ref(false)
const currentImprovement = ref(null)

// 筛选表单
const filterForm = reactive({
  status: undefined,
  severity: undefined,
  assignee: undefined,
  dateRange: []
})

// 创建表单
const createForm = reactive({
  title: '',
  type: '',
  severity: '',
  assignee: '',
  deadline: null,
  description: '',
  requirements: '',
  relatedInspection: undefined
})

// 进度更新表单
const progressForm = reactive({
  progress: 0,
  description: '',
  issues: '',
  nextPlan: '',
  attachments: []
})

// 统计数据
const stats = reactive({
  pending: 12,
  processing: 8,
  completed: 35,
  completionRate: 85.2
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '问题标题',
    dataIndex: 'title',
    key: 'title',
    width: 180,
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 80
  },
  {
    title: '级别',
    dataIndex: 'severity',
    key: 'severity',
    width: 80
  },
  {
    title: '责任人',
    dataIndex: 'assigneeName',
    key: 'assigneeName',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 整改列表数据
const improvementList = ref([
  {
    id: 1,
    title: '李催收员沟通技巧待改进',
    type: 'communication',
    severity: 'high',
    assigneeName: '李催收员',
    assignee: 'user2',
    status: 'processing',
    progress: 60,
    creator: '张质检',
    createTime: '2024-01-15 09:00:00',
    deadline: '2024-01-25 18:00:00',
    completeTime: null,
    description: '在质检中发现李催收员与客户沟通时语气过于强硬，需要改进沟通技巧',
    requirements: '1. 参加沟通技巧培训\n2. 学习情绪控制方法\n3. 实践温和催收话术',
    relatedInspection: 'qc001',
    progressRecords: [
      {
        id: 1,
        type: 'create',
        title: '创建整改任务',
        description: '基于质检结果创建整改任务',
        time: '2024-01-15 09:00:00',
        operator: '张质检'
      },
      {
        id: 2,
        type: 'update',
        title: '开始整改',
        description: '已安排参加沟通技巧培训课程',
        time: '2024-01-16 14:30:00',
        operator: '李催收员'
      },
      {
        id: 3,
        type: 'update',
        title: '培训完成',
        description: '完成2天沟通技巧培训，开始实践应用',
        time: '2024-01-18 17:00:00',
        operator: '李催收员',
        attachments: ['培训证书.pdf']
      }
    ],
    evaluation: null
  },
  {
    id: 2,
    title: '短信内容合规性问题',
    type: 'compliance',
    severity: 'critical',
    assigneeName: '王催收员',
    assignee: 'user3',
    status: 'completed',
    progress: 100,
    creator: '王专员',
    createTime: '2024-01-10 10:00:00',
    deadline: '2024-01-20 18:00:00',
    completeTime: '2024-01-19 16:30:00',
    description: '发现短信内容存在不当用词，可能引起客户投诉',
    requirements: '1. 重新学习短信规范\n2. 建立短信审核机制\n3. 使用标准模板',
    relatedInspection: 'qc003',
    progressRecords: [
      {
        id: 1,
        type: 'create',
        title: '创建整改任务',
        description: '发现短信合规问题，要求立即整改',
        time: '2024-01-10 10:00:00',
        operator: '王专员'
      },
      {
        id: 2,
        type: 'update',
        title: '学习规范',
        description: '重新学习短信发送规范和法律要求',
        time: '2024-01-12 09:00:00',
        operator: '王催收员'
      },
      {
        id: 3,
        type: 'complete',
        title: '整改完成',
        description: '建立审核机制，使用标准模板，通过验证',
        time: '2024-01-19 16:30:00',
        operator: '王催收员'
      }
    ],
    evaluation: {
      effect: '优秀',
      score: 95,
      evaluator: '王专员',
      time: '2024-01-20 10:00:00',
      comments: '整改彻底，建立了完善的审核机制，效果显著'
    }
  },
  {
    id: 3,
    title: '外访服务流程不规范',
    type: 'process',
    severity: 'medium',
    assigneeName: '张催收员',
    assignee: 'user1',
    status: 'pending',
    progress: 0,
    creator: '李主管',
    createTime: '2024-01-18 15:00:00',
    deadline: '2024-01-30 18:00:00',
    completeTime: null,
    description: '外访过程中未严格按照标准流程执行，存在遗漏环节',
    requirements: '1. 重新学习外访流程\n2. 使用标准检查清单\n3. 加强现场记录',
    relatedInspection: 'qc002',
    progressRecords: [
      {
        id: 1,
        type: 'create',
        title: '创建整改任务',
        description: '外访质检发现流程问题，需要整改',
        time: '2024-01-18 15:00:00',
        operator: '李主管'
      }
    ],
    evaluation: null
  }
])

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    overdue: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待整改',
    processing: '整改中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return texts[status] || status
}

const getSeverityColor = (severity) => {
  const colors = {
    critical: 'red',
    high: 'orange',
    medium: 'blue',
    low: 'green'
  }
  return colors[severity] || 'default'
}

const getSeverityText = (severity) => {
  const texts = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || severity
}

const getTypeText = (type) => {
  const texts = {
    communication: '沟通问题',
    compliance: '合规问题',
    service: '服务问题',
    system: '系统问题',
    process: '流程问题'
  }
  return texts[type] || type
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleSearch = () => {
  pagination.current = 1
  refreshData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    status: undefined,
    severity: undefined,
    assignee: undefined,
    dateRange: []
  })
  handleSearch()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  refreshData()
}

const viewDetail = (record) => {
  currentImprovement.value = record
  showDetailModal.value = true
}

const updateProgress = (record) => {
  currentImprovement.value = record
  progressForm.progress = record.progress
  showProgressModal.value = true
}

const editImprovement = (record) => {
  console.log('编辑整改:', record)
}

const transferResponsibility = (record) => {
  console.log('转交责任:', record)
}

const extendDeadline = (record) => {
  console.log('延期处理:', record)
}

const closeImprovement = (record) => {
  console.log('关闭整改:', record)
}

const handleCreateSubmit = () => {
  console.log('创建整改:', createForm)
  showCreateModal.value = false
  resetCreateForm()
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    title: '',
    type: '',
    severity: '',
    assignee: '',
    deadline: null,
    description: '',
    requirements: '',
    relatedInspection: undefined
  })
}

const handleProgressSubmit = () => {
  console.log('更新进度:', progressForm)
  
  // 更新当前记录的进度
  if (currentImprovement.value) {
    currentImprovement.value.progress = progressForm.progress
    
    // 添加新的进度记录
    currentImprovement.value.progressRecords.push({
      id: Date.now(),
      type: 'update',
      title: '进度更新',
      description: progressForm.description,
      time: new Date().toLocaleString(),
      operator: '当前用户'
    })
    
    // 如果进度100%，更新状态
    if (progressForm.progress === 100) {
      currentImprovement.value.status = 'completed'
      currentImprovement.value.completeTime = new Date().toLocaleString()
    }
  }
  
  showProgressModal.value = false
  
  // 重置表单
  Object.assign(progressForm, {
    progress: 0,
    description: '',
    issues: '',
    nextPlan: '',
    attachments: []
  })
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.quality-improvement {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-section {
  margin-bottom: 16px;
}

.filter-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.improvement-list {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.improvement-detail {
  max-height: 700px;
  overflow-y: auto;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.timeline-title {
  font-weight: 500;
  color: #262626;
}

.timeline-time {
  color: #8c8c8c;
  font-size: 12px;
}

.timeline-description {
  color: #595959;
  margin-bottom: 8px;
}

.timeline-attachments {
  margin-top: 8px;
}

.no-evaluation {
  text-align: center;
  padding: 40px;
}
</style>