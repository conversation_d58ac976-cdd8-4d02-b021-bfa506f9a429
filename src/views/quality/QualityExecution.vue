<template>
  <div class="quality-execution">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>质检执行</h2>
        <div class="header-actions">
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            新建质检
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计面板 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="待质检"
              :value="stats.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><ClockCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="进行中"
              :value="stats.ongoing"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><PlayCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="已完成"
              :value="stats.completed"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><CheckCircleOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card>
            <a-statistic
              title="今日质检"
              :value="stats.todayInspection"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix><CalendarOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="4">
          <a-card>
            <a-statistic
              title="平均得分"
              :value="stats.avgScore"
              :value-style="{ color: '#faad14' }"
              suffix="分"
            >
              <template #prefix><TrophyOutlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <a-card class="search-card enhanced-search">
      <template #title>
        质检执行搜索
      </template>

      <a-form :model="filterForm" @submit="handleSearch">
        <!-- 基础搜索 -->
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="质检状态">
              <a-select
                v-model:value="filterForm.status"
                placeholder="请选择状态"
                allow-clear
              >
                <template #suffixIcon><CheckCircleOutlined /></template>
                <a-select-option value="pending">
                  <a-tag color="warning">待质检</a-tag>
                </a-select-option>
                <a-select-option value="ongoing">
                  <a-tag color="processing">进行中</a-tag>
                </a-select-option>
                <a-select-option value="completed">
                  <a-tag color="success">已完成</a-tag>
                </a-select-option>
                <a-select-option value="rejected">
                  <a-tag color="error">不合格</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="质检类型">
              <a-select
                v-model:value="filterForm.type"
                placeholder="请选择类型"
                allow-clear
              >
                <template #suffixIcon><AuditOutlined /></template>
                <a-select-option value="call">
                  <a-tag color="blue">通话质检</a-tag>
                </a-select-option>
                <a-select-option value="visit">
                  <a-tag color="green">外访质检</a-tag>
                </a-select-option>
                <a-select-option value="sms">
                  <a-tag color="orange">短信质检</a-tag>
                </a-select-option>
                <a-select-option value="service">
                  <a-tag color="purple">服务质检</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="质检员">
              <a-select
                v-model:value="filterForm.inspector"
                placeholder="请选择质检员"
                allow-clear
              >
                <template #suffixIcon><UserOutlined /></template>
                <a-select-option value="user1">张质检</a-select-option>
                <a-select-option value="user2">李主管</a-select-option>
                <a-select-option value="user3">王专员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="质检时间">
              <a-range-picker
                v-model:value="filterForm.dateRange"
                :presets="timePresets"
                format="YYYY-MM-DD"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 高级搜索 -->
        <div v-if="showAdvanced">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="得分范围">
                <a-slider
                  v-model:value="filterForm.scoreRange"
                  range
                  :min="0"
                  :max="100"
                  :marks="{ 0: '0分', 60: '及格', 80: '良好', 100: '优秀' }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="被质检人">
                <a-input
                  v-model:value="filterForm.inspectee"
                  placeholder="请输入被质检人"
                  allow-clear
                >
                  <template #prefix><UserOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="质检结果">
                <a-select
                  v-model:value="filterForm.result"
                  placeholder="请选择结果"
                  allow-clear
                >
                  <a-select-option value="excellent">优秀</a-select-option>
                  <a-select-option value="good">良好</a-select-option>
                  <a-select-option value="qualified">合格</a-select-option>
                  <a-select-option value="unqualified">不合格</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="质检编号">
                <a-input
                  v-model:value="filterForm.inspectionNo"
                  placeholder="请输入质检编号"
                  allow-clear
                >
                  <template #prefix><NumberOutlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 搜索按钮 -->
        <a-row>
          <a-col :span="24">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetFilter">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
                <a-button @click="toggleAdvanced">
                  <span>{{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}</span>
                  <component :is="showAdvanced ? 'UpOutlined' : 'DownOutlined'" />
                </a-button>
                <a-button @click="exportExecutions">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 质检任务列表 -->
    <div class="task-list">
      <a-table
        :columns="columns"
        :data-source="taskList"
        :pagination="pagination"
        :loading="loading"
        :scroll="{ x: 1000 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>{{ getTypeText(record.type) }}</span>
          </template>
          <template v-else-if="column.key === 'score'">
            <span v-if="record.score !== null" :class="getScoreClass(record.score)">
              {{ record.score }}分
            </span>
            <span v-else class="text-gray">未评分</span>
          </template>
          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space :size="4">
              <a-button
                v-if="record.status === 'pending' || record.status === 'ongoing'"
                type="primary"
                size="small"
                @click="startInspection(record)"
              >
                {{ record.status === 'pending' ? '开始质检' : '继续质检' }}
              </a-button>
              <a-button
                v-if="record.status === 'completed'"
                type="link"
                size="small"
                @click="viewResult(record)"
              >
                查看结果
              </a-button>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看详情
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="editTask(record)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="assignTask(record)">
                      <UserOutlined />
                      重新分配
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deleteTask(record)" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建质检任务模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建质检任务"
      width="800px"
      @ok="handleCreateSubmit"
      @cancel="resetCreateForm"
    >
      <a-form :model="createForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="任务标题" required>
          <a-input v-model:value="createForm.title" placeholder="请输入任务标题" />
        </a-form-item>
        
        <a-form-item label="质检类型" required>
          <a-select v-model:value="createForm.type" placeholder="请选择质检类型">
            <a-select-option value="call">通话质检</a-select-option>
            <a-select-option value="visit">外访质检</a-select-option>
            <a-select-option value="sms">短信质检</a-select-option>
            <a-select-option value="service">服务质检</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="质检对象" required>
          <a-select v-model:value="createForm.target" placeholder="选择质检对象" mode="multiple">
            <a-select-option value="emp1">李催收员</a-select-option>
            <a-select-option value="emp2">张催收员</a-select-option>
            <a-select-option value="emp3">王催收员</a-select-option>
            <a-select-option value="emp4">赵催收员</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="质检员" required>
          <a-select v-model:value="createForm.inspector" placeholder="选择质检员">
            <a-select-option value="user1">张质检</a-select-option>
            <a-select-option value="user2">李主管</a-select-option>
            <a-select-option value="user3">王专员</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="优先级" required>
          <a-select v-model:value="createForm.priority" placeholder="选择优先级">
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="截止时间" required>
          <a-date-picker 
            v-model:value="createForm.deadline" 
            show-time 
            placeholder="选择截止时间"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="质检标准">
          <a-select v-model:value="createForm.standard" placeholder="选择质检标准">
            <a-select-option value="standard1">通话规范标准</a-select-option>
            <a-select-option value="standard2">外访服务标准</a-select-option>
            <a-select-option value="standard3">沟通技巧标准</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="任务描述">
          <a-textarea v-model:value="createForm.description" placeholder="请输入任务描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 质检详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="质检任务详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentTask" class="task-detail">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="任务标题">{{ currentTask.title }}</a-descriptions-item>
          <a-descriptions-item label="质检类型">{{ getTypeText(currentTask.type) }}</a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(currentTask.status)">
              {{ getStatusText(currentTask.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentTask.priority)">
              {{ getPriorityText(currentTask.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="质检员">{{ currentTask.inspector }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentTask.createTime }}</a-descriptions-item>
          <a-descriptions-item label="截止时间">{{ currentTask.deadline }}</a-descriptions-item>
          <a-descriptions-item label="完成时间">{{ currentTask.completeTime || '未完成' }}</a-descriptions-item>
          <a-descriptions-item label="质检得分" v-if="currentTask.score !== null">
            <span :class="getScoreClass(currentTask.score)">{{ currentTask.score }}分</span>
          </a-descriptions-item>
          <a-descriptions-item label="任务描述" :span="2">{{ currentTask.description }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 质检执行模态框 -->
    <a-modal
      v-model:open="showInspectionModal"
      title="质检执行"
      width="1200px"
      :footer="null"
      :mask-closable="false"
    >
      <div v-if="currentTask" class="inspection-content">
        <a-steps :current="inspectionStep" class="inspection-steps">
          <a-step title="基础信息" />
          <a-step title="质检评分" />
          <a-step title="总结评价" />
        </a-steps>
        
        <div class="step-content">
          <!-- 步骤1: 基础信息 -->
          <div v-if="inspectionStep === 0" class="step-basic">
            <h4>质检对象信息</h4>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="被质检人">{{ currentTask.targetName }}</a-descriptions-item>
              <a-descriptions-item label="部门">{{ currentTask.department }}</a-descriptions-item>
              <a-descriptions-item label="岗位">{{ currentTask.position }}</a-descriptions-item>
              <a-descriptions-item label="工作时长">{{ currentTask.workTime }}</a-descriptions-item>
            </a-descriptions>
            
            <h4 style="margin-top: 24px;">质检内容</h4>
            <a-list 
              :data-source="inspectionContent" 
              item-layout="vertical"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.title }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                  <div class="content-actions">
                    <a-button size="small" @click="playRecord(item)" v-if="item.type === 'audio'">
                      <SoundOutlined />
                      播放录音
                    </a-button>
                    <a-button size="small" @click="viewContent(item)" v-if="item.type === 'text'">
                      <FileTextOutlined />
                      查看内容
                    </a-button>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>
          
          <!-- 步骤2: 质检评分 -->
          <div v-if="inspectionStep === 1" class="step-scoring">
            <h4>评分标准</h4>
            <div class="scoring-form">
              <a-form :model="scoringForm" layout="vertical">
                <div v-for="item in scoringItems" :key="item.id" class="scoring-item">
                  <div class="item-header">
                    <span class="item-name">{{ item.name }}</span>
                    <span class="item-weight">权重: {{ item.weight }}%</span>
                  </div>
                  <div class="item-content">
                    <a-rate 
                      v-model:value="scoringForm[item.id]" 
                      :count="10" 
                      allow-half
                      style="margin-bottom: 8px"
                    />
                    <span class="score-display">{{ (scoringForm[item.id] || 0) * 10 }}分</span>
                  </div>
                  <a-textarea 
                    v-model:value="scoringForm[`${item.id}_comment`]" 
                    placeholder="评价说明"
                    :rows="2"
                    style="margin-top: 8px"
                  />
                </div>
              </a-form>
              
              <div class="total-score">
                <h4>总分: {{ calculateTotalScore() }}分</h4>
              </div>
            </div>
          </div>
          
          <!-- 步骤3: 总结评价 -->
          <div v-if="inspectionStep === 2" class="step-summary">
            <a-form layout="vertical">
              <a-form-item label="总体评价">
                <a-textarea 
                  v-model:value="summaryForm.evaluation" 
                  placeholder="请输入总体评价"
                  :rows="4"
                />
              </a-form-item>
              
              <a-form-item label="发现问题">
                <a-textarea 
                  v-model:value="summaryForm.problems" 
                  placeholder="请描述发现的问题"
                  :rows="3"
                />
              </a-form-item>
              
              <a-form-item label="改进建议">
                <a-textarea 
                  v-model:value="summaryForm.suggestions" 
                  placeholder="请提供改进建议"
                  :rows="3"
                />
              </a-form-item>
              
              <a-form-item label="是否合格">
                <a-radio-group v-model:value="summaryForm.qualified">
                  <a-radio :value="true">合格</a-radio>
                  <a-radio :value="false">不合格</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
          </div>
        </div>
        
        <div class="step-actions">
          <a-button v-if="inspectionStep > 0" @click="inspectionStep--">
            上一步
          </a-button>
          <a-button 
            v-if="inspectionStep < 2" 
            type="primary" 
            @click="inspectionStep++"
          >
            下一步
          </a-button>
          <a-button 
            v-if="inspectionStep === 2" 
            type="primary" 
            @click="submitInspection"
          >
            提交质检
          </a-button>
          <a-button @click="showInspectionModal = false">
            取消
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  ReloadOutlined,
  PlusOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  EditOutlined,
  UserOutlined,
  DeleteOutlined,
  DownOutlined,
  SoundOutlined,
  FileTextOutlined,
  UpOutlined,
  SearchOutlined,
  AuditOutlined,
  NumberOutlined,
  DownloadOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showInspectionModal = ref(false)
const currentTask = ref(null)
const inspectionStep = ref(0)
const showAdvanced = ref(false)

// 筛选表单
const filterForm = reactive({
  status: undefined,
  type: undefined,
  inspector: undefined,
  scoreRange: [0, 100],
  dateRange: [],
  inspectee: '',
  result: undefined,
  inspectionNo: ''
})

// 时间预设
const timePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] }
]

// 创建表单
const createForm = reactive({
  title: '',
  type: '',
  target: [],
  inspector: '',
  priority: '',
  deadline: null,
  standard: '',
  description: ''
})

// 评分表单
const scoringForm = reactive({})

// 总结表单
const summaryForm = reactive({
  evaluation: '',
  problems: '',
  suggestions: '',
  qualified: true
})

// 统计数据
const stats = reactive({
  pending: 15,
  ongoing: 8,
  completed: 42,
  avgScore: 86.5,
  todayInspection: 12
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '任务标题',
    dataIndex: 'title',
    key: 'title',
    width: 180,
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 80
  },
  {
    title: '质检对象',
    dataIndex: 'targetName',
    key: 'targetName',
    width: 90
  },
  {
    title: '质检员',
    dataIndex: 'inspector',
    key: 'inspector',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 70
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    width: 60
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    fixed: 'right'
  }
]

// 质检任务数据
const taskList = ref([
  {
    id: 1,
    title: '李催收员通话质检-案件CASE001',
    type: 'call',
    targetName: '李催收员',
    department: '催收一部',
    position: '催收专员',
    workTime: '2年',
    inspector: '张质检',
    status: 'ongoing',
    priority: 'high',
    score: null,
    deadline: '2024-01-20 18:00:00',
    createTime: '2024-01-15 09:00:00',
    completeTime: null,
    description: '针对李催收员处理案件CASE001的通话质检'
  },
  {
    id: 2,
    title: '外访服务质量检查-张催收员',
    type: 'visit',
    targetName: '张催收员',
    department: '催收二部',
    position: '高级催收员',
    workTime: '3年',
    inspector: '李主管',
    status: 'completed',
    priority: 'medium',
    score: 92,
    deadline: '2024-01-18 17:00:00',
    createTime: '2024-01-15 10:00:00',
    completeTime: '2024-01-17 16:30:00',
    description: '张催收员外访服务质量检查'
  },
  {
    id: 3,
    title: '短信催收合规检查',
    type: 'sms',
    targetName: '王催收员',
    department: '催收三部',
    position: '催收专员',
    workTime: '1年',
    inspector: '王专员',
    status: 'pending',
    priority: 'low',
    score: null,
    deadline: '2024-01-22 18:00:00',
    createTime: '2024-01-16 14:00:00',
    completeTime: null,
    description: '王催收员短信催收内容合规性检查'
  }
])

// 质检内容数据
const inspectionContent = ref([
  {
    id: 1,
    title: '通话录音1',
    description: '与客户张三的沟通录音',
    type: 'audio',
    duration: '5分32秒'
  },
  {
    id: 2,
    title: '通话录音2',
    description: '与客户李四的沟通录音',
    type: 'audio',
    duration: '3分18秒'
  },
  {
    id: 3,
    title: '短信记录',
    description: '发送给客户的催收短信内容',
    type: 'text',
    content: '尊敬的客户，您好...'
  }
])

// 评分项目
const scoringItems = ref([
  { id: 'communication', name: '沟通技巧', weight: 30 },
  { id: 'compliance', name: '合规性', weight: 25 },
  { id: 'efficiency', name: '工作效率', weight: 20 },
  { id: 'professionalism', name: '专业性', weight: 15 },
  { id: 'result', name: '处理结果', weight: 10 }
])

// 计算总分
const calculateTotalScore = () => {
  let totalScore = 0
  scoringItems.value.forEach(item => {
    const score = scoringForm[item.id] || 0
    totalScore += (score * 10) * (item.weight / 100)
  })
  return Math.round(totalScore * 10) / 10
}

// 方法定义
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    ongoing: 'blue',
    completed: 'green',
    rejected: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待质检',
    ongoing: '进行中',
    completed: '已完成',
    rejected: '不合格'
  }
  return texts[status] || status
}

const getTypeText = (type) => {
  const texts = {
    call: '通话质检',
    visit: '外访质检',
    sms: '短信质检',
    service: '服务质检'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 70) return 'score-average'
  return 'score-poor'
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const handleSearch = () => {
  pagination.current = 1
  refreshData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    status: undefined,
    type: undefined,
    inspector: undefined,
    scoreRange: [0, 100],
    dateRange: [],
    inspectee: '',
    result: undefined,
    inspectionNo: ''
  })
  handleSearch()
}

// 切换高级搜索
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 导出执行记录
const exportExecutions = () => {
  console.log('导出质检执行记录')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  refreshData()
}

const startInspection = (record) => {
  currentTask.value = record
  inspectionStep.value = 0
  showInspectionModal.value = true
}

const viewResult = (record) => {
  console.log('查看结果:', record)
}

const viewDetail = (record) => {
  currentTask.value = record
  showDetailModal.value = true
}

const editTask = (record) => {
  console.log('编辑任务:', record)
}

const assignTask = (record) => {
  console.log('重新分配:', record)
}

const deleteTask = (record) => {
  console.log('删除任务:', record)
}

const handleCreateSubmit = () => {
  console.log('创建任务:', createForm)
  showCreateModal.value = false
  resetCreateForm()
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    title: '',
    type: '',
    target: [],
    inspector: '',
    priority: '',
    deadline: null,
    standard: '',
    description: ''
  })
}

const playRecord = (item) => {
  console.log('播放录音:', item)
}

const viewContent = (item) => {
  console.log('查看内容:', item)
}

const submitInspection = () => {
  console.log('提交质检:', {
    scoring: scoringForm,
    summary: summaryForm,
    totalScore: calculateTotalScore()
  })
  
  currentTask.value.status = 'completed'
  currentTask.value.score = calculateTotalScore()
  currentTask.value.completeTime = new Date().toLocaleString()
  
  showInspectionModal.value = false
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.quality-execution {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-section {
  margin-bottom: 16px;
}

.filter-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-list {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-detail {
  max-height: 600px;
  overflow-y: auto;
}

.inspection-content {
  max-height: 700px;
  overflow-y: auto;
}

.inspection-steps {
  margin-bottom: 24px;
}

.step-content {
  min-height: 400px;
  margin-bottom: 24px;
}

.step-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.step-actions .ant-btn {
  margin: 0 8px;
}

.content-actions {
  margin-top: 8px;
}

.scoring-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-name {
  font-weight: 500;
  color: #262626;
}

.item-weight {
  color: #666;
  font-size: 12px;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.score-display {
  font-weight: 500;
  color: #1890ff;
}

.total-score {
  text-align: center;
  padding: 16px;
  background: #f6f8fa;
  border-radius: 6px;
  margin-top: 24px;
}

.score-excellent {
  color: #52c41a;
  font-weight: 500;
}

.score-good {
  color: #1890ff;
  font-weight: 500;
}

.score-average {
  color: #faad14;
  font-weight: 500;
}

.score-poor {
  color: #ff4d4f;
  font-weight: 500;
}

.text-gray {
  color: #999;
}

/* 增强搜索样式 */
.enhanced-search {
  margin-bottom: 16px;

  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    > span {
      font-weight: 500;
      font-size: 16px;
    }

    .search-stats {
      display: flex;
      gap: 16px;
      align-items: center;
    }
  }

  .ant-card-extra {
    .ant-space {
      .ant-btn {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    display: flex;
    justify-content: flex-start;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>