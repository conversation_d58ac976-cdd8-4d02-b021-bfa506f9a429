* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

// 全局样式
.page-container {
  padding: 24px;
  background: #f0f2f5;
}

.content-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
}

.table-operations {
  margin-bottom: 16px;
}

.status-tag {
  &.success {
    color: #52c41a;
    background: #f6ffed;
    border-color: #b7eb8f;
  }

  &.warning {
    color: #faad14;
    background: #fffbe6;
    border-color: #ffe58f;
  }

  &.error {
    color: #ff4d4f;
    background: #fff2f0;
    border-color: #ffccc7;
  }

  &.default {
    color: #d9d9d9;
    background: #fafafa;
    border-color: #d9d9d9;
  }
}