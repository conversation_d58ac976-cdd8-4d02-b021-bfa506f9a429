import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      dts: 'src/auto-imports.d.ts',
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false,
        }),
      ],
      dts: 'src/components.d.ts',
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 4000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 Vue 相关依赖打包到一个 chunk
          vue: ['vue', 'vue-router', 'pinia'],
          // 将 Ant Design Vue 打包到一个 chunk
          antd: ['ant-design-vue', '@ant-design/icons-vue'],
          // 将 ECharts 打包到一个 chunk
          echarts: ['echarts', 'vue-echarts'],
          // 将工具类库打包到一个 chunk
          utils: ['axios', 'dayjs', 'lodash-es'],
        },
      },
    },
    // 调整 chunk 大小警告的阈值
    chunkSizeWarningLimit: 800,
  },
})